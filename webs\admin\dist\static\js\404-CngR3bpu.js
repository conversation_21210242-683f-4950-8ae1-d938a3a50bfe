import{_ as k,r as i,c as g,a as o,d as e,w as s,u as d,j as m,B as v,C as w,$ as B,at as b,i as x,l as C,m as f}from"./index-XtNpSMFt.js";const H={__name:"404",setup(c,{expose:t}){t();const r=d(),u={router:r,goHome:()=>{r.push("/")},goBack:()=>{r.go(-1)},get useRouter(){return d},get House(){return x},get ArrowLeft(){return b},get Search(){return B},get Bowl(){return w},get ShoppingCart(){return v},get User(){return m}};return Object.defineProperty(u,"__isScriptSetup",{enumerable:!1,value:!0}),u}},S={class:"not-found-container"},q={class:"not-found-content"},y={class:"error-actions"},N={class:"quick-links"},V={class:"link-grid"},j={class:"not-found-illustration"},A={class:"illustration-circle"};function E(c,t,r,n,p,u){const l=i("el-icon"),_=i("el-button"),a=i("router-link");return C(),g("div",S,[o("div",q,[t[7]||(t[7]=o("div",{class:"error-code"},"404",-1)),t[8]||(t[8]=o("div",{class:"error-message"},"页面未找到",-1)),t[9]||(t[9]=o("div",{class:"error-description"}," 抱歉，您访问的页面不存在或已被移除 ",-1)),o("div",y,[e(_,{type:"primary",onClick:n.goHome},{default:s(()=>[e(l,null,{default:s(()=>[e(n.House)]),_:1}),t[0]||(t[0]=f(" 返回首页 ",-1))]),_:1,__:[0]}),e(_,{onClick:n.goBack},{default:s(()=>[e(l,null,{default:s(()=>[e(n.ArrowLeft)]),_:1}),t[1]||(t[1]=f(" 返回上页 ",-1))]),_:1,__:[1]})]),o("div",N,[t[6]||(t[6]=o("h4",null,"快速导航",-1)),o("div",V,[e(a,{to:"/dashboard",class:"quick-link"},{default:s(()=>[e(l,null,{default:s(()=>[e(n.House)]),_:1}),t[2]||(t[2]=o("span",null,"仪表盘",-1))]),_:1,__:[2]}),e(a,{to:"/menu/dishes",class:"quick-link"},{default:s(()=>[e(l,null,{default:s(()=>[e(n.Bowl)]),_:1}),t[3]||(t[3]=o("span",null,"菜品管理",-1))]),_:1,__:[3]}),e(a,{to:"/order/list",class:"quick-link"},{default:s(()=>[e(l,null,{default:s(()=>[e(n.ShoppingCart)]),_:1}),t[4]||(t[4]=o("span",null,"订单管理",-1))]),_:1,__:[4]}),e(a,{to:"/user/list",class:"quick-link"},{default:s(()=>[e(l,null,{default:s(()=>[e(n.User)]),_:1}),t[5]||(t[5]=o("span",null,"用户管理",-1))]),_:1,__:[5]})])])]),o("div",j,[o("div",A,[e(l,{size:"120",color:"#e6f7ff"},{default:s(()=>[e(n.Search)]),_:1})])])])}const R=k(H,[["render",E],["__scopeId","data-v-52711040"],["__file","E:/wx-nan/webs/admin/src/views/404.vue"]]);export{R as default};
