<!-- 🎨 现代卡片式菜品详情页面 -->
<view class="detail-page">
  <!-- 主要内容区域 -->
  <scroll-view
    class="detail-content"
    scroll-y="true"
    enhanced="true"
    show-scrollbar="false"
  >
    <!-- 🎨 现代卡片式主要信息区域 -->
    <view class="hero-card">
      <view class="card-layout">
        <!-- 左侧图片 -->
        <view class="image-section">
          <image
            class="dish-image"
            src="{{menuItem.img || menuItem.image || '/assets/image/default-dish.jpg'}}"
            mode="aspectFill"
            lazy-load="true"
            bindtap="onImagePreview"
            bindload="onImageLoad"
          />

          <!-- 图片加载状态 -->
          <view class="image-loading" wx:if="{{!imageLoaded}}">
            <van-loading type="spinner" size="16px" color="#3b82f6" />
          </view>
        </view>

        <!-- 右侧信息 -->
        <view class="info-section">
          <view class="dish-name">{{menuItem.name || '美味菜品'}}</view>

          <!-- 标签展示 -->
          <view
            class="tags-container"
            wx:if="{{menuItem.tags && menuItem.tags.length > 0}}"
          >
            <text
              wx:for="{{menuItem.tags}}"
              wx:key="*this"
              wx:for-item="tag"
              class="tag-item"
              >{{tag}}
            </text>
          </view>

          <!-- 基本信息 -->
          <view class="meta-info">
            <view class="meta-item" wx:if="{{menuItem.category}}">
              <text class="category-badge"
                >{{menuItem.category.name || menuItem.categoryName || '未分类'}}
              </text>
            </view>
            <view class="meta-item" wx:if="{{menuItem.creator}}">
              <van-icon name="manager-o" size="12px" color="#6366f1" />
              <text
                class="meta-text creator-name"
                >{{menuItem.creator.name}}</text
              >
            </view>
            <view class="meta-item" wx:if="{{menuItem.createdDate}}">
              <van-icon name="clock-o" size="12px" color="#9ca3af" />
              <text class="meta-text">{{menuItem.createdDate}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 🎨 详细信息卡片区域 -->
    <view class="content-section">
      <!-- � 菜品描述卡片 -->
      <view
        class="info-card cooking-card"
        wx:if="{{menuItem.remark || menuItem.description}}"
      >
        <view class="card-header">
          <view class="header-icon">
            <van-icon name="description" size="18px" color="#8b5cf6" />
          </view>
          <text class="card-title">菜品介绍</text>
        </view>
        <view class="card-content">
          <view class="description-text">
            {{menuItem.remark || menuItem.description}}
          </view>
        </view>
      </view>

      <!-- �🛒 食材清单卡片 -->
      <view class="info-card cooking-card" wx:if="{{menuItem.ingredients}}">
        <view class="card-header">
          <view class="header-icon">
            <van-icon name="shopping-cart-o" size="18px" color="#10b981" />
          </view>
          <text class="card-title">所需食材</text>
        </view>
        <view class="card-content">
          <view class="ingredients-text">
            {{menuItem.ingredients}}
          </view>
        </view>
      </view>

      <!-- 👨‍🍳 制作方法卡片 -->
      <view class="info-card cooking-card" wx:if="{{menuItem.cookingMethod}}">
        <view class="card-header">
          <view class="header-icon">
            <van-icon name="guide-o" size="18px" color="#f59e0b" />
          </view>
          <text class="card-title">制作方法</text>
        </view>
        <view class="card-content">
          <view class="cooking-text">
            {{menuItem.cookingMethod}}
          </view>
        </view>
      </view>
    </view>
    <view style="height: 160rpx;"></view>
    <!-- 底部安全区域 -->
    <view class="safe-bottom"></view>
  </scroll-view>

  <!-- 🔥 底部操作栏 -->
  <view class="bottom-actions">
    <view class="action-left">
      <view
        class="action-btn secondary"
        bindtap="goToBasket"
        wx:if="{{basketCount > 0}}"
      >
        <van-icon name="shopping-cart-o" size="20px" color="#6b7280" />
        <text class="btn-text">购物车({{basketCount}})</text>
      </view>
    </view>
    <view class="action-right">
      <view class="action-btn primary" bindtap="addToCart">
        <van-icon name="plus" size="18px" color="#fff" />
        <text class="btn-text">加入购物车</text>
      </view>
    </view>
  </view>
</view>
