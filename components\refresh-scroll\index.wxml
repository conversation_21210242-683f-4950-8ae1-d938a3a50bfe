<!-- 通用下拉刷新和上拉加载更多组件 -->
<view class="refresh-scroll-wrapper">
  <!-- 顶部刷新指示器 -->
  <view class="refresh-indicator-top {{refreshing ? 'show' : ''}}" wx:if="{{refresherEnabled}}">
    <view class="refresh-content-top">
      <!-- wx:if="{{refreshing}}" -->
      <van-loading type="spinner" color="{{loadingColor}}" size="16px" />
      <!-- <van-icon wx:else name="arrow-down" class="refresh-icon-top" /> -->
      <!-- <text class="refresh-text-top">{{refreshingText }}</text> -->
    </view>
  </view>
  <scroll-view class="refresh-scroll-container" style="height: {{containerHeight}}px" scroll-y="{{true}}" throttle="{{true}}" bindscrolltolower="onLoadMore" scroll-top="{{scrollTop}}" refresher-triggered="{{refreshing}}" refresher-background="{{refresherBackground}}" refresher-default-style="none" refresher-threshold="{{refresherThreshold}}" bindrefresherrefresh="onRefresh" refresher-enabled="{{refresherEnabled}}" will-change="transform" enhanced="{{true}}" show-scrollbar="{{showScrollbar}}">
    <view class="refresh-scroll-content" style="min-height: {{containerHeight}}px;">
      <!-- 外部内容插槽（由外部页面渲染具体内容） -->
      <slot name="content">
      </slot>

      <!-- 加载更多区域 -->
      <view wx:if="{{showLoadMore && list.length > pageSize}}" class="load-more-container">
        <view wx:if="{{(requestUrl ? internalHasMore : hasMore) && !(requestUrl ? internalLoadingMore : loadingMore)}}" class="load-more-trigger" bindtap="onLoadMore">
          <van-icon name="arrow-down" class="load-more-icon" />
          <text class="load-more-text">{{loadMoreText}}</text>
        </view>

        <view wx:elif="{{requestUrl ? internalLoadingMore : loadingMore}}" class="loading-more">
          <van-loading type="spinner" color="{{loadingColor}}" size="{{loadingSize}}" />
          <text class="loading-text">{{loadingText}}</text>
        </view>

        <view wx:else class="no-more">
          <text class="no-more-text">{{noMoreText}}</text>
        </view>
      </view>
      <!-- 空状态插槽（包含错误和无数据情况） -->
      <view wx:if="{{hasError || (requestUrl ? internalIsEmpty : isEmpty)}}" class="empty-container">
        <slot name="empty" wx:if="{{hasEmptySlot}}">
        </slot>
        <view class="default-empty" wx:else>
          <van-icon name="{{emptyIcon}}" class="empty-icon" />
          <text class="empty-text">{{emptyText}}</text>
          <text class="empty-tip">{{emptyTip}}</text>
        </view>
      </view>
    </view>
  </scroll-view>
</view>