import request from '@/utils/request'

/**
 * 微信登录相关 API
 */
export const wechatLoginApi = {
  /**
   * 生成微信登录二维码
   * @returns {Promise} 二维码信息
   */
  generateQRCode() {
    return request({
      url: '/auth/wechat/qrcode',
      method: 'POST',
      data: {
        scene: 'admin_login', // 场景值，用于区分不同的登录场景
        width: 430, // 二维码宽度
        autoColor: false,
        lineColor: { r: 0, g: 0, b: 0 } // 二维码颜色
      }
    })
  },

  /**
   * 检查扫码状态
   * @param {string} key - 二维码唯一标识
   * @returns {Promise} 扫码状态信息
   */
  checkScanStatus(key) {
    return request({
      url: `/auth/wechat/scan-status/${key}`,
      method: 'GET'
    })
  },

  /**
   * 微信授权回调处理
   * @param {string} code - 微信授权码
   * @param {string} state - 状态参数
   * @returns {Promise} 用户信息和token
   */
  handleCallback(code, state) {
    return request({
      url: '/auth/wechat/callback',
      method: 'POST',
      data: {
        code,
        state
      }
    })
  },

  /**
   * 绑定微信账号
   * @param {Object} data - 绑定数据
   * @param {string} data.openid - 微信openid
   * @param {string} data.unionid - 微信unionid
   * @param {Object} data.userInfo - 微信用户信息
   * @returns {Promise} 绑定结果
   */
  bindWechatAccount(data) {
    return request({
      url: '/auth/wechat/bind',
      method: 'POST',
      data
    })
  },

  /**
   * 解绑微信账号
   * @returns {Promise} 解绑结果
   */
  unbindWechatAccount() {
    return request({
      url: '/auth/wechat/unbind',
      method: 'POST'
    })
  },

  /**
   * 获取微信用户信息
   * @param {string} openid - 微信openid
   * @returns {Promise} 用户信息
   */
  getWechatUserInfo(openid) {
    return request({
      url: `/auth/wechat/user-info/${openid}`,
      method: 'GET'
    })
  },

  /**
   * 刷新微信access_token
   * @param {string} refreshToken - 刷新token
   * @returns {Promise} 新的access_token
   */
  refreshAccessToken(refreshToken) {
    return request({
      url: '/auth/wechat/refresh-token',
      method: 'POST',
      data: {
        refresh_token: refreshToken
      }
    })
  },

  /**
   * 获取微信登录配置
   * @returns {Promise} 微信登录配置信息
   */
  getWechatConfig() {
    return request({
      url: '/auth/wechat/config',
      method: 'GET'
    })
  },

  /**
   * 验证微信登录权限
   * @param {string} openid - 微信openid
   * @returns {Promise} 权限验证结果
   */
  validateWechatPermission(openid) {
    return request({
      url: '/auth/wechat/validate-permission',
      method: 'POST',
      data: {
        openid
      }
    })
  }
}

/**
 * 微信小程序登录 API
 */
export const wechatMiniApi = {
  /**
   * 小程序登录
   * @param {string} code - 小程序登录凭证
   * @returns {Promise} 登录结果
   */
  miniLogin(code) {
    return request({
      url: '/auth/wechat/mini/login',
      method: 'POST',
      data: {
        code
      }
    })
  },

  /**
   * 获取小程序用户信息
   * @param {Object} data - 用户信息数据
   * @returns {Promise} 用户信息
   */
  getMiniUserInfo(data) {
    return request({
      url: '/auth/wechat/mini/user-info',
      method: 'POST',
      data
    })
  }
}

/**
 * 微信支付相关 API
 */
export const wechatPayApi = {
  /**
   * 创建支付订单
   * @param {Object} orderData - 订单数据
   * @returns {Promise} 支付参数
   */
  createPayOrder(orderData) {
    return request({
      url: '/payment/wechat/create',
      method: 'POST',
      data: orderData
    })
  },

  /**
   * 查询支付状态
   * @param {string} orderId - 订单ID
   * @returns {Promise} 支付状态
   */
  queryPayStatus(orderId) {
    return request({
      url: `/payment/wechat/query/${orderId}`,
      method: 'GET'
    })
  },

  /**
   * 申请退款
   * @param {Object} refundData - 退款数据
   * @returns {Promise} 退款结果
   */
  applyRefund(refundData) {
    return request({
      url: '/payment/wechat/refund',
      method: 'POST',
      data: refundData
    })
  }
}

// 默认导出
export default {
  wechatLoginApi,
  wechatMiniApi,
  wechatPayApi
}
