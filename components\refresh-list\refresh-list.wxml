<view class="refresh-list-container">
  <!-- 下拉刷新区域 -->
  <scroll-view
    class="refresh-scroll-view"
    scroll-y="{{true}}"
    refresher-enabled="{{refresherEnabled}}"
    refresher-threshold="{{refresherThreshold}}"
    refresher-default-style="{{refresherDefaultStyle}}"
    refresher-background="{{refresherBackground}}"
    refresher-triggered="{{refresherTriggered}}"
    bindrefresherrefresh="onRefresh"
    bindscrolltolower="onLoadMore"
    lower-threshold="{{lowerThreshold}}"
    style="height: {{height}};"
  >
    <!-- 自定义下拉刷新样式 -->
    <view slot="refresher" class="custom-refresher" wx:if="{{showCustomRefresher}}">
      <view class="refresher-content">
        <view class="refresher-icon {{refresherTriggered ? 'rotating' : ''}}">
          <van-icon name="arrow-down" size="32rpx" color="#666" />
        </view>
        <text class="refresher-text">{{refresherText}}</text>
      </view>
    </view>

    <!-- 列表内容 -->
    <view class="list-content">
      <slot></slot>
    </view>

    <!-- 加载更多状态 -->
    <view class="load-more-container" wx:if="{{showLoadMore}}">
      <view class="load-more-content" wx:if="{{loadMoreStatus === 'loading'}}">
        <van-loading size="24rpx" color="#999" />
        <text class="load-more-text">{{loadingText}}</text>
      </view>
      
      <view class="load-more-content" wx:elif="{{loadMoreStatus === 'nomore'}}">
        <text class="load-more-text no-more">{{noMoreText}}</text>
      </view>
      
      <view class="load-more-content" wx:elif="{{loadMoreStatus === 'error'}}">
        <text class="load-more-text error" bindtap="onLoadMore">{{errorText}}</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-container" wx:if="{{isEmpty && !loading}}">
      <view class="empty-content">
        <image class="empty-image" src="{{emptyImage}}" mode="aspectFit" />
        <text class="empty-text">{{emptyText}}</text>
        <view class="empty-action" wx:if="{{showEmptyAction}}">
          <van-button
            size="small"
            type="primary"
            bind:click="onEmptyAction"
          >
            {{emptyActionText}}
          </van-button>
        </view>
      </view>
    </view>

    <!-- 首次加载状态 -->
    <view class="loading-container" wx:if="{{loading && isEmpty}}">
      <view class="loading-content">
        <van-loading size="32rpx" color="#3b82f6" />
        <text class="loading-text">{{loadingText}}</text>
      </view>
    </view>
  </scroll-view>
</view>
