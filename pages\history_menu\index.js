const {menuApi, orderApi} = require('../../services/api');
const {debounce, throttle, preloadManager} = require('../../utils/performance');

Page({
  data: {
    historyMenus: [],
    loading: true,
    viewingToday: false,
    loadMoreStatus: 'none', // none, loading, nomore, error
    currentPage: 1,
    pageSize: 10,
    hasMore: true,
    refreshListHeight: 'calc(100vh - 200rpx)' // 默认高度
  },

  async onLoad(options) {
    // 检查是否是查看今日菜单
    const viewToday = options && options.viewToday === 'true';

    if (viewToday) {
      this.setData({viewingToday: true});
      await this.loadTodayOrders();
    } else {
      // 使用防抖的加载方法
      await this.debouncedLoadHistoryMenus();
    }

    // 计算可用高度
    this.calculateRefreshListHeight();
  },

  // 页面渲染完成后
  onReady() {
    // 再次计算高度，确保准确
    this.calculateRefreshListHeight();
  },

  // 计算refresh-list的高度
  calculateRefreshListHeight() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const headerHeight = 200; // rpx
      const headerHeightPx = (headerHeight * systemInfo.windowWidth) / 750;
      const windowHeightPx = systemInfo.windowHeight;
      const refreshListHeightPx = windowHeightPx - headerHeightPx;

      this.setData({
        refreshListHeight: `${refreshListHeightPx}px`
      });
    } catch (error) {
      // 使用默认高度
      this.setData({
        refreshListHeight: 'calc(100vh - 200rpx)'
      });
    }
  },

  // 创建防抖的加载方法
  debouncedLoadHistoryMenus: debounce(function () {
    return this.loadHistoryMenus();
  }, 300),

  // 创建节流的刷新方法
  throttledRefresh: throttle(function () {
    return this.performRefresh();
  }, 1000),

  // 创建节流的加载更多方法
  throttledLoadMore: throttle(function () {
    return this.performLoadMore();
  }, 500),

  // 加载今日订单
  async loadTodayOrders() {
    try {
      wx.showLoading({title: '加载中...'});

      const result = await orderApi.getTodayOrders();

      if (result.code === 200 && result.data.length > 0) {
        const formattedMenus = result.data.map(order => ({
          id: order.id,
          date: this.formatOrderDate(order.diningTime),
          summary: this.formatOrderSummary(order.items),
          remark: order.remark || '无',
          status: order.status,
          isToday: true,
          dishes: this.parseOrderItems(order.items), // 解析订单项为 dishes 格式
          formattedTime: this.formatRelativeTime(
            order.createdAt || order.diningTime
          ) // 添加相对时间
        }));

        this.setData({
          historyMenus: formattedMenus,
          loading: false
        });
      } else {
        // 如果没有今日订单，检查本地缓存
        const todayMenu = wx.getStorageSync('todayMenu');
        if (todayMenu) {
          const formattedMenu = {
            id: todayMenu.id || Date.now(),
            date: todayMenu.date || '今日',
            summary: this.formatSummary(todayMenu.dishes),
            remark: todayMenu.remark || '无',
            isToday: true,
            dishes: todayMenu.dishes || [], // 确保 dishes 数组存在
            formattedTime: this.formatRelativeTime(
              todayMenu.createdAt || todayMenu.date
            ) // 添加相对时间
          };

          this.setData({
            historyMenus: [formattedMenu],
            loading: false
          });
        } else {
          this.setData({
            historyMenus: [],
            loading: false
          });
        }
      }
    } catch (error) {
      this.setData({loading: false});
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 加载历史菜单
  async loadHistoryMenus(refresh = false) {
    try {
      if (refresh) {
        this.setData({
          currentPage: 1,
          hasMore: true,
          loadMoreStatus: 'none'
        });
      }

      if (!refresh && this.data.currentPage === 1) {
        wx.showLoading({title: '加载中...'});
      }

      // 使用缓存优化的数据获取
      const cacheKey = `history_menus_${this.data.currentPage}_${this.data.pageSize}`;
      const result = await preloadManager.preload(
        cacheKey,
        () =>
          menuApi.getHistoryMenusWithPaging({
            page: this.data.currentPage,
            size: this.data.pageSize
          }),
        2 * 60 * 1000 // 2分钟缓存
      );

      if (result.code === 200) {
        const {list, total, totalPages} = result.data;

        const formattedMenus = list.map(menu => ({
          id: menu.id,
          date: this.formatMenuDate(menu.date || menu.createdAt),
          summary: this.formatMenuSummary(menu.dishes),
          remark: menu.remark || '无',
          creator: menu.creator,
          orderCount: menu.orderCount || 0,
          isToday: menu.isToday,
          dishes: menu.dishes || [], // 确保 dishes 数组存在
          formattedTime: this.formatRelativeTime(menu.createdAt || menu.date) // 添加相对时间
        }));

        let newMenus = [];
        if (refresh || this.data.currentPage === 1) {
          newMenus = formattedMenus;
        } else {
          newMenus = [...this.data.historyMenus, ...formattedMenus];
        }

        this.setData({
          historyMenus: newMenus,
          loading: false,
          hasMore: this.data.currentPage < totalPages,
          loadMoreStatus: this.data.currentPage < totalPages ? 'none' : 'nomore'
        });

        // 同步到本地缓存
        if (this.data.currentPage === 1) {
          wx.setStorageSync('historyMenus', newMenus);
        }
      } else {
        // 服务器失败时使用本地缓存
        await this.loadFromLocalCache();
      }
    } catch (error) {
      // 异常时使用本地缓存
      await this.loadFromLocalCache();
    } finally {
      if (this.data.currentPage === 1) {
        wx.hideLoading();
      }
    }
  },

  // 从本地缓存加载数据
  async loadFromLocalCache() {
    try {
      const historyMenus = wx.getStorageSync('historyMenus') || [];

      if (historyMenus.length > 0) {
        const formattedMenus = historyMenus.map(menu => ({
          id: menu.id || Math.random().toString(36).substr(2, 9),
          date: menu.date,
          summary: this.formatSummary(menu.dishes),
          remark: menu.remark || '无',
          creator: menu.creator,
          isToday: menu.isToday || false,
          dishes: menu.dishes || [], // 确保 dishes 数组存在
          formattedTime: this.formatRelativeTime(menu.createdAt || menu.date) // 添加相对时间
        }));

        this.setData({
          historyMenus: formattedMenus,
          loading: false,
          hasMore: false,
          loadMoreStatus: 'nomore'
        });
      } else {
        this.setData({
          historyMenus: [],
          loading: false,
          hasMore: false,
          loadMoreStatus: 'nomore'
        });
      }
    } catch (error) {
      this.setData({
        historyMenus: [],
        loading: false,
        hasMore: false,
        loadMoreStatus: 'error'
      });
    }
  },

  // 格式化菜品摘要
  formatSummary(dishes) {
    if (!dishes || !dishes.length) {
      return '暂无菜品';
    }

    return dishes.map(dish => `${dish.name}x${dish.count}`).join('、');
  },

  // 格式化订单摘要
  formatOrderSummary(items) {
    if (!items) {
      return '暂无菜品';
    }

    try {
      const itemsArray = typeof items === 'string' ? JSON.parse(items) : items;
      return itemsArray
        .map(item => `${item.dishName}x${item.count}`)
        .join('、');
    } catch (error) {
      return '菜品信息解析失败';
    }
  },

  // 格式化菜单摘要
  formatMenuSummary(dishes) {
    if (!dishes || !dishes.length) {
      return '暂无菜品';
    }

    return dishes
      .map(dish => `${dish.dish?.name || dish.name}x${dish.count}`)
      .join('、');
  },

  // 解析订单项为 dishes 格式
  parseOrderItems(items) {
    if (!items) {
      return [];
    }

    try {
      const itemsArray = typeof items === 'string' ? JSON.parse(items) : items;
      return itemsArray.map(item => ({
        id: item.dishId || item.id || Math.random().toString(36).substr(2, 9),
        name: item.dishName || item.name,
        count: item.count || 1
      }));
    } catch (error) {
      return [];
    }
  },

  // 格式化相对时间
  formatRelativeTime(dateString) {
    if (!dateString) return '未知时间';

    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now - date;
      const diffMins = Math.floor(diffMs / (1000 * 60));
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

      if (diffMins < 1) {
        return '刚刚';
      } else if (diffMins < 60) {
        return `${diffMins}分钟前`;
      } else if (diffHours < 24) {
        return `${diffHours}小时前`;
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return '未知时间';
    }
  },

  // 格式化订单日期
  formatOrderDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const orderDate = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );

    const diffDays = Math.floor((orderDate - today) / (24 * 60 * 60 * 1000));

    let dayText = '';
    if (diffDays === 0) {
      dayText = '今天';
    } else if (diffDays === -1) {
      dayText = '昨天';
    } else if (diffDays === 1) {
      dayText = '明天';
    } else {
      dayText = `${date.getMonth() + 1}-${date.getDate()}`;
    }

    const hour = date.getHours();
    let mealTime = '';
    if (hour < 10) {
      mealTime = '早餐';
    } else if (hour < 16) {
      mealTime = '午餐';
    } else {
      mealTime = '晚餐';
    }

    return `${dayText} ${mealTime}`;
  },

  // 格式化菜单日期
  formatMenuDate(dateString) {
    const date = new Date(dateString);
    return `${date.getMonth() + 1}-${date.getDate()}`;
  },

  // 下拉刷新（使用节流）
  onRefresh() {
    this.throttledRefresh();
  },

  // 实际执行刷新的方法
  async performRefresh() {
    try {
      // 设置刷新状态
      this.setData({
        loading: true
      });

      // 清除缓存
      preloadManager.clear();

      // 重置分页
      this.setData({
        currentPage: 1,
        hasMore: true,
        loadMoreStatus: 'none'
      });

      // 重新加载数据
      if (this.data.viewingToday) {
        await this.loadTodayOrders();
      } else {
        await this.loadHistoryMenus(true); // 传入refresh=true
      }

      // 确保数据更新完成后再停止刷新
      await this.waitForDataUpdate();

      // 设置加载完成状态
      this.setData({
        loading: false
      });

      // 安全地停止刷新动画
      this.safeStopRefresh();
    } catch (error) {
      // 确保加载状态被重置
      this.setData({
        loading: false
      });

      this.safeStopRefresh();
      wx.showToast({
        title: '刷新失败',
        icon: 'error'
      });
    }
  },

  // 等待数据更新完成
  async waitForDataUpdate() {
    return new Promise(resolve => {
      // 使用 nextTick 确保数据已经更新到视图
      wx.nextTick(() => {
        setTimeout(() => {
          resolve();
        }, 100); // 额外等待100ms确保渲染完成
      });
    });
  },

  // 安全地停止刷新动画
  safeStopRefresh() {
    try {
      // 通过id='refresh'获取组件实例
      const refreshList = this.selectComponent('#refresh');

      if (refreshList && typeof refreshList.stopRefresh === 'function') {
        refreshList.stopRefresh();
      } else {
        // 备用方案：直接设置loading状态
        this.setData({
          loading: false
        });
      }
    } catch (error) {
      this.setData({
        loading: false
      });
    }
  },

  // 上拉加载更多（使用节流）
  onLoadMore() {
    if (!this.data.hasMore || this.data.loadMoreStatus === 'loading') {
      return;
    }
    this.throttledLoadMore();
  },

  // 实际执行加载更多的方法
  async performLoadMore() {
    if (!this.data.hasMore || this.data.loadMoreStatus === 'loading') {
      return;
    }

    this.setData({loadMoreStatus: 'loading'});

    try {
      const nextPage = this.data.currentPage + 1;
      let result;

      if (this.data.viewingToday) {
        // 今日菜单通常只有一页，不需要分页
        this.setData({
          loadMoreStatus: 'nomore',
          hasMore: false
        });
        return;
      } else {
        result = await menuApi.getHistoryMenus({
          page: nextPage,
          pageSize: this.data.pageSize
        });
      }

      if (result.code === 200) {
        const newMenus = result.data || [];

        if (newMenus.length === 0) {
          this.setData({
            loadMoreStatus: 'nomore',
            hasMore: false
          });
        } else {
          const formattedMenus = newMenus.map(menu => ({
            id: menu.id,
            date: this.formatMenuDate(menu.date),
            summary: this.formatMenuSummary(menu.dishes),
            remark: menu.remark || '无',
            dishes: menu.dishes || []
          }));

          this.setData({
            historyMenus: [...this.data.historyMenus, ...formattedMenus],
            currentPage: nextPage,
            loadMoreStatus:
              newMenus.length < this.data.pageSize ? 'nomore' : 'none',
            hasMore: newMenus.length >= this.data.pageSize
          });
        }
      } else {
        throw new Error(result.message || '加载失败');
      }
    } catch (error) {
      this.setData({loadMoreStatus: 'error'});
      wx.showToast({
        title: '加载失败，点击重试',
        icon: 'none'
      });
    }
  },

  // 空状态操作
  onEmptyAction() {
    this.onRefresh();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
