/**
 * 性能优化工具函数
 */

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} wait 等待时间（毫秒）
 * @param {boolean} immediate 是否立即执行
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait, immediate = false) {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func.apply(this, args);
    };
    
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func.apply(this, args);
  };
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} limit 时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
  let inThrottle;
  
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 缓存装饰器
 * @param {Function} func 要缓存的函数
 * @param {number} ttl 缓存时间（毫秒）
 * @returns {Function} 带缓存的函数
 */
function memoize(func, ttl = 5 * 60 * 1000) { // 默认5分钟
  const cache = new Map();
  
  return function(...args) {
    const key = JSON.stringify(args);
    const cached = cache.get(key);
    
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.value;
    }
    
    const result = func.apply(this, args);
    cache.set(key, {
      value: result,
      timestamp: Date.now()
    });
    
    return result;
  };
}

/**
 * 批量处理函数
 * @param {Function} func 要批量执行的函数
 * @param {number} batchSize 批次大小
 * @param {number} delay 批次间延迟（毫秒）
 * @returns {Function} 批量处理函数
 */
function batchProcess(func, batchSize = 10, delay = 100) {
  return async function(items) {
    const results = [];
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(item => func(item))
      );
      
      results.push(...batchResults);
      
      // 批次间延迟，避免阻塞UI
      if (i + batchSize < items.length) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    return results;
  };
}

/**
 * 智能重试函数
 * @param {Function} func 要重试的函数
 * @param {number} maxRetries 最大重试次数
 * @param {number} baseDelay 基础延迟时间（毫秒）
 * @param {number} maxDelay 最大延迟时间（毫秒）
 * @returns {Function} 带重试的函数
 */
function retry(func, maxRetries = 3, baseDelay = 1000, maxDelay = 10000) {
  return async function(...args) {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await func.apply(this, args);
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          throw error;
        }
        
        // 指数退避延迟
        const delay = Math.min(
          baseDelay * Math.pow(2, attempt),
          maxDelay
        );
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  };
}

/**
 * 数据预加载管理器
 */
class PreloadManager {
  constructor() {
    this.cache = new Map();
    this.loading = new Set();
  }
  
  /**
   * 预加载数据
   * @param {string} key 缓存键
   * @param {Function} loader 加载函数
   * @param {number} ttl 缓存时间
   * @returns {Promise} 加载结果
   */
  async preload(key, loader, ttl = 5 * 60 * 1000) {
    // 检查缓存
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data;
    }
    
    // 避免重复加载
    if (this.loading.has(key)) {
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!this.loading.has(key)) {
            const result = this.cache.get(key);
            resolve(result ? result.data : null);
          } else {
            setTimeout(checkLoading, 100);
          }
        };
        checkLoading();
      });
    }
    
    this.loading.add(key);
    
    try {
      const data = await loader();
      this.cache.set(key, {
        data,
        timestamp: Date.now()
      });
      return data;
    } finally {
      this.loading.delete(key);
    }
  }
  
  /**
   * 清除缓存
   * @param {string} key 缓存键，不传则清除所有
   */
  clear(key) {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }
}

/**
 * 虚拟滚动管理器
 */
class VirtualScrollManager {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 100;
    this.containerHeight = options.containerHeight || 600;
    this.buffer = options.buffer || 5;
    this.visibleCount = Math.ceil(this.containerHeight / this.itemHeight);
  }
  
  /**
   * 计算可见项目范围
   * @param {number} scrollTop 滚动位置
   * @param {number} totalItems 总项目数
   * @returns {Object} 可见范围
   */
  getVisibleRange(scrollTop, totalItems) {
    const startIndex = Math.floor(scrollTop / this.itemHeight);
    const endIndex = Math.min(
      startIndex + this.visibleCount + this.buffer,
      totalItems
    );
    
    return {
      startIndex: Math.max(0, startIndex - this.buffer),
      endIndex,
      offsetY: startIndex * this.itemHeight
    };
  }
}

/**
 * 图片懒加载管理器
 */
class LazyImageManager {
  constructor() {
    this.observer = null;
    this.images = new Set();
    this.init();
  }
  
  init() {
    if (typeof IntersectionObserver !== 'undefined') {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: '50px',
          threshold: 0.1
        }
      );
    }
  }
  
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        const src = img.dataset.src;
        
        if (src) {
          img.src = src;
          img.removeAttribute('data-src');
          this.observer.unobserve(img);
          this.images.delete(img);
        }
      }
    });
  }
  
  observe(img) {
    if (this.observer && img) {
      this.images.add(img);
      this.observer.observe(img);
    }
  }
  
  unobserve(img) {
    if (this.observer && img) {
      this.observer.unobserve(img);
      this.images.delete(img);
    }
  }
  
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
      this.images.clear();
    }
  }
}

// 创建全局实例
const preloadManager = new PreloadManager();
const lazyImageManager = new LazyImageManager();

module.exports = {
  debounce,
  throttle,
  memoize,
  batchProcess,
  retry,
  PreloadManager,
  VirtualScrollManager,
  LazyImageManager,
  preloadManager,
  lazyImageManager
};
