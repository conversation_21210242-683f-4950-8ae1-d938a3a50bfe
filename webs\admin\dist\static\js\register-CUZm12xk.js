var F=(v,t,c)=>new Promise((e,w)=>{var h=l=>{try{a(c.next(l))}catch(m){w(m)}},d=l=>{try{a(c.throw(l))}catch(m){w(m)}},a=l=>l.done?e(l.value):Promise.resolve(l.value).then(h,d);a((c=c.apply(v,t)).next())});import{_ as G,r as x,c as J,a as g,d as n,w as p,u as T,g as y,h as B,q as _,s as A,v as S,j as K,k as U,E as f,l as W,m as u,t as k,x as Q,y as q,p as X,b as Y}from"./index-Cy8N1eGd.js";const $={__name:"register",setup(v,{expose:t}){t();const c=T(),e=y(),w=y(!1),h=y(!1),d=y(0);let a=null;const l=B({username:"",phone:"",email:"",emailCode:"",password:"",confirmPassword:"",agreement:!1}),m=(r,o,s)=>{if(!o){s(new Error("请输入用户名"));return}if(!/^[a-zA-Z0-9_]{3,20}$/.test(o)){s(new Error("用户名只能包含字母、数字和下划线，长度3-20位"));return}s()},z=(r,o,s)=>{if(!o){s(new Error("请输入手机号"));return}if(!/^1[3-9]\d{9}$/.test(o)){s(new Error("请输入正确的手机号"));return}s()},b=(r,o,s)=>{if(!o){s(new Error("请输入密码"));return}if(o.length<6){s(new Error("密码长度不能少于6位"));return}if(o.length>20){s(new Error("密码长度不能超过20位"));return}s()},C=(r,o,s)=>{if(!o){s(new Error("请再次输入密码"));return}if(o!==l.password){s(new Error("两次输入的密码不一致"));return}s()},i=(r,o,s)=>{if(!o){s(new Error("请输入邮箱验证码"));return}if(o.length!==6){s(new Error("验证码为6位数字"));return}s()},P=(r,o,s)=>{if(!o){s(new Error("请阅读并同意用户协议和隐私政策"));return}s()},L={username:[{validator:m,trigger:"blur"}],phone:[{validator:z,trigger:"blur"}],email:[{required:!0,message:"请输入邮箱地址",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],emailCode:[{validator:i,trigger:"blur"}],password:[{validator:b,trigger:"blur"}],confirmPassword:[{validator:C,trigger:"blur"}],agreement:[{validator:P,trigger:"change"}]},V=_(()=>{const r=l.password;if(!r)return 0;let o=0;return r.length>=6&&(o+=1),r.length>=8&&(o+=1),/[a-z]/.test(r)&&(o+=1),/[A-Z]/.test(r)&&(o+=1),/\d/.test(r)&&(o+=1),/[!@#$%^&*(),.?":{}|<>]/.test(r)&&(o+=1),Math.min(o,4)}),I=_(()=>{const r=V.value;return["","弱","一般","强","很强"][r]||""}),N=_(()=>{const r=V.value;return["","weak","fair","good","strong"][r]||""}),O=_(()=>`${V.value/4*100}%`),j=()=>F(this,null,function*(){try{yield e.value.validateField("email"),h.value=!0;const r=yield S.sendEmailCode(l.email.trim(),"register");r.code===200?(f.success("验证码已发送到您的邮箱"),R()):f.error(r.message||"发送失败，请重试")}catch(r){console.error("发送验证码失败:",r),f.error("发送失败，请重试")}finally{h.value=!1}}),R=()=>{d.value=60,a=setInterval(()=>{d.value--,d.value<=0&&(clearInterval(a),a=null)},1e3)},H=()=>F(this,null,function*(){try{yield e.value.validate(),w.value=!0,f.info("正在注册，请稍候...");const r=yield S.adminRegister({username:l.username.trim(),phone:l.phone.trim(),email:l.email.trim(),password:l.password,emailCode:l.emailCode.trim()});if(r.code===200||r.code===201){const o=l.username.trim();f.success("注册成功！正在跳转到登录页面..."),e.value.resetFields(),setTimeout(()=>{c.push({path:"/login",query:{username:o}})},1500)}else f.error(r.message||"注册失败，请检查信息后重试")}catch(r){console.error("注册失败:",r);let o="注册失败，请重试";if(r.response){const s=r.response.status,E=r.response.data;s===400?o=E.message||"请求参数错误":s===409?o="用户名、手机号或邮箱已被注册":s===403?o="邀请码无效或已过期":s>=500&&(o="服务器错误，请稍后再试")}f.error(o)}finally{w.value=!1}}),Z=()=>{U({title:"用户协议",message:`    <div style="max-height: 500px; overflow-y: auto; padding: 20px; line-height: 1.8; word-wrap: break-word; word-break: break-all;">

      <h3 style="color: #409eff; margin-bottom: 16px; font-size: 18px; font-weight: bold;">楠楠家厨管理系统用户协议</h3>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第一条 总则</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.1 本协议是您与楠楠家厨管理系统之间关于使用本系统服务的法律协议。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.2 您通过注册、登录、使用本系统，即表示您已阅读、理解并同意接受本协议的全部条款。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第二条 账户管理</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.1 您应当使用真实、准确、完整的信息注册账户。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.2 您有义务维护账户信息的安全性，不得将账户借给他人使用。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.3 如发现账户被盗用，应立即通知系统管理员。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第三条 使用规范</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.1 您应当合法、正当地使用本系统，不得从事违法违规活动。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.2 不得恶意攻击系统、传播病毒或进行其他危害系统安全的行为。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.3 不得利用系统从事商业竞争或其他损害系统利益的活动。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第四条 数据保护</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.1 我们承诺保护您的个人信息安全，不会未经授权向第三方披露。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.2 您上传的数据仅用于系统功能实现，我们不会用于其他商业目的。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第五条 免责声明</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.1 因不可抗力、网络故障等原因导致的服务中断，我们不承担责任。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.2 您因违反本协议导致的损失，由您自行承担。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第六条 协议变更</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.1 我们有权根据需要修改本协议，修改后的协议将在系统内公布。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.2 如您不同意修改后的协议，可以停止使用本系统。</p>

      <p style="margin-top: 20px; color: #666; font-size: 12px;">
        本协议最终解释权归楠楠家厨管理系统所有。<br>
        最后更新日期：2025年7月31
      </p>
    </div>
  `,dangerouslyUseHTMLString:!0,confirmButtonText:"我已阅读",showCancelButton:!1,closeOnClickModal:!0,customStyle:{width:"90vw",maxWidth:"700px",minWidth:"320px"}})},D=()=>{U({title:"隐私政策",message:`
    <div style="max-height: 500px; overflow-y: auto; padding: 20px; line-height: 1.8; word-wrap: break-word; word-break: break-all;">
      <h3 style="color: #409eff; margin-bottom: 16px; font-size: 18px; font-weight: bold;">楠楠家厨管理系统隐私政策</h3>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第一条 信息收集</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.1 我们收集您主动提供的信息，包括但不限于用户名、邮箱、手机号等。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.2 我们会自动收集您使用系统时的操作日志，用于系统优化和安全监控。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第二条 信息使用</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.1 您的个人信息仅用于提供系统服务、身份验证和安全保护。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.2 我们不会将您的个人信息用于营销推广或其他商业目的。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.3 在法律要求的情况下，我们可能需要配合相关部门提供必要信息。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第三条 信息保护</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.1 我们采用行业标准的安全措施保护您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.2 您的密码经过加密存储，我们无法获取您的明文密码。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.3 我们定期进行安全审计，确保数据安全。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第四条 信息共享</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.1 除本政策明确说明外，我们不会与第三方共享您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.2 在获得您明确同意的情况下，我们可能与合作伙伴共享必要信息。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第五条 用户权利</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.1 您有权查询、更正、删除您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.2 您可以随时注销账户，我们将删除您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.3 如对隐私政策有疑问，可以联系系统管理员。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第六条 政策更新</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.1 我们可能会不定期更新本隐私政策。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.2 重大变更会通过系统通知或邮件方式告知您。</p>

      <p style="margin-top: 20px; color: #666; font-size: 12px;">
        如有隐私相关问题，请联系：<EMAIL><br>
        最后更新日期：2025年7月31日
      </p>
    </div>
  `,dangerouslyUseHTMLString:!0,confirmButtonText:"我已阅读",showCancelButton:!1,closeOnClickModal:!0,customStyle:{width:"90vw",maxWidth:"700px",minWidth:"320px"}})};A(()=>{a&&clearInterval(a)});const M={router:c,registerFormRef:e,loading:w,sendingCode:h,emailCountdown:d,get emailTimer(){return a},set emailTimer(r){a=r},registerForm:l,validateUsername:m,validatePhone:z,validatePassword:b,validateConfirmPassword:C,validateEmailCode:i,validateAgreement:P,registerRules:L,passwordStrength:V,passwordStrengthText:I,passwordStrengthClass:N,passwordStrengthWidth:O,sendEmailCode:j,startEmailCountdown:R,handleRegister:H,showUserAgreement:Z,showPrivacyPolicy:D,ref:y,reactive:B,computed:_,onUnmounted:A,get useRouter(){return T},get ElMessage(){return f},get ElMessageBox(){return U},get User(){return K},get authApi(){return S}};return Object.defineProperty(M,"__isScriptSetup",{enumerable:!1,value:!0}),M}},ee={class:"register-container"},re={class:"register-form"},oe={class:"code-input-group"},te={class:"password-strength"},se={class:"strength-bar"},ne={class:"form-footer"};function le(v,t,c,e,w,h){const d=x("el-input"),a=x("el-form-item"),l=x("el-button"),m=x("el-link"),z=x("el-checkbox"),b=x("el-icon"),C=x("el-form");return W(),J("div",ee,[g("div",re,[t[13]||(t[13]=g("div",{class:"form-header"},[g("h2",null,"管理员注册"),g("p",null,"创建新的管理员账户，开始使用楠楠家厨管理系统")],-1)),n(C,{ref:"registerFormRef",model:e.registerForm,rules:e.registerRules,"label-width":"80px",size:"large"},{default:p(()=>[n(a,{label:"用户名",prop:"username"},{default:p(()=>[n(d,{modelValue:e.registerForm.username,"onUpdate:modelValue":t[0]||(t[0]=i=>e.registerForm.username=i),placeholder:"请输入用户名（3-20位字母数字下划线）","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1}),n(a,{label:"手机号",prop:"phone"},{default:p(()=>[n(d,{modelValue:e.registerForm.phone,"onUpdate:modelValue":t[1]||(t[1]=i=>e.registerForm.phone=i),placeholder:"请输入手机号","prefix-icon":"Phone",clearable:"",maxlength:"11"},null,8,["modelValue"])]),_:1}),n(a,{label:"邮箱",prop:"email"},{default:p(()=>[n(d,{modelValue:e.registerForm.email,"onUpdate:modelValue":t[2]||(t[2]=i=>e.registerForm.email=i),placeholder:"请输入邮箱地址","prefix-icon":"Message",clearable:""},null,8,["modelValue"])]),_:1}),n(a,{label:"验证码",prop:"emailCode"},{default:p(()=>[g("div",oe,[n(d,{modelValue:e.registerForm.emailCode,"onUpdate:modelValue":t[3]||(t[3]=i=>e.registerForm.emailCode=i),placeholder:"请输入邮箱验证码","prefix-icon":"Lock",clearable:"",maxlength:"6"},null,8,["modelValue"]),n(l,{disabled:e.emailCountdown>0||!e.registerForm.email,onClick:e.sendEmailCode,class:"code-btn",loading:e.sendingCode},{default:p(()=>[u(k(e.emailCountdown>0?`${e.emailCountdown}s`:"发送验证码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),n(a,{label:"密码",prop:"password"},{default:p(()=>[n(d,{modelValue:e.registerForm.password,"onUpdate:modelValue":t[4]||(t[4]=i=>e.registerForm.password=i),type:"password",placeholder:"请输入密码（6-20位，建议包含字母数字）","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"]),g("div",te,[g("div",se,[g("div",{class:q(["strength-fill",e.passwordStrengthClass]),style:Q({width:e.passwordStrengthWidth})},null,6)]),g("span",{class:q(["strength-text",e.passwordStrengthClass])},k(e.passwordStrengthText),3)])]),_:1}),n(a,{label:"确认密码",prop:"confirmPassword"},{default:p(()=>[n(d,{modelValue:e.registerForm.confirmPassword,"onUpdate:modelValue":t[5]||(t[5]=i=>e.registerForm.confirmPassword=i),type:"password",placeholder:"请再次输入密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),n(a,{prop:"agreement"},{default:p(()=>[n(z,{modelValue:e.registerForm.agreement,"onUpdate:modelValue":t[6]||(t[6]=i=>e.registerForm.agreement=i)},{default:p(()=>[t[10]||(t[10]=u(" 我已阅读并同意 ",-1)),n(m,{type:"primary",onClick:e.showUserAgreement},{default:p(()=>t[8]||(t[8]=[u("《用户协议》",-1)])),_:1,__:[8]}),t[11]||(t[11]=u(" 和 ",-1)),n(m,{type:"primary",onClick:e.showPrivacyPolicy},{default:p(()=>t[9]||(t[9]=[u("《隐私政策》",-1)])),_:1,__:[9]})]),_:1,__:[10,11]},8,["modelValue"])]),_:1}),n(a,null,{default:p(()=>[n(l,{type:"primary",loading:e.loading,onClick:e.handleRegister,disabled:!e.registerForm.agreement,style:{width:"100%"}},{default:p(()=>[e.loading?Y("v-if",!0):(W(),X(b,{key:0},{default:p(()=>[n(e.User)]),_:1})),u(" "+k(e.loading?"注册中...":"立即注册"),1)]),_:1},8,["loading","disabled"])]),_:1}),g("div",ne,[n(m,{type:"primary",onClick:t[7]||(t[7]=i=>v.$router.push("/login"))},{default:p(()=>t[12]||(t[12]=[u(" 已有账户？立即登录 ",-1)])),_:1,__:[12]})])]),_:1},8,["model"])])])}const pe=G($,[["render",le],["__scopeId","data-v-1046051f"],["__file","E:/wx-nan/webs/admin/src/views/auth/register.vue"]]);export{pe as default};
