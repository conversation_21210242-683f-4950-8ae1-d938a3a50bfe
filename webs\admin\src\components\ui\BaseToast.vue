<template>
  <Teleport to="body">
    <div
      v-if="visible"
      :class="containerClasses"
      style="z-index: 9999;"
    >
      <Transition
        enter-active-class="transform ease-out duration-300 transition"
        enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
        enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
        leave-active-class="transition ease-in duration-100"
        leave-from-class="opacity-100"
        leave-to-class="opacity-0"
      >
        <div
          v-if="visible"
          :class="toastClasses"
        >
          <div class="flex">
            <!-- 图标 -->
            <div class="flex-shrink-0">
              <component
                :is="iconComponent"
                :class="iconClasses"
                aria-hidden="true"
              />
            </div>
            
            <!-- 内容 -->
            <div class="ml-3 w-0 flex-1">
              <!-- 标题 -->
              <p v-if="title" :class="titleClasses">
                {{ title }}
              </p>
              
              <!-- 消息内容 -->
              <p :class="messageClasses">
                {{ message }}
              </p>
              
              <!-- 操作按钮 -->
              <div v-if="$slots.actions || showAction" class="mt-3 flex space-x-3">
                <slot name="actions">
                  <BaseButton
                    v-if="actionText"
                    variant="ghost"
                    size="sm"
                    @click="handleAction"
                  >
                    {{ actionText }}
                  </BaseButton>
                </slot>
              </div>
            </div>
            
            <!-- 关闭按钮 -->
            <div v-if="closable" class="ml-4 flex-shrink-0 flex">
              <button
                type="button"
                :class="closeButtonClasses"
                @click="handleClose"
              >
                <span class="sr-only">关闭</span>
                <XMarkIcon class="h-5 w-5" aria-hidden="true" />
              </button>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Teleport>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
  XMarkIcon
} from '@heroicons/vue/20/solid'
import BaseButton from './BaseButton.vue'

const props = defineProps({
  // 消息类型
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'warning', 'error', 'info'].includes(value)
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 消息内容
  message: {
    type: String,
    required: true
  },
  // 显示位置
  position: {
    type: String,
    default: 'top-right',
    validator: (value) => [
      'top-left', 'top-center', 'top-right',
      'bottom-left', 'bottom-center', 'bottom-right'
    ].includes(value)
  },
  // 自动关闭时间(毫秒)
  duration: {
    type: Number,
    default: 4000
  },
  // 是否可关闭
  closable: {
    type: Boolean,
    default: true
  },
  // 是否显示操作按钮
  showAction: {
    type: Boolean,
    default: false
  },
  // 操作按钮文本
  actionText: {
    type: String,
    default: '操作'
  },
  // 自定义图标
  icon: {
    type: [Object, Function],
    default: null
  }
})

const emit = defineEmits(['close', 'action'])

const visible = ref(false)
let timer = null

// 类型配置
const typeConfig = {
  success: {
    icon: CheckCircleIcon,
    iconClasses: 'text-green-400',
    titleClasses: 'text-green-800 dark:text-green-200',
    messageClasses: 'text-green-700 dark:text-green-300',
    closeButtonClasses: 'text-green-500 hover:text-green-600 focus:ring-green-600'
  },
  warning: {
    icon: ExclamationTriangleIcon,
    iconClasses: 'text-yellow-400',
    titleClasses: 'text-yellow-800 dark:text-yellow-200',
    messageClasses: 'text-yellow-700 dark:text-yellow-300',
    closeButtonClasses: 'text-yellow-500 hover:text-yellow-600 focus:ring-yellow-600'
  },
  error: {
    icon: XCircleIcon,
    iconClasses: 'text-red-400',
    titleClasses: 'text-red-800 dark:text-red-200',
    messageClasses: 'text-red-700 dark:text-red-300',
    closeButtonClasses: 'text-red-500 hover:text-red-600 focus:ring-red-600'
  },
  info: {
    icon: InformationCircleIcon,
    iconClasses: 'text-blue-400',
    titleClasses: 'text-blue-800 dark:text-blue-200',
    messageClasses: 'text-blue-700 dark:text-blue-300',
    closeButtonClasses: 'text-blue-500 hover:text-blue-600 focus:ring-blue-600'
  }
}

const config = computed(() => typeConfig[props.type])

const containerClasses = computed(() => {
  const baseClasses = ['fixed pointer-events-none']
  
  // 位置样式
  const positionClasses = {
    'top-left': 'top-0 left-0 p-6',
    'top-center': 'top-0 left-1/2 transform -translate-x-1/2 p-6',
    'top-right': 'top-0 right-0 p-6',
    'bottom-left': 'bottom-0 left-0 p-6',
    'bottom-center': 'bottom-0 left-1/2 transform -translate-x-1/2 p-6',
    'bottom-right': 'bottom-0 right-0 p-6'
  }
  
  return [
    ...baseClasses,
    positionClasses[props.position]
  ].join(' ')
})

const toastClasses = computed(() => {
  return [
    'max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden',
    'p-4'
  ].join(' ')
})

const iconComponent = computed(() => {
  return props.icon || config.value.icon
})

const iconClasses = computed(() => {
  return ['h-6 w-6', config.value.iconClasses].join(' ')
})

const titleClasses = computed(() => {
  return ['text-sm font-medium', config.value.titleClasses].join(' ')
})

const messageClasses = computed(() => {
  const classes = ['text-sm']
  
  if (props.title) {
    classes.push('mt-1')
  }
  
  return [
    ...classes,
    config.value.messageClasses
  ].join(' ')
})

const closeButtonClasses = computed(() => {
  return [
    'bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2',
    config.value.closeButtonClasses
  ].join(' ')
})

const show = () => {
  visible.value = true
  
  if (props.duration > 0) {
    timer = setTimeout(() => {
      handleClose()
    }, props.duration)
  }
}

const hide = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
}

const handleClose = () => {
  hide()
  emit('close')
}

const handleAction = () => {
  emit('action')
}

// 暴露方法给父组件
defineExpose({
  show,
  hide
})

onMounted(() => {
  show()
})

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})
</script>
