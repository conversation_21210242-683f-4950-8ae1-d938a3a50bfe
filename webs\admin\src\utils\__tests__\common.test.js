import {describe, it, expect} from 'vitest';
import {
  formatTime,
  formatDate,
  formatFileSize,
  formatNumber
} from '../common.js';

describe('Common Utils', () => {
  describe('formatTime', () => {
    it('should format time correctly', () => {
      const date = new Date('2024-01-01 12:30:45');
      expect(formatTime(date)).toBe('2024-01-01 12:30:45');
      expect(formatTime(date, 'YYYY-MM-DD')).toBe('2024-01-01');
    });

    it('should return empty string for invalid input', () => {
      expect(formatTime(null)).toBe('');
      expect(formatTime(undefined)).toBe('');
      expect(formatTime('')).toBe('');
    });
  });

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const date = new Date('2024-01-01 12:30:45');
      expect(formatDate(date)).toBe('2024-01-01');
      expect(formatDate(date, 'MM/DD/YYYY')).toBe('01/01/2024');
    });

    it('should return empty string for invalid input', () => {
      expect(formatDate(null)).toBe('');
      expect(formatDate(undefined)).toBe('');
      expect(formatDate('')).toBe('');
    });
  });

  describe('formatFileSize', () => {
    it('should format file size correctly', () => {
      expect(formatFileSize(0)).toBe('0 Bytes');
      expect(formatFileSize(1024)).toBe('1 KB');
      expect(formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(formatFileSize(1024 * 1024 * 1024)).toBe('1 GB');
    });

    it('should handle decimal places', () => {
      expect(formatFileSize(1536, 2)).toBe('1.5 KB');
      expect(formatFileSize(1536, 0)).toBe('2 KB');
    });
  });

  describe('formatNumber', () => {
    it('should format number with Chinese units', () => {
      expect(formatNumber(1000)).toBe('1.00k');
      expect(formatNumber(10000)).toBe('1.00万');
      expect(formatNumber(100000000)).toBe('1.00亿');
      expect(formatNumber(123)).toBe('123');
    });

    it('should handle precision', () => {
      expect(formatNumber(1500, 1)).toBe('1.5k');
      expect(formatNumber(15000, 0)).toBe('2万');
    });
  });
});
