const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');
const notificationService = require('../services/notificationService');
const wechatSubscriptionService = require('../services/wechatSubscriptionService');

/**
 * 创建订单并推送
 * @route POST /api/orders/create-and-push
 */
const createOrderAndPush = async (req, res) => {
  try {
    const {menuId, items, remark, diningTime, pushToUsers = []} = req.body;
    const userId = req.user.id;

    console.log('📥 收到订单推送请求:', {
      menuId,
      items: items?.length,
      pushToUsers
    });

    // 验证必需参数
    if (!items || !Array.isArray(items) || items.length === 0) {
      return error(res, 'Items are required', 400);
    }

    let menu = null;
    let actualMenuId = menuId;

    // 如果没有提供menuId或menuId为null，则自动创建临时菜单
    if (!menuId) {
      console.log('🔧 创建临时菜单...');

      // 创建临时菜单（标记为今日菜单）
      menu = await prisma.menu.create({
        data: {
          createdBy: userId,
          date: new Date(),
          remark: remark || '临时菜单',
          isToday: true // 修改为true，让新创建的菜单显示在首页
        },
        include: {
          creator: {
            select: {id: true, name: true}
          }
        }
      });

      actualMenuId = menu.id;
      console.log('✅ 临时菜单创建成功:', actualMenuId);

      // 为临时菜单创建菜单项
      console.log('🔧 为临时菜单添加菜品...');
      for (const item of items) {
        await prisma.menuItem.create({
          data: {
            menuId: actualMenuId,
            dishId: item.dishId,
            count: item.count || 1
          }
        });
      }
      console.log(`✅ 已添加 ${items.length} 个菜品到临时菜单`);
    } else {
      // 检查菜单是否存在且未删除
      menu = await prisma.menu.findUnique({
        where: {id: menuId},
        include: {
          creator: {
            select: {id: true, name: true}
          }
        }
      });

      if (!menu) {
        return error(res, 'Menu not found', 404);
      }

      if (menu.deleted) {
        return error(res, 'Cannot order from deleted menu', 400);
      }
    }

    // 验证推送目标用户是否都是关联用户
    if (pushToUsers.length > 0) {
      const connections = await prisma.userConnection.findMany({
        where: {
          OR: [
            {
              senderId: userId,
              receiverId: {in: pushToUsers},
              status: 'accepted'
            },
            {
              receiverId: userId,
              senderId: {in: pushToUsers},
              status: 'accepted'
            }
          ]
        }
      });

      const connectedUserIds = connections.map(conn =>
        conn.senderId === userId ? conn.receiverId : conn.senderId
      );

      const invalidUsers = pushToUsers.filter(
        targetId => !connectedUserIds.includes(targetId)
      );

      if (invalidUsers.length > 0) {
        return error(res, 'Some target users are not connected to you', 400);
      }
    }

    // 创建订单
    const order = await prisma.order.create({
      data: {
        userId,
        menuId: actualMenuId, // 使用实际的菜单ID
        items: JSON.stringify(items),
        remark,
        diningTime: diningTime ? new Date(diningTime) : null,
        status: 'pending'
      },
      include: {
        user: {
          select: {id: true, name: true, avatar: true}
        },
        menu: {
          select: {id: true, remark: true, date: true}
        }
      }
    });

    // 创建推送记录并发送通知
    const pushPromises = pushToUsers.map(async targetUserId => {
      // 创建推送记录
      const orderPush = await prisma.orderPush.create({
        data: {
          orderId: order.id,
          pushedBy: userId,
          targetUserId,
          message: `${req.user.name}点了菜品并推送给您`
        }
      });

      // 获取菜品名称用于通知
      const itemNames = items.map(item => item.name || '菜品').join('、');

      // 发送站内通知
      await notificationService.sendNotification({
        userId: targetUserId,
        senderId: userId,
        type: 'order_push',
        title: '收到订单推送',
        content: `${req.user.name}点了菜品：${itemNames}`,
        data: JSON.stringify({
          orderId: order.id,
          menuId: actualMenuId, // 使用实际的菜单ID
          items: itemNames,
          pushedBy: req.user.name
        })
      });

      // 发送微信订阅消息
      try {
        const targetUser = await prisma.user.findUnique({
          where: {id: targetUserId},
          select: {id: true, name: true, openid: true}
        });

        if (
          targetUser &&
          targetUser.openid &&
          !targetUser.openid.startsWith('mock_')
        ) {
          console.log(`📨 发送订阅消息给用户: ${targetUser.name}`);

          const result = await wechatSubscriptionService.sendOrderNotification(
            order,
            req.user,
            [targetUser]
          );

          if (result.success) {
            console.log(`✅ 订阅消息发送成功: ${targetUser.name}`);
          } else {
            console.log(`❌ 订阅消息发送失败: ${result.error}`);
          }
        } else {
          console.log(`⚠️ 用户 ${targetUserId} 没有有效的openid，跳过订阅消息`);
        }
      } catch (subscriptionError) {
        console.error('发送订阅消息失败:', subscriptionError);
        // 不阻断主流程
      }

      return orderPush;
    });

    await Promise.all(pushPromises);

    // 检查菜单数据完整性
    if (order.menuId && !order.menu) {
      console.warn(`⚠️  订单 ${order.id} 的菜单ID存在但菜单数据为空`);
    }

    return success(
      res,
      {
        order: {
          id: order.id,
          userId: order.userId,
          menuId: order.menuId,
          items: JSON.parse(order.items),
          remark: order.remark,
          diningTime: order.diningTime,
          status: order.status,
          createdAt: order.createdAt,
          user: order.user,
          menu: order.menu // 可能为 null，前端需要处理
        },
        menu: order.menu, // 单独提供 menu 字段，便于前端访问
        pushedToCount: pushToUsers.length
      },
      'Order created and pushed successfully',
      201
    );
  } catch (err) {
    console.error('Create order and push error:', err);
    return error(res, 'Failed to create order and push', 500);
  }
};

/**
 * 获取用户可见的订单
 * @route GET /api/orders/visible
 */
const getVisibleOrders = async (req, res) => {
  try {
    const userId = req.user.id;
    const {page = 1, size = 20} = req.query;

    const pageNum = parseInt(page);
    const pageSize = parseInt(size);
    const skip = (pageNum - 1) * pageSize;

    // 获取用户可见的订单
    const orders = await prisma.order.findMany({
      where: {
        OR: [
          {userId}, // 自己的订单
          {
            orderPushes: {
              some: {targetUserId: userId}
            }
          } // 推送给自己的订单
        ]
      },
      include: {
        user: {
          select: {id: true, name: true, avatar: true}
        },
        menu: {
          select: {
            id: true,
            remark: true,
            date: true,
            deleted: true,
            creator: {
              select: {id: true, name: true}
            }
          }
        },
        orderPushes: {
          where: {targetUserId: userId},
          include: {
            pusher: {
              select: {id: true, name: true, phone: true, avatar: true}
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip,
      take: pageSize
    });

    // 获取总数
    const total = await prisma.order.count({
      where: {
        OR: [
          {userId},
          {
            orderPushes: {
              some: {targetUserId: userId}
            }
          }
        ]
      }
    });

    // 格式化订单数据
    const formattedOrders = orders.map(order => ({
      id: order.id,
      userId: order.userId,
      menuId: order.menuId,
      items: JSON.parse(order.items),
      remark: order.remark,
      diningTime: order.diningTime,
      status: order.status,
      createdAt: order.createdAt,
      user: order.user,
      menu: order.menu,
      isPushedToMe: order.orderPushes.length > 0,
      pushedBy:
        order.orderPushes.length > 0 ? order.orderPushes[0].pusher : null
    }));

    return success(res, {
      list: formattedOrders,
      total,
      page: pageNum,
      size: pageSize,
      totalPages: Math.ceil(total / pageSize)
    });
  } catch (err) {
    console.error('Get visible orders error:', err);
    return error(res, 'Failed to get visible orders', 500);
  }
};

module.exports = {
  createOrderAndPush,
  getVisibleOrders
};
