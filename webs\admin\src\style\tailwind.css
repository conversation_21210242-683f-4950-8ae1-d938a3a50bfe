@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    font-family: 'Inter', 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 卡片样式 */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-soft border border-gray-200 dark:border-gray-700;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }

  .card-body {
    @apply p-6;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900;
  }

  /* 表单样式 */
  .form-group {
    @apply space-y-1;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300;
  }

  .form-input {
    @apply block w-full rounded-lg border-0 py-2 px-3 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 dark:focus:ring-primary-500 sm:text-sm sm:leading-6 bg-white dark:bg-gray-800;
  }

  .form-select {
    @apply block w-full rounded-lg border-0 py-2 pl-3 pr-10 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-inset focus:ring-primary-600 dark:focus:ring-primary-500 sm:text-sm sm:leading-6 bg-white dark:bg-gray-800;
  }

  .form-textarea {
    @apply block w-full rounded-lg border-0 py-2 px-3 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 dark:focus:ring-primary-500 sm:text-sm sm:leading-6 bg-white dark:bg-gray-800;
  }

  .form-error {
    @apply text-sm text-error-600 dark:text-error-400;
  }

  .form-help {
    @apply text-sm text-gray-500 dark:text-gray-400;
  }

  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white shadow-sm hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white shadow-sm hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply bg-success-600 text-white shadow-sm hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply bg-warning-600 text-white shadow-sm hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-error {
    @apply bg-error-600 text-white shadow-sm hover:bg-error-700 focus:ring-error-500;
  }

  .btn-outline {
    @apply bg-white text-gray-700 border border-gray-300 shadow-sm hover:bg-gray-50 focus:ring-primary-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700;
  }

  .btn-ghost {
    @apply bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-primary-500 dark:text-gray-300 dark:hover:bg-gray-800;
  }

  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
  }

  .badge-success {
    @apply bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
  }

  .badge-error {
    @apply bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-200;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200;
  }

  /* 表格样式 */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table-header {
    @apply bg-gray-50 dark:bg-gray-900;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700;
  }

  .table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-700;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }
}

/* 自定义工具类 */
@layer utilities {
  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  /* 阴影类 */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-soft-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* 文本省略 */
  .text-ellipsis {
    @apply truncate;
  }

  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 居中类 */
  .center {
    @apply flex items-center justify-center;
  }

  .center-x {
    @apply flex justify-center;
  }

  .center-y {
    @apply flex items-center;
  }
}

/* 
@layer base {
  @font-face {
    font-family: "PingFang SC";
    font-weight: 400;
    src: url("@/assets/fonts/pingfangsc-regular.otf") format("truetype");
  }
  @font-face {
    font-family: "Alibaba";
    font-weight: 400;
    src: url("@/assets/fonts/AlibabaPuHuiTi.ttf") format("truetype");
  }
  html {
    font-family: "PingFang SC";
    moz-user-select: -moz-none;
    -moz-user-select: none;
    -o-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  span,
  p,
  text {
    font-family: "PingFang SC";
    font-weight: 400;
  }
} */

@layer components {
  .flex-c {
    @apply flex justify-center items-center;
  }

  .flex-ac {
    @apply flex justify-around items-center;
  }

  .flex-bc {
    @apply flex justify-between items-center;
  }

  .navbar-bg-hover {
    @apply dark:text-white dark:hover:!bg-[#242424];
  }
}
