Component({
  properties: {
    // 是否正在加载
    loading: {
      type: Boolean,
      value: false
    },
    // 是否还有更多数据
    hasMore: {
      type: Boolean,
      value: true
    },
    // 总数据量
    total: {
      type: Number,
      value: 0
    },
    // 当前数据量
    current: {
      type: Number,
      value: 0
    },
    // 每页数量（用于判断是否显示加载更多）
    pageSize: {
      type: Number,
      value: 10
    },
    // 自定义文本
    loadingText: {
      type: String,
      value: '加载中...'
    },
    noMoreText: {
      type: String,
      value: '暂无更多数据'
    },
    // 是否显示组件
    show: {
      type: Boolean,
      value: true
    }
  },

  data: {
    // 内部状态
    status: 'more' // more: 可以加载更多, loading: 正在加载, nomore: 没有更多数据
  },

  observers: {
    'loading, hasMore, total, current, pageSize': function() {
      this.updateStatus();
    }
  },

  methods: {
    /**
     * 更新状态
     */
    updateStatus() {
      const { loading, hasMore, total, current, pageSize } = this.properties;
      
      let status = 'more';
      
      if (loading) {
        status = 'loading';
      } else if (!hasMore || (total > 0 && current >= total)) {
        // 如果没有更多数据，或者当前数量已达到总数
        status = 'nomore';
      } else if (current < pageSize) {
        // 如果当前数据少于一页，不显示加载更多
        status = 'hidden';
      }
      
      this.setData({ status });
    },

    /**
     * 点击加载更多
     */
    onLoadMore() {
      if (this.data.status === 'more') {
        this.triggerEvent('loadmore');
      }
    }
  },

  lifetimes: {
    attached() {
      this.updateStatus();
    }
  }
});
