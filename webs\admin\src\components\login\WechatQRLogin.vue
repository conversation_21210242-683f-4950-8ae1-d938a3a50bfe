<template>
  <div class="wechat-qr-login">
    <!-- 登录方式切换 -->
    <div class="login-tabs">
      <div class="tab-buttons">
        <button
          :class="['tab-button', { active: loginType === 'password' }]"
          @click="loginType = 'password'"
        >
          <KeyIcon class="w-4 h-4" />
          密码登录
        </button>
        <button
          :class="['tab-button', { active: loginType === 'wechat' }]"
          @click="loginType = 'wechat'"
        >
          <QrCodeIcon class="w-4 h-4" />
          微信扫码
        </button>
      </div>
    </div>

    <!-- 密码登录表单 -->
    <div v-if="loginType === 'password'" class="password-login">
      <slot name="password-form"></slot>
    </div>

    <!-- 微信扫码登录 -->
    <div v-else class="wechat-login">
      <div class="qr-container">
        <!-- 二维码显示区域 -->
        <div class="qr-code-wrapper">
          <div v-if="qrStatus === 'loading'" class="qr-loading">
            <LoadingSpinner size="lg" />
            <p class="loading-text">正在生成二维码...</p>
          </div>
          
          <div v-else-if="qrStatus === 'ready'" class="qr-code">
            <img :src="qrCodeUrl" alt="微信登录二维码" />
            <div class="qr-overlay" v-if="scanStatus !== 'waiting'">
              <div v-if="scanStatus === 'scanned'" class="scan-status scanned">
                <CheckCircleIcon class="w-12 h-12 text-green-500" />
                <p>扫码成功</p>
                <p class="text-sm">请在手机上确认登录</p>
              </div>
              <div v-else-if="scanStatus === 'confirmed'" class="scan-status confirmed">
                <CheckCircleIcon class="w-12 h-12 text-blue-500" />
                <p>登录确认中...</p>
              </div>
              <div v-else-if="scanStatus === 'expired'" class="scan-status expired">
                <XCircleIcon class="w-12 h-12 text-red-500" />
                <p>二维码已过期</p>
                <BaseButton size="sm" @click="refreshQRCode">刷新二维码</BaseButton>
              </div>
            </div>
          </div>
          
          <div v-else-if="qrStatus === 'error'" class="qr-error">
            <ExclamationTriangleIcon class="w-12 h-12 text-red-500" />
            <p>二维码生成失败</p>
            <BaseButton size="sm" @click="generateQRCode">重新生成</BaseButton>
          </div>
        </div>

        <!-- 扫码提示 -->
        <div class="qr-tips">
          <div class="tip-item">
            <div class="tip-icon">
              <PhoneIcon class="w-5 h-5" />
            </div>
            <div class="tip-text">
              <p class="font-medium">使用微信扫码登录</p>
              <p class="text-sm text-gray-500">打开微信扫一扫功能</p>
            </div>
          </div>
          
          <div class="tip-item">
            <div class="tip-icon">
              <ShieldCheckIcon class="w-5 h-5" />
            </div>
            <div class="tip-text">
              <p class="font-medium">安全便捷</p>
              <p class="text-sm text-gray-500">无需记住密码，扫码即可登录</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 二维码刷新倒计时 -->
      <div v-if="qrStatus === 'ready' && countdown > 0" class="countdown">
        <p class="text-sm text-gray-500">
          二维码将在 <span class="font-medium text-primary-600">{{ countdown }}</span> 秒后过期
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import {
  QrCodeIcon,
  KeyIcon,
  PhoneIcon,
  ShieldCheckIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import LoadingSpinner from '@/components/ui/LoadingSpinner.vue'
import BaseButton from '@/components/ui/BaseButton.vue'
import { wechatLoginApi } from '@/api/wechat'
import { useToast } from '@/composables/useToast'

const props = defineProps({
  // 是否自动生成二维码
  autoGenerate: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['login-success', 'login-error'])

const { success, error } = useToast()

// 响应式数据
const loginType = ref('password') // password | wechat
const qrStatus = ref('loading') // loading | ready | error
const scanStatus = ref('waiting') // waiting | scanned | confirmed | expired
const qrCodeUrl = ref('')
const qrCodeKey = ref('')
const countdown = ref(300) // 5分钟倒计时
const pollingTimer = ref(null)
const countdownTimer = ref(null)

// 生成二维码
const generateQRCode = async () => {
  try {
    qrStatus.value = 'loading'
    scanStatus.value = 'waiting'
    
    const response = await wechatLoginApi.generateQRCode()
    
    if (response.success) {
      qrCodeUrl.value = response.data.qrCodeUrl
      qrCodeKey.value = response.data.key
      qrStatus.value = 'ready'
      
      // 开始轮询检查扫码状态
      startPolling()
      
      // 开始倒计时
      startCountdown()
      
      console.log('二维码生成成功')
    } else {
      throw new Error(response.message || '二维码生成失败')
    }
  } catch (err) {
    console.error('生成二维码失败:', err)
    qrStatus.value = 'error'
    error('二维码生成失败，请重试')
  }
}

// 刷新二维码
const refreshQRCode = () => {
  stopPolling()
  stopCountdown()
  generateQRCode()
}

// 开始轮询检查扫码状态
const startPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
  }
  
  pollingTimer.value = setInterval(async () => {
    try {
      const response = await wechatLoginApi.checkScanStatus(qrCodeKey.value)
      
      if (response.success) {
        const { status, userInfo, token } = response.data
        
        switch (status) {
          case 'scanned':
            scanStatus.value = 'scanned'
            break
            
          case 'confirmed':
            scanStatus.value = 'confirmed'
            break
            
          case 'success':
            // 登录成功
            stopPolling()
            stopCountdown()
            success('微信登录成功！')
            emit('login-success', { userInfo, token, loginType: 'wechat' })
            break
            
          case 'expired':
            scanStatus.value = 'expired'
            stopPolling()
            stopCountdown()
            break
            
          case 'cancelled':
            // 用户取消登录
            scanStatus.value = 'waiting'
            break
        }
      }
    } catch (err) {
      console.error('检查扫码状态失败:', err)
    }
  }, 2000) // 每2秒检查一次
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 300 // 5分钟
  
  countdownTimer.value = setInterval(() => {
    countdown.value--
    
    if (countdown.value <= 0) {
      // 倒计时结束，二维码过期
      scanStatus.value = 'expired'
      stopPolling()
      stopCountdown()
    }
  }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
}

// 监听登录方式切换
watch(loginType, (newType) => {
  if (newType === 'wechat' && qrStatus.value !== 'ready') {
    generateQRCode()
  } else if (newType === 'password') {
    stopPolling()
    stopCountdown()
  }
})

// 生命周期
onMounted(() => {
  if (props.autoGenerate && loginType.value === 'wechat') {
    generateQRCode()
  }
})

onUnmounted(() => {
  stopPolling()
  stopCountdown()
})

// 暴露方法
defineExpose({
  generateQRCode,
  refreshQRCode,
  loginType
})
</script>

<style scoped>
.wechat-qr-login {
  @apply w-full;
}

.login-tabs {
  @apply mb-6;
}

.tab-buttons {
  @apply flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1;
}

.tab-button {
  @apply flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200;
  @apply text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white;
}

.tab-button.active {
  @apply bg-white dark:bg-gray-700 text-primary-600 dark:text-primary-400 shadow-sm;
}

.qr-container {
  @apply space-y-6;
}

.qr-code-wrapper {
  @apply relative flex justify-center;
}

.qr-loading {
  @apply flex flex-col items-center space-y-3 p-8;
}

.loading-text {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

.qr-code {
  @apply relative;
}

.qr-code img {
  @apply w-48 h-48 border border-gray-200 dark:border-gray-700 rounded-lg;
}

.qr-overlay {
  @apply absolute inset-0 bg-white bg-opacity-90 dark:bg-gray-900 dark:bg-opacity-90 flex items-center justify-center rounded-lg;
}

.scan-status {
  @apply flex flex-col items-center space-y-2 text-center;
}

.qr-error {
  @apply flex flex-col items-center space-y-3 p-8;
}

.qr-tips {
  @apply space-y-4;
}

.tip-item {
  @apply flex items-start space-x-3;
}

.tip-icon {
  @apply flex-shrink-0 w-8 h-8 bg-primary-100 dark:bg-primary-900 rounded-full flex items-center justify-center;
  @apply text-primary-600 dark:text-primary-400;
}

.tip-text p:first-child {
  @apply text-gray-900 dark:text-white;
}

.countdown {
  @apply text-center mt-4;
}
</style>
