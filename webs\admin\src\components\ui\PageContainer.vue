<template>
  <div :class="containerClasses">
    <!-- 页面头部 -->
    <PageHeader
      v-if="showHeader"
      :title="title"
      :subtitle="subtitle"
      :breadcrumb="breadcrumb"
      :back-button="backButton"
      @back="$emit('back')"
    >
      <template #actions>
        <slot name="header-actions"></slot>
      </template>
    </PageHeader>

    <!-- 页面内容 -->
    <div :class="contentClasses">
      <slot></slot>
    </div>

    <!-- 页面底部 -->
    <div v-if="$slots.footer" :class="footerClasses">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import PageHeader from './PageHeader.vue'

const props = defineProps({
  // 页面标题
  title: {
    type: String,
    default: ''
  },
  // 页面副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 面包屑导航
  breadcrumb: {
    type: Array,
    default: () => []
  },
  // 是否显示返回按钮
  backButton: {
    type: Boolean,
    default: false
  },
  // 是否显示头部
  showHeader: {
    type: Boolean,
    default: true
  },
  // 容器最大宽度
  maxWidth: {
    type: String,
    default: '7xl',
    validator: (value) => ['sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl', '7xl', 'full'].includes(value)
  },
  // 内边距
  padding: {
    type: String,
    default: 'normal',
    validator: (value) => ['none', 'sm', 'normal', 'lg'].includes(value)
  },
  // 是否流式布局
  fluid: {
    type: Boolean,
    default: false
  },
  // 背景色
  background: {
    type: String,
    default: 'gray',
    validator: (value) => ['white', 'gray', 'transparent'].includes(value)
  }
})

const emit = defineEmits(['back'])

const containerClasses = computed(() => {
  const classes = ['min-h-screen']
  
  // 背景色
  const backgroundClasses = {
    white: 'bg-white dark:bg-gray-900',
    gray: 'bg-gray-50 dark:bg-gray-900',
    transparent: 'bg-transparent'
  }
  
  classes.push(backgroundClasses[props.background])
  
  return classes.join(' ')
})

const contentClasses = computed(() => {
  const classes = []
  
  // 最大宽度
  if (!props.fluid) {
    const maxWidthClasses = {
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl',
      '3xl': 'max-w-3xl',
      '4xl': 'max-w-4xl',
      '5xl': 'max-w-5xl',
      '6xl': 'max-w-6xl',
      '7xl': 'max-w-7xl',
      full: 'max-w-full'
    }
    
    classes.push('mx-auto', maxWidthClasses[props.maxWidth])
  }
  
  // 内边距
  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2 sm:px-6',
    normal: 'px-4 py-6 sm:px-6 lg:px-8',
    lg: 'px-6 py-8 sm:px-8 lg:px-12'
  }
  
  classes.push(paddingClasses[props.padding])
  
  return classes.join(' ')
})

const footerClasses = computed(() => {
  const classes = ['border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800']
  
  // 内边距
  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2 sm:px-6',
    normal: 'px-4 py-4 sm:px-6 lg:px-8',
    lg: 'px-6 py-6 sm:px-8 lg:px-12'
  }
  
  classes.push(paddingClasses[props.padding])
  
  return classes.join(' ')
})
</script>
