<template>
  <el-config-provider :locale="locale">
    <router-view />

    <!-- 全局加载组件 -->
    <GlobalLoading
      :visible="loadingStore.globalLoading"
      :text="loadingStore.loadingText"
      :tip="loadingStore.loadingTip"
    />
  </el-config-provider>
</template>

<script setup>
import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import GlobalLoading from '@/components/GlobalLoading.vue'
import { useLoadingStore } from '@/stores/loading'

const locale = zhCn;
const loadingStore = useLoadingStore();
</script>

<style lang="scss">
#app {
  font-family:
    "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
    "Microsoft YaHei", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
}
</style>
