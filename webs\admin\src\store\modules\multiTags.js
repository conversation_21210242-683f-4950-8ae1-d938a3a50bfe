import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useMultiTagsStore = defineStore('multiTags', () => {
  // 状态
  const multiTags = ref([]);
  const multiTagsCache = ref(false);

  // 添加标签
  const addTag = (tag) => {
    const existingTag = multiTags.value.find(t => t.path === tag.path);
    if (!existingTag) {
      multiTags.value.push(tag);
    }
  };

  // 删除标签
  const removeTag = (path) => {
    const index = multiTags.value.findIndex(t => t.path === path);
    if (index > -1) {
      multiTags.value.splice(index, 1);
    }
  };

  // 清空所有标签
  const clearTags = () => {
    multiTags.value = [];
  };

  return {
    multiTags,
    multiTagsCache,
    addTag,
    removeTag,
    clearTags
  };
});

// 导出hook函数以保持兼容性
export const useMultiTagsStoreHook = () => {
  return useMultiTagsStore();
};
