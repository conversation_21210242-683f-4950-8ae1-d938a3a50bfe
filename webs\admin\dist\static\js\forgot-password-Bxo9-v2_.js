var F=(C,o,f)=>new Promise((e,w)=>{var p=r=>{try{t(f.next(r))}catch(u){w(u)}},d=r=>{try{t(f.throw(r))}catch(u){w(u)}},t=r=>r.done?e(r.value):Promise.resolve(r.value).then(p,d);t((f=f.apply(C,o)).next())});import{_ as A,r as _,c as L,a as c,d as n,w as m,u as b,g as y,h as k,s as N,v as V,z as S,A as z,E as g,l as T,b as v,m as h,t as U,p as j}from"./index-XtNpSMFt.js";const D={__name:"forgot-password",setup(C,{expose:o}){o();const f=b(),e=y(),w=y(!1),p=y(!1),d=y(0);let t=null;const r=k({email:"",emailCode:"",newPassword:"",confirmPassword:""}),u=(s,l,a)=>{if(!l){a(new Error("请输入邮箱地址"));return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(l)){a(new Error("请输入正确的邮箱地址"));return}a()},E=(s,l,a)=>{if(!l){a(new Error("请输入验证码"));return}if(l.length!==6){a(new Error("验证码为6位数字"));return}a()},P=(s,l,a)=>{if(!l){a(new Error("请输入新密码"));return}if(l.length<6){a(new Error("密码长度不能少于6位"));return}if(l.length>20){a(new Error("密码长度不能超过20位"));return}a()},i=(s,l,a)=>{if(!l){a(new Error("请确认密码"));return}if(l!==r.newPassword){a(new Error("两次输入的密码不一致"));return}a()},B={email:[{validator:u,trigger:"blur"}],emailCode:[{validator:E,trigger:"blur"}],newPassword:[{validator:P,trigger:"blur"}],confirmPassword:[{validator:i,trigger:"blur"}]},I=()=>F(this,null,function*(){try{yield e.value.validateField("email"),p.value=!0;const s=yield V.sendEmailCode(r.email.trim(),"reset-password");s.code===200?(g.success("验证码已发送到您的邮箱"),x()):g.error(s.message||"发送失败，请重试")}catch(s){console.error("发送验证码失败:",s),g.error("发送失败，请重试")}finally{p.value=!1}}),M=()=>F(this,null,function*(){try{yield e.value.validate(),w.value=!0;const s=yield V.verifyEmailCode(r.email.trim(),r.emailCode.trim(),"reset-password");if(s.code!==200){g.error(s.message||"验证码错误");return}const l=yield V.resetPasswordWithCode(r.email.trim(),r.emailCode.trim(),r.newPassword);l.code===200?(g.success("密码重置成功！正在跳转到登录页面..."),setTimeout(()=>{f.push("/login")},1500)):g.error(l.message||"重置失败，请重试")}catch(s){console.error("重置密码失败:",s),g.error("重置失败，请重试")}finally{w.value=!1}}),x=()=>{d.value=60,t=setInterval(()=>{d.value--,d.value<=0&&(clearInterval(t),t=null)},1e3)};N(()=>{t&&clearInterval(t)});const R={router:f,forgotFormRef:e,loading:w,sendingCode:p,emailCountdown:d,get emailTimer(){return t},set emailTimer(s){t=s},forgotForm:r,validateEmail:u,validateEmailCode:E,validateNewPassword:P,validateConfirmPassword:i,forgotRules:B,sendEmailCode:I,handleResetPassword:M,startEmailCountdown:x,ref:y,reactive:k,onUnmounted:N,get useRouter(){return b},get ElMessage(){return g},get Message(){return z},get CircleCheck(){return S},get authApi(){return V}};return Object.defineProperty(R,"__isScriptSetup",{enumerable:!1,value:!0}),R}},O={class:"forgot-password-container"},W={class:"forgot-password-form"},q={class:"code-input-group"},G={class:"form-footer"};function H(C,o,f,e,w,p){const d=_("el-input"),t=_("el-form-item"),r=_("el-button"),u=_("el-icon"),E=_("el-link"),P=_("el-form");return T(),L("div",O,[c("div",W,[o[9]||(o[9]=c("div",{class:"form-header"},[c("h2",null,"重置密码"),c("p",null,"请输入您的邮箱地址，获取验证码后设置新密码")],-1)),n(P,{ref:"forgotFormRef",model:e.forgotForm,rules:e.forgotRules,"label-width":"0",size:"large"},{default:m(()=>[v(" 邮箱输入 "),n(t,{prop:"email"},{default:m(()=>[n(d,{modelValue:e.forgotForm.email,"onUpdate:modelValue":o[0]||(o[0]=i=>e.forgotForm.email=i),placeholder:"请输入注册邮箱","prefix-icon":"Message",clearable:""},null,8,["modelValue"])]),_:1}),v(" 验证码输入 "),n(t,{prop:"emailCode"},{default:m(()=>[c("div",q,[n(d,{modelValue:e.forgotForm.emailCode,"onUpdate:modelValue":o[1]||(o[1]=i=>e.forgotForm.emailCode=i),placeholder:"请输入邮箱验证码","prefix-icon":"Lock",clearable:"",maxlength:"6"},null,8,["modelValue"]),n(r,{disabled:e.emailCountdown>0||!e.forgotForm.email,onClick:e.sendEmailCode,class:"code-btn",loading:e.sendingCode},{default:m(()=>[h(U(e.emailCountdown>0?`${e.emailCountdown}s`:"发送验证码"),1)]),_:1},8,["disabled","loading"])])]),_:1}),v(" 新密码 "),n(t,{prop:"newPassword"},{default:m(()=>[n(d,{modelValue:e.forgotForm.newPassword,"onUpdate:modelValue":o[2]||(o[2]=i=>e.forgotForm.newPassword=i),type:"password",placeholder:"请输入新密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),v(" 确认密码 "),n(t,{prop:"confirmPassword"},{default:m(()=>[n(d,{modelValue:e.forgotForm.confirmPassword,"onUpdate:modelValue":o[3]||(o[3]=i=>e.forgotForm.confirmPassword=i),type:"password",placeholder:"请确认新密码","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),v(" 重置密码按钮 "),n(t,null,{default:m(()=>[n(r,{type:"primary",loading:e.loading,onClick:e.handleResetPassword,style:{width:"100%"}},{default:m(()=>[e.loading?v("v-if",!0):(T(),j(u,{key:0},{default:m(()=>[n(e.CircleCheck)]),_:1})),h(" "+U(e.loading?"重置中...":"重置密码"),1)]),_:1},8,["loading"])]),_:1}),c("div",G,[n(E,{type:"primary",onClick:o[4]||(o[4]=i=>C.$router.push("/login"))},{default:m(()=>o[6]||(o[6]=[h(" 返回登录 ",-1)])),_:1,__:[6]}),o[8]||(o[8]=c("span",{class:"divider"},"|",-1)),n(E,{type:"primary",onClick:o[5]||(o[5]=i=>C.$router.push("/register"))},{default:m(()=>o[7]||(o[7]=[h(" 注册账户 ",-1)])),_:1,__:[7]})])]),_:1},8,["model"])])])}const X=A(D,[["render",H],["__scopeId","data-v-5ae8e79f"],["__file","E:/wx-nan/webs/admin/src/views/auth/forgot-password.vue"]]);export{X as default};
