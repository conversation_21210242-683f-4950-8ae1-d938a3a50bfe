const axios = require('axios');

/**
 * 微信服务
 * 用于处理微信登录和获取用户信息
 */
class WechatService {
  constructor() {
    this.appId = process.env.WECHAT_APPID;
    this.appSecret = process.env.WECHAT_SECRET;
  }

  /**
   * 获取微信用户的 OpenID 和会话密钥
   * @param {string} code - 微信登录时获取的 code
   * @returns {Promise<Object>} 包含 openid 和 session_key 的对象
   */
  async getOpenId(code) {
    try {
      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${this.appId}&secret=${this.appSecret}&js_code=${code}&grant_type=authorization_code`;

      const response = await axios.get(url);

      if (response.data.errcode) {
        throw new Error(`WeChat API error: ${response.data.errmsg}`);
      }

      return {
        openid: response.data.openid,
        session_key: response.data.session_key
      };
    } catch (error) {
      console.error('Error getting WeChat OpenID:', error);

      // 模拟微信登录，返回随机 OpenID (仅用于开发)
      if (process.env.NODE_ENV === 'development') {
        return {
          openid: `mock_openid_${Date.now()}`,
          session_key: `mock_session_key_${Date.now()}`
        };
      }

      throw new Error('Failed to get WeChat OpenID');
    }
  }

  /**
   * 获取微信用户手机号
   * @param {string} code - 手机号授权码
   * @returns {Promise<string>} 用户手机号
   */
  async getPhoneNumber(code) {
    try {
      // 首先获取access_token
      const tokenUrl = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${this.appId}&secret=${this.appSecret}`;
      const tokenResponse = await axios.get(tokenUrl);

      if (tokenResponse.data.errcode) {
        throw new Error(`Get access_token error: ${tokenResponse.data.errmsg}`);
      }

      const accessToken = tokenResponse.data.access_token;

      // 使用access_token获取手机号
      const phoneUrl = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${accessToken}`;
      const phoneResponse = await axios.post(phoneUrl, {code});

      if (phoneResponse.data.errcode !== 0) {
        throw new Error(`Get phone number error: ${phoneResponse.data.errmsg}`);
      }

      return phoneResponse.data.phone_info.phoneNumber;
    } catch (error) {
      console.error('Error getting WeChat phone number:', error);

      // 开发环境模拟手机号
      if (process.env.NODE_ENV === 'development') {
        console.log('🔧 开发环境：模拟手机号获取');
        return '***********'; // 返回您的测试手机号
      }

      throw new Error('Failed to get WeChat phone number');
    }
  }
}

module.exports = new WechatService();
