<template>
  <div :class="cardClasses">
    <div class="p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <!-- 图标容器 -->
          <div :class="iconContainerClasses">
            <component
              :is="icon"
              :class="iconClasses"
              aria-hidden="true"
            />
          </div>
        </div>
        
        <div class="ml-5 w-0 flex-1">
          <!-- 标题 -->
          <dl>
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
              {{ title }}
            </dt>
            <dd class="flex items-baseline">
              <!-- 主要数值 -->
              <div :class="valueClasses">
                <CountUp
                  v-if="animated"
                  :end-val="value"
                  :duration="animationDuration"
                  :options="countUpOptions"
                />
                <span v-else>{{ formattedValue }}</span>
              </div>
              
              <!-- 变化趋势 -->
              <div v-if="change !== null && change !== undefined" class="ml-2 flex items-baseline text-sm">
                <component
                  :is="changeIcon"
                  :class="changeIconClasses"
                  aria-hidden="true"
                />
                <span :class="changeTextClasses">
                  {{ Math.abs(change) }}{{ changeUnit }}
                </span>
              </div>
            </dd>
          </dl>
          
          <!-- 描述文本 -->
          <p v-if="description" class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            {{ description }}
          </p>
        </div>
      </div>
      
      <!-- 底部操作区域 -->
      <div v-if="$slots.actions" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <slot name="actions"></slot>
      </div>
    </div>
    
    <!-- 点击效果 -->
    <div v-if="clickable" class="absolute inset-0 bg-black opacity-0 hover:opacity-5 transition-opacity duration-200 rounded-lg pointer-events-none"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  ArrowUpIcon, 
  ArrowDownIcon,
  MinusIcon
} from '@heroicons/vue/20/solid'

const props = defineProps({
  // 卡片标题
  title: {
    type: String,
    required: true
  },
  // 数值
  value: {
    type: [Number, String],
    required: true
  },
  // 图标
  icon: {
    type: [Object, Function],
    required: true
  },
  // 变化值
  change: {
    type: Number,
    default: null
  },
  // 变化单位
  changeUnit: {
    type: String,
    default: '%'
  },
  // 描述文本
  description: {
    type: String,
    default: ''
  },
  // 卡片颜色主题
  color: {
    type: String,
    default: 'blue',
    validator: (value) => ['blue', 'green', 'yellow', 'red', 'purple', 'gray'].includes(value)
  },
  // 卡片尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  },
  // 是否显示动画
  animated: {
    type: Boolean,
    default: true
  },
  // 动画持续时间
  animationDuration: {
    type: Number,
    default: 2
  },
  // 数值格式化选项
  formatOptions: {
    type: Object,
    default: () => ({})
  },
  // 是否显示阴影
  shadow: {
    type: Boolean,
    default: true
  },
  // 是否显示边框
  bordered: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

// 颜色配置
const colorConfig = {
  blue: {
    iconBg: 'bg-blue-500',
    iconText: 'text-white',
    value: 'text-blue-600 dark:text-blue-400'
  },
  green: {
    iconBg: 'bg-green-500',
    iconText: 'text-white',
    value: 'text-green-600 dark:text-green-400'
  },
  yellow: {
    iconBg: 'bg-yellow-500',
    iconText: 'text-white',
    value: 'text-yellow-600 dark:text-yellow-400'
  },
  red: {
    iconBg: 'bg-red-500',
    iconText: 'text-white',
    value: 'text-red-600 dark:text-red-400'
  },
  purple: {
    iconBg: 'bg-purple-500',
    iconText: 'text-white',
    value: 'text-purple-600 dark:text-purple-400'
  },
  gray: {
    iconBg: 'bg-gray-500',
    iconText: 'text-white',
    value: 'text-gray-600 dark:text-gray-400'
  }
}

const cardClasses = computed(() => {
  const classes = [
    'relative bg-white dark:bg-gray-800 overflow-hidden rounded-lg transition-all duration-200'
  ]
  
  if (props.shadow) {
    classes.push('shadow-soft hover:shadow-soft-lg')
  }
  
  if (props.bordered) {
    classes.push('border border-gray-200 dark:border-gray-700')
  }
  
  if (props.clickable) {
    classes.push('cursor-pointer hover:scale-105 transform')
  }
  
  return classes.join(' ')
})

const iconContainerClasses = computed(() => {
  const config = colorConfig[props.color]
  const sizeClasses = {
    sm: 'p-2',
    md: 'p-3',
    lg: 'p-4'
  }
  
  return [
    'rounded-md',
    config.iconBg,
    sizeClasses[props.size]
  ].join(' ')
})

const iconClasses = computed(() => {
  const config = colorConfig[props.color]
  const sizeClasses = {
    sm: 'h-5 w-5',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }
  
  return [
    config.iconText,
    sizeClasses[props.size]
  ].join(' ')
})

const valueClasses = computed(() => {
  const config = colorConfig[props.color]
  const sizeClasses = {
    sm: 'text-xl',
    md: 'text-2xl',
    lg: 'text-3xl'
  }
  
  return [
    'font-semibold',
    config.value,
    sizeClasses[props.size]
  ].join(' ')
})

const changeIcon = computed(() => {
  if (props.change > 0) return ArrowUpIcon
  if (props.change < 0) return ArrowDownIcon
  return MinusIcon
})

const changeIconClasses = computed(() => {
  const classes = ['h-4 w-4 flex-shrink-0 self-center']
  
  if (props.change > 0) {
    classes.push('text-green-500')
  } else if (props.change < 0) {
    classes.push('text-red-500')
  } else {
    classes.push('text-gray-500')
  }
  
  return classes.join(' ')
})

const changeTextClasses = computed(() => {
  const classes = ['font-medium']
  
  if (props.change > 0) {
    classes.push('text-green-700 dark:text-green-400')
  } else if (props.change < 0) {
    classes.push('text-red-700 dark:text-red-400')
  } else {
    classes.push('text-gray-700 dark:text-gray-400')
  }
  
  return classes.join(' ')
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return new Intl.NumberFormat('zh-CN', props.formatOptions).format(props.value)
  }
  return props.value
})

const countUpOptions = computed(() => ({
  useEasing: true,
  useGrouping: true,
  separator: ',',
  decimal: '.',
  ...props.formatOptions
}))

const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>

<script>
// 简单的 CountUp 组件实现
import { ref, onMounted, watch } from 'vue'

const CountUp = {
  props: {
    endVal: {
      type: Number,
      required: true
    },
    duration: {
      type: Number,
      default: 2
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const displayValue = ref(0)
    
    const animate = () => {
      const startTime = Date.now()
      const startVal = 0
      const endVal = props.endVal
      const duration = props.duration * 1000
      
      const step = () => {
        const now = Date.now()
        const progress = Math.min((now - startTime) / duration, 1)
        
        displayValue.value = Math.floor(startVal + (endVal - startVal) * progress)
        
        if (progress < 1) {
          requestAnimationFrame(step)
        }
      }
      
      requestAnimationFrame(step)
    }
    
    onMounted(() => {
      animate()
    })
    
    watch(() => props.endVal, () => {
      animate()
    })
    
    return {
      displayValue
    }
  },
  template: '<span>{{ displayValue.toLocaleString() }}</span>'
}

export { CountUp }
</script>
