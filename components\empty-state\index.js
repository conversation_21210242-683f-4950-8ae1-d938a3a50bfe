// 通用缺省页组件
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 图标名称（使用van-icon）
    icon: {
      type: String,
      value: 'friends-o'
    },
    
    // 图标大小
    iconSize: {
      type: String,
      value: '60rpx'
    },
    
    // 图标颜色
    iconColor: {
      type: String,
      value: '#D1D5DB'
    },
    
    // 主要文字
    text: {
      type: String,
      value: '暂无数据'
    },
    
    // 描述文字
    description: {
      type: String,
      value: ''
    },
    
    // 操作按钮文字
    actionText: {
      type: String,
      value: ''
    },
    
    // 按钮大小
    actionSize: {
      type: String,
      value: 'small'
    },
    
    // 按钮类型
    actionType: {
      type: String,
      value: 'primary'
    },
    
    // 按钮是否为朴素按钮
    actionPlain: {
      type: Boolean,
      value: true
    },
    
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    },
    
    // 主题（light, card, minimal）
    theme: {
      type: String,
      value: '',
      observer: function(newVal) {
        if (newVal) {
          this.setData({
            customClass: this.data.customClass + ' theme-' + newVal
          });
        }
      }
    },
    
    // 尺寸（small, normal, large）
    size: {
      type: String,
      value: 'normal',
      observer: function(newVal) {
        if (newVal && newVal !== 'normal') {
          this.setData({
            customClass: this.data.customClass + ' size-' + newVal
          });
        }
      }
    },
    
    // 是否启用动画
    animated: {
      type: Boolean,
      value: false,
      observer: function(newVal) {
        if (newVal) {
          this.setData({
            customClass: this.data.customClass + ' animated'
          });
        }
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 操作按钮点击事件
     */
    onActionClick() {
      this.triggerEvent('action', {
        timestamp: Date.now()
      });
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 组件实例被放入页面节点树后执行
      console.log('EmptyState component attached');
    },
    
    detached() {
      // 组件实例被从页面节点树移除后执行
      console.log('EmptyState component detached');
    }
  }
});
