.container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 120rpx;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  .header-content {
    flex: 1;

    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
    }

    .page-subtitle {
      font-size: 28rpx;
      opacity: 0.9;
    }
  }

  .header-actions {
    .van-button {
      background: rgba(255, 255, 255, 0.2);
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      color: white;

      &:active {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.statistics-card {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-around;

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: 48rpx;
      font-weight: bold;
      color: #667eea;
      margin-bottom: 8rpx;
    }

    .stat-label {
      font-size: 24rpx;
      color: #969799;
    }
  }
}

.filter-tags {
  padding: 0 20rpx 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;

  .filter-tag-item {
    background: #667eea;
    color: white;
    padding: 12rpx 20rpx;
    border-radius: 24rpx;
    font-size: 24rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;

    .van-icon {
      cursor: pointer;

      &:active {
        opacity: 0.7;
      }
    }
  }
}

.history-list {
  padding: 0 20rpx;
}

.history-item {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);

  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .user-info {
      display: flex;
      align-items: center;
      flex: 1;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 20rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f5f5f5;

        image {
          width: 100%;
          height: 100%;
        }
      }

      .info {
        flex: 1;

        .name {
          font-size: 32rpx;
          font-weight: 600;
          color: #323233;
          margin-bottom: 8rpx;
        }

        .type-badge {
          font-size: 22rpx;
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
          display: inline-block;

          &.sent {
            background: rgba(7, 193, 96, 0.1);
            color: #07c160;
          }

          &.received {
            background: rgba(255, 149, 0, 0.1);
            color: #ff9500;
          }
        }
      }
    }

    .actions {
      display: flex;
      gap: 12rpx;
    }
  }

  .message-content {
    background: #f7f8fa;
    padding: 20rpx;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    display: flex;
    align-items: flex-start;
    gap: 12rpx;
    font-size: 28rpx;
    color: #646566;
    line-height: 1.5;

    .van-icon {
      margin-top: 4rpx;
      flex-shrink: 0;
    }
  }
}

/* 移除了 .load-more 样式，现在使用组件 */

.loading-container {
  padding: 80rpx 0;
  text-align: center;
}

.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;

  .empty-text {
    font-size: 32rpx;
    color: #969799;
    margin: 32rpx 0 16rpx;
    font-weight: 500;
  }

  .empty-desc {
    font-size: 26rpx;
    color: #c8c9cc;
    line-height: 1.5;
  }
}

.filter-content {
  padding: 40rpx 0;

  .filter-section {
    margin-bottom: 40rpx;

    .filter-title {
      font-size: 28rpx;
      font-weight: 600;
      color: #323233;
      padding: 0 40rpx 20rpx;
    }

    .filter-options {
      display: flex;
      flex-wrap: wrap;
      gap: 20rpx;
      padding: 0 40rpx;

      .filter-option {
        flex: 1;
        min-width: 120rpx;
        padding: 20rpx;
        text-align: center;
        background: #f7f8fa;
        border-radius: 12rpx;
        font-size: 26rpx;
        color: #646566;
        transition: all 0.3s ease;

        &.active {
          background: #667eea;
          color: white;
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }
}

/* Vant 组件样式覆盖 */
.van-button--mini {
  height: 56rpx;
  padding: 0 16rpx;
  font-size: 22rpx;
}

.van-button--small {
  height: 64rpx;
  padding: 0 20rpx;
  font-size: 24rpx;
}

.van-action-sheet__header {
  font-size: 32rpx;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .statistics-card {
    margin: 16rpx;
    padding: 24rpx;

    .stat-item .stat-number {
      font-size: 40rpx;
    }
  }

  .history-item {
    padding: 20rpx;
    margin-bottom: 16rpx;

    .history-header .user-info .avatar {
      width: 64rpx;
      height: 64rpx;
    }
  }
}
