<template>
  <div class="change-password-page">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>修改密码</h3>
          <p>为了账户安全，建议定期更换密码</p>
        </div>
      </template>

      <div class="form-container">
        <el-form
          ref="changePasswordFormRef"
          :model="changePasswordForm"
          :rules="changePasswordRules"
          label-width="100px"
          size="large"
          style="max-width: 500px"
        >
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input
              v-model="changePasswordForm.currentPassword"
              type="password"
              placeholder="请输入当前密码"
              prefix-icon="Lock"
              show-password
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="changePasswordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              prefix-icon="Lock"
              show-password
            />
            <div class="password-tips">
              <p>密码要求：</p>
              <ul>
                <li>长度在 6-20 个字符之间</li>
                <li>建议包含字母、数字和特殊字符</li>
                <li>不能与当前密码相同</li>
              </ul>
            </div>
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="changePasswordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              prefix-icon="Lock"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="loading"
              @click="handleChangePassword"
            >
              修改密码
            </el-button>
            <el-button @click="resetForm"> 重置 </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 安全提示 -->
    <el-card style="margin-top: 20px">
      <template #header>
        <h4>安全提示</h4>
      </template>
      <div class="security-tips">
        <el-alert title="密码安全建议" type="info" :closable="false" show-icon>
          <ul>
            <li>定期更换密码，建议每3-6个月更换一次</li>
            <li>不要使用过于简单的密码，如生日、姓名等</li>
            <li>不要在多个平台使用相同密码</li>
            <li>如发现账户异常，请立即修改密码</li>
          </ul>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'

const changePasswordFormRef = ref()
const loading = ref(false)

const changePasswordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const validateCurrentPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入当前密码'))
  } else {
    callback()
  }
}

const validateNewPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入新密码'))
  } else if (value.length < 6 || value.length > 20) {
    callback(new Error('密码长度在 6 到 20 个字符'))
  } else if (value === changePasswordForm.currentPassword) {
    callback(new Error('新密码不能与当前密码相同'))
  } else {
    callback()
  }
}

const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请确认新密码'))
  } else if (value !== changePasswordForm.newPassword) {
    callback(new Error('两次输入的密码不一致'))
  } else {
    callback()
  }
}

const changePasswordRules = {
  currentPassword: [
    { validator: validateCurrentPassword, trigger: 'blur' }
  ],
  newPassword: [
    { validator: validateNewPassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

const handleChangePassword = async () => {
  try {
    await changePasswordFormRef.value.validate()

    // 确认操作
    await ElMessageBox.confirm(
      '修改密码后需要重新登录，确定要继续吗？',
      '确认修改',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    loading.value = true

    // TODO: 调用修改密码API
    // const response = await authApi.changePassword({
    //   currentPassword: changePasswordForm.currentPassword,
    //   newPassword: changePasswordForm.newPassword
    // })

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('密码修改成功！请重新登录')

    // 清除登录状态，跳转到登录页
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')

    setTimeout(() => {
      window.location.href = '/login'
    }, 1500)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('修改密码失败:', error)
      ElMessage.error('修改密码失败，请检查当前密码是否正确')
    }
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  changePasswordFormRef.value.resetFields()
}
</script>

<style scoped>
.change-password-page {
  padding: 20px;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.card-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.form-container {
  padding: 20px 0;
}

.password-tips {
  margin-top: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.password-tips p {
  margin: 0 0 8px 0;
  font-weight: 500;
}

.password-tips ul {
  margin: 0;
  padding-left: 16px;
}

.password-tips li {
  margin: 4px 0;
}

.security-tips ul {
  margin: 0;
  padding-left: 20px;
}

.security-tips li {
  margin: 8px 0;
  line-height: 1.5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-alert__content) {
  padding-top: 0;
}
</style>
