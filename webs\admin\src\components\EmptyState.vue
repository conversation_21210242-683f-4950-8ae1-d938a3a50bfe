<template>
  <div 
    class="empty-state"
    v-motion
    :initial="{ opacity: 0, scale: 0.8 }"
    :enter="{ opacity: 1, scale: 1, transition: { duration: 600 } }"
  >
    <el-empty
      :image="imageUrl"
      :image-size="imageSize"
      :description="description"
      class="custom-empty"
    >
      <template #image>
        <div class="empty-image">
          <el-icon v-if="icon" :size="iconSize" :color="iconColor">
            <component :is="icon" />
          </el-icon>
          <img v-else-if="imageUrl" :src="imageUrl" :alt="description" />
          <div v-else class="default-empty-icon">
            <el-icon :size="80" color="#dcdfe6">
              <Box />
            </el-icon>
          </div>
        </div>
      </template>
      
      <template #description>
        <div class="empty-description">
          <h3 class="empty-title">{{ title || '暂无数据' }}</h3>
          <p class="empty-text">{{ description || '当前没有可显示的内容' }}</p>
        </div>
      </template>
      
      <template #default>
        <div class="empty-actions" v-if="showAction">
          <el-button 
            v-if="actionText" 
            :type="actionType" 
            :icon="actionIcon"
            @click="handleAction"
            class="action-button"
          >
            {{ actionText }}
          </el-button>
          <slot name="actions" />
        </div>
      </template>
    </el-empty>
  </div>
</template>

<script setup>
import { Box, Plus, Refresh, Search } from '@element-plus/icons-vue'

const props = defineProps({
  // 图标相关
  icon: {
    type: [String, Object],
    default: null
  },
  iconSize: {
    type: Number,
    default: 80
  },
  iconColor: {
    type: String,
    default: '#dcdfe6'
  },
  
  // 图片相关
  imageUrl: {
    type: String,
    default: ''
  },
  imageSize: {
    type: Number,
    default: 200
  },
  
  // 文本相关
  title: {
    type: String,
    default: '暂无数据'
  },
  description: {
    type: String,
    default: '当前没有可显示的内容'
  },
  
  // 操作按钮相关
  showAction: {
    type: Boolean,
    default: true
  },
  actionText: {
    type: String,
    default: '刷新'
  },
  actionType: {
    type: String,
    default: 'primary'
  },
  actionIcon: {
    type: [String, Object],
    default: () => Refresh
  },
  
  // 样式相关
  height: {
    type: String,
    default: 'auto'
  },
  background: {
    type: String,
    default: 'transparent'
  }
})

const emit = defineEmits(['action-click'])

const handleAction = () => {
  emit('action-click')
}
</script>

<style scoped lang="scss">
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  padding: 40px 20px;
  
  .custom-empty {
    .empty-image {
      margin-bottom: 20px;
      
      .default-empty-icon {
        opacity: 0.6;
        transition: all 0.3s ease;
        
        &:hover {
          opacity: 0.8;
          transform: scale(1.05);
        }
      }
    }
    
    .empty-description {
      margin-bottom: 24px;
      
      .empty-title {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
        line-height: 1.4;
      }
      
      .empty-text {
        margin: 0;
        font-size: 14px;
        color: #909399;
        line-height: 1.6;
      }
    }
    
    .empty-actions {
      .action-button {
        padding: 12px 24px;
        font-size: 14px;
        border-radius: 6px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }
}

// 不同类型的空状态样式
.empty-state {
  &.no-data {
    .empty-image {
      .el-icon {
        color: #e6a23c;
      }
    }
  }
  
  &.no-search {
    .empty-image {
      .el-icon {
        color: #909399;
      }
    }
  }
  
  &.error {
    .empty-image {
      .el-icon {
        color: #f56c6c;
      }
    }
  }
  
  &.loading {
    .empty-image {
      .el-icon {
        color: #409eff;
        animation: pulse 2s infinite;
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .empty-state {
    min-height: 200px;
    padding: 20px 10px;
    
    .custom-empty {
      .empty-description {
        .empty-title {
          font-size: 16px;
        }
        
        .empty-text {
          font-size: 13px;
        }
      }
      
      .empty-actions {
        .action-button {
          padding: 10px 20px;
          font-size: 13px;
        }
      }
    }
  }
}
</style>
