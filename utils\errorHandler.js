/**
 * 统一错误处理工具
 * 处理网络请求错误、业务逻辑错误等
 */

// 尝试加载UI工具，如果失败则使用兼容版本
let ui;
try {
  ui = require('./ui.js');
} catch (error) {
  ui = require('./ui-compat.js');
}

class ErrorHandler {
  constructor() {
    this.errorCodes = {
      // 网络错误
      NETWORK_ERROR: -1,
      TIMEOUT_ERROR: -2,

      // 认证错误
      UNAUTHORIZED: 401,
      FORBIDDEN: 403,

      // 业务错误
      NOT_FOUND: 404,
      CONFLICT: 409,
      VALIDATION_ERROR: 422,

      // 服务器错误
      INTERNAL_ERROR: 500,
      SERVICE_UNAVAILABLE: 503
    };

    this.errorMessages = {
      [this.errorCodes.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
      [this.errorCodes.TIMEOUT_ERROR]: '请求超时，请稍后重试',
      [this.errorCodes.UNAUTHORIZED]: '登录已过期，请重新登录',
      [this.errorCodes.FORBIDDEN]: '权限不足，无法执行此操作',
      [this.errorCodes.NOT_FOUND]: '请求的资源不存在',
      [this.errorCodes.CONFLICT]: '数据冲突，请刷新后重试',
      [this.errorCodes.VALIDATION_ERROR]: '数据验证失败，请检查输入',
      [this.errorCodes.INTERNAL_ERROR]: '服务器内部错误，请稍后重试',
      [this.errorCodes.SERVICE_UNAVAILABLE]: '服务暂时不可用，请稍后重试'
    };
  }

  /**
   * 处理API请求错误
   * @param {Object} error 错误对象
   * @param {Object} options 处理选项
   */
  handleApiError(error, options = {}) {
    const {
      showToast = true,
      showNotify = false,
      customMessage,
      onUnauthorized,
      onForbidden,
      onNotFound
    } = options;

    let message = customMessage;
    let code = error.code || error.statusCode;

    // 处理不同类型的错误
    if (error.name === 'NetworkError' || error.message === '网络错误') {
      code = this.errorCodes.NETWORK_ERROR;
    } else if (
      error.name === 'TimeoutError' ||
      error.message?.includes('timeout')
    ) {
      code = this.errorCodes.TIMEOUT_ERROR;
    }

    // 获取错误消息
    if (!message) {
      message =
        error.message || this.errorMessages[code] || '操作失败，请稍后重试';
    }

    // 特殊错误处理
    switch (code) {
      case this.errorCodes.UNAUTHORIZED:
        if (onUnauthorized) {
          onUnauthorized();
        } else {
          this.handleUnauthorized();
        }
        break;

      case this.errorCodes.FORBIDDEN:
        if (onForbidden) {
          onForbidden();
        }
        break;

      case this.errorCodes.NOT_FOUND:
        if (onNotFound) {
          onNotFound();
        }
        break;
    }

    // 显示错误提示
    if (showToast) {
      ui.showError(message);
    } else if (showNotify) {
      ui.showErrorNotify(message);
    }

    return {
      code,
      message,
      handled: true
    };
  }

  /**
   * 处理业务逻辑错误
   * @param {string} message 错误消息
   * @param {Object} options 处理选项
   */
  handleBusinessError(message, options = {}) {
    const {
      showToast = true,
      showNotify = false,
      code = 'BUSINESS_ERROR'
    } = options;

    if (showToast) {
      ui.showError(message);
    } else if (showNotify) {
      ui.showErrorNotify(message);
    }

    return {
      code,
      message,
      handled: true
    };
  }

  /**
   * 处理表单验证错误
   * @param {Object} errors 验证错误对象
   * @param {Object} options 处理选项
   */
  handleValidationErrors(errors, options = {}) {
    const {showToast = true, showFirstOnly = true} = options;

    if (!errors || typeof errors !== 'object') {
      return this.handleBusinessError('表单验证失败');
    }

    const errorMessages = Object.values(errors).flat();

    if (errorMessages.length === 0) {
      return this.handleBusinessError('表单验证失败');
    }

    const message = showFirstOnly ? errorMessages[0] : errorMessages.join('; ');

    if (showToast) {
      ui.showError(message);
    }

    return {
      code: this.errorCodes.VALIDATION_ERROR,
      message,
      errors,
      handled: true
    };
  }

  /**
   * 处理未授权错误
   */
  handleUnauthorized() {
    // 清除本地存储的token
    wx.removeStorageSync('token');
    wx.removeStorageSync('accessToken');
    wx.removeStorageSync('userInfo');

    // 显示提示
    ui.showError('登录已过期，请重新登录');

    // 延迟跳转到登录页
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/index'
      });
    }, 1500);
  }

  /**
   * 创建错误对象
   * @param {string} message 错误消息
   * @param {number|string} code 错误代码
   * @param {Object} data 额外数据
   */
  createError(message, code = 'UNKNOWN_ERROR', data = null) {
    const error = new Error(message);
    error.code = code;
    error.data = data;
    return error;
  }

  /**
   * 包装异步函数，自动处理错误
   * @param {Function} fn 异步函数
   * @param {Object} options 错误处理选项
   */
  wrapAsync(fn, options = {}) {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        this.handleApiError(error, options);
        throw error;
      }
    };
  }

  /**
   * 重试机制
   * @param {Function} fn 要重试的函数
   * @param {Object} options 重试选项
   */
  async retry(fn, options = {}) {
    const {maxRetries = 3, delay = 1000, backoff = 1.5, onRetry} = options;

    let lastError;

    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;

        if (i === maxRetries) {
          break;
        }

        if (onRetry) {
          onRetry(i + 1, error);
        }

        // 等待后重试
        await new Promise(resolve =>
          setTimeout(resolve, delay * Math.pow(backoff, i))
        );
      }
    }

    throw lastError;
  }
}

// 创建全局实例
const errorHandler = new ErrorHandler();

// 挂载到全局 app
const app = getApp();
if (app) {
  app.errorHandler = errorHandler;
}

module.exports = errorHandler;
