<template>
  <div class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
    <div class="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
      <!-- 面包屑导航 -->
      <Breadcrumb v-if="breadcrumb.length" :items="breadcrumb" class="mb-4" />
      
      <div class="md:flex md:items-center md:justify-between">
        <div class="flex-1 min-w-0">
          <div class="flex items-center">
            <!-- 返回按钮 -->
            <BaseButton
              v-if="backButton"
              variant="ghost"
              size="sm"
              :prefix-icon="ArrowLeftIcon"
              @click="$emit('back')"
              class="mr-3"
            >
              返回
            </BaseButton>
            
            <div>
              <!-- 页面标题 -->
              <h1 class="text-2xl font-bold leading-7 text-gray-900 dark:text-white sm:truncate">
                {{ title }}
              </h1>
              
              <!-- 页面副标题 -->
              <p v-if="subtitle" class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {{ subtitle }}
              </p>
            </div>
          </div>
        </div>
        
        <!-- 操作按钮区域 -->
        <div v-if="$slots.actions" class="mt-4 flex md:mt-0 md:ml-4">
          <slot name="actions"></slot>
        </div>
      </div>
      
      <!-- 标签页或其他内容 -->
      <div v-if="$slots.tabs" class="mt-6">
        <slot name="tabs"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowLeftIcon } from '@heroicons/vue/20/solid'
import BaseButton from './BaseButton.vue'
import Breadcrumb from './Breadcrumb.vue'

defineProps({
  // 页面标题
  title: {
    type: String,
    required: true
  },
  // 页面副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 面包屑导航
  breadcrumb: {
    type: Array,
    default: () => []
  },
  // 是否显示返回按钮
  backButton: {
    type: Boolean,
    default: false
  }
})

defineEmits(['back'])
</script>
