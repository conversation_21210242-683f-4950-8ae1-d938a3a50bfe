/**
 * 高度计算工具类
 * 用于计算页面滚动区域的合适高度
 */

/**
 * 计算滚动区域高度
 * @param {Object} options 配置选项
 * @param {string} options.headerSelector 头部元素选择器
 * @param {number} options.minHeightRatio 最小高度比例 (0-1)
 * @param {number} options.maxHeightRatio 最大高度比例 (0-1)
 * @param {number} options.extraPadding 额外边距
 * @param {Function} options.callback 计算完成回调
 * @returns {Promise} 返回计算结果
 */
const calculateScrollHeight = (options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      const {
        headerSelector = '.header-section',
        minHeightRatio = 0.25,
        maxHeightRatio = 1,
        extraPadding = 20,
        callback
      } = options;

      const systemInfo = wx.getSystemInfoSync();
      const {
        windowHeight,
        windowWidth,
        statusBarHeight = 0,
        safeArea,
        platform,
        model
      } = systemInfo;

      // 获取安全区域信息
      const safeAreaBottom = safeArea?.bottom || windowHeight;
      const safeAreaTop = safeArea?.top || statusBarHeight;

      // 创建查询对象，获取固定元素的高度
      const query = wx.createSelectorQuery();
      if (headerSelector) {
        query.select(headerSelector).boundingClientRect();
      }

      query.exec(res => {
        try {
          let usedHeight = 0;

          // 累加已使用的高度
          if (res && res.length > 0) {
            res.forEach(rect => {
              if (rect && rect.height) {
                usedHeight += rect.height;
              }
            });
          }

          // 根据平台和设备型号调整参数
          let navigationBarHeight = 44; // 默认导航栏高度
          let deviceExtraPadding = extraPadding;

          // iOS设备特殊处理
          if (platform === 'ios') {
            // iPhone X系列及以上有更大的安全区域
            if (
              model &&
              (model.includes('iPhone X') || model.includes('iPhone 1'))
            ) {
              navigationBarHeight = 88; // 包含状态栏的导航栏高度
              deviceExtraPadding = Math.max(extraPadding, 34); // 底部安全区域
            }
          }

          // 计算系统占用的高度
          const systemOccupiedHeight =
            windowHeight - safeAreaBottom + safeAreaTop;
          const totalPadding =
            systemOccupiedHeight + navigationBarHeight + deviceExtraPadding;

          // 计算可用高度
          const availableHeight = Math.max(
            0,
            windowHeight - usedHeight - totalPadding
          );

          // 根据屏幕尺寸动态设置高度范围
          const screenRatio = windowHeight / windowWidth;
          let adjustedMinRatio = minHeightRatio;
          let adjustedMaxRatio = maxHeightRatio;
          // 计算最小和最大高度
          const minHeight = Math.max(250, windowHeight * adjustedMinRatio);
          const maxHeight = windowHeight * adjustedMaxRatio;

          // 确保高度在合理范围内
          let finalHeight = availableHeight;

          // 如果计算出的高度太小，使用最小高度
          if (finalHeight < minHeight) {
            finalHeight = minHeight;
          }

          const result = {
            height: Math.round(finalHeight),
            details: {
              设备信息: {
                platform,
                model,
                windowHeight,
                windowWidth,
                screenRatio
              },
              安全区域: {
                safeAreaTop,
                safeAreaBottom,
                systemOccupiedHeight
              },
              高度计算: {
                usedHeight,
                totalPadding,
                availableHeight
              },
              高度范围: {
                minHeight,
                maxHeight,
                finalHeight
              },
              比例设置: {
                adjustedMinRatio,
                adjustedMaxRatio
              }
            }
          };

          // 执行回调
          if (callback && typeof callback === 'function') {
            callback(result.height);
          }

          resolve(result);
        } catch (execError) {
          const fallbackHeight = getFallbackHeight(systemInfo);

          if (callback) {
            callback(fallbackHeight);
          }

          resolve({
            height: fallbackHeight,
            error: execError,
            fallback: true
          });
        }
      });
    } catch (error) {
      const fallbackHeight = getFallbackHeight();

      if (options.callback) {
        options.callback(fallbackHeight);
      }

      reject({
        height: fallbackHeight,
        error,
        fallback: true
      });
    }
  });
};

/**
 * 获取降级高度
 * @param {Object} systemInfo 系统信息
 * @returns {number} 降级高度
 */
const getFallbackHeight = systemInfo => {
  try {
    const info = systemInfo || wx.getSystemInfoSync();
    const fallbackHeight = Math.max(400, info.windowHeight * 0.5);
    return Math.round(fallbackHeight);
  } catch (error) {
    return 400; // 最后的兜底方案
  }
};

/**
 * 响应式高度计算（支持屏幕方向变化）
 * @param {Object} options 配置选项
 * @param {Function} options.onHeightChange 高度变化回调
 * @returns {Object} 包含计算函数和清理函数的对象
 */
const createResponsiveHeightCalculator = (options = {}) => {
  let isCalculating = false;
  let timeoutId = null;

  const calculate = async () => {
    if (isCalculating) return;

    isCalculating = true;

    try {
      const result = await calculateScrollHeight({
        ...options,
        callback: height => {
          if (options.onHeightChange) {
            options.onHeightChange(height);
          }
        }
      });

      return result;
    } finally {
      isCalculating = false;
    }
  };

  const debouncedCalculate = () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      calculate();
    }, 100);
  };

  return {
    calculate,
    debouncedCalculate,
    cleanup: () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    }
  };
};

/**
 * 预设配置
 */
const presets = {
  // 列表页面配置
  list: {
    minHeightRatio: 0.3,
    maxHeightRatio: 0.7,
    extraPadding: 20
  },

  // 详情页面配置
  detail: {
    minHeightRatio: 0.4,
    maxHeightRatio: 0.8,
    extraPadding: 30
  },

  // 表单页面配置
  form: {
    minHeightRatio: 0.25,
    maxHeightRatio: 0.6,
    extraPadding: 40
  },

  // 全屏模式配置
  fullscreen: {
    minHeightRatio: 0.8,
    maxHeightRatio: 0.95,
    extraPadding: 10
  }
};

module.exports = {
  calculateScrollHeight,
  getFallbackHeight,
  createResponsiveHeightCalculator,
  presets
};
