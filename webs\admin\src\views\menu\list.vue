<template>
  <div class="menu-list">
    <CustomTable
      title="菜单管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新建菜单
        </el-button>
      </template>

      <template #date="{ row }">
        {{ formatDate(row.date) }}
      </template>

      <template #isToday="{ row }">
        <el-tag :type="row.isToday ? 'success' : 'info'">
          {{ row.isToday ? '今日菜单' : '历史菜单' }}
        </el-tag>
      </template>

      <template #dishes="{ row }">
        <el-tooltip effect="dark" placement="top">
          <template #content>
            <div v-for="dish in row.dishes" :key="dish.id">
              {{ dish.dish.name }} x{{ dish.count }}
            </div>
          </template>
          <span>{{ row.dishes.length }}道菜</span>
        </el-tooltip>
      </template>

      <template #actions="{ row }">
        <el-button size="small" @click="handleView(row)">查看</el-button>
        <el-button size="small" type="primary" @click="handleEdit(row)"
          >编辑</el-button
        >
        <el-button size="small" type="danger" @click="handleDelete(row)"
          >删除</el-button
        >
      </template>
    </CustomTable>

    <!-- 菜单表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <MenuForm
        ref="menuFormRef"
        :form-data="formData"
        :is-edit="isEdit"
        @submit="handleSubmit"
      />
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import MenuForm from './components/MenuForm.vue'
import { menuApi } from '@/api/menu'
import dayjs from 'dayjs'

export default defineComponent({
  name: 'MenuList',
  components: {
    CustomTable,
    MenuForm,
    Plus
  },
  setup() {
    const loading = ref(false)
    const tableData = ref([])
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const menuFormRef = ref()

    const pagination = reactive({
      page: 1,
      size: 10,
      total: 0
    })

    const searchParams = reactive({
      date: '',
      isToday: ''
    })

    const formData = ref({})

    // 表格列配置
    const columns = [
      { prop: 'id', label: 'ID', width: 80 },
      { prop: 'date', label: '日期', slot: 'date' },
      { prop: 'isToday', label: '类型', slot: 'isToday' },
      { prop: 'dishes', label: '菜品数量', slot: 'dishes' },
      { prop: 'remark', label: '备注', showOverflowTooltip: true }
    ]

    // 搜索字段配置
    const searchFields = [
      {
        prop: 'date',
        label: '日期',
        type: 'date',
        placeholder: '选择日期'
      },
      {
        prop: 'isToday',
        label: '类型',
        type: 'select',
        placeholder: '选择类型',
        options: [
          { label: '今日菜单', value: true },
          { label: '历史菜单', value: false }
        ]
      }
    ]

    // 对话框标题
    const dialogTitle = computed(() => {
      return isEdit.value ? '编辑菜单' : '新建菜单'
    })

    // 加载数据
    const loadData = async () => {
      loading.value = true
      try {
        const params = {
          page: pagination.page,
          size: pagination.size,
          ...searchParams
        }
        const response = await menuApi.getMenus(params)
        if (response.data) {
          tableData.value = response.data.list || []
          pagination.total = response.data.total || 0
        }
      } catch (error) {
        console.error('加载菜单列表失败:', error)
        ElMessage.error('加载数据失败')
      } finally {
        loading.value = false
      }
    }

    // 搜索
    const handleSearch = (params) => {
      Object.assign(searchParams, params)
      pagination.page = 1
      loadData()
    }

    // 重置
    const handleReset = () => {
      Object.keys(searchParams).forEach(key => {
        searchParams[key] = ''
      })
      pagination.page = 1
      loadData()
    }

    // 分页变化
    const handleCurrentChange = (page) => {
      pagination.page = page
      loadData()
    }

    const handleSizeChange = (size) => {
      pagination.size = size
      pagination.page = 1
      loadData()
    }

    // 新建
    const handleCreate = () => {
      isEdit.value = false
      formData.value = {}
      dialogVisible.value = true
    }

    // 查看
    const handleView = (row) => {
      // 跳转到菜单详情页面
      console.log('查看菜单:', row)
    }

    // 编辑
    const handleEdit = (row) => {
      isEdit.value = true
      formData.value = { ...row }
      dialogVisible.value = true
    }

    // 删除
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm('确定要删除这个菜单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await menuApi.deleteMenu(row.id)
        ElMessage.success('删除成功')
        loadData()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除菜单失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    // 提交表单
    const handleSubmit = async (data) => {
      try {
        if (isEdit.value) {
          await menuApi.updateMenu(formData.value.id, data)
          ElMessage.success('更新成功')
        } else {
          await menuApi.createMenu(data)
          ElMessage.success('创建成功')
        }

        dialogVisible.value = false
        loadData()
      } catch (error) {
        console.error('保存菜单失败:', error)
        ElMessage.error('保存失败')
      }
    }

    // 关闭对话框
    const handleDialogClose = () => {
      formData.value = {}
    }

    // 格式化日期
    const formatDate = (date) => {
      return dayjs(date).format('YYYY-MM-DD')
    }

    onMounted(() => {
      loadData()
    })

    return {
      loading,
      tableData,
      columns,
      searchFields,
      pagination,
      dialogVisible,
      dialogTitle,
      isEdit,
      formData,
      menuFormRef,
      handleSearch,
      handleReset,
      handleCurrentChange,
      handleSizeChange,
      handleCreate,
      handleView,
      handleEdit,
      handleDelete,
      handleSubmit,
      handleDialogClose,
      formatDate
    }
  }
})
</script>
