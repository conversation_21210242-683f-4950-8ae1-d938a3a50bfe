<template>
  <Transition
    enter-active-class="transition ease-out duration-300"
    enter-from-class="opacity-0 transform scale-95"
    enter-to-class="opacity-100 transform scale-100"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100 transform scale-100"
    leave-to-class="opacity-0 transform scale-95"
  >
    <div v-if="visible" :class="alertClasses">
      <div class="flex">
        <!-- 图标 -->
        <div class="flex-shrink-0">
          <component
            :is="iconComponent"
            :class="iconClasses"
            aria-hidden="true"
          />
        </div>
        
        <!-- 内容 -->
        <div class="ml-3 flex-1">
          <!-- 标题 -->
          <h3 v-if="title" :class="titleClasses">
            {{ title }}
          </h3>
          
          <!-- 描述 -->
          <div :class="descriptionClasses">
            <slot>
              <p>{{ description }}</p>
            </slot>
          </div>
          
          <!-- 操作按钮 -->
          <div v-if="$slots.actions || showDefaultActions" class="mt-4">
            <slot name="actions">
              <div class="flex space-x-3">
                <BaseButton
                  v-if="confirmText"
                  :variant="confirmVariant"
                  size="sm"
                  @click="handleConfirm"
                >
                  {{ confirmText }}
                </BaseButton>
                <BaseButton
                  v-if="cancelText"
                  variant="ghost"
                  size="sm"
                  @click="handleCancel"
                >
                  {{ cancelText }}
                </BaseButton>
              </div>
            </slot>
          </div>
        </div>
        
        <!-- 关闭按钮 -->
        <div v-if="closable" class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              type="button"
              :class="closeButtonClasses"
              @click="handleClose"
            >
              <span class="sr-only">关闭</span>
              <XMarkIcon class="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
  XMarkIcon
} from '@heroicons/vue/20/solid'
import BaseButton from './BaseButton.vue'

const props = defineProps({
  // 警告类型
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'warning', 'error', 'info'].includes(value)
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 描述
  description: {
    type: String,
    default: ''
  },
  // 是否可关闭
  closable: {
    type: Boolean,
    default: false
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 自定义图标
  icon: {
    type: [Object, Function],
    default: null
  },
  // 是否显示默认操作按钮
  showDefaultActions: {
    type: Boolean,
    default: false
  },
  // 确认按钮文本
  confirmText: {
    type: String,
    default: ''
  },
  // 确认按钮变体
  confirmVariant: {
    type: String,
    default: 'primary'
  },
  // 取消按钮文本
  cancelText: {
    type: String,
    default: ''
  },
  // 自动关闭时间(毫秒)
  duration: {
    type: Number,
    default: 0
  },
  // 是否显示边框
  bordered: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'confirm', 'cancel'])

const visible = ref(true)

// 类型配置
const typeConfig = {
  success: {
    icon: CheckCircleIcon,
    alertClasses: 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
    iconClasses: 'text-green-400',
    titleClasses: 'text-green-800 dark:text-green-200',
    descriptionClasses: 'text-green-700 dark:text-green-300',
    closeButtonClasses: 'text-green-500 hover:bg-green-100 dark:hover:bg-green-800 focus:ring-green-600'
  },
  warning: {
    icon: ExclamationTriangleIcon,
    alertClasses: 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
    iconClasses: 'text-yellow-400',
    titleClasses: 'text-yellow-800 dark:text-yellow-200',
    descriptionClasses: 'text-yellow-700 dark:text-yellow-300',
    closeButtonClasses: 'text-yellow-500 hover:bg-yellow-100 dark:hover:bg-yellow-800 focus:ring-yellow-600'
  },
  error: {
    icon: XCircleIcon,
    alertClasses: 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
    iconClasses: 'text-red-400',
    titleClasses: 'text-red-800 dark:text-red-200',
    descriptionClasses: 'text-red-700 dark:text-red-300',
    closeButtonClasses: 'text-red-500 hover:bg-red-100 dark:hover:bg-red-800 focus:ring-red-600'
  },
  info: {
    icon: InformationCircleIcon,
    alertClasses: 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
    iconClasses: 'text-blue-400',
    titleClasses: 'text-blue-800 dark:text-blue-200',
    descriptionClasses: 'text-blue-700 dark:text-blue-300',
    closeButtonClasses: 'text-blue-500 hover:bg-blue-100 dark:hover:bg-blue-800 focus:ring-blue-600'
  }
}

const config = computed(() => typeConfig[props.type])

const alertClasses = computed(() => {
  const baseClasses = ['rounded-lg p-4']
  
  if (props.bordered) {
    baseClasses.push('border')
  }
  
  return [
    ...baseClasses,
    config.value.alertClasses
  ].join(' ')
})

const iconComponent = computed(() => {
  return props.icon || config.value.icon
})

const iconClasses = computed(() => {
  return ['h-5 w-5', config.value.iconClasses].join(' ')
})

const titleClasses = computed(() => {
  return ['text-sm font-medium', config.value.titleClasses].join(' ')
})

const descriptionClasses = computed(() => {
  const classes = ['text-sm']
  
  if (props.title) {
    classes.push('mt-1')
  }
  
  return [
    ...classes,
    config.value.descriptionClasses
  ].join(' ')
})

const closeButtonClasses = computed(() => {
  return [
    'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
    config.value.closeButtonClasses
  ].join(' ')
})

const handleClose = () => {
  visible.value = false
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
}

// 自动关闭
onMounted(() => {
  if (props.duration > 0) {
    setTimeout(() => {
      handleClose()
    }, props.duration)
  }
})
</script>
