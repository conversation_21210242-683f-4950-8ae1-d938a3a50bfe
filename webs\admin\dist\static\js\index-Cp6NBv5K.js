var M=(h,e,u)=>new Promise((s,m)=>{var w=t=>{try{n(u.next(t))}catch(f){m(f)}},c=t=>{try{n(u.throw(t))}catch(f){m(f)}},n=t=>t.done?s(t.value):Promise.resolve(t.value).then(w,c);n((u=u.apply(h,e)).next())});import{_ as P,r as b,c as k,a,b as I,d as l,w as d,e as D,u as V,f as T,g as x,h as C,o as F,E as p,n as y,i as B,j as z,k as K,l as N,m as E,p as j,t as q}from"./index-XtNpSMFt.js";const H={__name:"index",setup(h,{expose:e}){e();const u=V(),s=T(),m=x(),w=x(!1),c=x(!1),n=C({username:"",password:""}),t=(i,r,o)=>{if(!r){o(new Error("请输入用户名/手机号/邮箱"));return}const g=/^1[3-9]\d{9}$/,v=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,O=/^[a-zA-Z0-9_]{3,20}$/;g.test(r)||v.test(r)||O.test(r)?o():o(new Error("请输入正确的用户名、手机号或邮箱"))},f=(i,r,o)=>{if(!r){o(new Error("请输入密码"));return}if(r.length<6){o(new Error("密码长度不能少于6位"));return}if(r.length>20){o(new Error("密码长度不能超过20位"));return}o()},S={username:[{validator:t,trigger:"blur"}],password:[{validator:f,trigger:"blur"}]},R=()=>M(this,null,function*(){if(m.value)try{if(!(yield m.value.validate()))return;w.value=!0,p.info("正在验证登录信息...");const r=yield s.login({username:n.username.trim(),password:n.password,loginType:"password"});if(r.success){p.success("登录成功！正在跳转..."),c.value?(localStorage.setItem("remembered_username",n.username.trim()),localStorage.setItem("remember_me","true")):(localStorage.removeItem("remembered_username"),localStorage.removeItem("remember_me")),yield y();const o=sessionStorage.getItem("login_redirect"),g=o||"/";o&&sessionStorage.removeItem("login_redirect"),setTimeout(()=>{u.push(g)},500)}else{const o=_(r.message,r.code);p.error(o)}}catch(i){console.error("登录错误:",i);let r="登录失败，请重试";if(i.response){const o=i.response.status;o===401?r="用户名或密码错误":o===403?r="账户已被禁用，请联系管理员":o===429?r="登录尝试次数过多，请稍后再试":o>=500&&(r="服务器错误，请稍后再试")}else i.code==="NETWORK_ERROR"&&(r="网络连接失败，请检查网络设置");p.error(r)}finally{w.value=!1}}),_=(i,r)=>({INVALID_CREDENTIALS:"用户名或密码错误",ACCOUNT_DISABLED:"账户已被禁用，请联系管理员",ACCOUNT_LOCKED:"账户已被锁定，请稍后再试",TOO_MANY_ATTEMPTS:"登录尝试次数过多，请稍后再试",USER_NOT_FOUND:"用户不存在",PASSWORD_EXPIRED:"密码已过期，请重置密码"})[r]||i||"登录失败，请重试",A=()=>{u.push("/forgot-password")},L=()=>{u.push("/register")};F(()=>{if(s.isLoggedIn){p.info("您已登录，正在跳转..."),u.push("/");return}const i=new URLSearchParams(window.location.search),r=i.get("username");if(r)n.username=r,p.success("注册成功！请输入密码登录");else{const g=localStorage.getItem("remembered_username"),v=localStorage.getItem("remember_me");g&&v==="true"&&(n.username=g,c.value=!0)}y(()=>{if(m.value){const g=m.value.$el.querySelector('input[placeholder*="用户名"]');if(g&&!n.username)g.focus();else if(n.username){const v=m.value.$el.querySelector('input[type="password"]');v&&v.focus()}}});const o=i.get("redirect");o&&sessionStorage.setItem("login_redirect",o)});const U={router:u,userStore:s,loginFormRef:m,loading:w,rememberMe:c,loginForm:n,validateUsername:t,validatePassword:f,loginRules:S,handleLogin:R,getLoginErrorMessage:_,handleForgetPassword:A,handleRegister:L,reactive:C,ref:x,onMounted:F,nextTick:y,get useRouter(){return V},get ElMessage(){return p},get ElMessageBox(){return K},get User(){return z},get House(){return B},get useUserStore(){return T}};return Object.defineProperty(U,"__isScriptSetup",{enumerable:!1,value:!0}),U}},W={class:"login-container"},X={class:"login-box"},Y={class:"login-header"},Z={class:"logo"},G={class:"login-options flex justify-between"},J={class:"register-link"};function Q(h,e,u,s,m,w){const c=b("el-icon"),n=b("el-input"),t=b("el-form-item"),f=b("el-checkbox"),S=b("el-button"),R=b("el-form");return N(),k("div",W,[e[10]||(e[10]=a("div",{class:"login-background"},[a("div",{class:"bg-shape shape-1"}),a("div",{class:"bg-shape shape-2"}),a("div",{class:"bg-shape shape-3"})],-1)),a("div",X,[a("div",Y,[a("div",Z,[l(c,{size:"48",color:"#409eff"},{default:d(()=>[l(s.House)]),_:1})]),e[3]||(e[3]=a("h1",null,"楠楠家厨管理系统",-1)),e[4]||(e[4]=a("p",null,"欢迎登录后台管理系统",-1))]),I(" 账号密码登录表单 "),I(" 注意：微信扫码登录暂时禁用，待后端API实现后再启用 "),l(R,{ref:"loginFormRef",model:s.loginForm,rules:s.loginRules,class:"login-form",onKeyup:D(s.handleLogin,["enter"])},{default:d(()=>[l(t,{prop:"username"},{default:d(()=>[l(n,{modelValue:s.loginForm.username,"onUpdate:modelValue":e[0]||(e[0]=_=>s.loginForm.username=_),placeholder:"请输入用户名/手机号",size:"large","prefix-icon":"User",clearable:""},null,8,["modelValue"])]),_:1}),l(t,{prop:"password"},{default:d(()=>[l(n,{modelValue:s.loginForm.password,"onUpdate:modelValue":e[1]||(e[1]=_=>s.loginForm.password=_),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",clearable:""},null,8,["modelValue"])]),_:1}),l(t,null,{default:d(()=>[a("div",G,[l(f,{modelValue:s.rememberMe,"onUpdate:modelValue":e[2]||(e[2]=_=>s.rememberMe=_)},{default:d(()=>e[5]||(e[5]=[E("记住我",-1)])),_:1,__:[5]},8,["modelValue"]),l(S,{type:"text",class:"forgot-password-btn",onClick:s.handleForgetPassword},{default:d(()=>e[6]||(e[6]=[E(" 忘记密码？ ",-1)])),_:1,__:[6]})])]),_:1}),l(t,null,{default:d(()=>[l(S,{type:"primary",size:"large",loading:s.loading,onClick:s.handleLogin,class:"login-btn"},{default:d(()=>[s.loading?I("v-if",!0):(N(),j(c,{key:0},{default:d(()=>[l(s.User)]),_:1})),E(" "+q(s.loading?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1}),l(t,null,{default:d(()=>[a("div",J,[e[8]||(e[8]=a("span",null,"还没有账户？",-1)),l(S,{type:"text",class:"register-btn",onClick:s.handleRegister},{default:d(()=>e[7]||(e[7]=[E(" 立即注册 ",-1)])),_:1,__:[7]})])]),_:1})]),_:1},8,["model"]),e[9]||(e[9]=a("div",{class:"login-footer"},[a("p",null,"© 2024 楠楠家厨管理系统. All rights reserved.")],-1))])])}const re=P(H,[["render",Q],["__scopeId","data-v-2bf2fc29"],["__file","E:/wx-nan/webs/admin/src/views/login/index.vue"]]);export{re as default};
