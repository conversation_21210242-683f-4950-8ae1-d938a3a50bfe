import {
  BaseInput,
  BaseSelect,
  BaseButton,
  BaseTable,
  BaseModal,
  BaseTextarea,
  BaseDatePicker,
  BaseDateRangePicker,
  BaseUpload,
  StatsCard,
  SearchForm,
  PageContainer,
  PageHeader,
  Breadcrumb,
  Pagination,
  EmptyState,
  LoadingSpinner,
  BaseAlert,
  BaseToast,
  BaseConfirm
} from '@/components/ui'

// 全局组件列表
const components = {
  BaseInput,
  BaseSelect,
  BaseButton,
  BaseTable,
  BaseModal,
  BaseTextarea,
  BaseDatePicker,
  BaseDateRangePicker,
  BaseUpload,
  StatsCard,
  SearchForm,
  PageContainer,
  PageHeader,
  Breadcrumb,
  Pagination,
  EmptyState,
  LoadingSpinner,
  BaseAlert,
  BaseToast,
  BaseConfirm
}

// 安装插件
export default {
  install(app) {
    // 注册所有组件为全局组件
    Object.keys(components).forEach(name => {
      app.component(name, components[name])
    })
  }
}

// 按需导出
export {
  BaseInput,
  BaseSelect,
  BaseButton,
  BaseTable,
  BaseModal,
  BaseTextarea,
  BaseDatePicker,
  BaseDateRangePicker,
  BaseUpload,
  StatsCard,
  SearchForm,
  PageContainer,
  PageHeader,
  Breadcrumb,
  Pagination,
  EmptyState,
  LoadingSpinner,
  BaseAlert,
  BaseToast,
  BaseConfirm
}
