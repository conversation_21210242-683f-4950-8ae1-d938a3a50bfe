# 通用下拉刷新和上拉加载更多组件

## 功能特点

- 🔄 **原生下拉刷新** - 使用小程序原生的 refresher 功能
- ✨ **顶部Loading效果** - 下拉刷新时显示顶部loading指示器
- 📜 **上拉加载更多** - 支持点击和滚动触发
- 🎨 **插槽支持** - 通过插槽自定义内容和空状态
- 🎯 **高度自适应** - 支持动态设置容器高度
- 🎨 **样式可配置** - 支持自定义颜色、文字等
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🚫 **无干扰体验** - 刷新完成后无弹窗提示

## 使用方法

### 1. 在页面配置中引入组件

```json
{
  "usingComponents": {
    "refresh-scroll": "../../components/refresh-scroll/index"
  }
}
```

### 2. 在页面中使用组件

```xml
<refresh-scroll
  id="refresh-scroll"
  container-height="{{scrollViewHeight}}"
  has-more="{{hasMore}}"
  loading-more="{{loadingMore}}"
  is-empty="{{list.length === 0}}"
  empty-icon="warning-o"
  empty-text="暂无数据"
  empty-tip="下拉刷新试试"
  bind:refresh="onRefresh"
  bind:loadmore="onLoadMore"
>
  <!-- 内容插槽 -->
  <view slot="content" wx:if="{{list.length > 0}}">
    <view wx:for="{{list}}" wx:key="id" class="item">
      {{item.title}}
    </view>
  </view>
  
  <!-- 自定义空状态插槽（可选） -->
  <view slot="empty" wx:if="{{list.length === 0}}">
    <view class="custom-empty">
      <image src="/images/empty.png" class="empty-image" />
      <text class="empty-text">自定义空状态</text>
    </view>
  </view>
</refresh-scroll>
```

### 3. 在页面JS中处理事件

```javascript
Page({
  data: {
    list: [],
    hasMore: true,
    loadingMore: false,
    scrollViewHeight: 400
  },

  onLoad() {
    this.calculateHeight();
    this.loadData();
  },

  // 计算高度
  calculateHeight() {
    const systemInfo = wx.getSystemInfoSync();
    const height = systemInfo.windowHeight - 200; // 减去其他元素高度
    this.setData({ scrollViewHeight: height });
  },

  // 下拉刷新
  async onRefresh() {
    try {
      const data = await this.fetchData(1);
      this.setData({ list: data });
      
      // 停止刷新动画
      setTimeout(() => {
        this.selectComponent('#refresh-scroll').stopRefresh();
      }, 500);
    } catch (error) {
      console.error('刷新失败:', error);
      this.selectComponent('#refresh-scroll').stopRefresh();
    }
  },

  // 上拉加载更多
  async onLoadMore() {
    if (!this.data.hasMore || this.data.loadingMore) return;
    
    this.setData({ loadingMore: true });
    
    try {
      const newData = await this.fetchData(this.data.page + 1);
      this.setData({
        list: [...this.data.list, ...newData],
        hasMore: newData.length >= 10,
        loadingMore: false
      });
    } catch (error) {
      console.error('加载更多失败:', error);
      this.setData({ loadingMore: false });
    }
  }
});
```

## 使用 Behavior 简化开发

### 1. 引入 Behavior

```javascript
const refreshScrollBehavior = require('../../behaviors/refresh-scroll-behavior.js');

Page({
  behaviors: [
    refreshScrollBehavior({
      listName: 'messages',           // 列表数据字段名
      requestFunc: api.getMessages,   // 请求函数
      pageSize: 10,                   // 每页数据量
      autoLoad: true,                 // 是否自动加载
      transformData: (response) => {  // 数据转换函数
        return {
          list: response.data.list,
          hasMore: response.data.hasMore
        };
      },
      onRefreshComplete: (list) => {  // 刷新完成回调
        console.log('刷新完成，共', list.length, '条数据');
      }
    })
  ],

  // 页面其他逻辑...
});
```

### 2. 在模板中使用

```xml
<refresh-scroll
  id="refresh-scroll"
  container-height="{{scrollViewHeight}}"
  has-more="{{messagesHasMore}}"
  loading-more="{{messagesLoading}}"
  is-empty="{{messagesIsEmpty}}"
  bind:refresh="onMessagesRefresh"
  bind:loadmore="onMessagesLoadMore"
>
  <view slot="content" wx:if="{{messages.length > 0}}">
    <view wx:for="{{messages}}" wx:key="id" class="message-item">
      {{item.content}}
    </view>
  </view>
</refresh-scroll>
```

## 属性配置

| 属性名               | 类型    | 默认值           | 说明                 |
| -------------------- | ------- | ---------------- | -------------------- |
| container-height     | Number  | 600              | 容器高度（px）       |
| refresher-enabled    | Boolean | true             | 是否启用下拉刷新     |
| refresher-background | String  | '#f7f7f7'        | 下拉刷新背景色       |
| refresher-style      | String  | 'white'          | 下拉刷新样式         |
| refresher-threshold  | Number  | 25               | 下拉刷新阈值         |
| has-more             | Boolean | true             | 是否还有更多数据     |
| loading-more         | Boolean | false            | 是否正在加载更多     |
| show-load-more       | Boolean | true             | 是否显示加载更多区域 |
| load-more-text       | String  | '点击加载更多'   | 加载更多文字         |
| loading-text         | String  | '加载中...'      | 加载中文字           |
| no-more-text         | String  | '没有更多数据了' | 没有更多数据文字     |
| loading-color        | String  | '#6366F1'        | 加载动画颜色         |
| loading-size         | String  | '20px'           | 加载动画大小         |
| show-scrollbar       | Boolean | false            | 是否显示滚动条       |
| is-empty             | Boolean | false            | 是否为空状态         |
| empty-icon           | String  | 'warning-o'      | 空状态图标           |
| empty-text           | String  | '暂无数据'       | 空状态文字           |
| empty-tip            | String  | '下拉刷新试试'   | 空状态提示           |

## 事件

| 事件名   | 说明             | 参数 |
| -------- | ---------------- | ---- |
| refresh  | 下拉刷新触发     | -    |
| loadmore | 上拉加载更多触发 | -    |

## 方法

| 方法名      | 说明             | 参数 |
| ----------- | ---------------- | ---- |
| stopRefresh | 停止下拉刷新动画 | -    |
| scrollToTop | 滚动到顶部       | -    |
| resetScroll | 重置滚动状态     | -    |

## 插槽

| 插槽名  | 说明                                       |
| ------- | ------------------------------------------ |
| content | 主要内容区域                               |
| empty   | 空状态内容（可选，不使用则显示默认空状态） |

## 最佳实践

1. **高度计算**：建议动态计算容器高度，确保页面布局合理
2. **错误处理**：在刷新和加载更多时要处理错误情况
3. **状态管理**：正确管理 hasMore 和 loadingMore 状态
4. **用户体验**：适当的加载提示和错误提示
5. **性能优化**：大列表时考虑虚拟滚动或分页策略
