var G=Object.defineProperty;var L=Object.getOwnPropertySymbols;var K=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var R=(y,e,a)=>e in y?G(y,e,{enumerable:!0,configurable:!0,writable:!0,value:a}):y[e]=a,Y=(y,e)=>{for(var a in e||(e={}))K.call(e,a)&&R(y,a,e[a]);if(L)for(var a of L(e))X.call(e,a)&&R(y,a,e[a]);return y};var z=(y,e,a)=>new Promise((t,x)=>{var C=s=>{try{h(a.next(s))}catch(w){x(w)}},m=s=>{try{h(a.throw(s))}catch(w){x(w)}},h=s=>s.done?t(s.value):Promise.resolve(s.value).then(C,m);h((a=a.apply(y,e)).next())});import{_ as W,r as u,c as E,a as c,d as r,t as p,w as n,m as d,b as S,p as A,g as D,q as F,T as Z,k as P,E as v,l as T,h as I,o as H,R as $,O as ee,P as te,Q as ne,U as oe,H as ae,I as re}from"./index-Cy8N1eGd.js";import{C as le}from"./CustomTable-DWghEWMD.js";import{o as M}from"./order-D36AVmfp.js";import{f as N}from"./common-CIDIMsc8.js";const se={__name:"OrderDetail",props:{order:{type:Object,required:!0}},emits:["close","statusChanged"],setup(y,{expose:e,emit:a}){e();const t=y,x=a,C=D(!1),m=F(()=>{try{return JSON.parse(t.order.items||"[]")}catch(f){return[]}}),h=f=>({pending:"warning",completed:"success",cancelled:"danger"})[f]||"info",s=f=>({pending:"待处理",completed:"已完成",cancelled:"已取消"})[f]||"未知",k={props:t,emit:x,statusLoading:C,orderItems:m,getStatusType:h,getStatusText:s,handleStatusChange:f=>z(this,null,function*(){try{const o=s(f);yield P.confirm(`确定要将订单状态改为"${o}"吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),C.value=!0,yield M.updateOrderStatus(t.order.id,f),v.success("状态更新成功"),x("statusChanged",f)}catch(o){o!=="cancel"&&(console.error("更新订单状态失败:",o),v.error("状态更新失败"))}finally{C.value=!1}}),handleClose:()=>{x("close")},handlePrint:()=>{var g,i;const f=`
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="text-align: center; margin-bottom: 20px;">订单详情</h2>
      <div style="margin-bottom: 15px;">
        <strong>订单号：</strong>${t.order.id}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>用户：</strong>${((g=t.order.user)==null?void 0:g.name)||"未知用户"}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>手机号：</strong>${((i=t.order.user)==null?void 0:i.phone)||"未提供"}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>用餐时间：</strong>${N(t.order.mealTime)}
      </div>
      <div style="margin-bottom: 15px;">
        <strong>下单时间：</strong>${N(t.order.createdAt)}
      </div>
      <div style="margin-bottom: 20px;">
        <strong>状态：</strong>${s(t.order.status)}
      </div>
      
      <h3>菜品清单：</h3>
      <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
        <thead>
          <tr style="background-color: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">菜品名称</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">数量</th>
            <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">备注</th>
          </tr>
        </thead>
        <tbody>
          ${m.value.map(V=>`
            <tr>
              <td style="border: 1px solid #ddd; padding: 8px;">${V.dishName}</td>
              <td style="border: 1px solid #ddd; padding: 8px; text-align: center;">${V.count}</td>
              <td style="border: 1px solid #ddd; padding: 8px;">${V.remark||"无"}</td>
            </tr>
          `).join("")}
        </tbody>
      </table>
      
      ${t.order.remark?`
        <div style="margin-bottom: 20px;">
          <strong>订单备注：</strong>${t.order.remark}
        </div>
      `:""}
      
      <div style="text-align: center; margin-top: 30px; color: #666;">
        打印时间：${N(new Date)}
      </div>
    </div>
  `,o=window.open("","_blank");o.document.write(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>订单详情 - ${t.order.id}</title>
      <style>
        @media print {
          body { margin: 0; }
          @page { margin: 1cm; }
        }
      </style>
    </head>
    <body>
      ${f}
    </body>
    </html>
  `),o.document.close(),o.print(),o.close(),v.success("打印任务已发送")},ref:D,computed:F,get ElMessage(){return v},get ElMessageBox(){return P},get Printer(){return Z},get orderApi(){return M},get formatTime(){return N}};return Object.defineProperty(k,"__isScriptSetup",{enumerable:!1,value:!0}),k}},de={class:"order-detail"},ie={class:"detail-header"},ce={class:"order-info"},me={class:"order-meta"},ue={class:"order-id"},pe={class:"detail-content"},ge={class:"info-section"},_e={class:"info-section"},fe={class:"info-section"},he={key:0,class:"info-section"},be={class:"remark-content"},ye={class:"info-section"},ve={class:"status-actions"},xe={class:"detail-footer"};function we(y,e,a,t,x,C){const m=u("el-tag"),h=u("el-divider"),s=u("el-descriptions-item"),w=u("el-descriptions"),_=u("el-col"),O=u("el-row"),k=u("el-table-column"),f=u("el-table"),o=u("el-button"),g=u("el-icon");return T(),E("div",de,[c("div",ie,[c("div",ce,[e[4]||(e[4]=c("h3",null,"订单详情",-1)),c("div",me,[c("span",ue,"订单号："+p(a.order.id),1),r(m,{type:t.getStatusType(a.order.status),size:"large"},{default:n(()=>[d(p(t.getStatusText(a.order.status)),1)]),_:1},8,["type"])])])]),r(h),c("div",pe,[r(O,{gutter:20},{default:n(()=>[r(_,{span:12},{default:n(()=>[c("div",ge,[e[5]||(e[5]=c("h4",null,"用户信息",-1)),r(w,{column:1,border:"",size:"small"},{default:n(()=>[r(s,{label:"姓名"},{default:n(()=>{var i;return[d(p(((i=a.order.user)==null?void 0:i.name)||"未知用户"),1)]}),_:1}),r(s,{label:"手机号"},{default:n(()=>{var i;return[d(p(((i=a.order.user)==null?void 0:i.phone)||"未提供"),1)]}),_:1}),r(s,{label:"微信昵称"},{default:n(()=>{var i;return[d(p(((i=a.order.user)==null?void 0:i.nickname)||"未提供"),1)]}),_:1})]),_:1})])]),_:1}),r(_,{span:12},{default:n(()=>[c("div",_e,[e[6]||(e[6]=c("h4",null,"订单信息",-1)),r(w,{column:1,border:"",size:"small"},{default:n(()=>[r(s,{label:"用餐时间"},{default:n(()=>[d(p(t.formatTime(a.order.mealTime)),1)]),_:1}),r(s,{label:"下单时间"},{default:n(()=>[d(p(t.formatTime(a.order.createdAt)),1)]),_:1}),r(s,{label:"更新时间"},{default:n(()=>[d(p(t.formatTime(a.order.updatedAt)),1)]),_:1})]),_:1})])]),_:1})]),_:1}),c("div",fe,[e[7]||(e[7]=c("h4",null,"菜品清单",-1)),r(f,{data:t.orderItems,border:"",size:"small"},{default:n(()=>[r(k,{prop:"dishName",label:"菜品名称"}),r(k,{prop:"count",label:"数量",width:"80",align:"center"}),r(k,{label:"备注",prop:"remark","show-overflow-tooltip":""},{default:n(({row:i})=>[d(p(i.remark||"无"),1)]),_:1})]),_:1},8,["data"])]),a.order.remark?(T(),E("div",he,[e[8]||(e[8]=c("h4",null,"订单备注",-1)),c("div",be,p(a.order.remark),1)])):S("v-if",!0),c("div",ye,[e[13]||(e[13]=c("h4",null,"状态操作",-1)),c("div",ve,[a.order.status==="pending"?(T(),A(o,{key:0,type:"success",onClick:e[0]||(e[0]=i=>t.handleStatusChange("completed")),loading:t.statusLoading},{default:n(()=>e[9]||(e[9]=[d(" 标记为已完成 ",-1)])),_:1,__:[9]},8,["loading"])):S("v-if",!0),a.order.status==="pending"?(T(),A(o,{key:1,type:"danger",onClick:e[1]||(e[1]=i=>t.handleStatusChange("cancelled")),loading:t.statusLoading},{default:n(()=>e[10]||(e[10]=[d(" 取消订单 ",-1)])),_:1,__:[10]},8,["loading"])):S("v-if",!0),a.order.status==="cancelled"?(T(),A(o,{key:2,type:"primary",onClick:e[2]||(e[2]=i=>t.handleStatusChange("pending")),loading:t.statusLoading},{default:n(()=>e[11]||(e[11]=[d(" 恢复订单 ",-1)])),_:1,__:[11]},8,["loading"])):S("v-if",!0),a.order.status==="completed"?(T(),A(o,{key:3,type:"warning",onClick:e[3]||(e[3]=i=>t.handleStatusChange("pending")),loading:t.statusLoading},{default:n(()=>e[12]||(e[12]=[d(" 重新处理 ",-1)])),_:1,__:[12]},8,["loading"])):S("v-if",!0)])])]),c("div",xe,[r(o,{onClick:t.handleClose},{default:n(()=>e[14]||(e[14]=[d("关闭",-1)])),_:1,__:[14]}),r(o,{type:"primary",onClick:t.handlePrint},{default:n(()=>[r(g,null,{default:n(()=>[r(t.Printer)]),_:1}),e[15]||(e[15]=d(" 打印订单 ",-1))]),_:1,__:[15]})])])}const Ce=W(se,[["render",we],["__scopeId","data-v-617da033"],["__file","E:/wx-nan/webs/admin/src/views/order/components/OrderDetail.vue"]]),ke={__name:"list",setup(y,{expose:e}){e();const a=D(!1),t=D([]),x=D(!1),C=D({}),m=I({page:1,size:10,total:0}),h=I({status:"",userName:"",startDate:"",endDate:""}),s=[{prop:"id",label:"订单号",width:100},{prop:"userName",label:"用户信息",slot:"userName",minWidth:120},{prop:"items",label:"菜品",slot:"items",width:100},{prop:"status",label:"状态",slot:"status",width:100},{prop:"mealTime",label:"用餐时间",slot:"mealTime",width:150},{prop:"remark",label:"备注",showOverflowTooltip:!0,minWidth:120},{prop:"createdAt",label:"下单时间",slot:"createdAt",width:150}],w=[{prop:"status",label:"状态",type:"select",placeholder:"选择状态",options:[{label:"待处理",value:"pending"},{label:"已完成",value:"completed"},{label:"已取消",value:"cancelled"}]},{prop:"userName",label:"用户名",type:"input",placeholder:"请输入用户名"},{prop:"startDate",label:"开始日期",type:"date",placeholder:"选择开始日期"},{prop:"endDate",label:"结束日期",type:"date",placeholder:"选择结束日期"}],_=()=>z(this,null,function*(){a.value=!0;try{const l=Y({page:m.page,size:m.size},h),b=yield M.getOrders(l);b.code===200?(t.value=b.data.list||[],m.total=b.data.total||0):v.error(b.message||"加载数据失败")}catch(l){console.error("加载订单列表失败:",l),v.error("加载数据失败")}finally{a.value=!1}}),O=l=>{Object.assign(h,l),m.page=1,_()},k=()=>{Object.keys(h).forEach(l=>{h[l]=""}),m.page=1,_()},f=l=>{m.page=l,_()},o=l=>{m.size=l,m.page=1,_()},g=l=>{C.value=l,x.value=!0},i=(l,b)=>z(this,null,function*(){try{const B=yield M.updateOrderStatus(l.id,b);B.code===200?(v.success("状态更新成功"),_()):v.error(B.message||"状态更新失败")}catch(B){console.error("更新订单状态失败:",B),v.error("状态更新失败")}}),V=l=>z(this,null,function*(){try{yield P.confirm("确定要删除这个订单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const b=yield M.deleteOrder(l.id);b.code===200?(v.success("删除成功"),_()):v.error(b.message||"删除失败")}catch(b){b!=="cancel"&&(console.error("删除订单失败:",b),v.error("删除失败"))}}),q=l=>{try{return JSON.parse(l||"[]")}catch(b){return[]}},J=l=>({pending:"warning",completed:"success",cancelled:"danger"})[l]||"info",U=l=>({pending:"待处理",completed:"已完成",cancelled:"已取消"})[l]||"未知",Q=l=>N(l,"YYYY-MM-DD HH:mm");H(()=>{_()});const j={loading:a,tableData:t,detailVisible:x,selectedOrder:C,pagination:m,searchParams:h,columns:s,searchFields:w,loadData:_,handleSearch:O,handleReset:k,handleCurrentChange:f,handleSizeChange:o,handleView:g,handleStatusChange:i,handleDelete:V,getOrderItems:q,getStatusType:J,getStatusText:U,formatDateTime:Q,ref:D,reactive:I,onMounted:H,get ElMessage(){return v},get ElMessageBox(){return P},get ArrowDown(){return oe},get View(){return ne},get Edit(){return te},get Delete(){return ee},get Refresh(){return $},CustomTable:le,OrderDetail:Ce,get orderApi(){return M},get formatTime(){return N}};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}},Te={class:"order-list"},Se={class:"user-info"};function De(y,e,a,t,x,C){const m=u("el-tooltip"),h=u("el-tag"),s=u("el-button"),w=u("el-icon"),_=u("el-dropdown-item"),O=u("el-dropdown-menu"),k=u("el-dropdown"),f=u("el-dialog");return T(),E("div",Te,[r(t.CustomTable,{title:"订单管理",data:t.tableData,columns:t.columns,loading:t.loading,pagination:t.pagination,"show-search":!0,"search-fields":t.searchFields,onSearch:t.handleSearch,onReset:t.handleReset,onCurrentChange:t.handleCurrentChange,onSizeChange:t.handleSizeChange},{userName:n(({row:o})=>{var g,i;return[c("div",Se,[c("span",null,p(((g=o.user)==null?void 0:g.name)||"未知用户"),1),c("small",null,p((i=o.user)==null?void 0:i.phone),1)])]}),items:n(({row:o})=>[r(m,{effect:"dark",placement:"top"},{content:n(()=>[(T(!0),E(ae,null,re(t.getOrderItems(o.items),g=>(T(),E("div",{key:g.dishName},p(g.dishName)+" x"+p(g.count),1))),128))]),default:n(()=>[c("span",null,p(t.getOrderItems(o.items).length)+"道菜",1)]),_:2},1024)]),status:n(({row:o})=>[r(h,{type:t.getStatusType(o.status)},{default:n(()=>[d(p(t.getStatusText(o.status)),1)]),_:2},1032,["type"])]),mealTime:n(({row:o})=>[d(p(t.formatDateTime(o.mealTime)),1)]),createdAt:n(({row:o})=>[d(p(t.formatDateTime(o.createdAt)),1)]),actions:n(({row:o})=>[r(s,{size:"small",onClick:g=>t.handleView(o)},{default:n(()=>e[2]||(e[2]=[d("查看",-1)])),_:2,__:[2]},1032,["onClick"]),r(k,{onCommand:g=>t.handleStatusChange(o,g)},{dropdown:n(()=>[r(O,null,{default:n(()=>[r(_,{command:"pending"},{default:n(()=>e[4]||(e[4]=[d("待处理",-1)])),_:1,__:[4]}),r(_,{command:"completed"},{default:n(()=>e[5]||(e[5]=[d("已完成",-1)])),_:1,__:[5]}),r(_,{command:"cancelled"},{default:n(()=>e[6]||(e[6]=[d("已取消",-1)])),_:1,__:[6]})]),_:1})]),default:n(()=>[r(s,{size:"small",type:"primary"},{default:n(()=>[e[3]||(e[3]=d(" 更新状态",-1)),r(w,{class:"el-icon--right"},{default:n(()=>[r(t.ArrowDown)]),_:1})]),_:1,__:[3]})]),_:2},1032,["onCommand"]),r(s,{size:"small",type:"danger",onClick:g=>t.handleDelete(o)},{default:n(()=>e[7]||(e[7]=[d("删除",-1)])),_:2,__:[7]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),S(" 订单详情对话框 "),r(f,{modelValue:t.detailVisible,"onUpdate:modelValue":e[1]||(e[1]=o=>t.detailVisible=o),title:"订单详情",width:"600px"},{default:n(()=>[t.detailVisible?(T(),A(t.OrderDetail,{key:0,order:t.selectedOrder,onClose:e[0]||(e[0]=o=>t.detailVisible=!1)},null,8,["order"])):S("v-if",!0)]),_:1},8,["modelValue"])])}const Ae=W(ke,[["render",De],["__scopeId","data-v-db568ccc"],["__file","E:/wx-nan/webs/admin/src/views/order/list.vue"]]);export{Ae as default};
