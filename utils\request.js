/**
 * HTTP 请求工具类
 * 封装微信小程序的网络请求 API
 */

// 导入错误处理器
const errorHandler = require('./errorHandler');

// 导入统一的环境配置
const {baseURL} = require('../config/env');

/**
 * 发送 HTTP 请求
 * @param {Object} options - 请求选项
 * @returns {Promise} - 返回 Promise 对象
 */
function request(options) {
  // HTTP 请求
  return new Promise((resolve, reject) => {
    const {url, method = 'GET', data = {}, header = {}} = options;

    // 添加认证信息
    let token = wx.getStorageSync('accessToken') || wx.getStorageSync('token');
    const headers = {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': 'true', // 跳过ngrok警告页面
      ...header
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    wx.request({
      url: `${baseURL}${url}`,
      method,
      data,
      header: headers,
      success: res => {
        // 请求成功
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          // 构建错误对象
          const error = {
            code: res.statusCode,
            message: res.data?.message || '请求失败',
            data: res.data
          };

          // 使用统一的错误处理器
          errorHandler.handleApiError(error, {
            showToast: false // 在请求层不显示toast，由业务层决定
          });

          // 抛出错误供业务层处理
          reject(error);
        }
      },
      fail: err => {
        // 网络错误
        const error = {
          code: -1,
          message: err.errMsg || '网络错误',
          data: null
        };
        reject(error);
      }
    });
  });
}

/**
 * GET 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求参数
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function get(url, data = {}, header = {}) {
  return request({
    url,
    method: 'GET',
    data,
    header
  });
}

/**
 * POST 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function post(url, data = {}, header = {}) {
  return request({
    url,
    method: 'POST',
    data,
    header
  });
}

/**
 * PUT 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function put(url, data = {}, header = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    header
  });
}

/**
 * DELETE 请求
 * @param {string} url - 请求 URL
 * @param {Object} data - 请求数据
 * @param {Object} header - 请求头
 * @returns {Promise} - 返回 Promise 对象
 */
function del(url, data = {}, header = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    header
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  del
};
