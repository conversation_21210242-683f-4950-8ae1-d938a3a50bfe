/* 我的页面 - 小程序兼容设计 */
@import "../../styles/miniprogram-design.scss";

.container {
	@include page-container;
	@include page-container-safe;
}

.mine-user-card {
	@include modern-card;
	@include card-primary;
	@include flex;
	@include items-start;
	@include gap-3;
	@include mb-4;
	padding: 40rpx 32rpx;
}

.mine-user-avatar {
	width: 80rpx;
	height: 80rpx;
	@include rounded-full;
	@include shadow-sm;
	flex-shrink: 0;
	display: flex;
	align-items: center;
	justify-content: center;

	image {
		width: 100%;
		height: 100%;
		@include rounded-full;
	}
}

.mine-user-info {
	@include flex-1;
	@include flex;
	@include flex-col;
	@include gap-2;
}

.mine-user-name {
	@include text-xl;
	@include font-bold;
	@include text-gray-900;
}

.mine-user-phone,
.mine-user-role,
.mine-user-id {
	@include text-xs;
	@include text-gray-600;
	@include flex;
	@include items-center;
}

.mine-user-badge {
	@include flex;
	@include items-center;
	@include justify-center;
	width: 48rpx;
	height: 48rpx;
	@include rounded-full;
	background: rgba(99, 102, 241, 0.1);
	@include transition;

	&:active {
		background: rgba(99, 102, 241, 0.2);
		transform: scale(0.95);
	}
}

.mine-action-card {
	@include modern-card;
	@include p-4;
}

.mine-links-section {
	@include flex;
	@include flex-wrap;
	@include gap-2;
	@include mb-4;
}

.mine-link-btn {
	@include modern-btn;
	@include btn-secondary;
	width: calc(50% - 4rpx);
	@include text-sm;

	&.profile {
		@include btn-primary;
	}

	&.connection {
		@include btn-secondary;
	}

	&.order {
		@include btn-primary;
	}

	&.message {
		@include btn-secondary;
	}

	&.notice {
		@include btn-secondary;
		position: relative;

		.btn-content {
			display: flex;
			align-items: center;
			position: relative;
			width: 100%;
			justify-content: center;
		}

		.btn-text {
			margin-left: 16rpx;
		}

		.notification-badge {
			position: absolute;
			top: -12rpx;
			right: -12rpx;
			min-width: 36rpx;
			height: 36rpx;
			background: linear-gradient(135deg, #ff6b6b, #ff5252);
			color: white;
			border-radius: 18rpx;
			font-size: 22rpx;
			font-weight: bold;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 0 8rpx;
			box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
			animation: badge-pulse 2s infinite;
			z-index: 10;

			// 确保在小数字时保持圆形
			&:has-text {
				min-width: 36rpx;
			}
		}
	}

	&.dish-add {
		@include btn-primary;
	}

	&.dish-manage {
		@include btn-secondary;
		position: relative;
	}
}

// Badge样式
.badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	min-width: 32rpx;
	height: 32rpx;
	@include rounded-full;
	background: #ee0a24;
	color: white;
	@include text-xs;
	@include flex;
	@include items-center;
	@include justify-center;
	padding: 0 8rpx;
	font-weight: 500;
	box-shadow: 0 2rpx 4rpx rgba(238, 10, 36, 0.3);
}

.mine-action-card {
	@include modern-card;
	@include card-flat;
	padding: 24rpx;
	margin-top: 24rpx;
}

.mine-action-btn {
	@include modern-btn;
	@include btn-full;
	@include text-base;
	@include font-medium;
	@include flex;
	@include items-center;
	@include justify-center;
	@include gap-2;
	height: 88rpx;
	@include rounded-lg;
	@include transition;

	&.logout-btn {
		background: #ffffff;
		border: 2rpx solid #ee0a24;
		color: #ee0a24;
		box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);

		&:active {
			background: rgba(238, 10, 36, 0.05);
			transform: scale(0.98);
			border-color: #d60021;
		}

		.van-icon {
			color: #ee0a24;
			font-size: 32rpx;
		}
	}
}

// 角标脉冲动画
@keyframes badge-pulse {

	0%,
	100% {
		transform: scale(1);
		opacity: 1;
	}

	50% {
		transform: scale(1.1);
		opacity: 0.8;
	}
}