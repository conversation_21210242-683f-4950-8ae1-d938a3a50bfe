/**
 * 全局Loading组件
 * 统一的加载状态管理
 */

Component({
  properties: {
    // 是否显示loading
    show: {
      type: Boolean,
      value: false
    },
    // loading类型
    type: {
      type: String,
      value: 'spinner' // spinner, circular
    },
    // loading颜色
    color: {
      type: String,
      value: '#6366F1'
    },
    // loading大小
    size: {
      type: String,
      value: '48rpx'
    },
    // 提示文字
    text: {
      type: String,
      value: ''
    },
    // 是否显示遮罩
    mask: {
      type: Boolean,
      value: true
    },
    // 遮罩透明度
    maskOpacity: {
      type: Number,
      value: 0.3
    },
    // z-index层级
    zIndex: {
      type: Number,
      value: 9999
    }
  },

  data: {
    // 内部状态
  },

  methods: {
    // 点击遮罩事件（可选择是否允许点击关闭）
    onMaskTap() {
      // 默认不允许点击遮罩关闭loading
      // 如果需要可以触发事件让父组件处理
      this.triggerEvent('masktap');
    }
  }
});
