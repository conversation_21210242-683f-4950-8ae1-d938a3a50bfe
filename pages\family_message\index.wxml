<view class="container">
  <!-- 发送留言区域 -->
  <view class="send-card">
    <!-- 接收者选择 -->
    <view class="recipient-selector">
      <view class="selector-label">发送给：</view>
      <view class="selector-content" bindtap="showRecipientSelector">
        <text class="selector-text">{{selectedRecipientsText}}</text>
        <van-icon name="arrow-down" class="selector-arrow" />
      </view>
    </view>

    <!-- 输入框 -->
    <view class="msg-form">
      <input
        class="msg-input"
        placeholder="写下你的留言..."
        placeholder-class="placeholder-style"
        bindinput="onMessageInput"
        value="{{messageInput}}"
        confirm-type="send"
        bindconfirm="addMessage"
      />
      <view class="msg-send" bindtap="addMessage">
        <van-icon name="guide-o" class="send-icon" />
      </view>
    </view>
  </view>

  <!-- 留言列表 -->
  <view class="messages-card">
    <view class="messages-header">
      <text class="messages-title">留言记录</text>
      <text class="messages-count">{{messages.length}}条</text>
    </view>

    <!-- 使用通用刷新组件 -->
    <refresh-scroll
      id="refresh-scroll"
      container-height="{{scrollViewHeight}}"
      request-url="{{requestUrl}}"
      request-params="{{requestParams}}"
      request-headers="{{requestHeaders}}"
      page-size="10"
      auto-load="{{true}}"
      data-field="data"
      list-field="list"
      total-field="total"
      empty-icon="chat-o"
      empty-text="暂无留言记录"
      empty-tip="发送第一条留言开始对话吧"
      bind:datachange="onDataChange"
    >
      <!-- 外部渲染留言列表 -->
      <view
        slot="content"
        wx:if="{{messages.length > 0}}"
        class="messages-list"
      >
        <view wx:for="{{messages}}" wx:key="id" class="msg-item">
          <view class="msg-header">
            <text class="msg-user {{item.userType}}">{{item.userName}}</text>
            <text class="msg-time">{{item.time}}</text>
          </view>
          <view class="msg-content">{{item.content}}</view>
        </view>
      </view>
    </refresh-scroll>
  </view>

  <!-- 接收者选择弹窗 -->
  <van-popup
    show="{{showRecipientPopup}}"
    position="bottom"
    round
    bind:close="hideRecipientSelector"
  >
    <view class="recipient-popup">
      <view class="popup-header">
        <view class="popup-title">选择接收者</view>
        <view class="popup-subtitle">不选择将发送给所有关联用户</view>
      </view>

      <view class="recipient-list">
        <!-- 有关联用户时显示选择列表 -->
        <van-checkbox-group
          wx:if="{{connectedUsers.length > 0}}"
          value="{{selectedRecipients}}"
          bind:change="onRecipientChange"
        >
          <view wx:for="{{connectedUsers}}" wx:key="id" class="recipient-item">
            <van-checkbox name="{{item.id}}" shape="square">
              <view class="recipient-info">
                <view class="recipient-name">{{item.name}}</view>
                <view class="recipient-phone">{{item.phone}}</view>
              </view>
            </van-checkbox>
          </view>
        </van-checkbox-group>

        <!-- 无关联用户时显示缺省页 -->
        <view wx:else class="empty-recipients-state">
          <view class="empty-icon">
            <van-icon name="friends-o" size="60rpx" color="#D1D5DB" />
          </view>
          <view class="empty-text">暂无关联用户</view>
          <view class="empty-desc">添加关联用户后可发送留言给他们</view>
          <view class="empty-action">
            <van-button
              size="small"
              type="primary"
              plain
              bind:click="goToUserConnection"
            >
              去添加关联用户
            </van-button>
          </view>
        </view>
      </view>

      <view class="popup-actions">
        <van-button block type="primary" bind:click="confirmRecipientSelection">
          确定
        </van-button>
      </view>
    </view>
  </van-popup>

  <!-- 全局Loading -->
  <global-loading
    show="{{globalLoading.show}}"
    type="{{globalLoading.type}}"
    color="{{globalLoading.color}}"
    text="{{globalLoading.text}}"
    mask="{{globalLoading.mask}}"
  />
</view>
