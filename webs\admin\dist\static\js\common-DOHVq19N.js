import{G as t}from"./index-XtNpSMFt.js";function n(e,a="YYYY-MM-DD HH:mm:ss"){return e?t(e).format(a):""}function s(e,a="YYYY-MM-DD"){return e?t(e).format(a):""}function o(e){return{main:"主菜",soup:"汤品",dessert:"甜品",drink:"饮品",appetizer:"开胃菜",staple:"主食",vegetable:"蔬菜",meat:"肉类",seafood:"海鲜"}[e]||e}function p(e){return{main:"primary",soup:"info",dessert:"warning",drink:"success",appetizer:"danger",staple:"primary",vegetable:"success",meat:"danger",seafood:"info"}[e]||"default"}export{s as a,o as b,n as f,p as g};
