<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-soft p-6 mb-6">
    <form @submit.prevent="handleSearch">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <!-- 动态渲染搜索字段 -->
        <div
          v-for="field in visibleFields"
          :key="field.key"
          :class="field.span ? `col-span-${field.span}` : ''"
        >
          <!-- 输入框 -->
          <BaseInput
            v-if="field.type === 'input'"
            v-model="searchForm[field.key]"
            :label="field.label"
            :placeholder="field.placeholder"
            :prefix-icon="field.prefixIcon"
            :clearable="true"
          />
          
          <!-- 选择框 -->
          <BaseSelect
            v-else-if="field.type === 'select'"
            v-model="searchForm[field.key]"
            :label="field.label"
            :placeholder="field.placeholder"
            :options="field.options"
            :value-key="field.valueKey"
            :label-key="field.labelKey"
          />
          
          <!-- 日期选择器 -->
          <BaseDatePicker
            v-else-if="field.type === 'date'"
            v-model="searchForm[field.key]"
            :label="field.label"
            :placeholder="field.placeholder"
          />

          <!-- 日期范围选择器 -->
          <BaseDateRangePicker
            v-else-if="field.type === 'daterange'"
            v-model="searchForm[field.key]"
            :label="field.label"
            :start-placeholder="field.startPlaceholder || '开始日期'"
            :end-placeholder="field.endPlaceholder || '结束日期'"
          />
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex items-end space-x-3">
          <BaseButton
            type="submit"
            variant="primary"
            :loading="loading"
            prefix-icon="MagnifyingGlassIcon"
          >
            搜索
          </BaseButton>
          
          <BaseButton
            type="button"
            variant="outline"
            @click="handleReset"
          >
            重置
          </BaseButton>
          
          <!-- 展开/收起按钮 -->
          <BaseButton
            v-if="hasMoreFields"
            type="button"
            variant="ghost"
            @click="toggleExpanded"
          >
            {{ expanded ? '收起' : '展开' }}
            <ChevronUpIcon v-if="expanded" class="ml-1 h-4 w-4" />
            <ChevronDownIcon v-else class="ml-1 h-4 w-4" />
          </BaseButton>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
import { computed, reactive, ref, watch } from 'vue'
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/vue/20/solid'
import BaseInput from './BaseInput.vue'
import BaseSelect from './BaseSelect.vue'
import BaseButton from './BaseButton.vue'
import BaseDatePicker from './BaseDatePicker.vue'
import BaseDateRangePicker from './BaseDateRangePicker.vue'

const props = defineProps({
  // 搜索字段配置
  fields: {
    type: Array,
    default: () => []
  },
  // 初始搜索值
  modelValue: {
    type: Object,
    default: () => ({})
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  // 默认显示的字段数量
  defaultVisibleCount: {
    type: Number,
    default: 3
  },
  // 是否自动搜索
  autoSearch: {
    type: Boolean,
    default: false
  },
  // 自动搜索延迟时间(ms)
  autoSearchDelay: {
    type: Number,
    default: 500
  }
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

// 搜索表单数据
const searchForm = reactive({ ...props.modelValue })

// 是否展开
const expanded = ref(false)

// 是否有更多字段
const hasMoreFields = computed(() => {
  return props.fields.length > props.defaultVisibleCount
})

// 可见字段
const visibleFields = computed(() => {
  if (!hasMoreFields.value || expanded.value) {
    return props.fields
  }
  return props.fields.slice(0, props.defaultVisibleCount)
})

// 监听搜索表单变化
watch(
  () => searchForm,
  (newValue) => {
    emit('update:modelValue', { ...newValue })
    
    if (props.autoSearch) {
      // 防抖处理
      clearTimeout(autoSearchTimer.value)
      autoSearchTimer.value = setTimeout(() => {
        handleSearch()
      }, props.autoSearchDelay)
    }
  },
  { deep: true }
)

// 自动搜索定时器
const autoSearchTimer = ref(null)

// 初始化搜索表单
const initSearchForm = () => {
  props.fields.forEach(field => {
    if (!(field.key in searchForm)) {
      searchForm[field.key] = field.defaultValue || ''
    }
  })
}

// 处理搜索
const handleSearch = () => {
  const searchData = {}
  
  // 过滤空值
  Object.keys(searchForm).forEach(key => {
    const value = searchForm[key]
    if (value !== '' && value !== null && value !== undefined) {
      searchData[key] = value
    }
  })
  
  emit('search', searchData)
}

// 处理重置
const handleReset = () => {
  // 重置表单
  Object.keys(searchForm).forEach(key => {
    const field = props.fields.find(f => f.key === key)
    searchForm[key] = field?.defaultValue || ''
  })
  
  emit('reset')
  
  // 如果开启自动搜索，重置后自动搜索
  if (props.autoSearch) {
    handleSearch()
  }
}

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

// 初始化
initSearchForm()
</script>

<style scoped>
/* 动态列跨度样式 */
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
</style>
