const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController');
const {auth} = require('../middlewares/auth');

// 上传图片 (需要认证)
router.post(
  '/image',
  auth,
  uploadController.handleImageUpload,
  uploadController.uploadImage
);

// 删除图片 (需要认证)
router.delete('/image', auth, uploadController.deleteImage);

module.exports = router;
