<template>
  <div class="flex items-center space-x-2">
    <!-- 服务器状态指示器 -->
    <div class="flex items-center space-x-1">
      <div
        :class="statusIndicatorClass"
        class="w-2 h-2 rounded-full"
      ></div>
      <span :class="statusTextClass" class="text-xs font-medium">
        {{ statusText }}
      </span>
    </div>
    
    <!-- 服务器信息 -->
    <div class="text-xs text-gray-500 dark:text-gray-400">
      {{ serverInfo }}
    </div>
    
    <!-- 延迟信息 -->
    <div v-if="latency !== null" class="text-xs text-gray-500 dark:text-gray-400">
      {{ latency }}ms
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { getCurrentServerConfig } from '@/config/server'
import request from '@/utils/request'

// 响应式数据
const status = ref('checking') // checking | online | offline | error
const latency = ref(null)
const lastCheck = ref(null)

// 定时器
let statusTimer = null
let latencyTimer = null

// 服务器配置
const serverConfig = getCurrentServerConfig()

// 计算属性
const statusIndicatorClass = computed(() => {
  const classes = {
    checking: 'bg-yellow-400 animate-pulse',
    online: 'bg-green-400',
    offline: 'bg-red-400',
    error: 'bg-red-400 animate-pulse'
  }
  return classes[status.value] || 'bg-gray-400'
})

const statusTextClass = computed(() => {
  const classes = {
    checking: 'text-yellow-600 dark:text-yellow-400',
    online: 'text-green-600 dark:text-green-400',
    offline: 'text-red-600 dark:text-red-400',
    error: 'text-red-600 dark:text-red-400'
  }
  return classes[status.value] || 'text-gray-600 dark:text-gray-400'
})

const statusText = computed(() => {
  const texts = {
    checking: '检测中',
    online: '在线',
    offline: '离线',
    error: '错误'
  }
  return texts[status.value] || '未知'
})

const serverInfo = computed(() => {
  const url = new URL(serverConfig.baseURL)
  return `${serverConfig.name} (${url.hostname})`
})

// 方法
const checkServerStatus = async () => {
  try {
    status.value = 'checking'
    const startTime = Date.now()
    
    // 发送健康检查请求
    await request.get('/health', {
      timeout: 5000 // 5秒超时
    })
    
    const endTime = Date.now()
    latency.value = endTime - startTime
    status.value = 'online'
    lastCheck.value = new Date()
    
  } catch (error) {
    console.warn('服务器状态检查失败:', error)
    latency.value = null
    
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      status.value = 'offline'
    } else {
      status.value = 'error'
    }
  }
}

const startStatusCheck = () => {
  // 立即检查一次
  checkServerStatus()
  
  // 每30秒检查一次状态
  statusTimer = setInterval(checkServerStatus, 30000)
}

const stopStatusCheck = () => {
  if (statusTimer) {
    clearInterval(statusTimer)
    statusTimer = null
  }
  if (latencyTimer) {
    clearInterval(latencyTimer)
    latencyTimer = null
  }
}

// 生命周期
onMounted(() => {
  startStatusCheck()
})

onUnmounted(() => {
  stopStatusCheck()
})

// 暴露方法给父组件
defineExpose({
  checkServerStatus,
  startStatusCheck,
  stopStatusCheck
})
</script>
