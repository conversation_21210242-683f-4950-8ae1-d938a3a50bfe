<template>
  <div class="flex items-center justify-between">
    <!-- 左侧信息 -->
    <div class="flex-1 flex justify-between sm:hidden">
      <BaseButton
        variant="outline"
        size="sm"
        :disabled="current <= 1"
        @click="handlePrevious"
      >
        上一页
      </BaseButton>
      <BaseButton
        variant="outline"
        size="sm"
        :disabled="current >= totalPages"
        @click="handleNext"
      >
        下一页
      </BaseButton>
    </div>
    
    <!-- 桌面端分页 -->
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <!-- 统计信息 -->
      <div>
        <p class="text-sm text-gray-700 dark:text-gray-300">
          显示第
          <span class="font-medium">{{ startIndex }}</span>
          到
          <span class="font-medium">{{ endIndex }}</span>
          条，共
          <span class="font-medium">{{ total }}</span>
          条记录
        </p>
      </div>
      
      <!-- 分页控件 -->
      <div class="flex items-center space-x-2">
        <!-- 每页条数选择器 -->
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-700 dark:text-gray-300">每页</span>
          <BaseSelect
            :model-value="pageSize"
            :options="pageSizeOptions"
            size="sm"
            @update:model-value="handlePageSizeChange"
            class="w-20"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300">条</span>
        </div>
        
        <!-- 分页按钮 -->
        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
          <!-- 上一页 -->
          <button
            type="button"
            :disabled="current <= 1"
            @click="handlePrevious"
            class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="sr-only">上一页</span>
            <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
          </button>
          
          <!-- 页码按钮 -->
          <template v-for="page in visiblePages" :key="page">
            <button
              v-if="page !== '...'"
              type="button"
              :class="getPageButtonClass(page)"
              @click="handlePageClick(page)"
            >
              {{ page }}
            </button>
            <span
              v-else
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              ...
            </span>
          </template>
          
          <!-- 下一页 -->
          <button
            type="button"
            :disabled="current >= totalPages"
            @click="handleNext"
            class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span class="sr-only">下一页</span>
            <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
          </button>
        </nav>
        
        <!-- 快速跳转 -->
        <div v-if="showQuickJumper" class="flex items-center space-x-2">
          <span class="text-sm text-gray-700 dark:text-gray-300">跳至</span>
          <BaseInput
            v-model="jumpPage"
            type="number"
            size="sm"
            class="w-16"
            @keyup.enter="handleJump"
          />
          <span class="text-sm text-gray-700 dark:text-gray-300">页</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/20/solid'
import BaseButton from './BaseButton.vue'
import BaseSelect from './BaseSelect.vue'
import BaseInput from './BaseInput.vue'

const props = defineProps({
  // 当前页码
  current: {
    type: Number,
    default: 1
  },
  // 每页条数
  pageSize: {
    type: Number,
    default: 10
  },
  // 总条数
  total: {
    type: Number,
    default: 0
  },
  // 每页条数选项
  pageSizeOptions: {
    type: Array,
    default: () => [
      { label: '10', value: 10 },
      { label: '20', value: 20 },
      { label: '50', value: 50 },
      { label: '100', value: 100 }
    ]
  },
  // 显示的页码数量
  showSize: {
    type: Number,
    default: 5
  },
  // 是否显示快速跳转
  showQuickJumper: {
    type: Boolean,
    default: true
  },
  // 是否显示每页条数选择器
  showSizeChanger: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:current', 'update:pageSize', 'change'])

const jumpPage = ref('')

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(props.total / props.pageSize)
})

const startIndex = computed(() => {
  return (props.current - 1) * props.pageSize + 1
})

const endIndex = computed(() => {
  return Math.min(props.current * props.pageSize, props.total)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = props.current
  const showSize = props.showSize
  
  if (total <= showSize) {
    // 总页数小于等于显示数量，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // 总页数大于显示数量，需要省略
    const half = Math.floor(showSize / 2)
    let start = current - half
    let end = current + half
    
    if (start < 1) {
      start = 1
      end = showSize
    }
    
    if (end > total) {
      end = total
      start = total - showSize + 1
    }
    
    // 添加第一页
    if (start > 1) {
      pages.push(1)
      if (start > 2) {
        pages.push('...')
      }
    }
    
    // 添加中间页码
    for (let i = start; i <= end; i++) {
      pages.push(i)
    }
    
    // 添加最后一页
    if (end < total) {
      if (end < total - 1) {
        pages.push('...')
      }
      pages.push(total)
    }
  }
  
  return pages
})

// 方法
const getPageButtonClass = (page) => {
  const baseClass = 'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
  
  if (page === props.current) {
    return `${baseClass} z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 text-primary-600 dark:text-primary-400`
  }
  
  return `${baseClass} border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700`
}

const handlePageClick = (page) => {
  if (page !== props.current && page !== '...') {
    emit('update:current', page)
    emit('change', { current: page, pageSize: props.pageSize })
  }
}

const handlePrevious = () => {
  if (props.current > 1) {
    const newPage = props.current - 1
    emit('update:current', newPage)
    emit('change', { current: newPage, pageSize: props.pageSize })
  }
}

const handleNext = () => {
  if (props.current < totalPages.value) {
    const newPage = props.current + 1
    emit('update:current', newPage)
    emit('change', { current: newPage, pageSize: props.pageSize })
  }
}

const handlePageSizeChange = (newPageSize) => {
  emit('update:pageSize', newPageSize)
  emit('update:current', 1)
  emit('change', { current: 1, pageSize: newPageSize })
}

const handleJump = () => {
  const page = parseInt(jumpPage.value)
  if (page >= 1 && page <= totalPages.value && page !== props.current) {
    emit('update:current', page)
    emit('change', { current: page, pageSize: props.pageSize })
  }
  jumpPage.value = ''
}

// 监听当前页变化，清空跳转输入框
watch(() => props.current, () => {
  jumpPage.value = ''
})
</script>
