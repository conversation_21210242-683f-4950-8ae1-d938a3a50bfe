import{c as n,a as t,l as r,ab as f,ac as k,_ as h,r as w,p as b,w as C,q as S,b as c,d as v,t as o,H as g,I as m,y as L}from"./index-XtNpSMFt.js";function R(a,e){return r(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function I(a,e){return r(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"})])}function N(a,e){return r(),n("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M21.75 17.25v-.228a4.5 4.5 0 0 0-.12-1.03l-2.268-9.64a3.375 3.375 0 0 0-3.285-2.602H7.923a3.375 3.375 0 0 0-3.285 2.602l-2.268 9.64a4.5 4.5 0 0 0-.12 1.03v.228m19.5 0a3 3 0 0 1-3 3H5.25a3 3 0 0 1-3-3m19.5 0a3 3 0 0 0-3-3H5.25a3 3 0 0 0-3 3m16.5 0h.008v.008h-.008v-.008Zm-3 0h.008v.008h-.008v-.008Z"})])}function p(){const a=f(),e=k();return{server:a,serverType:e,api:{baseURL:a.baseURL,timeout:a.timeout,uploadURL:`${a.baseURL}/upload`,downloadURL:`${a.baseURL}/download`,staticURL:`${a.baseURL}/static`},websocket:{url:a.baseURL.replace(/^http/,"ws").replace("/api","/ws"),reconnectInterval:5e3,maxReconnectAttempts:5},app:{name:"楠楠厨房后台管理系统",version:"2.0.0",description:"基于 Vue 3 + Tailwind CSS 的现代化后台管理系统",copyright:"©楠楠厨房. All rights reserved."},features:{debug:e!=="production",performance:e==="production",errorReporting:e==="production",hotReload:e==="dev",devtools:e!=="production",mock:e==="dev",cache:!0,compression:e==="production"},storage:{tokenKey:"nannan_admin_token",userKey:"nannan_admin_user",settingsKey:"nannan_admin_settings",themeKey:"nannan_admin_theme",localeKey:"nannan_admin_locale"},pagination:{defaultPageSize:10,pageSizes:[10,20,50,100],maxPageSize:1e3},upload:{maxSize:10,allowedTypes:{image:["jpg","jpeg","png","gif","webp"],document:["pdf","doc","docx","xls","xlsx","ppt","pptx"],video:["mp4","avi","mov","wmv"],audio:["mp3","wav","flac"]},uploadPath:"/upload",multiple:!0,maxCount:10},security:{tokenExpiry:24,passwordMinLength:6,passwordMaxLength:20,maxLoginAttempts:5,lockoutDuration:30},theme:{default:"light",available:["light","dark","auto"],primaryColor:"#3b82f6",animations:!0,transitions:!0}}}p();const K={__name:"SystemConfig",setup(a,{expose:e}){e();const y=p(),s=[{text:"首页",to:"/"},{text:"系统管理",to:"/system"},{text:"系统配置"}],x={debug:"调试模式",performance:"性能监控",errorReporting:"错误上报",hotReload:"热更新",devtools:"开发工具",mock:"Mock 数据",cache:"缓存",compression:"压缩"},u={tokenKey:"Token 存储键",userKey:"用户信息存储键",settingsKey:"设置存储键",themeKey:"主题存储键",localeKey:"语言存储键"},d={envConfig:y,breadcrumbItems:s,featureNames:x,storageNames:u,getFeatureName:i=>x[i]||i,getStorageName:i=>u[i]||i,computed:S,get ServerIcon(){return N},get LinkIcon(){return I},get ClockIcon(){return R},get getEnvConfig(){return p}};return Object.defineProperty(d,"__isScriptSetup",{enumerable:!1,value:!0}),d}},U={class:"space-y-6"},z={class:"card"},M={class:"card-body"},B={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},j={class:"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg"},P={class:"flex items-center"},T={class:"flex-shrink-0"},E={class:"ml-3"},A={class:"text-lg font-semibold text-blue-600 dark:text-blue-400"},F={class:"bg-green-50 dark:bg-green-900/20 p-4 rounded-lg"},H={class:"flex items-center"},V={class:"flex-shrink-0"},D={class:"ml-3"},Z={class:"text-sm font-mono text-green-600 dark:text-green-400 break-all"},q={class:"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg"},O={class:"flex items-center"},G={class:"flex-shrink-0"},J={class:"ml-3"},Q={class:"text-lg font-semibold text-yellow-600 dark:text-yellow-400"},W={class:"card"},X={class:"card-body"},Y={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},$={class:"mt-1 text-sm text-gray-900 dark:text-white"},tt={class:"mt-1 text-sm text-gray-900 dark:text-white"},et={class:"mt-1 text-sm text-gray-900 dark:text-white"},st={class:"mt-1 text-sm text-gray-900 dark:text-white"},ot={class:"card"},at={class:"card-body"},rt={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},dt={class:"text-sm font-medium text-gray-900 dark:text-white capitalize"},nt={class:"card"},it={class:"card-body"},lt={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ct={class:"text-sm font-medium text-gray-500 dark:text-gray-400 capitalize"},gt={class:"mt-1 text-sm font-mono text-gray-900 dark:text-white"},mt={class:"card"},xt={class:"card-body"},ut={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},vt={class:"mt-1 text-sm text-gray-900 dark:text-white"},pt={class:"mt-1 text-sm text-gray-900 dark:text-white"},yt={class:"mt-1 text-sm text-gray-900 dark:text-white"},_t={class:"mt-6"},ft={class:"space-y-2"},kt={class:"text-sm font-medium text-gray-700 dark:text-gray-300 w-20 capitalize"},ht={class:"flex flex-wrap gap-1"},wt={class:"card"},bt={class:"card-body"},Ct={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"},St={class:"mt-1 text-sm text-gray-900 dark:text-white"},Lt={class:"mt-1 text-sm text-gray-900 dark:text-white"},Rt={class:"mt-1 text-sm text-gray-900 dark:text-white"},It={class:"mt-1 text-sm text-gray-900 dark:text-white"};function Nt(a,e,y,s,x,u){const _=w("PageContainer");return r(),b(_,{title:"系统配置",subtitle:"查看和管理系统配置信息",breadcrumb:s.breadcrumbItems},{default:C(()=>[t("div",U,[c(" 服务器配置 "),t("div",z,[e[3]||(e[3]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"服务器配置"),t("p",{class:"text-sm text-gray-500 dark:text-gray-400"},"当前连接的服务器信息")],-1)),t("div",M,[t("div",B,[t("div",j,[t("div",P,[t("div",T,[v(s.ServerIcon,{class:"h-8 w-8 text-blue-600"})]),t("div",E,[e[0]||(e[0]=t("p",{class:"text-sm font-medium text-blue-900 dark:text-blue-200"},"服务器环境",-1)),t("p",A,o(s.envConfig.server.name),1)])])]),t("div",F,[t("div",H,[t("div",V,[v(s.LinkIcon,{class:"h-8 w-8 text-green-600"})]),t("div",D,[e[1]||(e[1]=t("p",{class:"text-sm font-medium text-green-900 dark:text-green-200"},"API 地址",-1)),t("p",Z,o(s.envConfig.server.baseURL),1)])])]),t("div",q,[t("div",O,[t("div",G,[v(s.ClockIcon,{class:"h-8 w-8 text-yellow-600"})]),t("div",J,[e[2]||(e[2]=t("p",{class:"text-sm font-medium text-yellow-900 dark:text-yellow-200"},"请求超时",-1)),t("p",Q,o(s.envConfig.server.timeout)+"ms",1)])])])])])]),c(" 应用信息 "),t("div",W,[e[8]||(e[8]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"应用信息")],-1)),t("div",X,[t("dl",Y,[t("div",null,[e[4]||(e[4]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"应用名称",-1)),t("dd",$,o(s.envConfig.app.name),1)]),t("div",null,[e[5]||(e[5]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"版本号",-1)),t("dd",tt,o(s.envConfig.app.version),1)]),t("div",null,[e[6]||(e[6]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"描述",-1)),t("dd",et,o(s.envConfig.app.description),1)]),t("div",null,[e[7]||(e[7]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"版权信息",-1)),t("dd",st,o(s.envConfig.app.copyright),1)])])])]),c(" 功能开关 "),t("div",ot,[e[9]||(e[9]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"功能开关"),t("p",{class:"text-sm text-gray-500 dark:text-gray-400"},"当前环境下的功能启用状态")],-1)),t("div",at,[t("div",rt,[(r(!0),n(g,null,m(s.envConfig.features,(l,d)=>(r(),n("div",{key:d,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"},[t("span",dt,o(s.getFeatureName(d)),1),t("span",{class:L([l?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200","inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},o(l?"启用":"禁用"),3)]))),128))])])]),c(" 存储配置 "),t("div",nt,[e[10]||(e[10]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"存储配置")],-1)),t("div",it,[t("dl",lt,[(r(!0),n(g,null,m(s.envConfig.storage,(l,d)=>(r(),n("div",{key:d},[t("dt",ct,o(s.getStorageName(d)),1),t("dd",gt,o(l),1)]))),128))])])]),c(" 上传配置 "),t("div",mt,[e[15]||(e[15]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"上传配置")],-1)),t("div",xt,[t("div",ut,[t("div",null,[e[11]||(e[11]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"最大文件大小",-1)),t("dd",vt,o(s.envConfig.upload.maxSize)+" MB",1)]),t("div",null,[e[12]||(e[12]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"最大上传数量",-1)),t("dd",pt,o(s.envConfig.upload.maxCount)+" 个",1)]),t("div",null,[e[13]||(e[13]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"多文件上传",-1)),t("dd",yt,o(s.envConfig.upload.multiple?"支持":"不支持"),1)])]),t("div",_t,[e[14]||(e[14]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 mb-3"},"支持的文件类型",-1)),t("div",ft,[(r(!0),n(g,null,m(s.envConfig.upload.allowedTypes,(l,d)=>(r(),n("div",{key:d,class:"flex items-center"},[t("span",kt,o(d)+":",1),t("div",ht,[(r(!0),n(g,null,m(l,i=>(r(),n("span",{key:i,class:"inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"}," ."+o(i),1))),128))])]))),128))])])])]),c(" 安全配置 "),t("div",wt,[e[20]||(e[20]=t("div",{class:"card-header"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"安全配置")],-1)),t("div",bt,[t("dl",Ct,[t("div",null,[e[16]||(e[16]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"Token 过期时间",-1)),t("dd",St,o(s.envConfig.security.tokenExpiry)+" 小时",1)]),t("div",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"密码长度范围",-1)),t("dd",Lt,o(s.envConfig.security.passwordMinLength)+"-"+o(s.envConfig.security.passwordMaxLength)+" 位",1)]),t("div",null,[e[18]||(e[18]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"最大登录失败次数",-1)),t("dd",Rt,o(s.envConfig.security.maxLoginAttempts)+" 次",1)]),t("div",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400"},"账户锁定时间",-1)),t("dd",It,o(s.envConfig.security.lockoutDuration)+" 分钟",1)])])])])])]),_:1})}const Ut=h(K,[["render",Nt],["__file","E:/wx-nan/webs/admin/src/views/system/SystemConfig.vue"]]);export{Ut as default};
