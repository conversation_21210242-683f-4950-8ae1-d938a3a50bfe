const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');
const notificationService = require('../services/notificationService');

/**
 * 获取菜品列表（支持分页和搜索）
 * @route GET /api/dishes
 */
const getDishes = async (req, res) => {
  try {
    const {
      page = 1,
      size = 10,
      categoryId,
      name,
      category,
      isAvailable,
      search = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // 构建查询条件
    const where = {};

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (name) {
      where.name = {
        contains: name,
        mode: 'insensitive'
      };
    }

    if (search) {
      where.OR = [
        {name: {contains: search, mode: 'insensitive'}},
        {description: {contains: search, mode: 'insensitive'}}
      ];
    }

    if (category) {
      where.category = {
        name: {
          contains: category,
          mode: 'insensitive'
        }
      };
    }

    if (isAvailable !== undefined) {
      where.isAvailable = isAvailable === 'true';
    }

    // 计算分页
    const skip = (parseInt(page) - 1) * parseInt(size);
    const take = parseInt(size);

    // 获取总数
    const total = await prisma.dish.count({where});

    // 获取菜品列表
    const dishes = await prisma.dish.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: {
            menuItems: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take
    });

    // 格式化数据为后台管理系统需要的格式
    const formattedDishes = dishes.map(dish => ({
      id: dish.id,
      name: dish.name,
      category: dish.category?.name || '未分类',
      categoryId: dish.categoryId,
      description: dish.description,
      ingredients: dish.ingredients,
      cookingMethod: dish.cookingMethod,
      image: dish.image,
      isAvailable: dish.isAvailable,
      usageCount: dish._count.menuItems,
      createdAt: dish.createdAt,
      updatedAt: dish.updatedAt
    }));

    return success(res, {
      list: formattedDishes,
      total,
      page: parseInt(page),
      size: parseInt(size),
      totalPages: Math.ceil(total / parseInt(size))
    });
  } catch (err) {
    console.error('Get dishes error:', err);
    return error(res, 'Failed to get dishes', 500);
  }
};

/**
 * 获取菜品分类
 * @route GET /api/dishes/categories
 */
const getCategories = async (req, res) => {
  try {
    const {
      page,
      size,
      name,
      search = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // 构建查询条件
    const where = {};

    // 按名称搜索
    if (name) {
      where.name = {
        contains: name
      };
    }

    // 通用搜索
    if (search) {
      where.OR = [
        {name: {contains: search}},
        {description: {contains: search}}
      ];
    }

    // 基础查询配置
    const queryConfig = {
      where,
      include: {
        _count: {
          select: {
            dishes: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      }
    };

    // 如果传了分页参数，则使用分页
    if (page && size) {
      const skip = (parseInt(page) - 1) * parseInt(size);
      const take = parseInt(size);

      queryConfig.skip = skip;
      queryConfig.take = take;

      // 获取总数
      const total = await prisma.category.count({where});

      // 获取分类列表
      const categories = await prisma.category.findMany(queryConfig);

      // 格式化数据
      const formattedCategories = categories.map(category => ({
        id: category.id,
        name: category.name,
        description: category.description || '',
        dishCount: category._count.dishes,
        sort: category.sort || 0,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }));

      return success(res, {
        list: formattedCategories,
        total,
        page: parseInt(page),
        size: parseInt(size),
        totalPages: Math.ceil(total / parseInt(size))
      });
    } else {
      // 没有分页参数，返回全量数据
      const categories = await prisma.category.findMany(queryConfig);

      // 格式化数据
      const formattedCategories = categories.map(category => ({
        id: category.id,
        name: category.name,
        description: category.description || '',
        dishCount: category._count.dishes,
        sort: category.sort || 0,
        createdAt: category.createdAt,
        updatedAt: category.updatedAt
      }));

      return success(res, formattedCategories);
    }
  } catch (err) {
    console.error('Get categories error:', err);
    return error(res, 'Failed to get categories', 500);
  }
};

/**
 * 获取指定菜品
 * @route GET /api/dishes/:id
 */
const getDishById = async (req, res) => {
  try {
    const {id} = req.params;

    const dish = await prisma.dish.findUnique({
      where: {id},
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    if (!dish) {
      return error(res, 'Dish not found', 404);
    }

    // 格式化tags字段用于返回
    const formattedDish = {
      ...dish,
      tags: dish.tags ? JSON.parse(dish.tags) : []
    };

    return success(res, formattedDish);
  } catch (err) {
    console.error('Get dish error:', err);
    return error(res, 'Failed to get dish', 500);
  }
};

/**
 * 创建菜品
 * @route POST /api/dishes
 */
const createDish = async (req, res) => {
  try {
    const {
      name,
      description,
      ingredients,
      cookingMethod,
      remark,
      category,
      tags = [],
      isPublished = false,
      image
    } = req.body;

    // 验证必填字段
    if (!name || !ingredients || !cookingMethod) {
      return error(
        res,
        'Name, ingredients and cooking method are required',
        400
      );
    }

    // 查找或创建分类
    let categoryRecord = await prisma.category.findFirst({
      where: {name: category}
    });

    if (!categoryRecord) {
      categoryRecord = await prisma.category.create({
        data: {name: category}
      });
    }

    // 检查菜品名称是否已存在
    const existingDish = await prisma.dish.findFirst({
      where: {
        name,
        categoryId: categoryRecord.id
      }
    });

    if (existingDish) {
      return error(
        res,
        'Dish with this name already exists in this category',
        400
      );
    }

    // 使用前端已上传的图片URL
    const imageUrl = image || '';

    // 准备创建数据
    const createData = {
      name,
      description: description || `美味的${name}`,
      ingredients,
      cookingMethod,
      remark: remark || '',
      image: imageUrl,
      tags: Array.isArray(tags) ? JSON.stringify(tags) : tags,
      isPublished,
      createdBy: req.user.id, // 从token获取用户ID
      categoryId: categoryRecord.id
    };

    // 使用时间戳工具添加时间戳（如果中间件提供了的话）
    const dataWithTimestamps = req.addTimestamps
      ? req.addTimestamps.forCreate(createData)
      : createData;

    // 创建菜品
    const dish = await prisma.dish.create({
      data: dataWithTimestamps,
      include: {
        category: true,
        creator: true
      }
    });

    // 发送智能推送通知
    try {
      await notificationService.notifyNewDish(dish, req.user);
    } catch (notifyError) {
      console.error('Failed to send notification:', notifyError);
      // 不影响主要功能，只记录错误
    }

    // 转换tags字段用于返回
    const formattedDish = {
      ...dish,
      tags: dish.tags ? JSON.parse(dish.tags) : []
    };

    return success(res, formattedDish, 'Dish created successfully', 201);
  } catch (err) {
    console.error('Create dish error:', err);
    return error(res, 'Failed to create dish', 500);
  }
};

/**
 * 更新菜品 (支持新字段结构和权限检查)
 * @route PUT /api/dishes/:id
 */
const updateDish = async (req, res) => {
  try {
    const {id} = req.params;
    const {
      name,
      description,
      ingredients,
      cookingMethod,
      remark,
      category,
      tags = [],
      isPublished,
      image
    } = req.body;
    const userId = req.user.id;

    // 检查菜品是否存在且属于当前用户
    const existingDish = await prisma.dish.findFirst({
      where: {id, createdBy: userId}
    });

    if (!existingDish) {
      return error(res, 'Dish not found or access denied', 404);
    }

    // 构建更新数据
    const updateData = {};

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (ingredients !== undefined) updateData.ingredients = ingredients;
    if (cookingMethod !== undefined) updateData.cookingMethod = cookingMethod;
    if (remark !== undefined) updateData.remark = remark;
    if (tags !== undefined)
      updateData.tags = Array.isArray(tags) ? JSON.stringify(tags) : tags;
    if (isPublished !== undefined) updateData.isPublished = isPublished;
    if (image !== undefined) updateData.image = image;

    // 如果更新分类
    if (category) {
      let categoryRecord = await prisma.category.findFirst({
        where: {name: category}
      });

      if (!categoryRecord) {
        categoryRecord = await prisma.category.create({
          data: {name: category}
        });
      }

      updateData.categoryId = categoryRecord.id;
    }

    // 使用时间戳工具添加更新时间戳（如果中间件提供了的话）
    const dataWithTimestamps = req.addTimestamps
      ? req.addTimestamps.forUpdate(updateData)
      : updateData;

    // 更新菜品
    const updatedDish = await prisma.dish.update({
      where: {id},
      data: dataWithTimestamps,
      include: {
        category: true,
        creator: true
      }
    });

    // 转换tags字段用于返回
    const formattedDish = {
      ...updatedDish,
      tags: updatedDish.tags ? JSON.parse(updatedDish.tags) : []
    };

    return success(res, formattedDish, 'Dish updated successfully');
  } catch (err) {
    console.error('Update dish error:', err);
    return error(res, 'Failed to update dish', 500);
  }
};

/**
 * 删除菜品 (支持权限检查和云端图片删除)
 * @route DELETE /api/dishes/:id
 */
const deleteDish = async (req, res) => {
  try {
    const {id} = req.params;
    const userId = req.user.id;
    const {force = false} = req.query; // 是否强制删除

    // 检查菜品是否存在且属于当前用户
    const existingDish = await prisma.dish.findFirst({
      where: {id, createdBy: userId}
    });

    if (!existingDish) {
      return error(res, 'Dish not found or access denied', 404);
    }

    // 检查菜品引用情况
    const [menuItems, orders] = await Promise.all([
      // 检查菜单项引用
      prisma.menuItem.findMany({
        where: {dishId: id},
        include: {
          menu: {
            select: {id: true, date: true, createdBy: true}
          }
        }
      }),
      // 检查订单中的菜品引用（通过JSON搜索）
      prisma.order.findMany({
        where: {
          items: {
            path: '$[*].dishId',
            equals: id
          }
        },
        select: {id: true, createdAt: true, user: {select: {name: true}}}
      })
    ]);

    // 如果有引用且不是强制删除，返回详细的引用信息
    if ((menuItems.length > 0 || orders.length > 0) && !force) {
      const references = {
        menuItems: menuItems.length,
        orders: orders.length,
        details: {
          menus: menuItems.map(item => ({
            menuId: item.menu.id,
            date: item.menu.date,
            isOwn: item.menu.createdBy === userId
          })),
          orders: orders.slice(0, 5).map(order => ({
            orderId: order.id,
            date: order.createdAt,
            user: order.user.name
          }))
        }
      };

      return error(
        res,
        {
          message: 'Dish is referenced in menus or orders',
          code: 'DISH_REFERENCED',
          references
        },
        409
      );
    }

    // 🔥 优化：删除云端图片（支持多种格式）
    if (existingDish.image) {
      try {
        console.log('🗑️ 开始删除云端图片:', existingDish.image);

        // 🔥 新增：检查是否为云端图片（支持多种格式）
        const isCloudImage =
          existingDish.image.includes('caidan-') ||
          existingDish.image.includes('tupian-') ||
          existingDish.image.includes('menu_') ||
          existingDish.image.includes('edit_');

        if (isCloudImage) {
          const uploadService = require('../services/uploadService');
          if (uploadService && uploadService.deleteImage) {
            const deleteResult = await uploadService.deleteImage(
              existingDish.image
            );
            if (deleteResult) {
              console.log('✅ 云端图片删除成功:', existingDish.image);
            } else {
              console.warn(
                '⚠️ 云端图片删除失败，但不影响菜品删除:',
                existingDish.image
              );
            }
          } else {
            console.warn('⚠️ uploadService.deleteImage 方法不可用');
          }
        } else {
          console.log('ℹ️ 跳过非云端图片删除:', existingDish.image);
        }
      } catch (deleteError) {
        console.error('❌ 云端图片删除异常:', {
          image: existingDish.image,
          error: deleteError.message,
          stack: deleteError.stack
        });
        // 不阻止菜品删除，继续执行
      }
    } else {
      console.log('ℹ️ 菜品无图片，跳过图片删除');
    }

    // 删除菜品
    await prisma.dish.delete({
      where: {id}
    });

    return success(res, null, 'Dish deleted successfully');
  } catch (err) {
    console.error('Delete dish error:', err);
    return error(res, 'Failed to delete dish', 500);
  }
};

/**
 * 创建菜品分类
 * @route POST /api/dishes/categories
 */
const createCategory = async (req, res) => {
  try {
    const {name} = req.body;

    if (!name) {
      return error(res, 'Name is required', 400);
    }

    // 准备创建数据
    const createData = {name};

    // 使用时间戳工具添加时间戳（如果中间件提供了的话）
    const dataWithTimestamps = req.addTimestamps
      ? req.addTimestamps.forCreate(createData)
      : createData;

    // 创建分类
    const category = await prisma.category.create({
      data: dataWithTimestamps
    });

    return success(res, category, 'Category created successfully', 201);
  } catch (err) {
    console.error('Create category error:', err);
    return error(res, 'Failed to create category', 500);
  }
};

/**
 * 更新菜品分类
 * @route PUT /api/dishes/categories/:id
 */
const updateCategory = async (req, res) => {
  try {
    const {id} = req.params;
    const {name} = req.body;

    if (!name) {
      return error(res, 'Name is required', 400);
    }

    // 检查分类是否存在
    const existingCategory = await prisma.category.findUnique({
      where: {id}
    });

    if (!existingCategory) {
      return error(res, 'Category not found', 404);
    }

    // 准备更新数据
    const updateData = {name};

    // 使用时间戳工具添加更新时间戳（如果中间件提供了的话）
    const dataWithTimestamps = req.addTimestamps
      ? req.addTimestamps.forUpdate(updateData)
      : updateData;

    // 更新分类
    const updatedCategory = await prisma.category.update({
      where: {id},
      data: dataWithTimestamps
    });

    return success(res, updatedCategory, 'Category updated successfully');
  } catch (err) {
    console.error('Update category error:', err);
    return error(res, 'Failed to update category', 500);
  }
};

/**
 * 删除菜品分类
 * @route DELETE /api/dishes/categories/:id
 */
const deleteCategory = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查分类是否存在
    const existingCategory = await prisma.category.findUnique({
      where: {id}
    });

    if (!existingCategory) {
      return error(res, 'Category not found', 404);
    }

    // 检查分类是否被菜品引用
    const dishes = await prisma.dish.findMany({
      where: {categoryId: id}
    });

    if (dishes.length > 0) {
      return error(res, 'Cannot delete category that contains dishes', 400);
    }

    // 删除分类
    await prisma.category.delete({
      where: {id}
    });

    return success(res, null, 'Category deleted successfully');
  } catch (err) {
    console.error('Delete category error:', err);
    return error(res, 'Failed to delete category', 500);
  }
};

/**
 * 获取分类菜品（为小程序优化，支持用户关联关系）
 * @route GET /api/dishes/by-category
 */
const getDishesByCategory = async (req, res) => {
  try {
    const currentUserId = req.user?.id;

    // 🔥 新增：获取关联用户ID列表
    let allowedCreatorIds = [];

    if (currentUserId) {
      // 获取当前用户的关联关系
      const connections = await prisma.userConnection.findMany({
        where: {
          OR: [
            {senderId: currentUserId, status: 'accepted'},
            {receiverId: currentUserId, status: 'accepted'}
          ]
        }
      });

      // 提取关联用户ID
      const connectedUserIds = connections.map(conn =>
        conn.senderId === currentUserId ? conn.receiverId : conn.senderId
      );

      // 🔥 修复：始终包含当前用户，即使没有关联用户
      allowedCreatorIds = [currentUserId, ...connectedUserIds];

      console.log('🔗 用户关联信息:', {
        currentUserId,
        connectedUserIds,
        allowedCreatorIds,
        hasConnections: connectedUserIds.length > 0
      });
    } else {
      console.log('⚠️ 未认证用户访问菜品列表');
    }

    const categories = await prisma.category.findMany({
      include: {
        dishes: {
          where: {
            isPublished: true, // 只获取已上架的菜品
            // 🔥 修复：始终应用创建者过滤
            ...(allowedCreatorIds.length > 0
              ? {
                  createdBy: {in: allowedCreatorIds}
                }
              : {
                  // 如果没有允许的创建者ID，返回不可能的条件（空结果）
                  id: 'impossible-id-to-match-nothing'
                })
          },
          include: {
            // 🔥 新增：包含创建者信息
            creator: {
              select: {id: true, name: true, avatar: true}
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // 格式化为小程序需要的数据结构
    const formattedData = {};

    categories.forEach(category => {
      // 🔥 修复：使用分类名称的拼音或英文作为key
      let categoryKey = '';
      switch (category.name) {
        case '热菜':
          categoryKey = 'hot';
          break;
        case '火锅':
          categoryKey = 'hotpot';
          break;
        case '江湖菜':
          categoryKey = 'jianghu';
          break;
        case '小面':
          categoryKey = 'noodles';
          break;
        case '川菜':
          categoryKey = 'sichuan';
          break;
        case '湘菜':
          categoryKey = 'hunan';
          break;
        case '粤菜':
          categoryKey = 'cantonese';
          break;
        case '主食':
          categoryKey = 'staple';
          break;
        case '凉菜':
          categoryKey = 'cold';
          break;
        case '甜品':
          categoryKey = 'dessert';
          break;
        case '饮品':
          categoryKey = 'drinks';
          break;
        case '酒水':
          categoryKey = 'alcohol';
          break;
        case '小吃':
          categoryKey = 'snacks';
          break;
        case '汤品':
          categoryKey = 'soup';
          break;
        case '素菜':
          categoryKey = 'vegetarian';
          break;
        default:
          categoryKey = category.name.toLowerCase();
      }

      formattedData[categoryKey] = category.dishes.map(dish => ({
        id: dish.id,
        name: dish.name,
        img:
          dish.image ||
          'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
        remark: dish.description || '美味可口',
        material: '新鲜食材精心制作',
        method: '传统工艺，营养健康',
        // 🔥 新增：完整的菜品信息
        description: dish.description,
        ingredients: dish.ingredients || dish.description || '美味可口',
        tags: dish.tags ? JSON.parse(dish.tags) : [],
        category: {
          id: category.id,
          name: category.name
        },
        createdAt: dish.createdAt,
        createdDate: dish.createdAt,
        // 🔥 新增：创建者信息
        creator: dish.creator,
        createdBy: dish.createdBy,
        isMyDish: dish.createdBy === currentUserId
      }));
    });

    return success(res, formattedData);
  } catch (err) {
    console.error('Get dishes by category error:', err);
    return error(res, 'Failed to get dishes by category', 500);
  }
};

/**
 * 获取菜品详情（为小程序优化）
 * @route GET /api/dishes/:id/detail
 */
const getDishDetail = async (req, res) => {
  try {
    const {id} = req.params;

    const dish = await prisma.dish.findUnique({
      where: {id},
      include: {
        category: true,
        creator: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    if (!dish) {
      return error(res, 'Dish not found', 404);
    }

    // 格式化为小程序详情页需要的数据
    const dishDetail = {
      id: dish.id,
      name: dish.name,
      description: dish.description,
      image: dish.image,
      img:
        dish.image ||
        'https://cdn.pixabay.com/photo/2017/05/07/08/56/food-2290814_1280.jpg',
      remark: dish.description || '美味可口，营养丰富',
      ingredients: dish.ingredients,
      cookingMethod: dish.cookingMethod,
      tags: dish.tags ? JSON.parse(dish.tags) : [],
      category: {
        id: dish.category.id,
        name: dish.category.name
      },
      creator: dish.creator,
      createdAt: dish.createdAt,
      createdBy: dish.createdBy,
      isPublished: dish.isPublished,
      // 兼容旧字段
      material: dish.ingredients || '新鲜食材，精心挑选',
      method:
        dish.cookingMethod ||
        '1. 准备新鲜食材；2. 传统工艺制作；3. 营养搭配均衡。'
    };

    return success(res, dishDetail);
  } catch (err) {
    console.error('Get dish detail error:', err);
    return error(res, 'Failed to get dish detail', 500);
  }
};

/**
 * 获取菜品统计信息
 * @route GET /api/dishes/statistics
 */
const getDishStatistics = async (req, res) => {
  try {
    // 总菜品数
    const totalDishes = await prisma.dish.count();

    // 分类统计
    const categoryStats = await prisma.category.findMany({
      include: {
        _count: {
          select: {dishes: true}
        }
      }
    });

    // 最近添加的菜品
    const recentDishes = await prisma.dish.findMany({
      take: 5,
      orderBy: {createdAt: 'desc'},
      include: {category: true}
    });

    return success(res, {
      totalDishes,
      availableDishes: totalDishes, // 所有菜品都可用
      unavailableDishes: 0,
      categoryStats: categoryStats.map(cat => ({
        name: cat.name,
        count: cat._count.dishes
      })),
      recentDishes: recentDishes.map(dish => ({
        id: dish.id,
        name: dish.name,
        category: dish.category?.name,
        createdAt: dish.createdAt
      }))
    });
  } catch (err) {
    console.error('Get dish statistics error:', err);
    return error(res, 'Failed to get dish statistics', 500);
  }
};

/**
 * 获取热门菜品
 * @route GET /api/dishes/hot
 */
const getHotDishes = async (req, res) => {
  try {
    const {limit = 10} = req.query;

    // 这里可以根据订单数据来统计热门菜品
    // 暂时返回最新的菜品作为热门菜品
    const hotDishes = await prisma.dish.findMany({
      include: {category: true},
      orderBy: {createdAt: 'desc'},
      take: parseInt(limit)
    });

    return success(
      res,
      hotDishes.map(dish => ({
        id: dish.id,
        name: dish.name,
        category: dish.category?.name,
        description: dish.description,
        image: dish.image,
        orderCount: Math.floor(Math.random() * 100) + 10 // 模拟订单数
      }))
    );
  } catch (err) {
    console.error('Get hot dishes error:', err);
    return error(res, 'Failed to get hot dishes', 500);
  }
};

/**
 * 批量操作菜品
 * @route POST /api/dishes/batch
 */
const batchOperation = async (req, res) => {
  try {
    const {operation, dishIds} = req.body;

    if (!operation || !dishIds || !Array.isArray(dishIds)) {
      return error(res, 'Operation and dishIds are required', 400);
    }

    let result;

    switch (operation) {
      case 'delete':
        result = await prisma.dish.deleteMany({
          where: {id: {in: dishIds}}
        });
        break;
      default:
        return error(res, 'Invalid operation', 400);
    }

    return success(res, result, `Batch ${operation} completed successfully`);
  } catch (err) {
    console.error('Batch operation error:', err);
    return error(res, 'Failed to perform batch operation', 500);
  }
};

// 默认分类列表（当数据库为空时使用）
const DEFAULT_CATEGORIES = [
  {value: 'hotpot', label: '火锅', id: 'default-hotpot'},
  {value: 'jianghu', label: '江湖菜', id: 'default-jianghu'},
  {value: 'noodles', label: '小面', id: 'default-noodles'},
  {value: 'sichuan', label: '川菜', id: 'default-sichuan'},
  {value: 'hunan', label: '湘菜', id: 'default-hunan'},
  {value: 'cantonese', label: '粤菜', id: 'default-cantonese'},
  {value: 'staple', label: '主食', id: 'default-staple'},
  {value: 'cold', label: '凉菜', id: 'default-cold'},
  {value: 'dessert', label: '甜品', id: 'default-dessert'},
  {value: 'drinks', label: '饮品', id: 'default-drinks'},
  {value: 'alcohol', label: '酒水', id: 'default-alcohol'},
  {value: 'snacks', label: '小吃', id: 'default-snacks'},
  {value: 'soup', label: '汤品', id: 'default-soup'},
  {value: 'vegetarian', label: '素菜', id: 'default-vegetarian'}
];

/**
 * 获取分类列表（为小程序优化）
 * @route GET /api/dishes/categories/miniprogram
 */
const getCategoriesForMiniProgram = async (req, res) => {
  try {
    const categories = await prisma.category.findMany({
      orderBy: {
        createdAt: 'asc'
      }
    });

    // 格式化为小程序需要的格式
    const formattedCategories = categories.map((cat, index) => ({
      value: cat.name.toLowerCase().replace(/\s+/g, ''),
      label: cat.name,
      id: cat.id
    }));

    // 如果没有分类，自动创建默认分类并返回
    if (formattedCategories.length === 0) {
      console.log('⚠️  数据库中没有分类，自动创建默认分类...');

      try {
        // 批量创建默认分类
        const defaultCategoryNames = DEFAULT_CATEGORIES.map(cat => cat.label);
        const createPromises = defaultCategoryNames.map(name =>
          prisma.category.create({
            data: {name}
          })
        );

        const createdCategories = await Promise.all(createPromises);
        console.log(`✅ 成功创建 ${createdCategories.length} 个默认分类`);

        // 返回新创建的分类
        const newFormattedCategories = createdCategories.map(cat => ({
          value: cat.name.toLowerCase().replace(/\s+/g, ''),
          label: cat.name,
          id: cat.id
        }));

        return success(res, newFormattedCategories);
      } catch (createError) {
        console.error('创建默认分类失败:', createError);
        // 如果创建失败，返回静态默认分类
        return success(res, DEFAULT_CATEGORIES);
      }
    }

    return success(res, formattedCategories);
  } catch (err) {
    console.error('Get categories for miniprogram error:', err);
    // 发生错误时也返回默认分类，确保系统可用
    return success(res, DEFAULT_CATEGORIES);
  }
};

/**
 * 获取我的菜品列表
 * @route GET /api/dishes/my
 */
const getMyDishes = async (req, res) => {
  try {
    const {page = 1, size = 10, isPublished} = req.query;
    const userId = req.user.id;

    const where = {createdBy: userId};
    if (isPublished !== undefined) {
      where.isPublished = isPublished === 'true';
    }

    const skip = (parseInt(page) - 1) * parseInt(size);
    const take = parseInt(size);

    const dishes = await prisma.dish.findMany({
      where,
      include: {
        category: true,
        _count: {
          select: {menuItems: true}
        }
      },
      orderBy: {createdAt: 'desc'},
      skip,
      take
    });

    const total = await prisma.dish.count({where});

    // 转换tags字段从JSON字符串到数组
    const formattedDishes = dishes.map(dish => ({
      ...dish,
      tags: dish.tags ? JSON.parse(dish.tags) : []
    }));

    return success(res, {
      list: formattedDishes,
      total,
      page: parseInt(page),
      size: parseInt(size),
      totalPages: Math.ceil(total / parseInt(size))
    });
  } catch (err) {
    console.error('Get my dishes error:', err);
    return error(res, 'Failed to get my dishes', 500);
  }
};

/**
 * 更新菜品上架状态
 * @route PUT /api/dishes/:id/status
 */
const updateDishStatus = async (req, res) => {
  try {
    const {id} = req.params;
    const {isPublished} = req.body;
    const userId = req.user.id;

    // 检查菜品是否属于当前用户
    const dish = await prisma.dish.findFirst({
      where: {id, createdBy: userId}
    });

    if (!dish) {
      return error(res, 'Dish not found or access denied', 404);
    }

    // 如果是下架操作，检查影响范围
    if (!isPublished && dish.isPublished) {
      const impact = await checkDishImpact(id);
      console.log(`📊 菜品下架影响分析:`, impact);

      // 记录下架操作的影响（可选：发送通知给相关用户）
      if (impact.menuItems > 0 || impact.orders > 0) {
        console.log(
          `⚠️ 菜品 "${dish.name}" 下架将影响 ${impact.menuItems} 个菜单项和 ${impact.orders} 个订单`
        );
      }
    }

    // 准备更新数据
    const updateData = {isPublished};

    // 使用时间戳工具添加更新时间戳（如果中间件提供了的话）
    const dataWithTimestamps = req.addTimestamps
      ? req.addTimestamps.forUpdate(updateData)
      : updateData;

    const updatedDish = await prisma.dish.update({
      where: {id},
      data: dataWithTimestamps,
      include: {
        category: true,
        creator: true
      }
    });

    return success(res, updatedDish, 'Dish status updated successfully');
  } catch (err) {
    console.error('Update dish status error:', err);
    return error(res, 'Failed to update dish status', 500);
  }
};

/**
 * 获取菜品影响分析
 * @route GET /api/dishes/:id/impact
 */
const getDishImpact = async (req, res) => {
  try {
    const {id} = req.params;
    const userId = req.user.id;

    // 检查菜品是否存在且属于当前用户
    const dish = await prisma.dish.findFirst({
      where: {id, createdBy: userId},
      select: {id: true, name: true, isPublished: true}
    });

    if (!dish) {
      return error(res, 'Dish not found or access denied', 404);
    }

    // 获取详细的影响分析
    const [menuItems, orders] = await Promise.all([
      // 检查菜单项引用
      prisma.menuItem.findMany({
        where: {dishId: id},
        include: {
          menu: {
            select: {
              id: true,
              date: true,
              isToday: true,
              createdBy: true,
              creator: {select: {name: true}}
            }
          }
        }
      }),
      // 检查订单中的菜品引用
      prisma.order.findMany({
        where: {
          items: {
            path: '$[*].dishId',
            equals: id
          }
        },
        select: {
          id: true,
          createdAt: true,
          status: true,
          user: {select: {name: true}}
        },
        orderBy: {createdAt: 'desc'},
        take: 10 // 最多返回10个订单
      })
    ]);

    const impact = {
      dish: {
        id: dish.id,
        name: dish.name,
        isPublished: dish.isPublished
      },
      summary: {
        menuItems: menuItems.length,
        orders: orders.length,
        total: menuItems.length + orders.length
      },
      details: {
        menus: menuItems.map(item => ({
          menuId: item.menu.id,
          date: item.menu.date,
          isToday: item.menu.isToday,
          isOwn: item.menu.createdBy === userId,
          creatorName: item.menu.creator.name,
          count: item.count
        })),
        orders: orders.map(order => ({
          orderId: order.id,
          date: order.createdAt,
          status: order.status,
          userName: order.user.name
        }))
      },
      canDelete: menuItems.length === 0 && orders.length === 0,
      recommendations: []
    };

    // 生成建议
    if (impact.summary.total > 0) {
      if (dish.isPublished) {
        impact.recommendations.push('建议先下架菜品，再考虑删除');
      }
      if (impact.summary.menuItems > 0) {
        impact.recommendations.push('删除前需要先从相关菜单中移除此菜品');
      }
      if (impact.summary.orders > 0) {
        impact.recommendations.push('此菜品已被订购，删除可能影响历史订单显示');
      }
    }

    return success(res, impact, 'Dish impact analysis completed');
  } catch (err) {
    console.error('Get dish impact error:', err);
    return error(res, 'Failed to get dish impact', 500);
  }
};

/**
 * 检查菜品的影响范围
 * @param {string} dishId 菜品ID
 * @returns {Object} 影响统计
 */
const checkDishImpact = async dishId => {
  try {
    const [menuItems, orders] = await Promise.all([
      // 检查菜单项引用
      prisma.menuItem.count({
        where: {dishId}
      }),
      // 检查订单中的菜品引用
      prisma.order.count({
        where: {
          items: {
            path: '$[*].dishId',
            equals: dishId
          }
        }
      })
    ]);

    return {
      menuItems,
      orders,
      total: menuItems + orders
    };
  } catch (error) {
    console.error('检查菜品影响范围失败:', error);
    return {menuItems: 0, orders: 0, total: 0};
  }
};

module.exports = {
  getDishes,
  getCategories,
  getDishById,
  createDish,
  updateDish,
  deleteDish,
  createCategory,
  updateCategory,
  deleteCategory,
  getDishesByCategory,
  getDishDetail,
  getDishStatistics,
  getHotDishes,
  batchOperation,
  getCategoriesForMiniProgram, // 新增
  getMyDishes, // 新增
  updateDishStatus, // 新增
  getDishImpact // 新增
};
