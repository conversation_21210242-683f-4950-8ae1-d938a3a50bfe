const axios = require('axios');

/**
 * 微信订阅消息服务
 * 用于发送微信小程序订阅消息
 */
class WechatSubscriptionService {
  constructor() {
    // 🔥 修复：使用正确的环境变量名
    this.appId = process.env.WECHAT_APPID || process.env.WECHAT_APP_ID;
    this.appSecret = process.env.WECHAT_SECRET || process.env.WECHAT_APP_SECRET;
    this.templateId =
      process.env.WECHAT_TEMPLATE_ID ||
      'kDusapKJllk0UrDuT86oUfFoVID7eHDiQ4AK7i0esNc';
    this.accessToken = null;
    this.tokenExpireTime = null;

    // 生产环境下可以移除调试信息
    if (process.env.NODE_ENV !== 'production') {
      console.log('🔧 微信订阅服务初始化:');
      console.log(
        'AppID:',
        this.appId ? `${this.appId.substring(0, 10)}...` : '❌ 未配置'
      );
      console.log(
        'AppSecret:',
        this.appSecret ? `${this.appSecret.substring(0, 10)}...` : '❌ 未配置'
      );
      console.log(
        'TemplateID:',
        this.templateId ? `${this.templateId.substring(0, 10)}...` : '❌ 未配置'
      );
    }
  }

  /**
   * 获取微信访问令牌
   * @returns {Promise<string>} access_token
   */
  async getAccessToken() {
    try {
      // 检查是否有有效的token
      if (
        this.accessToken &&
        this.tokenExpireTime &&
        Date.now() < this.tokenExpireTime
      ) {
        return this.accessToken;
      }

      // 获取新的access_token
      const response = await axios.get(
        'https://api.weixin.qq.com/cgi-bin/token',
        {
          params: {
            grant_type: 'client_credential',
            appid: this.appId,
            secret: this.appSecret
          }
        }
      );

      if (response.data.access_token) {
        this.accessToken = response.data.access_token;
        // 提前5分钟过期，确保token有效性
        this.tokenExpireTime =
          Date.now() + (response.data.expires_in - 300) * 1000;
        return this.accessToken;
      } else {
        throw new Error(`获取access_token失败: ${response.data.errmsg}`);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * 发送订阅消息
   * @param {string} openid 用户openid
   * @param {object} data 消息数据
   * @returns {Promise<object>} 发送结果
   */
  async sendSubscriptionMessage(openid, data) {
    try {
      const accessToken = await this.getAccessToken();

      const messageData = {
        touser: openid,
        template_id: this.templateId,
        page: 'pages/today_order/index', // 点击消息跳转的页面
        data: data,
        miniprogram_state:
          process.env.NODE_ENV === 'production' ? 'formal' : 'trial'
      };

      const response = await axios.post(
        `https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${accessToken}`,
        messageData
      );

      if (response.data.errcode === 0) {
        console.log(`订阅消息发送成功: ${openid}`);
        return {success: true, data: response.data};
      } else {
        console.error(`订阅消息发送失败: ${response.data.errmsg}`);
        return {success: false, error: response.data.errmsg};
      }
    } catch (error) {
      console.error('发送订阅消息失败:', error);
      return {success: false, error: error.message};
    }
  }

  /**
   * 发送下单通知
   * @param {object} order 订单信息
   * @param {object} customer 下单用户信息
   * @param {Array} connectedUsers 关联用户列表
   */
  async sendOrderNotification(order, customer, connectedUsers) {
    try {
      // 格式化菜品名称
      let items = order.items;
      if (typeof items === 'string') {
        items = JSON.parse(items);
      }
      const dishNames = items
        .map(item => item.dishName || item.name)
        .join('，');

      // 格式化下单时间
      const orderTime = new Date(order.createdAt || Date.now()).toLocaleString(
        'zh-CN',
        {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }
      );

      // 构建消息数据（根据需求调整字段内容）
      const messageData = {
        thing1: {
          value: '新订单通知'
        },
        time1: {
          value: orderTime // 下单时间
        },
        thing2: {
          value: '关联好友' // 固定文字
        },
        thing3: {
          value: '订单通知'
        },
        thing4: {
          value: customer.name || '未知用户' // 下单用户
        },
        thing5: {
          value: dishNames // 菜品名字，用，拼接
        }
      };

      // 发送给所有关联用户（除了下单者，且有有效openid）
      const sendPromises = connectedUsers
        .filter(
          member =>
            member.id !== customer.id &&
            member.openid &&
            !member.openid.startsWith('mock_')
        )
        .map(member =>
          this.sendSubscriptionMessage(member.openid, messageData)
        );

      const results = await Promise.allSettled(sendPromises);

      let successCount = 0;
      let failCount = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value.success) {
          successCount++;
        } else {
          failCount++;
          console.error(
            `发送给用户 ${connectedUsers[index].name} 失败:`,
            result.status === 'rejected' ? result.reason : result.value.error
          );
        }
      });

      return {
        success: true,
        successCount,
        failCount,
        totalCount: connectedUsers.length - 1 // 排除下单者
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 发送菜单更新通知
   * @param {object} menu 菜单信息
   * @param {object} creator 创建者信息
   * @param {Array} connectedUsers 关联用户列表
   */
  async sendMenuUpdateNotification(menu, creator, connectedUsers) {
    try {
      const updateTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });

      const messageData = {
        thing1: {
          value: '菜单推送通知'
        },
        time1: {
          value: updateTime // 推送时间
        },
        thing2: {
          value: '关联好友' // 固定文字
        },
        thing3: {
          value: '菜单推送'
        },
        thing4: {
          value: creator.name || '未知用户' // 推送用户
        },
        thing5: {
          value: '今日菜单已更新，快来看看吧！' // 推送内容
        }
      };

      // 发送给所有关联用户（除了创建者，且有有效openid）
      const sendPromises = connectedUsers
        .filter(
          member =>
            member.id !== creator.id &&
            member.openid &&
            !member.openid.startsWith('mock_')
        )
        .map(member =>
          this.sendSubscriptionMessage(member.openid, messageData)
        );

      const results = await Promise.allSettled(sendPromises);

      let successCount = 0;
      let failCount = 0;

      results.forEach(result => {
        if (result.status === 'fulfilled' && result.value.success) {
          successCount++;
        } else {
          failCount++;
        }
      });

      return {
        success: true,
        successCount,
        failCount,
        totalCount: connectedUsers.length - 1
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new WechatSubscriptionService();
