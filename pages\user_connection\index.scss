.container {
  padding: 0;
  background-color: #f8fafc;
  min-height: 100vh;
}

.header {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  padding: 40rpx 32rpx 32rpx;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  .header-content {
    flex: 1;

    .title {
      font-size: 48rpx;
      font-weight: bold;
      margin-bottom: 8rpx;
    }

    .subtitle {
      font-size: 28rpx;
      opacity: 0.9;
      margin-bottom: 16rpx;
    }
  }

  .header-actions {
    display: flex;
    justify-content: flex-end;

    .van-button {
      background: rgba(255, 255, 255, 0.2);
      border: 2rpx solid rgba(255, 255, 255, 0.3);
      color: white;

      &:active {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.tab-content {
  padding: 32rpx;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 32rpx;
}

/* 连接列表样式 */
.connection-list,
.user-list,
.request-list {
  .connection-item,
  .user-item,
  .request-item {
    background: white;
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    overflow: hidden;

    .user-info {
      display: flex;
      align-items: flex-start;
      flex: 1;
      min-width: 0;
      margin-right: 24rpx;

      .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        background-color: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        overflow: hidden;

        image {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }

      .info {
        flex: 1;

        .name-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 12rpx;

          .name {
            font-size: 32rpx;
            font-weight: 600;
            color: #1f2937;
            flex: 1;
          }

          .role-badge {
            font-size: 20rpx;
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
            font-weight: 500;

            &.admin {
              background-color: #fef3c7;
              color: #d97706;
            }

            &.user {
              background-color: #e0e7ff;
              color: #6366f1;
            }
          }
        }

        .remark {
          display: flex;
          align-items: center;
          gap: 8rpx;
          font-size: 26rpx;
          color: #6366f1;
          background-color: #f0f9ff;
          padding: 8rpx 12rpx;
          border-radius: 8rpx;
          margin-bottom: 12rpx;
          border-left: 3rpx solid #6366f1;
        }

        .contact-info {
          display: flex;
          flex-direction: column;
          gap: 8rpx;
          margin-bottom: 12rpx;

          .phone {
            display: flex;
            align-items: center;
            gap: 8rpx;
            font-size: 26rpx;
            color: #6b7280;
          }

          .group-tag {
            display: flex;
            align-items: center;
            gap: 8rpx;
            font-size: 24rpx;
            padding: 6rpx 12rpx;
            border-radius: 12rpx;
            border: 1rpx solid;
            background-color: rgba(255, 255, 255, 0.8);
            align-self: flex-start;

            .group-dot {
              width: 12rpx;
              height: 12rpx;
              border-radius: 50%;
            }
          }
        }

        .connection-time {
          font-size: 22rpx;
          color: #9ca3af;
          border-top: 1rpx solid #f3f4f6;
          padding-top: 8rpx;
        }

        .message {
          font-size: 28rpx;
          color: #4b5563;
          margin-bottom: 8rpx;
          background-color: #f9fafb;
          padding: 16rpx;
          border-radius: 8rpx;
          margin-top: 8rpx;
        }

        .time {
          font-size: 24rpx;
          color: #9ca3af;
        }

        .request-meta {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-top: 16rpx;
          padding-top: 12rpx;
          border-top: 1rpx solid #f3f4f6;

          .request-time {
            font-size: 24rpx;
            color: #9ca3af;
          }
        }
      }
    }

    .actions {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 12rpx;
      flex-shrink: 0;
      min-width: 160rpx;
      max-width: 160rpx;

      .van-button {
        width: 100%;
        min-width: 120rpx;
        font-size: 24rpx;
        padding: 12rpx 16rpx;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;

  .empty-text {
    font-size: 32rpx;
    color: #6b7280;
    margin: 24rpx 0 16rpx;
    font-weight: 500;
  }

  .empty-desc {
    font-size: 28rpx;
    color: #9ca3af;
    line-height: 1.5;
  }

  &.search-empty {
    .empty-text {
      color: #374151;
    }

    .empty-desc {
      color: #6b7280;
    }
  }
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  text-align: center;

  .error-text {
    font-size: 32rpx;
    color: #dc2626;
    margin: 24rpx 0 16rpx;
    font-weight: 500;
  }

  .error-desc {
    font-size: 28rpx;
    color: #6b7280;
    line-height: 1.5;
    margin-bottom: 16rpx;
  }
}

/* 弹窗内容 */
.dialog-content {
  padding: 32rpx;

  .dialog-text {
    font-size: 32rpx;
    color: #374151;
    margin-bottom: 32rpx;
    text-align: center;
  }
}

/* 加载状态 */
.van-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* 标签页样式调整 */
.van-tabs {
  background: white;
}

.van-tabs__nav {
  background: white;
  border-bottom: 1rpx solid #e5e7eb;
}

.van-tab {
  font-size: 30rpx;
}

.van-tab--active {
  color: #6366f1;
  font-weight: 600;
}

.van-tabs__line {
  background-color: #6366f1;
}

/* 按钮样式调整 */
.van-button--primary {
  background-color: #6366f1;
  border-color: #6366f1;
}

.van-button--danger {
  color: #ef4444;
  border-color: #ef4444;
}

.van-button--danger.van-button--plain {
  background-color: transparent;
}

/* 标签样式 */
.van-tag--warning {
  background-color: #fef3c7;
  color: #d97706;
}

.van-tag--success {
  background-color: #d1fae5;
  color: #059669;
}

.van-tag--danger {
  background-color: #fee2e2;
  color: #dc2626;
}

/* 分组选择器样式 */
.group-picker-content {
  padding: 32rpx;
  max-height: 600rpx;
  overflow-y: auto;

  .group-option {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 1rpx solid #f3f4f6;

    &:last-child {
      border-bottom: none;
    }

    &.selected {
      background-color: #f8fafc;
      border-radius: 8rpx;
      padding: 24rpx 16rpx;
      margin: 0 -16rpx;
    }

    .group-info {
      display: flex;
      align-items: center;
      flex: 1;

      .group-color {
        width: 24rpx;
        height: 24rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }

      .group-name {
        font-size: 32rpx;
        color: #1f2937;
        margin-right: 16rpx;
      }

      .member-count {
        font-size: 24rpx;
        color: #6b7280;
        background-color: #f3f4f6;
        padding: 4rpx 12rpx;
        border-radius: 12rpx;
      }
    }
  }
}
