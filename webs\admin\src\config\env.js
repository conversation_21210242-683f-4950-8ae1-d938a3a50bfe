/**
 * 环境配置文件
 * 统一管理不同环境下的配置信息
 */

import {getCurrentServerConfig, getCurrentServerType} from './server';

/**
 * 获取环境配置
 */
export function getEnvConfig() {
  const serverConfig = getCurrentServerConfig();
  const serverType = getCurrentServerType();

  return {
    // 服务器配置
    server: serverConfig,
    serverType,

    // API 配置
    api: {
      baseURL: serverConfig.baseURL,
      timeout: serverConfig.timeout,
      uploadURL: `${serverConfig.baseURL}/upload`,
      downloadURL: `${serverConfig.baseURL}/download`,
      staticURL: `${serverConfig.baseURL}/static`
    },

    // WebSocket 配置
    websocket: {
      url: serverConfig.baseURL.replace(/^http/, 'ws').replace('/api', '/ws'),
      reconnectInterval: 5000,
      maxReconnectAttempts: 5
    },

    // 应用配置
    app: {
      name: '楠楠厨房后台管理系统',
      version: '2.0.0',
      description: '基于 Vue 3 + Tailwind CSS 的现代化后台管理系统',
      copyright: '©楠楠厨房. All rights reserved.'
    },

    // 功能开关
    features: {
      // 是否启用调试模式
      debug: serverType !== 'production',

      // 是否启用性能监控
      performance: serverType === 'production',

      // 是否启用错误上报
      errorReporting: serverType === 'production',

      // 是否启用热更新
      hotReload: serverType === 'dev',

      // 是否显示开发工具
      devtools: serverType !== 'production',

      // 是否启用 Mock 数据
      mock: serverType === 'dev',

      // 是否启用缓存
      cache: true,

      // 是否启用压缩
      compression: serverType === 'production'
    },

    // 存储配置
    storage: {
      // Token 存储 key
      tokenKey: 'nannan_admin_token',

      // 用户信息存储 key
      userKey: 'nannan_admin_user',

      // 设置存储 key
      settingsKey: 'nannan_admin_settings',

      // 主题存储 key
      themeKey: 'nannan_admin_theme',

      // 语言存储 key
      localeKey: 'nannan_admin_locale'
    },

    // 分页配置
    pagination: {
      defaultPageSize: 10,
      pageSizes: [10, 20, 50, 100],
      maxPageSize: 1000
    },

    // 文件上传配置
    upload: {
      // 最大文件大小 (MB)
      maxSize: 10,

      // 允许的文件类型
      allowedTypes: {
        image: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        document: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
        video: ['mp4', 'avi', 'mov', 'wmv'],
        audio: ['mp3', 'wav', 'flac']
      },

      // 上传路径
      uploadPath: '/upload',

      // 是否启用多文件上传
      multiple: true,

      // 最大上传数量
      maxCount: 10
    },

    // 安全配置
    security: {
      // Token 过期时间 (小时)
      tokenExpiry: 24,

      // 密码最小长度
      passwordMinLength: 6,

      // 密码最大长度
      passwordMaxLength: 20,

      // 登录失败最大次数
      maxLoginAttempts: 5,

      // 账户锁定时间 (分钟)
      lockoutDuration: 30
    },

    // 主题配置
    theme: {
      // 默认主题
      default: 'light',

      // 可用主题
      available: ['light', 'dark', 'auto'],

      // 主色调
      primaryColor: '#3b82f6',

      // 是否启用动画
      animations: true,

      // 是否启用过渡效果
      transitions: true
    }
  };
}

/**
 * 获取 API 基础 URL
 */
export function getApiBaseURL() {
  return getEnvConfig().api.baseURL;
}

/**
 * 获取上传 URL
 */
export function getUploadURL() {
  return getEnvConfig().api.uploadURL;
}

/**
 * 获取静态资源 URL
 */
export function getStaticURL(path = '') {
  return `${getEnvConfig().api.staticURL}/${path}`;
}

/**
 * 检查功能是否启用
 */
export function isFeatureEnabled(feature) {
  return getEnvConfig().features[feature] || false;
}

/**
 * 获取存储 key
 */
export function getStorageKey(key) {
  return getEnvConfig().storage[key];
}

/**
 * 是否为开发环境
 */
export function isDev() {
  return getCurrentServerType() === 'dev';
}

/**
 * 是否为测试环境
 */
export function isTest() {
  return getCurrentServerType() === 'test';
}

/**
 * 是否为生产环境
 */
export function isProd() {
  return getCurrentServerType() === 'production';
}

// 导出环境配置
export const envConfig = getEnvConfig();

// 默认导出
export default {
  getEnvConfig,
  getApiBaseURL,
  getUploadURL,
  getStaticURL,
  isFeatureEnabled,
  getStorageKey,
  isDev,
  isTest,
  isProd,
  envConfig
};
