var fe=Object.defineProperty;var J=Object.getOwnPropertySymbols;var me=Object.prototype.hasOwnProperty,_e=Object.prototype.propertyIsEnumerable;var K=(_,t,l)=>t in _?fe(_,t,{enumerable:!0,configurable:!0,writable:!0,value:l}):_[t]=l,Q=(_,t)=>{for(var l in t||(t={}))me.call(t,l)&&K(_,l,t[l]);if(J)for(var l of J(t))_e.call(t,l)&&K(_,l,t[l]);return _};var T=(_,t,l)=>new Promise((a,w)=>{var O=c=>{try{v(l.next(c))}catch(d){w(d)}},g=c=>{try{v(l.throw(c))}catch(d){w(d)}},v=c=>c.done?a(c.value):Promise.resolve(c.value).then(O,g);v((l=l.apply(_,t)).next())});import{_ as ge,r as M,c as Z,b,d as o,a as n,w as s,g as x,h as E,B as $,W as ee,D as te,X as ae,o as re,n as oe,s as se,G as A,M as ve,R as ye,E as ne,l as F,H as Ce,I as be,p as xe,m as D,t as L}from"./index-Cy8N1eGd.js";import{S as we,i as V,e as Se}from"./StatsCard-SUxJzo4F.js";import{C as Me}from"./CustomTable-DWghEWMD.js";import{o as P}from"./order-D36AVmfp.js";const De={__name:"statistics",setup(_,{expose:t}){t();const l=x(),a=x(),w=x(),O=x(),g=x("7d"),v=x(!1),c=x([]);let d=null,h=null,u=null,f=null;const r=E({page:1,size:10,total:0}),S=E({date:"",status:""}),C=E([{title:"今日订单",value:0,icon:$,type:"primary",trend:"+12%",description:"较昨日"},{title:"今日营收",value:0,icon:ee,type:"success",trend:"+8%",description:"较昨日"},{title:"平均订单价值",value:0,icon:te,type:"warning",trend:"+5%",description:"较昨日"},{title:"平均处理时间",value:0,icon:ae,type:"info",trend:"-3分钟",description:"较昨日"}]),le=[{prop:"date",label:"日期",width:120,formatter:e=>G(e.date)},{prop:"orderCount",label:"订单数量",width:120,slot:!0},{prop:"revenue",label:"营收金额",width:120,slot:!0},{prop:"avgOrderValue",label:"平均订单价值",width:140,slot:!0},{prop:"completedOrders",label:"完成订单",width:100},{prop:"cancelledOrders",label:"取消订单",width:100},{prop:"completionRate",label:"完成率",width:100,formatter:e=>`${e.completionRate}%`}],ie=[{prop:"date",label:"日期",type:"daterange"},{prop:"status",label:"状态",type:"select",options:[{label:"全部",value:""},{label:"已完成",value:"completed"},{label:"进行中",value:"processing"},{label:"已取消",value:"cancelled"}]}],B=()=>T(this,null,function*(){try{const e=yield P.getOrderStatistics();e.data&&(C[0].value=e.data.todayOrders||45,C[1].value=`¥${e.data.todayRevenue||1280}`,C[2].value=`¥${e.data.avgOrderValue||28.5}`,C[3].value=`${e.data.avgProcessTime||15}分钟`)}catch(e){console.error("加载订单统计失败:",e),C[0].value=45,C[1].value="¥1,280",C[2].value="¥28.5",C[3].value="15分钟"}}),R=()=>T(this,null,function*(){v.value=!0;try{const e=Q({page:r.page,size:r.size},S),i=yield P.getOrderStatistics(e);i.data&&(c.value=i.data.list||[],r.total=i.data.total||0)}catch(e){console.error("加载表格数据失败:",e),c.value=j(),r.total=30}finally{v.value=!1}}),j=()=>{const e=[];for(let i=0;i<10;i++){const m=Math.floor(Math.random()*50)+20,p=Math.floor(Math.random()*1e3)+500,y=Math.floor(m*.8),z=m-y;e.push({id:i+1,date:A().subtract(i,"day").format("YYYY-MM-DD"),orderCount:m,revenue:p,avgOrderValue:Math.round(p/m*100)/100,completedOrders:y,cancelledOrders:z,completionRate:Math.round(y/m*100)})}return e},H=()=>{l.value&&(d=V(l.value),N())},N=()=>{if(!d)return;const e=g.value==="7d"?7:g.value==="30d"?30:90,i=[],m=[],p=[];for(let z=e-1;z>=0;z--)i.push(A().subtract(z,"day").format("MM-DD")),m.push(Math.floor(Math.random()*50)+20),p.push(Math.floor(Math.random()*1e3)+500);const y={tooltip:{trigger:"axis",axisPointer:{type:"cross"}},legend:{data:["订单数","营收"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:i},yAxis:[{type:"value",name:"订单数",position:"left"},{type:"value",name:"营收(元)",position:"right"}],series:[{name:"订单数",type:"line",data:m,smooth:!0,itemStyle:{color:"#409EFF"}},{name:"营收",type:"bar",yAxisIndex:1,data:p,itemStyle:{color:"#67C23A"}}]};d.setOption(y)},Y=()=>{if(!a.value)return;h=V(a.value);const i={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},series:[{name:"订单状态",type:"pie",radius:["40%","70%"],center:["50%","60%"],data:[{value:65,name:"已完成",itemStyle:{color:"#67C23A"}},{value:25,name:"进行中",itemStyle:{color:"#E6A23C"}},{value:8,name:"已取消",itemStyle:{color:"#F56C6C"}},{value:2,name:"待确认",itemStyle:{color:"#909399"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};h.setOption(i)},I=()=>{w.value&&(u=V(w.value),U())},U=()=>{if(!u)return;const e=[],i=[];for(let p=6;p<=22;p++){e.push(p+":00");let y;p>=11&&p<=13?y=Math.floor(Math.random()*30)+40:p>=17&&p<=19?y=Math.floor(Math.random()*35)+45:y=Math.floor(Math.random()*15)+5,i.push(y)}const m={tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:e},yAxis:{type:"value"},series:[{name:"订单数",type:"line",data:i,smooth:!0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}},itemStyle:{color:"#409EFF"}}]};u.setOption(m)},W=()=>{O.value&&(f=V(O.value),X())},X=()=>{if(!f)return;const m={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:["红烧肉","宫保鸡丁","清炒时蔬","紫菜蛋花汤","米饭"]},series:[{name:"订单数",type:"bar",data:[156,142,128,98,89],itemStyle:{color:{type:"linear",x:0,y:0,x2:1,y2:0,colorStops:[{offset:0,color:"#409EFF"},{offset:1,color:"#67C23A"}]}}}]};f.setOption(m)},G=e=>A(e).format("MM-DD"),de=e=>{Object.assign(S,e),r.page=1,R()},ce=()=>{Object.keys(S).forEach(e=>{S[e]=""}),r.page=1,R()},ue=e=>{r.page=e,R()},he=e=>{r.size=e,r.page=1,R()},pe=()=>{ne.info("导出功能开发中...")},k=()=>{d&&d.resize(),h&&h.resize(),u&&u.resize(),f&&f.resize()};re(()=>T(this,null,function*(){yield B(),yield R(),oe(()=>{H(),Y(),I(),W()}),window.addEventListener("resize",k)})),se(()=>{window.removeEventListener("resize",k),d&&(d.dispose(),d=null),h&&(h.dispose(),h=null),u&&(u.dispose(),u=null),f&&(f.dispose(),f=null)});const q={trendChartRef:l,statusChartRef:a,timeChartRef:w,hotDishesChartRef:O,trendPeriod:g,tableLoading:v,tableData:c,get trendChart(){return d},set trendChart(e){d=e},get statusChart(){return h},set statusChart(e){h=e},get timeChart(){return u},set timeChart(e){u=e},get hotDishesChart(){return f},set hotDishesChart(e){f=e},pagination:r,searchParams:S,orderStats:C,columns:le,searchFields:ie,loadOrderStats:B,loadTableData:R,generateMockTableData:j,initTrendChart:H,updateTrendChart:N,initStatusChart:Y,initTimeChart:I,refreshTimeChart:U,initHotDishesChart:W,refreshHotDishes:X,formatDate:G,handleSearch:de,handleReset:ce,handleCurrentChange:ue,handleSizeChange:he,handleExport:pe,handleResize:k,ref:x,reactive:E,onMounted:re,nextTick:oe,onUnmounted:se,get echarts(){return Se},get ElMessage(){return ne},get Refresh(){return ye},get Download(){return ve},get ShoppingCart(){return $},get TrendCharts(){return te},get Timer(){return ae},get Wallet(){return ee},StatsCard:we,CustomTable:Me,get orderApi(){return P},get dayjs(){return A}};return Object.defineProperty(q,"__isScriptSetup",{enumerable:!1,value:!0}),q}},Oe={class:"order-statistics"},Re={class:"chart-card"},ze={class:"chart-header"},Te={class:"chart-controls"},Ee={ref:"trendChartRef",class:"chart-container"},Ae={class:"chart-card"},Ve={ref:"statusChartRef",class:"chart-container"},ke={class:"chart-card"},Fe={class:"chart-header"},Le={ref:"timeChartRef",class:"chart-container"},Pe={class:"data-card"},Be={class:"card-header"},je={class:"hot-dishes-chart"},He={ref:"hotDishesChartRef",class:"chart-container"},Ne={class:"table-section"},Ye={class:"revenue-text"},Ie={class:"avg-value"};function Ue(_,t,l,a,w,O){const g=M("el-col"),v=M("el-row"),c=M("el-radio-button"),d=M("el-radio-group"),h=M("el-icon"),u=M("el-button"),f=M("el-tag");return F(),Z("div",Oe,[b(" 统计卡片 "),o(v,{gutter:20,class:"stats-cards"},{default:s(()=>[(F(!0),Z(Ce,null,be(a.orderStats,(r,S)=>(F(),xe(g,{xs:24,sm:12,md:6,key:S},{default:s(()=>[o(a.StatsCard,{title:r.title,value:r.value,icon:r.icon,type:r.type,trend:r.trend,description:r.description,animated:!0},null,8,["title","value","icon","type","trend","description"])]),_:2},1024))),128))]),_:1}),b(" 图表区域 "),o(v,{gutter:20,class:"charts-section"},{default:s(()=>[b(" 订单趋势图 "),o(g,{xs:24,lg:16},{default:s(()=>[n("div",Re,[n("div",ze,[t[4]||(t[4]=n("h3",null,"订单趋势分析",-1)),n("div",Te,[o(d,{modelValue:a.trendPeriod,"onUpdate:modelValue":t[0]||(t[0]=r=>a.trendPeriod=r),size:"small",onChange:a.updateTrendChart},{default:s(()=>[o(c,{label:"7d"},{default:s(()=>t[1]||(t[1]=[D("近7天",-1)])),_:1,__:[1]}),o(c,{label:"30d"},{default:s(()=>t[2]||(t[2]=[D("近30天",-1)])),_:1,__:[2]}),o(c,{label:"90d"},{default:s(()=>t[3]||(t[3]=[D("近90天",-1)])),_:1,__:[3]})]),_:1},8,["modelValue"])])]),n("div",Ee,null,512)])]),_:1}),b(" 订单状态分布 "),o(g,{xs:24,lg:8},{default:s(()=>[n("div",Ae,[t[5]||(t[5]=n("div",{class:"chart-header"},[n("h3",null,"订单状态分布")],-1)),n("div",Ve,null,512)])]),_:1})]),_:1}),b(" 时间分布和热门时段 "),o(v,{gutter:20,class:"analysis-section"},{default:s(()=>[b(" 订单时间分布 "),o(g,{xs:24,lg:12},{default:s(()=>[n("div",ke,[n("div",Fe,[t[7]||(t[7]=n("h3",null,"订单时间分布",-1)),o(u,{size:"small",onClick:a.refreshTimeChart},{default:s(()=>[o(h,null,{default:s(()=>[o(a.Refresh)]),_:1}),t[6]||(t[6]=D(" 刷新 ",-1))]),_:1,__:[6]})]),n("div",Le,null,512)])]),_:1}),b(" 热门菜品订单统计 "),o(g,{xs:24,lg:12},{default:s(()=>[n("div",Pe,[n("div",Be,[t[9]||(t[9]=n("h3",null,"热门菜品订单统计",-1)),o(u,{size:"small",onClick:a.refreshHotDishes},{default:s(()=>[o(h,null,{default:s(()=>[o(a.Refresh)]),_:1}),t[8]||(t[8]=D(" 刷新 ",-1))]),_:1,__:[8]})]),n("div",je,[n("div",He,null,512)])])]),_:1})]),_:1}),b(" 详细数据表格 "),n("div",Ne,[o(a.CustomTable,{title:"订单详细统计",data:a.tableData,columns:a.columns,loading:a.tableLoading,pagination:a.pagination,"show-search":!0,"search-fields":a.searchFields,onSearch:a.handleSearch,onReset:a.handleReset,onCurrentChange:a.handleCurrentChange,onSizeChange:a.handleSizeChange},{actions:s(()=>[o(u,{onClick:a.handleExport},{default:s(()=>[o(h,null,{default:s(()=>[o(a.Download)]),_:1}),t[10]||(t[10]=D(" 导出报表 ",-1))]),_:1,__:[10]})]),orderCount:s(({row:r})=>[o(f,{type:"primary"},{default:s(()=>[D(L(r.orderCount)+"单",1)]),_:2},1024)]),revenue:s(({row:r})=>[n("span",Ye,"¥"+L(r.revenue),1)]),avgOrderValue:s(({row:r})=>[n("span",Ie,"¥"+L(r.avgOrderValue),1)]),_:1},8,["data","loading","pagination"])])])}const Ke=ge(De,[["render",Ue],["__scopeId","data-v-ad0dc82c"],["__file","E:/wx-nan/webs/admin/src/views/order/statistics.vue"]]);export{Ke as default};
