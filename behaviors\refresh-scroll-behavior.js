/**
 * 下拉刷新和上拉加载更多 Behavior
 * 简化页面使用下拉刷新和上拉加载更多的逻辑
 */

const app = getApp();

module.exports = function(options = {}) {
  const {
    listName = 'list',           // 列表数据字段名
    requestFunc,                 // 请求函数
    pageSize = 10,              // 每页数据量
    autoLoad = true,            // 是否自动加载
    transformData,              // 数据转换函数
    onRefreshComplete,          // 刷新完成回调
    onLoadMoreComplete          // 加载更多完成回调
  } = options;

  return Behavior({
    data: {
      // 列表相关
      [listName]: [],
      [`${listName}Loading`]: false,
      [`${listName}Refreshing`]: false,
      [`${listName}HasMore`]: true,
      [`${listName}IsEmpty`]: false,
      
      // 分页参数
      [`${listName}Params`]: {
        page: 1,
        size: pageSize,
        ...options.defaultParams
      }
    },

    methods: {
      /**
       * 重置列表参数
       */
      [`reset${listName.charAt(0).toUpperCase() + listName.slice(1)}Params`](keepData = false) {
        const currentParams = this.data[`${listName}Params`];
        this.setData({
          [`${listName}Loading`]: false,
          [`${listName}Refreshing`]: false,
          [`${listName}HasMore`]: true,
          [`${listName}IsEmpty`]: false,
          [listName]: keepData ? this.data[listName] : [],
          [`${listName}Params`]: {
            ...currentParams,
            page: 1
          }
        });
      },

      /**
       * 获取列表数据
       */
      async [`get${listName.charAt(0).toUpperCase() + listName.slice(1)}Data`](isRefresh = false) {
        if (!requestFunc) {
          console.error('requestFunc is required');
          return;
        }

        const params = this.data[`${listName}Params`];
        const currentList = this.data[listName];

        try {
          this.setData({
            [`${listName}Loading`]: !isRefresh,
            [`${listName}Refreshing`]: isRefresh
          });

          const response = await requestFunc(params);
          
          // 数据转换
          let { list = [], total = 0, hasMore = true } = response;
          if (transformData && typeof transformData === 'function') {
            const transformed = transformData(response);
            list = transformed.list || list;
            total = transformed.total || total;
            hasMore = transformed.hasMore !== undefined ? transformed.hasMore : hasMore;
          }

          // 判断是否还有更多数据
          const noMoreData = list.length < params.size || !hasMore;
          
          // 更新数据
          const newList = isRefresh ? list : [...currentList, ...list];
          
          this.setData({
            [listName]: newList,
            [`${listName}HasMore`]: !noMoreData,
            [`${listName}IsEmpty`]: newList.length === 0,
            [`${listName}Loading`]: false,
            [`${listName}Refreshing`]: false
          });

          // 回调
          if (isRefresh && onRefreshComplete) {
            onRefreshComplete(newList, response);
          } else if (!isRefresh && onLoadMoreComplete) {
            onLoadMoreComplete(newList, response);
          }

          return { list: newList, response };
        } catch (error) {
          console.error(`获取${listName}数据失败:`, error);
          
          this.setData({
            [`${listName}Loading`]: false,
            [`${listName}Refreshing`]: false
          });

          wx.showToast({
            title: '加载失败',
            icon: 'error'
          });

          throw error;
        }
      },

      /**
       * 下拉刷新
       */
      [`on${listName.charAt(0).toUpperCase() + listName.slice(1)}Refresh`]() {
        console.log(`${listName} 下拉刷新`);
        
        // 重置参数
        this[`reset${listName.charAt(0).toUpperCase() + listName.slice(1)}Params`](true);
        
        // 获取数据
        return this[`get${listName.charAt(0).toUpperCase() + listName.slice(1)}Data`](true);
      },

      /**
       * 上拉加载更多
       */
      [`on${listName.charAt(0).toUpperCase() + listName.slice(1)}LoadMore`]() {
        const hasMore = this.data[`${listName}HasMore`];
        const loading = this.data[`${listName}Loading`];
        
        if (!hasMore || loading) {
          console.log(`${listName} 无法加载更多:`, { hasMore, loading });
          return;
        }

        console.log(`${listName} 上拉加载更多`);
        
        // 更新页码
        const currentParams = this.data[`${listName}Params`];
        this.setData({
          [`${listName}Params`]: {
            ...currentParams,
            page: currentParams.page + 1
          }
        });

        // 获取数据
        return this[`get${listName.charAt(0).toUpperCase() + listName.slice(1)}Data`](false);
      },

      /**
       * 停止刷新
       */
      [`stop${listName.charAt(0).toUpperCase() + listName.slice(1)}Refresh`]() {
        // 获取组件实例并停止刷新
        const refreshScrollComponent = this.selectComponent('#refresh-scroll');
        if (refreshScrollComponent) {
          refreshScrollComponent.stopRefresh();
        }
        
        this.setData({
          [`${listName}Refreshing`]: false
        });
      },

      /**
       * 更新列表参数
       */
      [`update${listName.charAt(0).toUpperCase() + listName.slice(1)}Params`](newParams) {
        const currentParams = this.data[`${listName}Params`];
        this.setData({
          [`${listName}Params`]: {
            ...currentParams,
            ...newParams,
            page: 1 // 重置页码
          }
        });
      }
    },

    lifetimes: {
      attached() {
        if (autoLoad) {
          // 自动加载数据
          setTimeout(() => {
            this[`get${listName.charAt(0).toUpperCase() + listName.slice(1)}Data`](true);
          }, 100);
        }
      }
    }
  });
};
