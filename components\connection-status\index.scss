.connection-status {
  padding: 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
  }
  
  // 大小变体
  &--small {
    padding: 16rpx;
    border-radius: 12rpx;
    
    .connection-status__text {
      font-size: 24rpx;
    }
    
    .connection-status__time {
      font-size: 20rpx;
    }
  }
  
  &--large {
    padding: 32rpx;
    border-radius: 20rpx;
    
    .connection-status__text {
      font-size: 32rpx;
      font-weight: 600;
    }
    
    .connection-status__time {
      font-size: 26rpx;
    }
  }
}

.connection-status__main {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.connection-status__icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.connection-status__content {
  flex: 1;
}

.connection-status__text {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.connection-status__time {
  font-size: 22rpx;
  color: #969799;
  line-height: 1.2;
}

.connection-status__progress {
  position: relative;
}

.connection-status__progress-track {
  height: 6rpx;
  background: #f2f3f5;
  border-radius: 3rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.connection-status__progress-fill {
  height: 100%;
  border-radius: 3rpx;
  transition: width 0.6s ease;
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20rpx;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3));
    animation: shimmer 2s infinite;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.connection-status__steps {
  display: flex;
  justify-content: space-between;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 24rpx;
    right: 24rpx;
    height: 2rpx;
    background: #f2f3f5;
    transform: translateY(-50%);
    z-index: 1;
  }
}

.connection-status__step {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: #fff;
  border: 3rpx solid #c8c9cc;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  
  &.active {
    border-width: 4rpx;
    transform: scale(1.1);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  }
  
  &.rejected {
    border-color: #ee0a24;
    background: rgba(238, 10, 36, 0.1);
  }
}

// 状态特定样式
.connection-status {
  &[data-status="pending"] {
    border-left: 6rpx solid #ff9500;
    
    .connection-status__icon {
      background: rgba(255, 149, 0, 0.1);
    }
  }
  
  &[data-status="accepted"] {
    border-left: 6rpx solid #07c160;
    
    .connection-status__icon {
      background: rgba(7, 193, 96, 0.1);
    }
  }
  
  &[data-status="rejected"] {
    border-left: 6rpx solid #ee0a24;
    
    .connection-status__icon {
      background: rgba(238, 10, 36, 0.1);
    }
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .connection-status {
    padding: 20rpx;
    
    &--large {
      padding: 28rpx;
    }
  }
  
  .connection-status__icon {
    width: 48rpx;
    height: 48rpx;
  }
  
  .connection-status__text {
    font-size: 26rpx;
  }
  
  .connection-status__time {
    font-size: 20rpx;
  }
}
