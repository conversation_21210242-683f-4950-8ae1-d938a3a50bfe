<template>
  <div class="space-y-1">
    <label 
      v-if="label" 
      :for="inputId"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {{ label }}
      <span v-if="required" class="text-error-500 ml-1">*</span>
    </label>
    
    <div class="relative">
      <!-- 前置图标 -->
      <div 
        v-if="prefixIcon" 
        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
      >
        <component 
          :is="prefixIcon" 
          class="h-5 w-5 text-gray-400" 
          aria-hidden="true" 
        />
      </div>
      
      <!-- 输入框 -->
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        v-bind="$attrs"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <!-- 后置图标 -->
      <div 
        v-if="suffixIcon" 
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
        :class="{ 'pointer-events-none': !suffixClickable }"
        @click="handleSuffixClick"
      >
        <component 
          :is="suffixIcon" 
          class="h-5 w-5 text-gray-400" 
          :class="{ 'cursor-pointer hover:text-gray-600': suffixClickable }"
          aria-hidden="true" 
        />
      </div>
      
      <!-- 清除按钮 -->
      <div 
        v-if="clearable && modelValue && !disabled && !readonly"
        class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
        @click="handleClear"
      >
        <XMarkIcon class="h-4 w-4 text-gray-400 hover:text-gray-600" />
      </div>
    </div>
    
    <!-- 帮助文本 -->
    <p 
      v-if="helpText" 
      class="text-sm text-gray-500 dark:text-gray-400"
    >
      {{ helpText }}
    </p>
    
    <!-- 错误信息 -->
    <p 
      v-if="error" 
      class="text-sm text-error-600 dark:text-error-400"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'filled', 'outlined'].includes(value)
  },
  prefixIcon: {
    type: [Object, Function],
    default: null
  },
  suffixIcon: {
    type: [Object, Function],
    default: null
  },
  suffixClickable: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus', 'clear', 'suffix-click'])

const inputId = ref(`input-${Math.random().toString(36).substr(2, 9)}`)

const inputClasses = computed(() => {
  const baseClasses = [
    'block w-full rounded-lg border-0 shadow-sm ring-1 ring-inset transition-colors duration-200',
    'placeholder:text-gray-400 focus:ring-2 focus:ring-inset',
    'disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200',
    'dark:bg-gray-900 dark:text-white dark:ring-gray-700 dark:placeholder:text-gray-500',
    'dark:focus:ring-primary-500 dark:disabled:bg-gray-800 dark:disabled:text-gray-400'
  ]

  // 尺寸样式
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  // 变体样式
  const variantClasses = {
    default: [
      'bg-white ring-gray-300 focus:ring-primary-600',
      props.error ? 'ring-error-300 focus:ring-error-600' : ''
    ],
    filled: [
      'bg-gray-50 ring-gray-200 focus:ring-primary-600 focus:bg-white',
      props.error ? 'ring-error-300 focus:ring-error-600' : ''
    ],
    outlined: [
      'bg-transparent ring-gray-300 focus:ring-primary-600',
      props.error ? 'ring-error-300 focus:ring-error-600' : ''
    ]
  }

  // 图标间距
  const iconClasses = []
  if (props.prefixIcon) {
    iconClasses.push('pl-10')
  }
  if (props.suffixIcon || props.clearable) {
    iconClasses.push('pr-10')
  }

  return [
    ...baseClasses,
    sizeClasses[props.size],
    ...variantClasses[props.variant],
    ...iconClasses
  ].filter(Boolean).join(' ')
})

const handleInput = (event) => {
  emit('update:modelValue', event.target.value)
}

const handleBlur = (event) => {
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}

const handleSuffixClick = () => {
  if (props.suffixClickable) {
    emit('suffix-click')
  }
}
</script>
