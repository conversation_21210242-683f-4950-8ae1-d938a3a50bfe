/**
 * API 服务类
 * 封装所有 API 请求
 */

const {get, post, put, del} = require('../utils/request');

// 导入统一的环境配置
const {baseURL, getApiUrl, getApiUrlWithParams} = require('../config/env');

/**
 * 用户相关 API
 */
const userApi = {
  // 登录
  login: data => post('/auth/login', data),

  // 注册
  register: data => post('/auth/register', data),

  // 获取用户信息
  getUserInfo: id => get(`/users/${id}`),

  // 获取家庭成员列表
  getFamilyMembers: () => get('/users/family'),

  // 更新用户信息
  updateUserInfo: (id, data) => put(`/users/${id}`, data)
};

/**
 * 菜单相关 API
 */
const menuApi = {
  // 获取菜品类别
  getCategories: () => get('/menus/categories'),

  // 获取所有菜品（按分类）
  getDishes: () => get('/dishes/by-category'),

  // 获取今日菜单
  getTodayMenu: () => get('/menus/today'),

  // 获取今日菜单（支持用户过滤）
  getTodayMenus: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/menus/today${queryString}`);
  },

  // 获取历史菜单
  getHistoryMenus: () => get('/menus/history'),

  // 获取历史菜单（支持分页）
  getHistoryMenusWithPaging: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/menus/history${queryString}`);
  },

  // 获取推荐菜单
  getRecommendedMenu: () => get('/menus/recommended'),

  // 获取统计信息
  getStatistics: () => get('/menus/statistics'),

  // 创建菜单
  createMenu: data => post('/menus', data),

  // 更新菜单
  updateMenu: (id, data) => put(`/menus/${id}`, data),

  // 更新菜单中的菜品
  updateMenuDishes: (id, data) => put(`/menus/${id}/dishes`, data),

  // 获取首页菜单数据（自己和关联用户的今日菜单，或热门菜品）
  getHomeMenus: () => get('/menus/home')
};

/**
 * 订单相关 API
 */
const orderApi = {
  // 获取所有订单
  getOrders: () => get('/orders'),

  // 获取今日订单
  getTodayOrders: () => get('/orders/today'),

  // 获取指定订单
  getOrder: id => get(`/orders/${id}`),

  // 创建订单
  createOrder: data => post('/orders', data),

  // 创建订单并推送
  createOrderAndPush: data => post('/order-push/create-and-push', data),

  // 获取可见订单
  getVisibleOrders: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/order-push/visible${queryString}`);
  },

  // 更新订单
  updateOrder: (id, data) => put(`/orders/${id}`, data),

  // 删除订单
  deleteOrder: id => del(`/orders/${id}`)
};

/**
 * 消息相关 API
 */
const messageApi = {
  // 获取所有消息（支持分页和排序）
  getMessages: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/messages${queryString}`);
  },

  // 获取指定消息
  getMessage: id => get(`/messages/${id}`),

  // 创建消息
  createMessage: data => post('/messages', data),

  // 更新消息
  updateMessage: (id, data) => put(`/messages/${id}`, data),

  // 删除消息
  deleteMessage: id => del(`/messages/${id}`)
};

/**
 * 通知相关 API
 */
const notificationApi = {
  // 获取所有通知（支持分页和筛选）
  getNotifications: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/notifications${queryString}`);
  },

  // 获取指定通知
  getNotification: id => get(`/notifications/${id}`),

  // 创建通知
  createNotification: data => post('/notifications', data),

  // 更新通知
  updateNotification: (id, data) => put(`/notifications/${id}`, data),

  // 标记通知为已读
  markAsRead: id => put(`/notifications/${id}/read`, {}),

  // 全部标记为已读
  markAllRead: () => put('/notifications/read-all', {}),

  // 删除通知
  deleteNotification: id => del(`/notifications/${id}`)
};

/**
 * 菜品相关 API
 */
const dishApi = {
  // 获取菜品详情（完整数据，用于编辑）
  getDishDetail: id => get(`/dishes/${id}`),

  // 获取菜品详情（小程序格式，用于展示）
  getDishDetailForDisplay: id => get(`/dishes/${id}/detail`),

  // 获取所有菜品
  getDishes: () => get('/dishes'),

  // 获取菜品分类
  getCategories: () => get('/dishes/categories'),

  // 获取分类列表（小程序专用）
  getCategoriesForMiniProgram: () => get('/dishes/categories/miniprogram'),

  // 获取我的菜品列表
  getMyDishes: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/dishes/my${queryString}`);
  },

  // 获取分类菜品
  getDishesByCategory: () => get('/dishes/by-category'),

  // 创建菜品
  createDish: data => post('/dishes', data),

  // 更新菜品
  updateDish: (id, data) => put(`/dishes/${id}`, data),

  // 更新菜品上架状态
  updateDishStatus: (id, data) => put(`/dishes/${id}/status`, data),

  // 获取菜品影响分析
  getDishImpact: id => get(`/dishes/${id}/impact`),

  // 删除菜品
  deleteDish: (id, force = false) =>
    del(`/dishes/${id}${force ? '?force=true' : ''}`),

  // 获取菜品统计
  getDishStatistics: () => get('/dishes/statistics')
};

/**
 * 用户关联相关 API
 */
const connectionApi = {
  // 获取可关联的用户列表
  getAvailableUsers: (params = {}) => {
    const queryString =
      Object.keys(params).length > 0
        ? '?' +
          Object.keys(params)
            .map(key => `${key}=${params[key]}`)
            .join('&')
        : '';
    return get(`/connections/users${queryString}`);
  },

  // 发送关联申请
  sendConnectionRequest: data => post('/connections/request', data),

  // 处理关联申请（同意/拒绝）
  respondToConnection: (id, action) =>
    put(`/connections/${id}/respond`, {
      action
    }),

  // 获取我的关联列表
  getMyConnections: (status = 'accepted') =>
    get(`/connections/my?status=${status}`),

  // 取消关联关系
  removeConnection: id => del(`/connections/${id}`),

  // 更新关联备注
  updateConnection: (id, data) => put(`/connections/${id}/update`, data),

  // 获取关联历史记录
  getConnectionHistory: (params = {}) => {
    // 过滤掉undefined值
    const filteredParams = {};
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        filteredParams[key] = params[key];
      }
    });

    const queryString =
      Object.keys(filteredParams).length > 0
        ? '?' +
          Object.keys(filteredParams)
            .map(key => `${key}=${encodeURIComponent(filteredParams[key])}`)
            .join('&')
        : '';
    return get(`/connections/history${queryString}`);
  },

  // 获取关联统计信息
  getConnectionStatistics: () => get('/connections/statistics')
};

/**
 * 订阅通知相关 API
 */
const subscriptionApi = {
  // 发送订单通知
  sendOrderNotification: data =>
    post('/subscriptions/order-notification', data),

  // 发送菜单更新通知
  sendMenuNotification: data => post('/subscriptions/menu-notification', data),

  // 测试订阅消息
  testSubscriptionMessage: data => post('/subscriptions/test', data)
};

/**
 * 文件上传相关 API
 */
const uploadApi = {
  // 上传图片
  uploadImage(filePath, options = {}) {
    return new Promise((resolve, reject) => {
      const token = wx.getStorageSync('token');
      if (!token) {
        reject(new Error('未登录'));
        return;
      }

      // 构建表单数据
      const formData = {
        type: options.type || 'general', // 图片类型：avatar, menu, general
        entityId: options.entityId || '', // 实体ID：用户ID或菜单ID
        category: options.category || '' // 分类信息
      };

      wx.uploadFile({
        url: getApiUrl('upload.image'),
        filePath: filePath,
        name: 'image',
        formData: formData,
        header: {
          Authorization: `Bearer ${token}`
        },
        success: res => {
          try {
            const data = JSON.parse(res.data);
            // 检查HTTP状态码和响应码
            if (
              res.statusCode >= 200 &&
              res.statusCode < 300 &&
              data.code >= 200 &&
              data.code < 300
            ) {
              resolve(data);
            } else {
              reject(new Error(data.message || '上传失败'));
            }
          } catch (e) {
            reject(new Error('响应解析失败'));
          }
        },
        fail: error => {
          reject(error);
        }
      });
    });
  },

  // 上传头像（便捷方法）
  uploadAvatar(filePath, userId) {
    return this.uploadImage(filePath, {
      type: 'avatar',
      entityId: userId,
      category: 'user'
    });
  },

  // 上传菜单图片（便捷方法）
  uploadMenuImage(filePath, menuId) {
    return this.uploadImage(filePath, {
      type: 'menu',
      entityId: menuId,
      category: 'food'
    });
  },

  // 删除图片
  deleteImage: imageUrl =>
    del('/upload/image', {
      imageUrl
    })
};

module.exports = {
  userApi,
  menuApi,
  orderApi,
  messageApi,
  notificationApi,
  dishApi,
  connectionApi,
  subscriptionApi,
  uploadApi
};
