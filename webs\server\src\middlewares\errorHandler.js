/**
 * 错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  // 检查是否是 Prisma 错误
  if (err.code && err.code.startsWith('P')) {
    // Prisma 错误
    switch (err.code) {
      case 'P2002': // 唯一约束错误
        return res.status(409).json({
          code: 409,
          message: 'Resource already exists',
          data: {field: err.meta?.target?.[0]}
        });
      case 'P2025': // 记录不存在
        return res.status(404).json({
          code: 404,
          message: 'Resource not found',
          data: null
        });
      default:
        return res.status(500).json({
          code: 500,
          message: 'Database error',
          data: null
        });
    }
  }

  // 默认错误响应
  res.status(err.statusCode || 500).json({
    code: err.statusCode || 500,
    message: err.message || 'Internal server error',
    data: null
  });
};

module.exports = errorHandler;
