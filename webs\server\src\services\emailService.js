/**
 * 邮件服务
 * 用于发送各种类型的邮件
 */

const nodemailer = require('nodemailer');

// 简单的内存存储，用于验证码和重置token
// 生产环境建议使用 Redis
const verificationCodes = new Map();
const resetTokens = new Map();

// 创建邮件传输器
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: 'smtp.qq.com',
    port: 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: '<EMAIL>',
      pass: 'ernbgewebsmrdhif' // QQ邮箱授权码
    }
  });
};

/**
 * 生成验证码
 * @param {number} length - 验证码长度
 * @returns {string} 验证码
 */
const generateCode = (length = 6) => {
  const chars = '0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 生成重置token
 * @returns {string} 重置token
 */
const generateResetToken = () => {
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
};

/**
 * 发送邮箱验证码
 * @param {string} email - 邮箱地址
 * @param {string} type - 验证码类型 (register, reset-password)
 * @returns {Promise<Object>} 发送结果
 */
const sendEmailCode = async (email, type = 'register') => {
  try {
    const code = generateCode(6);
    const key = `${email}_${type}`;

    // 存储验证码，5分钟过期
    verificationCodes.set(key, {
      code,
      expiresAt: Date.now() + 5 * 60 * 1000,
      attempts: 0
    });

    // 创建邮件传输器
    const transporter = createTransporter();

    // 根据类型设置邮件标题和内容
    let subject, content;
    if (type === 'reset-password') {
      subject = '密码重置验证码 - 楠楠家厨管理系统';
      content = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #409eff; margin: 0;">楠楠家厨管理系统</h1>
            <p style="color: #666; margin: 10px 0;">密码重置验证码</p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #333; margin-top: 0;">您好！</h2>
            <p style="color: #666; line-height: 1.6;">
              您正在申请重置密码，请使用以下验证码完成操作：
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <div style="background: #409eff; color: white; padding: 15px 30px; border-radius: 8px; display: inline-block; font-size: 24px; font-weight: bold; letter-spacing: 5px;">
                ${code}
              </div>
            </div>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <p style="color: #856404; margin: 0; font-size: 14px;">
                <strong>安全提示：</strong><br>
                • 验证码5分钟内有效<br>
                • 如果您没有申请重置密码，请忽略此邮件<br>
                • 请勿将验证码告诉他人
              </p>
            </div>
          </div>

          <div style="text-align: center; color: #999; font-size: 12px; border-top: 1px solid #eee; padding-top: 20px;">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>&copy; 2024 楠楠家厨管理系统. All rights reserved.</p>
          </div>
        </div>
      `;
    } else {
      subject = '注册验证码 - 楠楠家厨管理系统';
      content = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #409eff; margin: 0;">楠楠家厨管理系统</h1>
            <p style="color: #666; margin: 10px 0;">注册验证码</p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #333; margin-top: 0;">欢迎注册！</h2>
            <p style="color: #666; line-height: 1.6;">
              感谢您注册楠楠家厨管理系统，请使用以下验证码完成注册：
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <div style="background: #409eff; color: white; padding: 15px 30px; border-radius: 8px; display: inline-block; font-size: 24px; font-weight: bold; letter-spacing: 5px;">
                ${code}
              </div>
            </div>

            <div style="background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <p style="color: #0c5460; margin: 0; font-size: 14px;">
                <strong>提示：</strong><br>
                • 验证码5分钟内有效<br>
                • 请在注册页面输入此验证码
              </p>
            </div>
          </div>

          <div style="text-align: center; color: #999; font-size: 12px; border-top: 1px solid #eee; padding-top: 20px;">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>&copy; 2024 楠楠家厨管理系统. All rights reserved.</p>
          </div>
        </div>
      `;
    }

    // 邮件选项
    const mailOptions = {
      from: '"楠楠家厨管理系统" <<EMAIL>>',
      to: email,
      subject: subject,
      html: content
    };

    // 发送邮件
    console.log(
      `[邮件服务] 正在发送验证码到 ${email}: ${code} (类型: ${type})`
    );
    await transporter.sendMail(mailOptions);
    console.log(`[邮件服务] 验证码邮件发送成功: ${email}`);

    return {
      success: true,
      message: '验证码已发送到您的邮箱'
    };
  } catch (error) {
    console.error('发送邮箱验证码失败:', error);
    return {
      success: false,
      message: '发送验证码失败，请重试'
    };
  }
};

/**
 * 验证邮箱验证码
 * @param {string} email - 邮箱地址
 * @param {string} code - 验证码
 * @param {string} type - 验证码类型
 * @returns {Object} 验证结果
 */
const verifyEmailCode = (email, code, type = 'register') => {
  const key = `${email}_${type}`;
  const stored = verificationCodes.get(key);

  if (!stored) {
    return {
      success: false,
      message: '验证码不存在或已过期'
    };
  }

  // 检查过期时间
  if (Date.now() > stored.expiresAt) {
    verificationCodes.delete(key);
    return {
      success: false,
      message: '验证码已过期'
    };
  }

  // 检查尝试次数
  if (stored.attempts >= 3) {
    verificationCodes.delete(key);
    return {
      success: false,
      message: '验证码尝试次数过多，请重新获取'
    };
  }

  // 验证码错误
  if (stored.code !== code) {
    stored.attempts++;
    return {
      success: false,
      message: '验证码错误'
    };
  }

  // 验证成功，删除验证码
  verificationCodes.delete(key);
  return {
    success: true,
    message: '验证码验证成功'
  };
};

/**
 * 发送重置密码邮件
 * @param {string} email - 邮箱地址
 * @param {string} userName - 用户名
 * @returns {Promise<Object>} 发送结果
 */
const sendResetPasswordEmail = async (email, userName = '用户') => {
  try {
    const resetToken = generateResetToken();

    // 存储重置token，30分钟过期
    resetTokens.set(resetToken, {
      email,
      expiresAt: Date.now() + 30 * 60 * 1000
    });

    // 构建重置链接
    const resetUrl = `${
      process.env.FRONTEND_URL || 'http://localhost:8848'
    }/reset-password?token=${resetToken}`;

    // 创建邮件传输器
    const transporter = createTransporter();

    // 邮件内容
    const mailOptions = {
      from: '"楠楠家厨管理系统" <<EMAIL>>',
      to: email,
      subject: '重置密码 - 楠楠家厨管理系统',
      html: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #409eff; margin: 0;">楠楠家厨管理系统</h1>
            <p style="color: #666; margin: 10px 0;">密码重置请求</p>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #333; margin-top: 0;">您好，${userName}！</h2>
            <p style="color: #666; line-height: 1.6;">
              我们收到了您的密码重置请求。如果这是您本人的操作，请点击下面的按钮重置密码：
            </p>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}"
                 style="background: #409eff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                重置密码
              </a>
            </div>

            <p style="color: #666; line-height: 1.6;">
              如果按钮无法点击，请复制以下链接到浏览器地址栏：<br>
              <a href="${resetUrl}" style="color: #409eff; word-break: break-all;">${resetUrl}</a>
            </p>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <p style="color: #856404; margin: 0; font-size: 14px;">
                <strong>安全提示：</strong><br>
                • 此链接将在30分钟后失效<br>
                • 如果您没有请求重置密码，请忽略此邮件<br>
                • 请勿将此链接分享给他人
              </p>
            </div>
          </div>

          <div style="text-align: center; color: #999; font-size: 12px; border-top: 1px solid #eee; padding-top: 20px;">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>&copy; 2024 楠楠家厨管理系统. All rights reserved.</p>
          </div>
        </div>
      `
    };

    // 发送邮件
    console.log(`[邮件服务] 正在发送重置密码邮件到 ${email}`);
    await transporter.sendMail(mailOptions);
    console.log(`[邮件服务] 重置密码邮件发送成功: ${email}`);

    return {
      success: true,
      message: '重置密码邮件已发送',
      resetToken // 开发环境返回token，生产环境不应该返回
    };
  } catch (error) {
    console.error('发送重置密码邮件失败:', error);
    return {
      success: false,
      message: '发送邮件失败，请重试'
    };
  }
};

/**
 * 验证重置密码token
 * @param {string} token - 重置token
 * @returns {Object} 验证结果
 */
const verifyResetPasswordToken = token => {
  const stored = resetTokens.get(token);

  if (!stored) {
    return {
      success: false,
      message: '重置链接无效或已过期'
    };
  }

  // 检查过期时间
  if (Date.now() > stored.expiresAt) {
    resetTokens.delete(token);
    return {
      success: false,
      message: '重置链接已过期'
    };
  }

  return {
    success: true,
    email: stored.email,
    message: '重置链接验证成功'
  };
};

/**
 * 使用重置token（验证后删除）
 * @param {string} token - 重置token
 * @returns {Object} 验证结果
 */
const useResetPasswordToken = token => {
  const result = verifyResetPasswordToken(token);

  if (result.success) {
    // 使用后删除token
    resetTokens.delete(token);
  }

  return result;
};

/**
 * 清理过期的验证码和token
 */
const cleanupExpired = () => {
  const now = Date.now();

  // 清理过期的验证码
  for (const [key, value] of verificationCodes.entries()) {
    if (now > value.expiresAt) {
      verificationCodes.delete(key);
    }
  }

  // 清理过期的重置token
  for (const [key, value] of resetTokens.entries()) {
    if (now > value.expiresAt) {
      resetTokens.delete(key);
    }
  }
};

// 每5分钟清理一次过期数据
setInterval(cleanupExpired, 5 * 60 * 1000);

module.exports = {
  sendEmailCode,
  verifyEmailCode,
  sendResetPasswordEmail,
  verifyResetPasswordToken,
  useResetPasswordToken,
  cleanupExpired
};
