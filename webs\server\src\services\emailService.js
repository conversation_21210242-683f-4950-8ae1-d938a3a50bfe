/**
 * 邮件服务
 * 用于发送各种类型的邮件
 */

// 简单的内存存储，用于验证码和重置token
// 生产环境建议使用 Redis
const verificationCodes = new Map();
const resetTokens = new Map();

/**
 * 生成验证码
 * @param {number} length - 验证码长度
 * @returns {string} 验证码
 */
const generateCode = (length = 6) => {
  const chars = '0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * 生成重置token
 * @returns {string} 重置token
 */
const generateResetToken = () => {
  const crypto = require('crypto');
  return crypto.randomBytes(32).toString('hex');
};

/**
 * 发送邮箱验证码
 * @param {string} email - 邮箱地址
 * @param {string} type - 验证码类型 (register, reset-password)
 * @returns {Promise<Object>} 发送结果
 */
const sendEmailCode = async (email, type = 'register') => {
  try {
    const code = generateCode(6);
    const key = `${email}_${type}`;
    
    // 存储验证码，5分钟过期
    verificationCodes.set(key, {
      code,
      expiresAt: Date.now() + 5 * 60 * 1000,
      attempts: 0
    });
    
    // 模拟发送邮件
    console.log(`[邮件服务] 发送验证码到 ${email}: ${code} (类型: ${type})`);
    
    // 这里应该集成真实的邮件服务，如 SendGrid, AWS SES 等
    // 目前只是模拟发送
    
    return {
      success: true,
      message: '验证码已发送到您的邮箱'
    };
  } catch (error) {
    console.error('发送邮箱验证码失败:', error);
    return {
      success: false,
      message: '发送验证码失败，请重试'
    };
  }
};

/**
 * 验证邮箱验证码
 * @param {string} email - 邮箱地址
 * @param {string} code - 验证码
 * @param {string} type - 验证码类型
 * @returns {Object} 验证结果
 */
const verifyEmailCode = (email, code, type = 'register') => {
  const key = `${email}_${type}`;
  const stored = verificationCodes.get(key);
  
  if (!stored) {
    return {
      success: false,
      message: '验证码不存在或已过期'
    };
  }
  
  // 检查过期时间
  if (Date.now() > stored.expiresAt) {
    verificationCodes.delete(key);
    return {
      success: false,
      message: '验证码已过期'
    };
  }
  
  // 检查尝试次数
  if (stored.attempts >= 3) {
    verificationCodes.delete(key);
    return {
      success: false,
      message: '验证码尝试次数过多，请重新获取'
    };
  }
  
  // 验证码错误
  if (stored.code !== code) {
    stored.attempts++;
    return {
      success: false,
      message: '验证码错误'
    };
  }
  
  // 验证成功，删除验证码
  verificationCodes.delete(key);
  return {
    success: true,
    message: '验证码验证成功'
  };
};

/**
 * 发送重置密码邮件
 * @param {string} email - 邮箱地址
 * @param {string} userName - 用户名
 * @returns {Promise<Object>} 发送结果
 */
const sendResetPasswordEmail = async (email, userName = '用户') => {
  try {
    const resetToken = generateResetToken();
    
    // 存储重置token，30分钟过期
    resetTokens.set(resetToken, {
      email,
      expiresAt: Date.now() + 30 * 60 * 1000
    });
    
    // 构建重置链接
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:8848'}/reset-password?token=${resetToken}`;
    
    // 模拟发送邮件
    console.log(`[邮件服务] 发送重置密码邮件到 ${email}`);
    console.log(`重置链接: ${resetUrl}`);
    console.log(`重置token: ${resetToken}`);
    
    // 这里应该发送包含重置链接的邮件
    // 邮件内容应该包含用户名、重置链接、过期时间等信息
    
    return {
      success: true,
      message: '重置密码邮件已发送',
      resetToken // 开发环境返回token，生产环境不应该返回
    };
  } catch (error) {
    console.error('发送重置密码邮件失败:', error);
    return {
      success: false,
      message: '发送邮件失败，请重试'
    };
  }
};

/**
 * 验证重置密码token
 * @param {string} token - 重置token
 * @returns {Object} 验证结果
 */
const verifyResetPasswordToken = (token) => {
  const stored = resetTokens.get(token);
  
  if (!stored) {
    return {
      success: false,
      message: '重置链接无效或已过期'
    };
  }
  
  // 检查过期时间
  if (Date.now() > stored.expiresAt) {
    resetTokens.delete(token);
    return {
      success: false,
      message: '重置链接已过期'
    };
  }
  
  return {
    success: true,
    email: stored.email,
    message: '重置链接验证成功'
  };
};

/**
 * 使用重置token（验证后删除）
 * @param {string} token - 重置token
 * @returns {Object} 验证结果
 */
const useResetPasswordToken = (token) => {
  const result = verifyResetPasswordToken(token);
  
  if (result.success) {
    // 使用后删除token
    resetTokens.delete(token);
  }
  
  return result;
};

/**
 * 清理过期的验证码和token
 */
const cleanupExpired = () => {
  const now = Date.now();
  
  // 清理过期的验证码
  for (const [key, value] of verificationCodes.entries()) {
    if (now > value.expiresAt) {
      verificationCodes.delete(key);
    }
  }
  
  // 清理过期的重置token
  for (const [key, value] of resetTokens.entries()) {
    if (now > value.expiresAt) {
      resetTokens.delete(key);
    }
  }
};

// 每5分钟清理一次过期数据
setInterval(cleanupExpired, 5 * 60 * 1000);

module.exports = {
  sendEmailCode,
  verifyEmailCode,
  sendResetPasswordEmail,
  verifyResetPasswordToken,
  useResetPasswordToken,
  cleanupExpired
};
