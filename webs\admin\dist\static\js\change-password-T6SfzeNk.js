var E=(P,e,u)=>new Promise((r,m)=>{var g=o=>{try{t(u.next(o))}catch(c){m(c)}},l=o=>{try{t(u.throw(o))}catch(c){m(c)}},t=o=>o.done?r(o.value):Promise.resolve(o.value).then(g,l);t((u=u.apply(P,e)).next())});import{_ as N,r as f,c as S,d as a,b as U,w as n,g as v,f as F,u as V,h as C,v as b,as as k,k as B,E as _,l as L,a as s,m as R}from"./index-XtNpSMFt.js";const M={__name:"change-password",setup(P,{expose:e}){e();const u=v(),r=v(!1),m=F(),g=V(),l=C({currentPassword:"",newPassword:"",confirmPassword:""}),t=(i,w,d)=>{w?d():d(new Error("请输入当前密码"))},o=(i,w,d)=>{w?w.length<6||w.length>20?d(new Error("密码长度在 6 到 20 个字符")):w===l.currentPassword?d(new Error("新密码不能与当前密码相同")):d():d(new Error("请输入新密码"))},c=(i,w,d)=>{w?w!==l.newPassword?d(new Error("两次输入的密码不一致")):d():d(new Error("请确认新密码"))},y={changePasswordFormRef:u,loading:r,userStore:m,router:g,changePasswordForm:l,validateCurrentPassword:t,validateNewPassword:o,validateConfirmPassword:c,changePasswordRules:{currentPassword:[{validator:t,trigger:"blur"}],newPassword:[{validator:o,trigger:"blur"}],confirmPassword:[{validator:c,trigger:"blur"}]},handleChangePassword:()=>E(this,null,function*(){try{yield u.value.validate(),yield B.confirm("修改密码后需要重新登录，确定要继续吗？","确认修改",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),r.value=!0;try{const i=yield b.changePassword({currentPassword:l.currentPassword,newPassword:l.newPassword});i.code===200?(_.success("密码修改成功！请重新登录"),m.logout(),setTimeout(()=>{g.push("/login")},1500)):_.error(i.message||"修改密码失败")}catch(i){console.error("修改密码API调用失败:",i),_.error("修改密码失败，请检查当前密码是否正确")}}catch(i){i!=="cancel"&&(console.error("修改密码失败:",i),_.error("修改密码失败，请检查当前密码是否正确"))}finally{r.value=!1}}),resetForm:()=>{u.value.resetFields()},ref:v,reactive:C,get ElMessage(){return _},get ElMessageBox(){return B},get Lock(){return k},get authApi(){return b},get useUserStore(){return F},get useRouter(){return V}};return Object.defineProperty(y,"__isScriptSetup",{enumerable:!1,value:!0}),y}},T={class:"change-password-page"},A={class:"form-container"},I={class:"security-tips"};function j(P,e,u,r,m,g){const l=f("el-input"),t=f("el-form-item"),o=f("el-button"),c=f("el-form"),h=f("el-card"),x=f("el-alert");return L(),S("div",T,[a(h,null,{header:n(()=>e[3]||(e[3]=[s("div",{class:"card-header"},[s("h3",null,"修改密码"),s("p",null,"为了账户安全，建议定期更换密码")],-1)])),default:n(()=>[s("div",A,[a(c,{ref:"changePasswordFormRef",model:r.changePasswordForm,rules:r.changePasswordRules,"label-width":"100px",size:"large",style:{"max-width":"500px"}},{default:n(()=>[a(t,{label:"当前密码",prop:"currentPassword"},{default:n(()=>[a(l,{modelValue:r.changePasswordForm.currentPassword,"onUpdate:modelValue":e[0]||(e[0]=p=>r.changePasswordForm.currentPassword=p),type:"password",placeholder:"请输入当前密码","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),a(t,{label:"新密码",prop:"newPassword"},{default:n(()=>[a(l,{modelValue:r.changePasswordForm.newPassword,"onUpdate:modelValue":e[1]||(e[1]=p=>r.changePasswordForm.newPassword=p),type:"password",placeholder:"请输入新密码","prefix-icon":"Lock","show-password":""},null,8,["modelValue"]),e[4]||(e[4]=s("div",{class:"password-tips"},[s("p",null,"密码要求："),s("ul",null,[s("li",null,"长度在 6-20 个字符之间"),s("li",null,"建议包含字母、数字和特殊字符"),s("li",null,"不能与当前密码相同")])],-1))]),_:1,__:[4]}),a(t,{label:"确认密码",prop:"confirmPassword"},{default:n(()=>[a(l,{modelValue:r.changePasswordForm.confirmPassword,"onUpdate:modelValue":e[2]||(e[2]=p=>r.changePasswordForm.confirmPassword=p),type:"password",placeholder:"请再次输入新密码","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),a(t,null,{default:n(()=>[a(o,{type:"primary",loading:r.loading,onClick:r.handleChangePassword},{default:n(()=>e[5]||(e[5]=[R(" 修改密码 ",-1)])),_:1,__:[5]},8,["loading"]),a(o,{onClick:r.resetForm},{default:n(()=>e[6]||(e[6]=[R(" 重置 ",-1)])),_:1,__:[6]})]),_:1})]),_:1},8,["model"])])]),_:1}),U(" 安全提示 "),a(h,{style:{"margin-top":"20px"}},{header:n(()=>e[7]||(e[7]=[s("h4",null,"安全提示",-1)])),default:n(()=>[s("div",I,[a(x,{title:"密码安全建议",type:"info",closable:!1,"show-icon":""},{default:n(()=>e[8]||(e[8]=[s("ul",null,[s("li",null,"定期更换密码，建议每3-6个月更换一次"),s("li",null,"不要使用过于简单的密码，如生日、姓名等"),s("li",null,"不要在多个平台使用相同密码"),s("li",null,"如发现账户异常，请立即修改密码")],-1)])),_:1,__:[8]})])]),_:1})])}const q=N(M,[["render",j],["__scopeId","data-v-27a2246b"],["__file","E:/wx-nan/webs/admin/src/views/system/change-password.vue"]]);export{q as default};
