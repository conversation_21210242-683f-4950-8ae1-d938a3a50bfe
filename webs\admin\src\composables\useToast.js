import { createApp, ref } from 'vue'
import BaseToast from '@/components/ui/BaseToast.vue'

// 全局 Toast 实例管理
const toasts = ref([])
let toastId = 0

/**
 * Toast 服务
 */
class ToastService {
  constructor() {
    this.container = null
    this.init()
  }

  init() {
    // 创建 Toast 容器
    if (typeof document !== 'undefined') {
      this.container = document.createElement('div')
      this.container.id = 'toast-container'
      document.body.appendChild(this.container)
    }
  }

  /**
   * 显示 Toast
   * @param {Object} options - Toast 配置
   */
  show(options) {
    const id = ++toastId
    const toastOptions = {
      id,
      type: 'info',
      position: 'top-right',
      duration: 4000,
      closable: true,
      ...options
    }

    // 创建 Toast 组件实例
    const toastApp = createApp(BaseToast, {
      ...toastOptions,
      onClose: () => this.remove(id)
    })

    // 创建挂载点
    const mountPoint = document.createElement('div')
    this.container.appendChild(mountPoint)

    // 挂载组件
    const instance = toastApp.mount(mountPoint)

    // 保存实例信息
    toasts.value.push({
      id,
      app: toastApp,
      instance,
      mountPoint,
      options: toastOptions
    })

    return id
  }

  /**
   * 移除 Toast
   * @param {number} id - Toast ID
   */
  remove(id) {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      const toast = toasts.value[index]
      
      // 卸载组件
      toast.app.unmount()
      
      // 移除 DOM 节点
      if (toast.mountPoint && toast.mountPoint.parentNode) {
        toast.mountPoint.parentNode.removeChild(toast.mountPoint)
      }
      
      // 从列表中移除
      toasts.value.splice(index, 1)
    }
  }

  /**
   * 清除所有 Toast
   */
  clear() {
    toasts.value.forEach(toast => {
      toast.app.unmount()
      if (toast.mountPoint && toast.mountPoint.parentNode) {
        toast.mountPoint.parentNode.removeChild(toast.mountPoint)
      }
    })
    toasts.value = []
  }

  /**
   * 成功提示
   * @param {string} message - 消息内容
   * @param {Object} options - 额外配置
   */
  success(message, options = {}) {
    return this.show({
      type: 'success',
      message,
      ...options
    })
  }

  /**
   * 错误提示
   * @param {string} message - 消息内容
   * @param {Object} options - 额外配置
   */
  error(message, options = {}) {
    return this.show({
      type: 'error',
      message,
      duration: 6000, // 错误消息显示更长时间
      ...options
    })
  }

  /**
   * 警告提示
   * @param {string} message - 消息内容
   * @param {Object} options - 额外配置
   */
  warning(message, options = {}) {
    return this.show({
      type: 'warning',
      message,
      ...options
    })
  }

  /**
   * 信息提示
   * @param {string} message - 消息内容
   * @param {Object} options - 额外配置
   */
  info(message, options = {}) {
    return this.show({
      type: 'info',
      message,
      ...options
    })
  }

  /**
   * 加载提示
   * @param {string} message - 消息内容
   * @param {Object} options - 额外配置
   */
  loading(message = '加载中...', options = {}) {
    return this.show({
      type: 'info',
      message,
      duration: 0, // 不自动关闭
      closable: false,
      ...options
    })
  }
}

// 创建全局实例
const toastService = new ToastService()

/**
 * useToast 组合式函数
 */
export function useToast() {
  return {
    toast: toastService,
    success: toastService.success.bind(toastService),
    error: toastService.error.bind(toastService),
    warning: toastService.warning.bind(toastService),
    info: toastService.info.bind(toastService),
    loading: toastService.loading.bind(toastService),
    clear: toastService.clear.bind(toastService)
  }
}

// 默认导出服务实例
export default toastService
