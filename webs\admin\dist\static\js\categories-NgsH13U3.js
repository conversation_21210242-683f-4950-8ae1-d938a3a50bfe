var ee=Object.defineProperty;var N=Object.getOwnPropertySymbols;var ae=Object.prototype.hasOwnProperty,te=Object.prototype.propertyIsEnumerable;var O=(u,a,n)=>a in u?ee(u,a,{enumerable:!0,configurable:!0,writable:!0,value:n}):u[a]=n,R=(u,a)=>{for(var n in a||(a={}))ae.call(a,n)&&O(u,n,a[n]);if(N)for(var n of N(a))te.call(a,n)&&O(u,n,a[n]);return u};var E=(u,a,n)=>new Promise((e,D)=>{var C=s=>{try{i(n.next(s))}catch(b){D(b)}},f=s=>{try{i(n.throw(s))}catch(b){D(b)}},i=s=>s.done?e(s.value):Promise.resolve(s.value).then(C,f);i((n=n.apply(u,a)).next())});import{_ as le,r as _,c as B,d as t,b as z,w as l,g as p,h as U,q as P,o as F,E as h,M as oe,N as ne,O as re,P as ie,Q as se,J as de,k as ce,l as S,m as d,t as w,a as A,H as me,I as ue,p as fe}from"./index-XtNpSMFt.js";import{C as ge}from"./CustomTable-BfiU6RpM.js";import{d as x}from"./menu-DNv9gB1J.js";import{f as Y}from"./common-DOHVq19N.js";const _e={__name:"categories",setup(u,{expose:a}){a();const n=p(!1),e=p(!1),D=p([]),C=p(!1),f=p(!1),i=p(!1),s=p(),b=p(null),k=p([]),m=U({page:1,size:10,total:0}),V=U({}),y=p({name:"",description:"",sort:0}),T=[{prop:"name",label:"分类名称",minWidth:120},{prop:"description",label:"描述",minWidth:150,showOverflowTooltip:!0},{prop:"dishCount",label:"菜品数量",width:100,slot:!0},{prop:"sort",label:"排序",width:80},{prop:"createdAt",label:"创建时间",width:150,formatter:o=>Y(o.createdAt,"YYYY-MM-DD HH:mm")},{label:"操作",width:180,slot:"operation",fixed:"right"}],v=[{prop:"name",label:"分类名称",type:"input",placeholder:"请输入分类名称"}],M={name:[{required:!0,message:"请输入分类名称",trigger:"blur"},{min:2,max:20,message:"分类名称长度在 2 到 20 个字符",trigger:"blur"}]},r=P(()=>i.value?"编辑分类":"新增分类"),g=()=>E(this,null,function*(){n.value=!0;try{const o=R({page:m.page,size:m.size},V),c=yield x.getCategories(o);c.code===200?(D.value=c.data.list||c.data||[],m.total=c.data.total||c.data.length||0):h.error(c.message||"加载数据失败")}catch(o){console.error("加载分类列表失败:",o),h.error("加载数据失败")}finally{n.value=!1}}),j=o=>{Object.assign(V,o),m.page=1,g()},H=()=>{Object.keys(V).forEach(o=>{delete V[o]}),m.page=1,g()},L=o=>{m.page=o,g()},q=o=>{m.size=o,m.page=1,g()},W=()=>{i.value=!1,y.value={name:"",description:"",sort:0},C.value=!0},J=o=>E(this,null,function*(){b.value=o;try{const c=yield x.getDishes({categoryId:o.id});c.data&&(k.value=c.data.list||c.data)}catch(c){console.error("加载分类菜品失败:",c),k.value=[]}f.value=!0}),Q=o=>{i.value=!0,y.value=R({},o),C.value=!0},G=o=>E(this,null,function*(){try{yield x.deleteCategory(o.id),h.success("删除成功"),g()}catch(c){console.error("删除分类失败:",c),h.error("删除失败")}}),K=()=>{h.info("批量导入功能开发中...")},X=()=>{h.info("导出功能开发中...")},Z=()=>E(this,null,function*(){if(s.value)try{yield s.value.validate(),e.value=!0,i.value?(yield x.updateCategory(y.value.id,y.value),h.success("更新成功")):(yield x.createCategory(y.value),h.success("创建成功")),C.value=!1,g()}catch(o){if(o.errors)return;console.error("提交失败:",o),h.error("操作失败")}finally{e.value=!1}}),$=()=>{C.value=!1,y.value={name:"",description:"",sort:0},s.value&&s.value.resetFields()};F(()=>{g()});const I={loading:n,submitLoading:e,tableData:D,dialogVisible:C,detailVisible:f,isEdit:i,formRef:s,currentCategory:b,categoryDishes:k,pagination:m,searchParams:V,formData:y,columns:T,searchFields:v,formRules:M,dialogTitle:r,loadData:g,handleSearch:j,handleReset:H,handleCurrentChange:L,handleSizeChange:q,handleCreate:W,handleView:J,handleEdit:Q,handleDelete:G,handleBatchImport:K,handleExport:X,handleSubmit:Z,handleDialogClose:$,ref:p,reactive:U,computed:P,onMounted:F,get ElMessage(){return h},get ElMessageBox(){return ce},get Plus(){return de},get View(){return se},get Edit(){return ie},get Delete(){return re},get Upload(){return ne},get Download(){return oe},CustomTable:ge,get dishApi(){return x},get formatTime(){return Y}};return Object.defineProperty(I,"__isScriptSetup",{enumerable:!1,value:!0}),I}},pe={class:"category-management"},he={class:"dialog-footer"},ye={key:0,class:"category-detail"},Ce={key:0,class:"category-dishes"},be={class:"dish-list"};function ve(u,a,n,e,D,C){const f=_("el-icon"),i=_("el-button"),s=_("el-tag"),b=_("el-popconfirm"),k=_("el-input"),m=_("el-form-item"),V=_("el-input-number"),y=_("el-form"),T=_("el-dialog"),v=_("el-descriptions-item"),M=_("el-descriptions");return S(),B("div",pe,[t(e.CustomTable,{title:"菜品分类管理",data:e.tableData,columns:e.columns,loading:e.loading,pagination:e.pagination,"show-search":!0,"search-fields":e.searchFields,onSearch:e.handleSearch,onReset:e.handleReset,onCurrentChange:e.handleCurrentChange,onSizeChange:e.handleSizeChange},{toolbar:l(()=>[t(i,{type:"primary",onClick:e.handleCreate},{default:l(()=>[t(f,null,{default:l(()=>[t(e.Plus)]),_:1}),a[5]||(a[5]=d(" 新增分类 ",-1))]),_:1,__:[5]}),t(i,{type:"success",onClick:e.handleBatchImport},{default:l(()=>[t(f,null,{default:l(()=>[t(e.Upload)]),_:1}),a[6]||(a[6]=d(" 批量导入 ",-1))]),_:1,__:[6]}),t(i,{type:"info",onClick:e.handleExport},{default:l(()=>[t(f,null,{default:l(()=>[t(e.Download)]),_:1}),a[7]||(a[7]=d(" 导出数据 ",-1))]),_:1,__:[7]})]),dishCount:l(({row:r})=>[t(s,{type:"info",size:"small"},{default:l(()=>[d(w(r.dishCount||0)+" 道菜 ",1)]),_:2},1024)]),operation:l(({row:r})=>[t(i,{size:"small",type:"primary",link:"",onClick:g=>e.handleView(r)},{default:l(()=>[t(f,null,{default:l(()=>[t(e.View)]),_:1}),a[8]||(a[8]=d(" 查看 ",-1))]),_:2,__:[8]},1032,["onClick"]),t(i,{size:"small",type:"warning",link:"",onClick:g=>e.handleEdit(r)},{default:l(()=>[t(f,null,{default:l(()=>[t(e.Edit)]),_:1}),a[9]||(a[9]=d(" 编辑 ",-1))]),_:2,__:[9]},1032,["onClick"]),t(b,{title:"确定要删除这个分类吗？删除后该分类下的菜品将被移动到默认分类。",onConfirm:g=>e.handleDelete(r)},{reference:l(()=>[t(i,{size:"small",type:"danger",link:""},{default:l(()=>[t(f,null,{default:l(()=>[t(e.Delete)]),_:1}),a[10]||(a[10]=d(" 删除 ",-1))]),_:1,__:[10]})]),_:2},1032,["onConfirm"])]),_:1},8,["data","loading","pagination"]),z(" 分类表单对话框 "),t(T,{modelValue:e.dialogVisible,"onUpdate:modelValue":a[3]||(a[3]=r=>e.dialogVisible=r),title:e.dialogTitle,width:"600px","close-on-click-modal":!1,onClose:e.handleDialogClose},{footer:l(()=>[A("div",he,[t(i,{onClick:e.handleDialogClose},{default:l(()=>a[11]||(a[11]=[d("取消",-1)])),_:1,__:[11]}),t(i,{type:"primary",onClick:e.handleSubmit,loading:e.submitLoading},{default:l(()=>a[12]||(a[12]=[d(" 确定 ",-1)])),_:1,__:[12]},8,["loading"])])]),default:l(()=>[t(y,{ref:"formRef",model:e.formData,rules:e.formRules,"label-width":"100px"},{default:l(()=>[t(m,{label:"分类名称",prop:"name"},{default:l(()=>[t(k,{modelValue:e.formData.name,"onUpdate:modelValue":a[0]||(a[0]=r=>e.formData.name=r),placeholder:"请输入分类名称",maxlength:"20","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(m,{label:"分类描述",prop:"description"},{default:l(()=>[t(k,{modelValue:e.formData.description,"onUpdate:modelValue":a[1]||(a[1]=r=>e.formData.description=r),type:"textarea",rows:3,placeholder:"请输入分类描述",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(m,{label:"排序",prop:"sort"},{default:l(()=>[t(V,{modelValue:e.formData.sort,"onUpdate:modelValue":a[2]||(a[2]=r=>e.formData.sort=r),min:0,max:999,placeholder:"排序值，数字越小越靠前"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),z(" 分类详情对话框 "),t(T,{modelValue:e.detailVisible,"onUpdate:modelValue":a[4]||(a[4]=r=>e.detailVisible=r),title:"分类详情",width:"600px"},{default:l(()=>[e.currentCategory?(S(),B("div",ye,[t(M,{column:2,border:""},{default:l(()=>[t(v,{label:"分类名称"},{default:l(()=>[d(w(e.currentCategory.name),1)]),_:1}),t(v,{label:"菜品数量"},{default:l(()=>[t(s,{type:"info"},{default:l(()=>[d(w(e.currentCategory.dishCount||0)+" 道菜",1)]),_:1})]),_:1}),t(v,{label:"排序值"},{default:l(()=>[d(w(e.currentCategory.sort||0),1)]),_:1}),t(v,{label:"创建时间"},{default:l(()=>[d(w(e.formatTime(e.currentCategory.createdAt)),1)]),_:1}),t(v,{label:"更新时间",span:"2"},{default:l(()=>[d(w(e.formatTime(e.currentCategory.updatedAt)),1)]),_:1}),t(v,{label:"分类描述",span:"2"},{default:l(()=>[d(w(e.currentCategory.description||"暂无描述"),1)]),_:1})]),_:1}),z(" 该分类下的菜品列表 "),e.categoryDishes.length?(S(),B("div",Ce,[a[13]||(a[13]=A("h4",null,"该分类下的菜品",-1)),A("div",be,[(S(!0),B(me,null,ue(e.categoryDishes,r=>(S(),fe(s,{key:r.id,class:"dish-tag",type:"success"},{default:l(()=>[d(w(r.name),1)]),_:2},1024))),128))])])):z("v-if",!0)])):z("v-if",!0)]),_:1},8,["modelValue"])])}const Ee=le(_e,[["render",ve],["__scopeId","data-v-c19a16b4"],["__file","E:/wx-nan/webs/admin/src/views/menu/categories.vue"]]);export{Ee as default};
