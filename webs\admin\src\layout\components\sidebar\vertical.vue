<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { emitter } from "@/utils/mitt";
import { useNav } from "@/layout/hooks/useNav";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal, isAllEmpty } from "@/utils/utils";
import { findRouteByPath, getParentPaths } from "@/router/utils";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { ref, computed, watch, onMounted, onBeforeUnmount } from "vue";
import Logo from "./logo.vue";
import SidebarItem from "./sidebarItem.vue";
import leftCollapse from "./leftCollapse.vue";

const route = useRoute();
const showLogo = ref(
  storageLocal().getItem<StorageConfigs>(
    `${responsiveStorageNameSpace()}configure`
  )?.showLogo ?? true
);

const { device, pureApp, isCollapse, menuSelect, toggleSideBar } = useNav();

const subMenuData = ref([]);

const menuData = computed(() => {
  const permissionStore = usePermissionStoreHook();
  // 确保权限store已初始化
  if (!permissionStore.isMenusLoaded) {
    permissionStore.initMenus();
  }

  let menus = pureApp.layout === "mix" && device.value !== "mobile"
    ? subMenuData.value
    : permissionStore.wholeMenus;

  // 如果权限store的菜单为空，直接从路由过滤
  if (!menus || menus.length === 0) {
    const router = useRouter();
    menus = router.options.routes.filter(route => {
      // 只显示Layout路由，排除登录相关页面和隐藏的路由
      return route.component?.name === 'Layout' &&
             !route.meta?.hidden &&
             route.path !== '/login' &&
             route.path !== '/register' &&
             route.path !== '/forgot-password';
    });
  }

  // console.log('menuData: 过滤后的菜单', menus)
  return menus;
});

const loading = computed(() =>
  pureApp.layout === "mix" ? false : menuData.value.length === 0 ? true : false
);
// 触发选中高亮
const defaultActive = computed(() =>
  !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path
);

function getSubMenuData() {
  let path = "";
  path = defaultActive.value;
  subMenuData.value = [];
  // console.log('path',path) 当前路由名字
  // path的上级路由组成的数组
  const parentPathArr = getParentPaths(
    path,
    usePermissionStoreHook().wholeMenus
  );
  // 当前路由的父级路由信息
  const parenetRoute = findRouteByPath(
    parentPathArr[0] || path,
    usePermissionStoreHook().wholeMenus
  );
  if (!parenetRoute?.children) return;
  subMenuData.value = parenetRoute?.children;
  // console.log('subMenuData',subMenuData.value)
}
// 路由切换触发
watch(
  () => [route.path, usePermissionStoreHook().wholeMenus],
  () => {
    if (route.path.includes("/redirect")) return;
    getSubMenuData(); //重新获取儿子和父亲
    // console.log(route.path,route)
    menuSelect(route.path); //通知标签
  }
);

onMounted(() => {
  getSubMenuData();

  emitter.on("logoChange", key => {
    showLogo.value = key;
  });
});

onBeforeUnmount(() => {
  // 解绑`logoChange`公共事件，防止多次触发
  emitter.off("logoChange");
});
</script>

<template>
  <div
    v-loading="loading"
    :class="['sidebar-container', showLogo ? 'has-logo' : 'no-logo']"
  >
    <Logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar
      wrap-class="scrollbar-wrapper"
      :class="[device === 'mobile' ? 'mobile' : 'pc']"
    >
      <el-menu
        router
        unique-opened
        mode="vertical"
        class="outer-most select-none"
        :collapse="isCollapse"
        :default-active="defaultActive"
        :collapse-transition="false"
      >
        <sidebar-item
          v-for="routes in menuData"
          :key="routes.path"
          :item="routes"
          :base-path="routes.path"
          class="outer-most select-none"
        />
      </el-menu>
    </el-scrollbar>
    <!--    展开折叠图标-->
    <leftCollapse
      v-if="device !== 'mobile'"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />
  </div>
</template>

<style scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}
</style>
