const app = getApp();
const {dishApi, notificationApi} = require('../../services/api');

Page({
  data: {
    userInfo: {},
    formattedPhone: '',
    roleText: '',
    myDishCount: 0,
    unreadNotificationCount: 0, // 未读通知数量
    badgeLoading: false // 角标加载状态
  },

  onLoad() {},

  onShow() {
    const user_info = wx.getStorageSync('userInfo');
    this.setData(
      {
        userInfo: user_info
      },
      () => {
        this.formatPhoneNumber();
        this.formatRoleText();
      }
    );
    // 每次显示页面时加载菜品数量和未读通知数量
    this.loadMyDishCount();
    this.loadUnreadNotificationCount();
  },

  // 加载我的菜品数量
  async loadMyDishCount() {
    try {
      const result = await dishApi.getMyDishes({
        page: 1,
        size: 1
      });
      if (result && result.success) {
        this.setData({
          myDishCount: result.data.total || 0
        });
      }
    } catch (error) {
      console.error('加载菜品数量失败:', error);
    }
  },

  formatPhoneNumber() {
    const {phone} = this.data.userInfo;
    // 格式化手机号为 138****8888 格式
    if (!phone) return;
    const formattedPhone = phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    this.setData({
      formattedPhone
    });
  },

  formatRoleText() {
    const {role} = this.data.userInfo;
    const roleMap = {
      admin: '管理员',
      user: '普通用户',
      vip: 'VIP用户',
      member: '会员'
    };
    this.setData({
      roleText: roleMap[role] || '用户'
    });
  },

  goToUserProfile() {
    wx.navigateTo({
      url: '/pages/user_profile/index'
    });
  },

  goToUserConnection() {
    wx.navigateTo({
      url: '/pages/user_connection/index'
    });
  },

  goToOrderList() {
    wx.navigateTo({
      url: '/pages/order_list/index'
    });
  },

  goToFamilyMessage() {
    wx.navigateTo({
      url: '/pages/family_message/index'
    });
  },

  goToNotice() {
    wx.navigateTo({
      url: '/pages/message/index'
    });
  },

  goToNotificationCenter() {
    wx.navigateTo({
      url: '/pages/notification_center/index'
    });
  },

  // 跳转到新增菜品
  goToAddDish() {
    wx.navigateTo({
      url: '/pages/dish/add/index'
    });
  },

  // 跳转到我的菜品
  goToMyDishes() {
    wx.navigateTo({
      url: '/pages/dish/my-dishes/index'
    });
  },

  onLogout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: res => {
        if (res.confirm) {
          // 清除用户信息
          app.globalData.userInfo = null;
          // 清空缓存
          const tem = wx.getStorageSync('savedAccount');
          wx.clearStorageSync();
          if (tem) {
            wx.setStorageSync('savedAccount', tem);
          }
          wx.reLaunch({
            url: '/pages/login/index'
          });
        }
      }
    });
  },

  // 获取未读通知数量
  async loadUnreadNotificationCount() {
    try {
      this.setData({
        badgeLoading: true
      });

      const res = await notificationApi.getNotifications({
        read: false,
        limit: 1,
        countOnly: true // 只获取数量，不获取具体数据
      });

      if (res.code === 200) {
        const count = res.data.total || res.data.count || 0;
        this.setData({
          unreadNotificationCount: count,
          badgeLoading: false
        });

        // 更新全局状态
        getApp().globalData.unreadNotificationCount = count;
      }
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
      this.setData({
        badgeLoading: false
      });
    }
  },

  // 跳转到通知中心
  goToNotificationCenter() {
    wx.navigateTo({
      url: '/pages/notification_center/index'
    });
  }
});
