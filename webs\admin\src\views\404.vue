<template>
  <div class="not-found-container">
    <div class="not-found-content">
      <div class="error-code">404</div>
      <div class="error-message">页面未找到</div>
      <div class="error-description">
        抱歉，您访问的页面不存在或已被移除
      </div>
      
      <div class="error-actions">
        <el-button type="primary" @click="goHome">
          <el-icon><House /></el-icon>
          返回首页
        </el-button>
        <el-button @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          返回上页
        </el-button>
      </div>
      
      <div class="quick-links">
        <h4>快速导航</h4>
        <div class="link-grid">
          <router-link to="/dashboard" class="quick-link">
            <el-icon><House /></el-icon>
            <span>仪表盘</span>
          </router-link>
          <router-link to="/menu/dishes" class="quick-link">
            <el-icon><Bowl /></el-icon>
            <span>菜品管理</span>
          </router-link>
          <router-link to="/order/list" class="quick-link">
            <el-icon><ShoppingCart /></el-icon>
            <span>订单管理</span>
          </router-link>
          <router-link to="/user/list" class="quick-link">
            <el-icon><User /></el-icon>
            <span>用户管理</span>
          </router-link>
        </div>
      </div>
    </div>
    
    <div class="not-found-illustration">
      <div class="illustration-circle">
        <el-icon size="120" color="#e6f7ff">
          <Search />
        </el-icon>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { 
  House, 
  ArrowLeft, 
  Search, 
  Bowl, 
  ShoppingCart, 
  User 
} from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style scoped lang="scss">
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.not-found-content {
  background: white;
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  width: 100%;
}

.error-code {
  font-size: 120px;
  font-weight: bold;
  color: #1890ff;
  line-height: 1;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.error-message {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.error-description {
  font-size: 16px;
  color: #666;
  margin-bottom: 40px;
  line-height: 1.6;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
  
  .el-button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
  }
}

.quick-links {
  h4 {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
  }
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.quick-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px 16px;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  text-decoration: none;
  color: #666;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #1890ff;
    color: #1890ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  }
  
  span {
    font-size: 14px;
    font-weight: 500;
  }
}

.not-found-illustration {
  display: none;
}

.illustration-circle {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

// 响应式设计
@media (min-width: 768px) {
  .not-found-container {
    flex-direction: row;
    gap: 60px;
  }
  
  .not-found-content {
    text-align: left;
    max-width: 500px;
  }
  
  .error-actions {
    justify-content: flex-start;
  }
  
  .not-found-illustration {
    display: block;
  }
}

@media (max-width: 480px) {
  .not-found-content {
    padding: 40px 20px;
  }
  
  .error-code {
    font-size: 80px;
  }
  
  .error-message {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
    
    .el-button {
      width: 100%;
      max-width: 200px;
    }
  }
  
  .link-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
