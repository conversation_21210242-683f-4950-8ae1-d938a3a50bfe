<view class="container">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-content">
      <view class="page-title">申请历史</view>
      <view class="page-subtitle">查看所有关联申请记录</view>
    </view>
    <view class="header-actions">
      <van-button
        size="small"
        type="default"
        icon="filter-o"
        bind:click="showFilterSheet"
      >
        筛选
      </van-button>
    </view>
  </view>

  <!-- 统计信息卡片 -->
  <view class="statistics-card">
    <view class="stat-item">
      <view class="stat-number">{{statistics.total}}</view>
      <view class="stat-label">总申请</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{statistics.pending}}</view>
      <view class="stat-label">待处理</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{statistics.accepted}}</view>
      <view class="stat-label">已同意</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{statistics.rejected}}</view>
      <view class="stat-label">已拒绝</view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view
    class="filter-tags"
    wx:if="{{filterStatus !== 'all' || filterType !== 'all'}}"
  >
    <view class="filter-tag-item" wx:if="{{filterStatus !== 'all'}}">
      状态:
      {{filterStatus === 'pending' ? '待处理' : filterStatus === 'accepted' ? '已同意' : '已拒绝'}}
      <van-icon name="cross" size="16rpx" bind:click="clearStatusFilter" />
    </view>
    <view class="filter-tag-item" wx:if="{{filterType !== 'all'}}">
      类型: {{filterType === 'sent' ? '我发送的' : '我接收的'}}
      <van-icon name="cross" size="16rpx" bind:click="clearTypeFilter" />
    </view>
  </view>

  <!-- 历史记录列表 -->
  <view class="history-list" wx:if="{{historyList.length > 0}}">
    <view wx:for="{{historyList}}" wx:key="id" class="history-item">
      <view class="history-header">
        <view class="user-info">
          <view class="avatar">
            <image
              wx:if="{{item.isSender ? item.receiver.avatar : item.sender.avatar}}"
              src="{{item.isSender ? item.receiver.avatar : item.sender.avatar}}"
              mode="aspectFill"
            />
            <van-icon wx:else name="user-o" size="40rpx" color="#6366F1" />
          </view>
          <view class="info">
            <view
              class="name"
              >{{item.isSender ? item.receiver.name : item.sender.name}}</view
            >
            <view class="type-badge {{item.isSender ? 'sent' : 'received'}}">
              {{item.isSender ? '我发送的' : '我接收的'}}
            </view>
          </view>
        </view>

        <view class="actions">
          <van-button
            wx:if="{{item.status === 'rejected' && item.isSender}}"
            size="mini"
            type="primary"
            bind:click="resendRequest"
            data-id="{{item.id}}"
            data-receiver-id="{{item.receiverId}}"
            data-receiver-name="{{item.receiver.name}}"
          >
            重新申请
          </van-button>

          <van-button
            size="mini"
            type="default"
            bind:click="viewDetail"
            data-id="{{item.id}}"
          >
            详情
          </van-button>
        </view>
      </view>

      <!-- 申请消息 -->
      <view class="message-content" wx:if="{{item.message}}">
        <van-icon name="chat-o" size="24rpx" />
        <text>{{item.message}}</text>
      </view>

      <!-- 状态组件 -->
      <connection-status
        status="{{item.status}}"
        is-sender="{{item.isSender}}"
        created-at="{{item.createdAt}}"
        size="small"
        show-progress="{{true}}"
        bind:statusclick="onConnectionStatusClick"
      />
    </view>
  </view>

  <!-- 加载更多组件 - 只在有数据时显示 -->
  <load-more
    wx:if="{{historyList.length > 0}}"
    loading="{{loadingMore}}"
    has-more="{{hasMore}}"
    total="{{statistics.total}}"
    current="{{historyList.length}}"
    page-size="{{pageSize}}"
    bind:loadmore="loadMoreHistory"
  />

  <!-- 首次加载状态 -->
  <view
    class="loading-container"
    wx:if="{{loading && historyList.length === 0}}"
  >
    <van-loading type="spinner" color="#6366F1" vertical>
      {{refreshing ? '刷新中...' : '加载中...'}}
    </van-loading>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{historyList.length === 0 && !loading}}" class="empty-state">
    <van-icon name="records" size="120rpx" color="#D1D5DB" />
    <view class="empty-text">暂无申请记录</view>
    <view class="empty-desc">当您发送或接收关联申请时，记录会显示在这里</view>
  </view>
</view>

<!-- 筛选面板 -->
<van-action-sheet
  show="{{showFilterSheet}}"
  title="筛选条件"
  bind:close="hideFilterSheet"
>
  <view class="filter-content">
    <!-- 状态筛选 -->
    <view class="filter-section">
      <view class="filter-title">申请状态</view>
      <view class="filter-options">
        <view
          class="filter-option {{filterStatus === 'all' ? 'active' : ''}}"
          data-status="all"
          bind:tap="onFilterStatusChange"
        >
          全部
        </view>
        <view
          class="filter-option {{filterStatus === 'pending' ? 'active' : ''}}"
          data-status="pending"
          bind:tap="onFilterStatusChange"
        >
          待处理
        </view>
        <view
          class="filter-option {{filterStatus === 'accepted' ? 'active' : ''}}"
          data-status="accepted"
          bind:tap="onFilterStatusChange"
        >
          已同意
        </view>
        <view
          class="filter-option {{filterStatus === 'rejected' ? 'active' : ''}}"
          data-status="rejected"
          bind:tap="onFilterStatusChange"
        >
          已拒绝
        </view>
      </view>
    </view>

    <!-- 类型筛选 -->
    <view class="filter-section">
      <view class="filter-title">申请类型</view>
      <view class="filter-options">
        <view
          class="filter-option {{filterType === 'all' ? 'active' : ''}}"
          data-type="all"
          bind:tap="onFilterTypeChange"
        >
          全部
        </view>
        <view
          class="filter-option {{filterType === 'sent' ? 'active' : ''}}"
          data-type="sent"
          bind:tap="onFilterTypeChange"
        >
          我发送的
        </view>
        <view
          class="filter-option {{filterType === 'received' ? 'active' : ''}}"
          data-type="received"
          bind:tap="onFilterTypeChange"
        >
          我接收的
        </view>
      </view>
    </view>
  </view>
</van-action-sheet>
