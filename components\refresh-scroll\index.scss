/* 通用下拉刷新和上拉加载更多组件样式 */

.refresh-scroll-wrapper {
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* 顶部刷新指示器 */
.refresh-indicator-top {
  position: absolute;
  top: -80rpx;
  left: 0;
  right: 0;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 0 0 16rpx 16rpx;
  z-index: 10;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(-20rpx);

  &.show {
    opacity: 1;
    transform: translateY(80rpx);
  }

  .refresh-content-top {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 16rpx 24rpx;

    .refresh-icon-top {
      font-size: 28rpx;
      color: #6366f1;
      transition: transform 0.3s ease;
    }

    .refresh-text-top {
      font-size: 26rpx;
      color: #64748b;
      font-weight: 500;
    }
  }
}

.refresh-scroll-container {
  width: 100%;
  position: relative;
}

.refresh-scroll-content {
  width: 100%;
  position: relative;
}

/* 内部数据渲染样式 */
.internal-list {
  width: 100%;
}

.internal-item {
  width: 100%;
}

.default-item {
  padding: 24rpx 32rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
  font-size: 28rpx;
  color: #334155;
  line-height: 1.5;
}

/* 加载更多区域样式 */
.load-more-container {
  width: 100%;
  padding: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.load-more-trigger {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  transition: all 0.2s ease;

  &:active {
    background: #f1f5f9;
    transform: scale(0.98);
  }

  .load-more-icon {
    font-size: 32rpx;
    color: #6366f1;
  }

  .load-more-text {
    font-size: 28rpx;
    color: #64748b;
    font-weight: 500;
  }
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 24rpx;

  .loading-text {
    font-size: 28rpx;
    color: #64748b;
  }
}

.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx;
  position: relative;

  .no-more-text {
    font-size: 24rpx;
    color: #94a3b8;
    padding: 0 32rpx;
    position: relative;
    z-index: 1;

    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 80rpx;
      height: 1rpx;
      background: #e2e8f0;
    }

    &::before {
      left: -100rpx;
    }

    &::after {
      right: -100rpx;
    }
  }
}

/* 空状态样式 */
.empty-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400rpx;
}

.default-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-icon {
    font-size: 120rpx;
    color: #cbd5e1;
  }

  .empty-text {
    font-size: 32rpx;
    font-weight: 500;
    color: #64748b;
  }

  .empty-tip {
    font-size: 28rpx;
    color: #94a3b8;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .load-more-trigger {
    padding: 20rpx 40rpx;

    .load-more-icon {
      font-size: 28rpx;
    }

    .load-more-text {
      font-size: 26rpx;
    }
  }

  .default-empty {
    padding: 60rpx 32rpx;

    .empty-icon {
      font-size: 100rpx;
    }

    .empty-text {
      font-size: 30rpx;
    }

    .empty-tip {
      font-size: 26rpx;
    }
  }
}