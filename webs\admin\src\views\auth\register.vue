<template>
  <div class="register-container">
    <div class="register-form">
      <div class="form-header">
        <h2>管理员注册</h2>
        <p>创建新的管理员账户，开始使用楠楠家厨管理系统</p>
      </div>

      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        label-width="80px"
        size="large"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名（3-20位字母数字下划线）"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="registerForm.phone"
            placeholder="请输入手机号"
            prefix-icon="Phone"
            clearable
            maxlength="11"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item label="验证码" prop="emailCode">
          <div class="code-input-group">
            <el-input
              v-model="registerForm.emailCode"
              placeholder="请输入邮箱验证码"
              prefix-icon="Lock"
              clearable
              maxlength="6"
            />
            <el-button
              :disabled="emailCountdown > 0 || !registerForm.email"
              @click="sendEmailCode"
              class="code-btn"
              :loading="sendingCode"
            >
              {{ emailCountdown > 0 ? `${emailCountdown}s` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码（6-20位，建议包含字母数字）"
            prefix-icon="Lock"
            show-password
            clearable
          />
          <div class="password-strength">
            <div class="strength-bar">
              <div
                class="strength-fill"
                :class="passwordStrengthClass"
                :style="{ width: passwordStrengthWidth }"
              ></div>
            </div>
            <span class="strength-text" :class="passwordStrengthClass">
              {{ passwordStrengthText }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="agreement">
          <el-checkbox v-model="registerForm.agreement">
            我已阅读并同意
            <el-link type="primary" @click="showUserAgreement"
              >《用户协议》</el-link
            >
            和
            <el-link type="primary" @click="showPrivacyPolicy"
              >《隐私政策》</el-link
            >
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleRegister"
            :disabled="!registerForm.agreement"
            style="width: 100%"
          >
            <el-icon v-if="!loading"><User /></el-icon>
            {{ loading ? "注册中..." : "立即注册" }}
          </el-button>
        </el-form-item>

        <div class="form-footer">
          <el-link type="primary" @click="$router.push('/login')">
            已有账户？立即登录
          </el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { authApi } from '@/api/auth'

const router = useRouter()
const registerFormRef = ref()
const loading = ref(false)
const sendingCode = ref(false)
const emailCountdown = ref(0)
let emailTimer = null

const registerForm = reactive({
  username: '',
  phone: '',
  email: '',
  emailCode: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

// 验证用户名
const validateUsername = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入用户名'));
    return;
  }

  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  if (!usernameRegex.test(value)) {
    callback(new Error('用户名只能包含字母、数字和下划线，长度3-20位'));
    return;
  }

  callback();
};

// 验证手机号
const validatePhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入手机号'));
    return;
  }

  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号'));
    return;
  }

  callback();
};

// 验证密码强度
const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入密码'));
    return;
  }

  if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'));
    return;
  }

  if (value.length > 20) {
    callback(new Error('密码长度不能超过20位'));
    return;
  }

  callback();
};

// 验证确认密码
const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请再次输入密码'));
    return;
  }

  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'));
    return;
  }

  callback();
};

// 验证邮箱验证码
const validateEmailCode = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入邮箱验证码'));
    return;
  }
  if (value.length !== 6) {
    callback(new Error('验证码为6位数字'));
    return;
  }
  callback();
};

// 验证协议同意
const validateAgreement = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请阅读并同意用户协议和隐私政策'));
    return;
  }
  callback();
};

const registerRules = {
  username: [
    { validator: validateUsername, trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  emailCode: [
    { validator: validateEmailCode, trigger: 'blur' }
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  agreement: [
    { validator: validateAgreement, trigger: 'change' }
  ]
}

// 密码强度检测
const passwordStrength = computed(() => {
  const password = registerForm.password;
  if (!password) return 0;

  let strength = 0;

  // 长度检查
  if (password.length >= 6) strength += 1;
  if (password.length >= 8) strength += 1;

  // 包含小写字母
  if (/[a-z]/.test(password)) strength += 1;

  // 包含大写字母
  if (/[A-Z]/.test(password)) strength += 1;

  // 包含数字
  if (/\d/.test(password)) strength += 1;

  // 包含特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;

  return Math.min(strength, 4);
});

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value;
  const texts = ['', '弱', '一般', '强', '很强'];
  return texts[strength] || '';
});

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value;
  const classes = ['', 'weak', 'fair', 'good', 'strong'];
  return classes[strength] || '';
});

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 4) * 100}%`;
});

// 发送邮箱验证码
const sendEmailCode = async () => {
  try {
    await registerFormRef.value.validateField('email')
    sendingCode.value = true

    const response = await authApi.sendEmailCode(registerForm.email.trim(), 'register')

    if (response.code === 200) {
      ElMessage.success('验证码已发送到您的邮箱')
      startEmailCountdown()
    } else {
      ElMessage.error(response.message || '发送失败，请重试')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 开始邮箱验证码倒计时
const startEmailCountdown = () => {
  emailCountdown.value = 60
  emailTimer = setInterval(() => {
    emailCountdown.value--
    if (emailCountdown.value <= 0) {
      clearInterval(emailTimer)
      emailTimer = null
    }
  }, 1000)
}

const handleRegister = async () => {
  try {
    await registerFormRef.value.validate()
    loading.value = true

    ElMessage.info('正在注册，请稍候...')

    // 调用管理员注册API（验证码验证在后端进行）
    const response = await authApi.adminRegister({
      username: registerForm.username.trim(),
      phone: registerForm.phone.trim(),
      email: registerForm.email.trim(),
      password: registerForm.password,
      emailCode: registerForm.emailCode.trim()
    })

    if (response.code === 200 || response.code === 201) {
      ElMessage.success('注册成功！正在跳转到登录页面...')

      // 清空表单
      registerFormRef.value.resetFields()

      // 跳转到登录页面，并预填用户名
      setTimeout(() => {
        router.push({
          path: '/login',
          query: { username: registerForm.username }
        })
      }, 1000)
    } else {
      ElMessage.error(response.message || '注册失败，请检查信息后重试')
    }
  } catch (error) {
    console.error('注册失败:', error)

    let errorMessage = '注册失败，请重试'
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      if (status === 400) {
        errorMessage = data.message || '请求参数错误'
      } else if (status === 409) {
        errorMessage = '用户名、手机号或邮箱已被注册'
      } else if (status === 403) {
        errorMessage = '邀请码无效或已过期'
      } else if (status >= 500) {
        errorMessage = '服务器错误，请稍后再试'
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 显示用户协议
const showUserAgreement = () => {
  const agreementContent = `    <div style="max-height: 500px; overflow-y: auto; padding: 20px; line-height: 1.8; word-wrap: break-word; word-break: break-all;">

      <h3 style="color: #409eff; margin-bottom: 16px; font-size: 18px; font-weight: bold;">楠楠家厨管理系统用户协议</h3>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第一条 总则</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.1 本协议是您与楠楠家厨管理系统之间关于使用本系统服务的法律协议。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.2 您通过注册、登录、使用本系统，即表示您已阅读、理解并同意接受本协议的全部条款。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第二条 账户管理</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.1 您应当使用真实、准确、完整的信息注册账户。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.2 您有义务维护账户信息的安全性，不得将账户借给他人使用。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.3 如发现账户被盗用，应立即通知系统管理员。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第三条 使用规范</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.1 您应当合法、正当地使用本系统，不得从事违法违规活动。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.2 不得恶意攻击系统、传播病毒或进行其他危害系统安全的行为。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.3 不得利用系统从事商业竞争或其他损害系统利益的活动。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第四条 数据保护</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.1 我们承诺保护您的个人信息安全，不会未经授权向第三方披露。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.2 您上传的数据仅用于系统功能实现，我们不会用于其他商业目的。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第五条 免责声明</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.1 因不可抗力、网络故障等原因导致的服务中断，我们不承担责任。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.2 您因违反本协议导致的损失，由您自行承担。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第六条 协议变更</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.1 我们有权根据需要修改本协议，修改后的协议将在系统内公布。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.2 如您不同意修改后的协议，可以停止使用本系统。</p>

      <p style="margin-top: 20px; color: #666; font-size: 12px;">
        本协议最终解释权归楠楠家厨管理系统所有。<br>
        最后更新日期：2025年7月31
      </p>
    </div>
  `;

  ElMessageBox({
    title: '用户协议',
    message: agreementContent,
    dangerouslyUseHTMLString: true,
    confirmButtonText: '我已阅读',
    showCancelButton: false,
    closeOnClickModal: true,
    customStyle: {
      width: '90vw',
      maxWidth: '700px',
      minWidth: '320px'
    }
  });
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  const policyContent = `
    <div style="max-height: 500px; overflow-y: auto; padding: 20px; line-height: 1.8; word-wrap: break-word; word-break: break-all;">
      <h3 style="color: #409eff; margin-bottom: 16px; font-size: 18px; font-weight: bold;">楠楠家厨管理系统隐私政策</h3>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第一条 信息收集</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.1 我们收集您主动提供的信息，包括但不限于用户名、邮箱、手机号等。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">1.2 我们会自动收集您使用系统时的操作日志，用于系统优化和安全监控。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第二条 信息使用</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.1 您的个人信息仅用于提供系统服务、身份验证和安全保护。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.2 我们不会将您的个人信息用于营销推广或其他商业目的。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">2.3 在法律要求的情况下，我们可能需要配合相关部门提供必要信息。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第三条 信息保护</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.1 我们采用行业标准的安全措施保护您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.2 您的密码经过加密存储，我们无法获取您的明文密码。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">3.3 我们定期进行安全审计，确保数据安全。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第四条 信息共享</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.1 除本政策明确说明外，我们不会与第三方共享您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">4.2 在获得您明确同意的情况下，我们可能与合作伙伴共享必要信息。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第五条 用户权利</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.1 您有权查询、更正、删除您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.2 您可以随时注销账户，我们将删除您的个人信息。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">5.3 如对隐私政策有疑问，可以联系系统管理员。</p>

      <h4 style="color: #333; margin: 16px 0 8px 0; font-size: 16px; font-weight: bold;">第六条 政策更新</h4>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.1 我们可能会不定期更新本隐私政策。</p>
      <p style="margin: 8px 0; color: #555; font-size: 14px; white-space: pre-wrap;">6.2 重大变更会通过系统通知或邮件方式告知您。</p>

      <p style="margin-top: 20px; color: #666; font-size: 12px;">
        如有隐私相关问题，请联系：<EMAIL><br>
        最后更新日期：2025年7月31日
      </p>
    </div>
  `;

  ElMessageBox({
    title: '隐私政策',
    message: policyContent,
    dangerouslyUseHTMLString: true,
    confirmButtonText: '我已阅读',
    showCancelButton: false,
    closeOnClickModal: true,
    customStyle: {
      width: '90vw',
      maxWidth: '700px',
      minWidth: '320px'
    }
  });
}

// 清理定时器
onUnmounted(() => {
  if (emailTimer) {
    clearInterval(emailTimer)
  }
})
</script>

<style scoped>
:deep(.el-message-box__container){
  display: none !important;
  overflow: hidden !important;
}
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-form {
  width: 100%;
  max-width: 500px;
  min-width: 320px;
  padding: 40px;
  margin: 0 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .register-form {
    padding: 30px 20px;
    margin: 0 10px;
  }
}

@media (max-width: 480px) {
  .register-form {
    padding: 20px 15px;
    margin: 0 5px;
  }
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.form-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 验证码输入框样式 */
.code-input-group {
  display: flex;
  gap: 10px;
}

.code-input-group .el-input {
  flex: 1;
}

.code-btn {
  width: 120px;
  flex-shrink: 0;
}

/* 密码强度样式 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background-color: #e4e7ed;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.strength-fill.weak {
  background-color: #f56c6c;
}

.strength-fill.fair {
  background-color: #e6a23c;
}

.strength-fill.good {
  background-color: #409eff;
}

.strength-fill.strong {
  background-color: #67c23a;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 30px;
}

.strength-text.weak {
  color: #f56c6c;
}

.strength-text.fair {
  color: #e6a23c;
}

.strength-text.good {
  color: #409eff;
}

.strength-text.strong {
  color: #67c23a;
}

/* 邀请码提示样式 */
.invite-code-tip {
  margin-top: 4px;
}

.invite-code-tip .el-text {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
