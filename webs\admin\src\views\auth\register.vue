<template>
  <div class="register-container">
    <div class="register-form">
      <div class="form-header">
        <h2>管理员注册</h2>
        <p>创建新的管理员账户，开始使用楠楠家厨管理系统</p>
      </div>

      <el-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        label-width="80px"
        size="large"
      >
        <el-form-item label="用户名" prop="username">
          <el-input
            v-model="registerForm.username"
            placeholder="请输入用户名（3-20位字母数字下划线）"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model="registerForm.phone"
            placeholder="请输入手机号"
            prefix-icon="Phone"
            clearable
            maxlength="11"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="registerForm.email"
            placeholder="请输入邮箱地址"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码（6-20位，建议包含字母数字）"
            prefix-icon="Lock"
            show-password
            clearable
          />
          <div class="password-strength">
            <div class="strength-bar">
              <div
                class="strength-fill"
                :class="passwordStrengthClass"
                :style="{ width: passwordStrengthWidth }"
              ></div>
            </div>
            <span class="strength-text" :class="passwordStrengthClass">
              {{ passwordStrengthText }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item prop="agreement">
          <el-checkbox v-model="registerForm.agreement">
            我已阅读并同意
            <el-link type="primary" @click="showUserAgreement"
              >《用户协议》</el-link
            >
            和
            <el-link type="primary" @click="showPrivacyPolicy"
              >《隐私政策》</el-link
            >
          </el-checkbox>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleRegister"
            :disabled="!registerForm.agreement"
            style="width: 100%"
          >
            <el-icon v-if="!loading"><User /></el-icon>
            {{ loading ? "注册中..." : "立即注册" }}
          </el-button>
        </el-form-item>

        <div class="form-footer">
          <el-link type="primary" @click="$router.push('/login')">
            已有账户？立即登录
          </el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User } from '@element-plus/icons-vue'
import { authApi } from '@/api/auth'

const router = useRouter()
const registerFormRef = ref()
const loading = ref(false)

const registerForm = reactive({
  username: '',
  phone: '',
  email: '',
  password: '',
  confirmPassword: '',
  agreement: false
})

// 验证用户名
const validateUsername = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入用户名'));
    return;
  }

  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  if (!usernameRegex.test(value)) {
    callback(new Error('用户名只能包含字母、数字和下划线，长度3-20位'));
    return;
  }

  callback();
};

// 验证手机号
const validatePhone = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入手机号'));
    return;
  }

  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号'));
    return;
  }

  callback();
};

// 验证密码强度
const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入密码'));
    return;
  }

  if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'));
    return;
  }

  if (value.length > 20) {
    callback(new Error('密码长度不能超过20位'));
    return;
  }

  callback();
};

// 验证确认密码
const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请再次输入密码'));
    return;
  }

  if (value !== registerForm.password) {
    callback(new Error('两次输入的密码不一致'));
    return;
  }

  callback();
};

// 验证协议同意
const validateAgreement = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请阅读并同意用户协议和隐私政策'));
    return;
  }
  callback();
};

const registerRules = {
  username: [
    { validator: validateUsername, trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  password: [
    { validator: validatePassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ],
  agreement: [
    { validator: validateAgreement, trigger: 'change' }
  ]
}

// 密码强度检测
const passwordStrength = computed(() => {
  const password = registerForm.password;
  if (!password) return 0;

  let strength = 0;

  // 长度检查
  if (password.length >= 6) strength += 1;
  if (password.length >= 8) strength += 1;

  // 包含小写字母
  if (/[a-z]/.test(password)) strength += 1;

  // 包含大写字母
  if (/[A-Z]/.test(password)) strength += 1;

  // 包含数字
  if (/\d/.test(password)) strength += 1;

  // 包含特殊字符
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength += 1;

  return Math.min(strength, 4);
});

const passwordStrengthText = computed(() => {
  const strength = passwordStrength.value;
  const texts = ['', '弱', '一般', '强', '很强'];
  return texts[strength] || '';
});

const passwordStrengthClass = computed(() => {
  const strength = passwordStrength.value;
  const classes = ['', 'weak', 'fair', 'good', 'strong'];
  return classes[strength] || '';
});

const passwordStrengthWidth = computed(() => {
  return `${(passwordStrength.value / 4) * 100}%`;
});

const handleRegister = async () => {
  try {
    await registerFormRef.value.validate()
    loading.value = true

    ElMessage.info('正在注册，请稍候...')

    // 调用管理员注册API
    const response = await authApi.adminRegister({
      username: registerForm.username.trim(),
      phone: registerForm.phone.trim() || null,
      email: registerForm.email.trim() || null,
      password: registerForm.password
    })

    if (response.code === 200) {
      ElMessage.success('注册成功！正在跳转到登录页面...')

      // 清空表单
      registerFormRef.value.resetFields()

      // 跳转到登录页面，并预填用户名
      setTimeout(() => {
        router.push({
          path: '/login',
          query: { username: registerForm.username }
        })
      }, 1000)
    } else {
      ElMessage.error(response.message || '注册失败，请检查信息后重试')
    }
  } catch (error) {
    console.error('注册失败:', error)

    let errorMessage = '注册失败，请重试'
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      if (status === 400) {
        errorMessage = data.message || '请求参数错误'
      } else if (status === 409) {
        errorMessage = '用户名、手机号或邮箱已被注册'
      } else if (status === 403) {
        errorMessage = '邀请码无效或已过期'
      } else if (status >= 500) {
        errorMessage = '服务器错误，请稍后再试'
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 显示用户协议
const showUserAgreement = () => {
  ElMessageBox.alert(
    '这里是用户协议的内容...\n\n1. 用户权利和义务\n2. 服务条款\n3. 免责声明\n4. 其他条款',
    '用户协议',
    {
      confirmButtonText: '我已阅读',
      type: 'info'
    }
  )
}

// 显示隐私政策
const showPrivacyPolicy = () => {
  ElMessageBox.alert(
    '这里是隐私政策的内容...\n\n1. 信息收集\n2. 信息使用\n3. 信息保护\n4. 用户权利',
    '隐私政策',
    {
      confirmButtonText: '我已阅读',
      type: 'info'
    }
  )
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-form {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.form-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

/* 密码强度样式 */
.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background-color: #e4e7ed;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.strength-fill.weak {
  background-color: #f56c6c;
}

.strength-fill.fair {
  background-color: #e6a23c;
}

.strength-fill.good {
  background-color: #409eff;
}

.strength-fill.strong {
  background-color: #67c23a;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 30px;
}

.strength-text.weak {
  color: #f56c6c;
}

.strength-text.fair {
  color: #e6a23c;
}

.strength-text.good {
  color: #409eff;
}

.strength-text.strong {
  color: #67c23a;
}

/* 邀请码提示样式 */
.invite-code-tip {
  margin-top: 4px;
}

.invite-code-tip .el-text {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
