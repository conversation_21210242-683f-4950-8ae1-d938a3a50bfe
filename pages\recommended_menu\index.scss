.recommended-menu-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;

  .page-header {
    text-align: center;
    padding: 40rpx 0;
    background: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;

    .page-title {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 10rpx;
    }

    .page-subtitle {
      font-size: 28rpx;
      color: #666;
    }
  }

  .menu-list {
    .menu-card {
      background: white;
      border-radius: 16rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

      .menu-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20rpx;

        .creator-info {
          display: flex;
          align-items: center;

          .creator-avatar {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            margin-right: 20rpx;
          }

          .creator-details {
            .creator-name {
              display: block;
              font-size: 30rpx;
              font-weight: bold;
              color: #333;
              margin-bottom: 5rpx;
            }

            .create-time {
              font-size: 24rpx;
              color: #999;
            }
          }
        }
      }

      .menu-content {
        .menu-remark {
          font-size: 28rpx;
          color: #666;
          margin-bottom: 20rpx;
          line-height: 1.5;
        }

        .dishes-preview {
          .dish-item {
            display: flex;
            align-items: center;
            padding: 15rpx 0;
            border-bottom: 1rpx solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .dish-image {
              width: 80rpx;
              height: 80rpx;
              border-radius: 8rpx;
              margin-right: 20rpx;
            }

            .dish-info {
              flex: 1;

              .dish-name {
                display: block;
                font-size: 28rpx;
                color: #333;
                margin-bottom: 5rpx;
              }

              .dish-count {
                font-size: 24rpx;
                color: #1989fa;
              }
            }
          }
        }
      }

      .menu-tags {
        display: flex;
        gap: 10rpx;
        margin-top: 20rpx;
      }
    }
  }
}

// 订单弹窗样式
.order-modal {
  height: 100%;
  display: flex;
  flex-direction: column;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .modal-content {
    flex: 1;
    padding: 20rpx;

    .order-dishes {
      .order-dish-item {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f0f0f0;

        .dish-image {
          width: 100rpx;
          height: 100rpx;
          border-radius: 8rpx;
          margin-right: 20rpx;
        }

        .dish-info {
          flex: 1;

          .dish-name {
            display: block;
            font-size: 30rpx;
            color: #333;
            margin-bottom: 10rpx;
          }

          .dish-description {
            font-size: 24rpx;
            color: #666;
          }
        }

        .dish-counter {
          display: flex;
          align-items: center;
          gap: 20rpx;

          .count-text {
            font-size: 28rpx;
            color: #333;
            min-width: 40rpx;
            text-align: center;
          }
        }
      }
    }

    .order-remark {
      margin: 30rpx 0;
    }

    .push-users {
      margin-top: 30rpx;

      .section-title {
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 20rpx;
      }

      .users-list {
        .user-item {
          display: flex;
          align-items: center;
          padding: 20rpx;
          border: 2rpx solid #f0f0f0;
          border-radius: 12rpx;
          margin-bottom: 15rpx;
          transition: all 0.3s;

          &.selected {
            border-color: #1989fa;
            background-color: #f0f9ff;
          }

          .user-avatar {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            margin-right: 20rpx;
          }

          .user-name {
            flex: 1;
            font-size: 28rpx;
            color: #333;
          }
        }
      }

      /* 关联用户缺省页样式 */
      .empty-users-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60rpx 40rpx;
        background: #fafafa;
        border-radius: 16rpx;
        border: 2rpx dashed #e5e7eb;

        .empty-icon {
          margin-bottom: 20rpx;
          opacity: 0.6;
        }

        .empty-text {
          font-size: 28rpx;
          color: #6b7280;
          font-weight: 500;
          margin-bottom: 8rpx;
        }

        .empty-desc {
          font-size: 24rpx;
          color: #9ca3af;
          text-align: center;
          line-height: 1.5;
          margin-bottom: 30rpx;
        }

        .empty-action {
          /* 按钮样式由van-button组件控制 */
        }
      }
    }
  }

  .modal-footer {
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
  }
}
