import request from '@/utils/request'

/**
 * 仪表板 API
 */
export const dashboardApi = {
  /**
   * 获取仪表板概览数据
   */
  getOverview() {
    return request({
      url: '/dashboard/overview',
      method: 'GET'
    })
  },

  /**
   * 获取统计数据
   */
  getStats() {
    return request({
      url: '/dashboard/stats',
      method: 'GET'
    })
  },

  /**
   * 获取用户增长数据
   * @param {Object} params - 查询参数
   * @param {string} params.period - 时间周期 (7d, 30d, 90d)
   */
  getUserGrowth(params = {}) {
    return request({
      url: '/dashboard/user-growth',
      method: 'GET',
      params
    })
  },

  /**
   * 获取订单统计数据
   * @param {Object} params - 查询参数
   * @param {string} params.period - 时间周期
   */
  getOrderStats(params = {}) {
    return request({
      url: '/dashboard/order-stats',
      method: 'GET',
      params
    })
  },

  /**
   * 获取菜品统计数据
   */
  getDishStats() {
    return request({
      url: '/dashboard/dish-stats',
      method: 'GET'
    })
  },

  /**
   * 获取最新用户列表
   * @param {number} limit - 限制数量
   */
  getRecentUsers(limit = 10) {
    return request({
      url: '/dashboard/recent-users',
      method: 'GET',
      params: { limit }
    })
  },

  /**
   * 获取热门菜品列表
   * @param {number} limit - 限制数量
   */
  getPopularDishes(limit = 10) {
    return request({
      url: '/dashboard/popular-dishes',
      method: 'GET',
      params: { limit }
    })
  },

  /**
   * 获取最新订单列表
   * @param {number} limit - 限制数量
   */
  getRecentOrders(limit = 10) {
    return request({
      url: '/dashboard/recent-orders',
      method: 'GET',
      params: { limit }
    })
  },

  /**
   * 获取系统健康状态
   */
  getSystemHealth() {
    return request({
      url: '/dashboard/system-health',
      method: 'GET'
    })
  },

  /**
   * 获取实时数据
   */
  getRealTimeData() {
    return request({
      url: '/dashboard/realtime',
      method: 'GET'
    })
  }
}

export default dashboardApi
