<view class="container">
	<!-- 头部标题 -->
	<view class="text-center mb-4">
		<view class="text-2xl font-bold text-gray-900 mb-2">楠楠家厨</view>
		<view class="text-sm text-gray-500">家庭厨房管理助手</view>
	</view>

	<!-- 登录卡片 -->
	<view class="card">
		<!-- 登录方式切换 -->
		<view class="tab-bar mb-4">
			<view class="tab-item {{loginType === 'wechat' ? 'active' : ''}}" bindtap="switchLoginType" data-type="wechat">
				微信登录
			</view>
			<view class="tab-item {{loginType === 'password' ? 'active' : ''}}" bindtap="switchLoginType" data-type="password">
				账号登录
			</view>
			<!-- <view class="tab-item {{loginType === 'register' ? 'active' : ''}}" bindtap="switchLoginType" data-type="register" >
        注册账号
      </view> -->
		</view>

		<!-- 账号密码登录 -->
		<view wx:if="{{loginType === 'password'}}">
			<view class="input-group">
				<view class="input-label">手机号/账号</view>
				<input class="input" id="login-username" placeholder="请输入手机号或账号" bindinput="onUsernameInput" value="{{username}}" maxlength="11" />
			</view>
			<view class="input-group">
				<view class="input-label">密码</view>
				<input class="input" id="login-password" password="{{true}}" placeholder="请输入密码" bindinput="onPasswordInput" value="{{password}}" />
			</view>
			<view class="flex justify-between items-center mb-3">
				<view class="flex items-center">
					<checkbox checked="{{rememberPwd}}" bindtap="toggleRememberPwd" color="var(--primary-600)" />
					<text class="text-sm text-gray-600 ml-2" bindtap="toggleRememberPwd">记住密码</text>
				</view>
				<text class="text-sm text-primary" bindtap="forgetPassword">忘记密码?</text>
			</view>
			<view wx:if="{{loginTip}}" class="text-sm text-red mb-3">{{loginTip}}</view>
			<button class="btn btn-primary w-full text-center {{loading ? 'opacity-50' : ''}}" bindtap="loginWithPassword" disabled="{{loading}}">
				{{loading ? '登录中...' : '登录'}}
			</button>
		</view>

		<!-- 注册表单 -->
		<view wx:if="{{loginType === 'register'}}">
			<view class="input-group">
				<view class="input-label">姓名</view>
				<input class="input" placeholder="请输入姓名" bindinput="onNameInput" value="{{registerForm.name}}" maxlength="20" />
			</view>
			<view class="input-group">
				<view class="input-label">手机号</view>
				<input class="input" placeholder="请输入手机号" bindinput="onRegisterPhoneInput" value="{{registerForm.phone}}" maxlength="11" type="number" />
			</view>
			<view class="input-group">
				<view class="input-label">密码</view>
				<input class="input" password="{{true}}" placeholder="请输入密码" bindinput="onRegisterPasswordInput" value="{{registerForm.password}}" />
			</view>
			<view class="input-group">
				<view class="input-label">确认密码</view>
				<input class="input" password="{{true}}" placeholder="请确认密码" bindinput="onConfirmPasswordInput" value="{{registerForm.confirmPassword}}" />
			</view>
			<view wx:if="{{loginTip}}" class="text-sm text-red mb-3">{{loginTip}}</view>
			<button class="btn btn-primary w-full {{loading ? 'opacity-50' : ''}}" bindtap="registerWithPassword" disabled="{{loading}}">
				{{loading ? '注册中...' : '注册'}}
			</button>
			<view class="text-center mt-3">
				<text class="text-sm text-gray-500">已有账号？</text>
				<text class="text-sm text-primary ml-1" bindtap="switchToLogin">立即登录</text>
			</view>
		</view>

		<!-- 微信一键登录 -->
		<view wx:if="{{loginType === 'wechat'}}">
			<view class="text-center mb-4">
				<view class="w-32 h-32 bg-green rounded-full flex items-center justify-center mx-auto mb-3">
					<van-icon name="wechat" size="80rpx" color="white" />
				</view>
				<view class="text-base text-gray-700 mb-2">微信快速登录</view>
				<view class="text-sm text-gray-500">点击下方按钮，快速登录</view>
			</view>
			<view wx:if="{{loginTip}}" class="text-sm text-red mb-3 text-center">{{loginTip}}</view>
			<!-- 微信一键登录 -->
			<button class="btn bg-green text-white w-full {{loading ? 'opacity-50' : ''}}" bindtap="loginWithWechat" disabled="{{loading}}">
				{{loading ? loginTip : '微信一键登录'}}
			</button>
			<view class="text-center mt-4">
				<text class="text-xs text-gray-400">登录即表示您已同意</text>
				<text class="text-xs text-primary" bindtap="showPrivacy">《用户协议和隐私政策》</text>
			</view>
		</view>
	</view>
</view>