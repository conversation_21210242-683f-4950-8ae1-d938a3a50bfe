const multer = require('multer');
const path = require('path');
const {success, error} = require('../utils/response');
const picxService = require('../services/picxService');

// 配置 multer 存储
const storage = multer.memoryStorage();

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 允许的文件类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];

  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(
      new Error('Invalid file type. Only JPEG, PNG, GIF and WebP are allowed.'),
      false
    );
  }
};

// 创建 multer 实例
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  },
  fileFilter
});

/**
 * 上传图片
 * @route POST /api/upload/image
 */
const uploadImage = async (req, res) => {
  try {
    // 检查是否有文件
    if (!req.file) {
      return error(res, 'No file uploaded', 400);
    }

    // 获取文件信息和上传参数
    const {buffer, originalname} = req.file;
    const {type = 'general', entityId = '', category = ''} = req.body;

    // 验证上传类型
    const validTypes = ['avatar', 'menu', 'general'];
    if (!validTypes.includes(type)) {
      return error(res, 'Invalid upload type', 400);
    }

    // 根据类型生成文件路径和名称
    const uploadConfig = generateUploadConfig(
      type,
      entityId,
      originalname,
      category
    );

    // 上传到 PicX
    const imageInfo = await picxService.uploadImage(buffer, uploadConfig);

    return success(res, imageInfo, 'Image uploaded successfully', 201);
  } catch (err) {
    console.error('Upload image error:', err);
    return error(res, err.message || 'Failed to upload image', 500);
  }
};

/**
 * 根据上传类型生成配置
 */
const generateUploadConfig = (type, entityId, originalname, category) => {
  const timestamp = Date.now();
  const fileExt = originalname.split('.').pop().toLowerCase();

  switch (type) {
    case 'avatar':
      return {
        folder: 'avatars',
        filename: entityId
          ? `${entityId}.${fileExt}`
          : `avatar_${timestamp}.${fileExt}`,
        originalname
      };

    case 'menu':
      return {
        folder: 'menus',
        filename: entityId
          ? `${entityId}.${fileExt}`
          : `menu_${timestamp}.${fileExt}`,
        originalname
      };

    default: // general
      return {
        folder: 'images',
        filename: `${timestamp}_${originalname}`,
        originalname
      };
  }
};

/**
 * 删除图片
 * @route DELETE /api/upload/image
 */
const deleteImage = async (req, res) => {
  try {
    // 🔥 修复：同时支持 url 和 imageUrl 参数
    const {url, imageUrl, path, sha} = req.body;
    const imageUrlToDelete = url || imageUrl; // 兼容两种参数名

    console.log('🗑️ 删除图片请求:', {
      url,
      imageUrl,
      imageUrlToDelete,
      path,
      sha
    });

    if (!imageUrlToDelete && !path) {
      return error(res, 'Image URL or path is required', 400);
    }

    // 使用uploadService的deleteImage方法，它会自动处理SHA获取
    if (imageUrlToDelete) {
      console.log('📡 调用uploadService.deleteImage:', imageUrlToDelete);
      const uploadService = require('../services/uploadService');
      const deleted = await uploadService.deleteImage(imageUrlToDelete);

      if (deleted) {
        return success(res, {deleted: true}, 'Image deleted successfully', 200);
      } else {
        return error(res, 'Failed to delete image', 500);
      }
    }

    // 如果提供了path和sha，直接调用picxService
    let imagePath = path;
    if (!imagePath && imageUrlToDelete) {
      imagePath = picxService.extractPathFromUrl(imageUrlToDelete);
    }

    if (!imagePath) {
      return error(res, 'Invalid image URL or path', 400);
    }

    if (!sha) {
      return error(res, 'SHA is required when using path parameter', 400);
    }

    // 删除图片
    const deleted = await picxService.deleteImage(imagePath, sha);

    if (deleted) {
      return success(res, {deleted: true}, 'Image deleted successfully', 200);
    } else {
      return error(res, 'Failed to delete image', 500);
    }
  } catch (err) {
    console.error('Delete image error:', err);
    return error(res, err.message || 'Failed to delete image', 500);
  }
};

// 导出中间件处理函数
const handleImageUpload = (req, res, next) => {
  // 使用 multer 处理单个文件上传
  upload.single('image')(req, res, err => {
    if (err) {
      if (err instanceof multer.MulterError) {
        // Multer 错误
        if (err.code === 'LIMIT_FILE_SIZE') {
          return error(res, 'File too large. Maximum size is 5MB.', 400);
        }
        return error(res, err.message, 400);
      }

      // 其他错误
      return error(res, err.message, 400);
    }

    next();
  });
};

module.exports = {
  uploadImage,
  deleteImage,
  handleImageUpload
};
