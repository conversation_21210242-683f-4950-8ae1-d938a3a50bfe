var D=Object.defineProperty;var x=Object.getOwnPropertySymbols;var H=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var F=(r,t,a)=>t in r?D(r,t,{enumerable:!0,configurable:!0,writable:!0,value:a}):r[t]=a,B=(r,t)=>{for(var a in t||(t={}))H.call(t,a)&&F(r,a,t[a]);if(x)for(var a of x(t))I.call(t,a)&&F(r,a,t[a]);return r};import{_ as R,r as i,c as u,b as d,d as _,a as U,t as q,a5 as T,w as p,H as z,I as V,m as P,p as m,a6 as L,g as j,h as A,F as E,l as n,a7 as W}from"./index-XtNpSMFt.js";const G={__name:"CustomTable",props:{title:{type:String,default:""},data:{type:Array,default:()=>[]},columns:{type:Array,required:!0},showHeader:{type:Boolean,default:!0},showSearch:{type:Boolean,default:!1},searchFields:{type:Array,default:()=>[]},showSelection:{type:Boolean,default:!1},showPagination:{type:Boolean,default:!0},pagination:{type:Object,default:()=>({page:1,size:10,total:0})},pageSizes:{type:Array,default:()=>[10,20,50,100]},loading:{type:Boolean,default:!1},loadingText:{type:String,default:"加载中..."}},emits:["search","reset","selection-change","sort-change","size-change","current-change"],setup(r,{expose:t,emit:a}){t();const o=r,c=a,S=j([]),h=A({}),g=()=>{o.searchFields.forEach(l=>{h[l.prop]=""})},v=()=>{c("search",B({},h))},C=()=>{Object.keys(h).forEach(l=>{h[l]=""}),c("reset")},b=l=>{c("selection-change",l)},f=l=>{c("sort-change",l)},k=l=>{c("size-change",l)},y=l=>{c("current-change",l)};E(()=>o.data,l=>{S.value=l},{immediate:!0}),g();const w={props:o,emit:c,tableData:S,searchForm:h,initSearchForm:g,handleSearch:v,handleReset:C,handleSelectionChange:b,handleSortChange:f,handleSizeChange:k,handleCurrentChange:y,ref:j,reactive:A,watch:E};return Object.defineProperty(w,"__isScriptSetup",{enumerable:!1,value:!0}),w}},J={class:"table-container"},K={key:0,class:"table-header"},M={class:"table-title"},Q={class:"table-actions"},X={key:1,class:"table-search"},Y={key:2,class:"table-pagination"};function Z(r,t,a,o,c,S){const h=i("el-input"),g=i("el-option"),v=i("el-select"),C=i("el-date-picker"),b=i("el-form-item"),f=i("el-button"),k=i("el-form"),y=i("el-table-column"),w=i("el-table"),l=i("el-pagination");return n(),u("div",J,[d(" 表格头部 "),a.showHeader?(n(),u("div",K,[U("div",M,q(a.title),1),U("div",Q,[T(r.$slots,"toolbar",{},void 0,!0)])])):d("v-if",!0),d(" 搜索栏 "),a.showSearch?(n(),u("div",X,[_(k,{model:o.searchForm,inline:""},{default:p(()=>[(n(!0),u(z,null,V(a.searchFields,e=>(n(),m(b,{key:e.prop,label:e.label},{default:p(()=>[e.type==="input"?(n(),m(h,{key:0,modelValue:o.searchForm[e.prop],"onUpdate:modelValue":s=>o.searchForm[e.prop]=s,placeholder:e.placeholder,clearable:""},null,8,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="select"?(n(),m(v,{key:1,modelValue:o.searchForm[e.prop],"onUpdate:modelValue":s=>o.searchForm[e.prop]=s,placeholder:e.placeholder,clearable:"",style:{"min-width":"150px",width:"100%"}},{default:p(()=>[(n(!0),u(z,null,V(e.options,s=>(n(),m(g,{key:s.value,label:s.label,value:s.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue","placeholder"])):e.type==="date"?(n(),m(C,{key:2,modelValue:o.searchForm[e.prop],"onUpdate:modelValue":s=>o.searchForm[e.prop]=s,type:"date",placeholder:e.placeholder},null,8,["modelValue","onUpdate:modelValue","placeholder"])):d("v-if",!0)]),_:2},1032,["label"]))),128)),_(b,null,{default:p(()=>[_(f,{type:"primary",onClick:o.handleSearch},{default:p(()=>t[2]||(t[2]=[P("搜索",-1)])),_:1,__:[2]}),_(f,{onClick:o.handleReset},{default:p(()=>t[3]||(t[3]=[P("重置",-1)])),_:1,__:[3]})]),_:1})]),_:1},8,["model"])])):d("v-if",!0),d(" 表格 "),_(w,L({data:o.tableData,loading:a.loading,"element-loading-text":a.loadingText,"element-loading-spinner":"el-icon-loading","element-loading-background":"rgba(0, 0, 0, 0.8)"},r.$attrs,{onSelectionChange:o.handleSelectionChange,onSortChange:o.handleSortChange,class:"custom-table"}),{default:p(()=>[a.showSelection?(n(),m(y,{key:0,type:"selection",width:"55"})):d("v-if",!0),(n(!0),u(z,null,V(a.columns,e=>(n(),m(y,{key:e.prop||e.label,prop:e.prop,label:e.label,width:e.width,"min-width":e.minWidth,sortable:e.sortable,formatter:e.formatter,"show-overflow-tooltip":e.showOverflowTooltip!==!1,fixed:e.fixed},W({_:2},[e.slot?{name:"default",fn:p(({row:s,column:N,$index:O})=>[T(r.$slots,e.slot===!0?e.prop:e.slot,{row:s,column:N,index:O},void 0,!0)]),key:"0"}:void 0]),1032,["prop","label","width","min-width","sortable","formatter","show-overflow-tooltip","fixed"]))),128))]),_:3},16,["data","loading","element-loading-text"]),d(" 分页 "),a.showPagination?(n(),u("div",Y,[_(l,{"current-page":a.pagination.page,"onUpdate:currentPage":t[0]||(t[0]=e=>a.pagination.page=e),"page-size":a.pagination.size,"onUpdate:pageSize":t[1]||(t[1]=e=>a.pagination.size=e),"page-sizes":a.pageSizes,total:a.pagination.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:o.handleSizeChange,onCurrentChange:o.handleCurrentChange},null,8,["current-page","page-size","page-sizes","total"])])):d("v-if",!0)])}const ae=R(G,[["render",Z],["__scopeId","data-v-ae568247"],["__file","E:/wx-nan/webs/admin/src/components/CustomTable.vue"]]);export{ae as C};
