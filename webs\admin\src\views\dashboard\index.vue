<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-6">
      <el-col
        :xs="24"
        :sm="12"
        :md="6"
        v-for="(stat, index) in statistics"
        :key="index"
      >
        <StatsCard
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :type="stat.type"
          :trend="stat.trend"
          :description="stat.description"
          :animated="true"
        />
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-6">
      <el-col :xs="24" :md="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>订单趋势</h3>
            <el-button-group size="small">
              <el-button
                :type="chartPeriod === '7d' ? 'primary' : ''"
                @click="chartPeriod = '7d'"
                >7天</el-button
              >
              <el-button
                :type="chartPeriod === '30d' ? 'primary' : ''"
                @click="chartPeriod = '30d'"
                >30天</el-button
              >
            </el-button-group>
          </div>
          <div ref="orderChartRef" class="chart-container"></div>
        </div>
      </el-col>
      <el-col :xs="24" :md="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>菜品分类统计</h3>
          </div>
          <div ref="categoryChartRef" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 最新数据 -->
    <el-row :gutter="20">
      <el-col :xs="24" :md="12">
        <div class="data-card">
          <div class="card-header">
            <h3>最新订单</h3>
            <el-button type="text" @click="refreshOrders">刷新</el-button>
          </div>
          <el-table :data="recentOrders" style="width: 100%" size="small">
            <el-table-column prop="id" label="订单号" width="80" />
            <el-table-column prop="userName" label="用户" />
            <el-table-column prop="items" label="菜品" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :xs="24" :md="12">
        <div class="data-card">
          <div class="card-header">
            <h3>最新留言</h3>
            <el-button type="text" @click="refreshMessages">刷新</el-button>
          </div>
          <div class="message-list">
            <div
              v-for="message in recentMessages"
              :key="message.id"
              class="message-item"
            >
              <div class="message-header">
                <span class="message-user">{{ message.userName }}</span>
                <span class="message-time">{{
                  formatTime(message.createdAt)
                }}</span>
              </div>
              <div class="message-content">{{ message.content }}</div>
            </div>
            <el-empty
              v-if="!recentMessages.length"
              description="暂无留言"
              :image-size="80"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from "vue";
import * as echarts from "echarts";
import { menuApi } from "@/api/menu";
import { orderApi } from "@/api/order";
import { messageApi } from "@/api/message";
import dayjs from "dayjs";
import StatsCard from "@/components/StatsCard.vue";
import { ShoppingCart, Bowl, User, TrendCharts } from "@element-plus/icons-vue";

const orderChartRef = ref();
const categoryChartRef = ref();
const chartPeriod = ref("7d");

// 统计数据
const statistics = ref([
  {
    title: "今日订单",
    value: 0,
    description: "今日新增订单数",
    icon: ShoppingCart,
    type: "primary",
    trend: { value: 12, unit: "%", label: "较昨日", type: "up" }
  },
  {
    title: "菜品总数",
    value: 0,
    description: "当前可用菜品",
    icon: Bowl,
    type: "success",
    trend: { value: 3, unit: "个", label: "新增", type: "up" }
  },
  {
    title: "活跃用户",
    value: 0,
    description: "本周活跃用户",
    icon: User,
    type: "warning",
    trend: { value: 8, unit: "%", label: "较上周", type: "up" }
  },
  {
    title: "月访问量",
    value: 0,
    description: "本月总访问次数",
    icon: TrendCharts,
    type: "info",
    trend: { value: 15, unit: "%", label: "较上月", type: "up" }
  }
]);

const recentOrders = ref([]);
const recentMessages = ref([]);

// 加载统计数据
const loadStatistics = async () => {
  try {
    const stats = await menuApi.getStatistics();
    if (stats.data) {
      statistics.value[0].value = stats.data.todayOrders || 0;
      statistics.value[1].value = stats.data.totalDishes || 0;
      statistics.value[2].value = stats.data.activeUsers || 0;
      statistics.value[3].value = stats.data.monthlyVisits || 0;
    }
  } catch (error) {
    console.error("加载统计数据失败:", error);
    // 使用模拟数据
    statistics.value[0].value = 28;
    statistics.value[1].value = 156;
    statistics.value[2].value = 1240;
    statistics.value[3].value = 8650;
  }
};

// 加载最新订单
const loadRecentOrders = async () => {
  try {
    const orders = await orderApi.getTodayOrders();
    if (orders.data) {
      recentOrders.value = orders.data.slice(0, 5).map(order => ({
        id: order.id,
        userName: order.user?.name || "未知用户",
        items: JSON.parse(order.items || "[]")
          .map(item => item.dishName)
          .join(", "),
        status: order.status
      }));
    }
  } catch (error) {
    console.error("加载最新订单失败:", error);
    // 使用模拟数据
    recentOrders.value = [
      { id: "001", userName: "张三", items: "红烧肉, 米饭", status: "pending" },
      {
        id: "002",
        userName: "李四",
        items: "西红柿鸡蛋, 紫菜蛋花汤",
        status: "completed"
      },
      { id: "003", userName: "王五", items: "凉拌黄瓜", status: "pending" }
    ];
  }
};

// 加载最新留言
const loadRecentMessages = async () => {
  try {
    const messages = await messageApi.getMessages({ limit: 5 });
    if (messages.data) {
      // 确保 messages.data 是数组
      const messageList = Array.isArray(messages.data) ? messages.data : messages.data.list || [];
      recentMessages.value = messageList.slice(0, 5);
    }
  } catch (error) {
    console.error("加载最新留言失败:", error);
    // 使用模拟数据
    recentMessages.value = [
      {
        id: 1,
        userName: "张三",
        content: "菜品很好吃，下次还会再来！",
        createdAt: new Date()
      },
      {
        id: 2,
        userName: "李四",
        content: "服务态度很好，推荐！",
        createdAt: new Date(Date.now() - 3600000)
      },
      {
        id: 3,
        userName: "王五",
        content: "菜单丰富，价格实惠！",
        createdAt: new Date(Date.now() - 7200000)
      }
    ];
  }
};

// 初始化订单趋势图表
const initOrderChart = () => {
  if (!orderChartRef.value) return;

  const chart = echarts.init(orderChartRef.value);
  const option = {
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.9)",
      borderColor: "#333",
      textStyle: { color: "#fff" }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data:
        chartPeriod.value === "7d"
          ? ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
          : Array.from({ length: 30 }, (_, i) => `${i + 1}日`),
      axisLine: { lineStyle: { color: "#e0e0e0" } }
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#e0e0e0" } },
      splitLine: { lineStyle: { color: "#f0f0f0" } }
    },
    series: [
      {
        data:
          chartPeriod.value === "7d"
            ? [12, 19, 3, 5, 2, 3, 8]
            : Array.from(
                { length: 30 },
                () => Math.floor(Math.random() * 20) + 5
              ),
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: { width: 3 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(24, 144, 255, 0.3)" },
            { offset: 1, color: "rgba(24, 144, 255, 0.1)" }
          ])
        },
        itemStyle: { color: "#1890ff" }
      }
    ]
  };
  chart.setOption(option);

  // 响应式
  window.addEventListener("resize", () => chart.resize());
};

// 初始化分类统计图表
const initCategoryChart = () => {
  if (!categoryChartRef.value) return;

  const chart = echarts.init(categoryChartRef.value);
  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left"
    },
    series: [
      {
        name: "菜品分类",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["60%", "50%"],
        data: [
          { value: 35, name: "热菜", itemStyle: { color: "#5470c6" } },
          { value: 25, name: "凉菜", itemStyle: { color: "#91cc75" } },
          { value: 20, name: "汤品", itemStyle: { color: "#fac858" } },
          { value: 15, name: "主食", itemStyle: { color: "#ee6666" } },
          { value: 5, name: "甜品", itemStyle: { color: "#73c0de" } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: true,
          formatter: "{b}: {d}%"
        }
      }
    ]
  };
  chart.setOption(option);

  // 响应式
  window.addEventListener("resize", () => chart.resize());
};

// 获取状态类型
const getStatusType = status => {
  const statusMap = {
    pending: "warning",
    completed: "success",
    cancelled: "danger"
  };
  return statusMap[status] || "info";
};

// 获取状态文本
const getStatusText = status => {
  const statusMap = {
    pending: "待处理",
    completed: "已完成",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知";
};

// 格式化时间
const formatTime = time => {
  return dayjs(time).format("MM-DD HH:mm");
};

// 刷新订单
const refreshOrders = () => {
  loadRecentOrders();
};

// 刷新留言
const refreshMessages = () => {
  loadRecentMessages();
};

// 监听图表周期变化
watch(chartPeriod, () => {
  nextTick(() => {
    initOrderChart();
  });
});

onMounted(async () => {
  await loadStatistics();
  await loadRecentOrders();
  await loadRecentMessages();

  nextTick(() => {
    initOrderChart();
    initCategoryChart();
  });
});
</script>

<style scoped lang="scss">
.dashboard {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.mb-6 {
  margin-bottom: 24px;
}

.chart-card,
.data-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.chart-header,
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }
}

.chart-container {
  height: 300px;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
}

.message-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-user {
  font-weight: 500;
  color: #333;
}

.message-time {
  font-size: 12px;
  color: #999;
}

.message-content {
  color: #666;
  line-height: 1.5;
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }

  .chart-card,
  .data-card {
    padding: 16px;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
