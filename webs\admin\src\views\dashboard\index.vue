<template>
  <div class="dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1 class="welcome-title">
          <el-icon class="welcome-icon"><Sunny /></el-icon>
          欢迎回来，{{ userInfo?.name || 'Admin' }}！
        </h1>
        <p class="welcome-subtitle">
          今天是 {{ currentDate }}，{{ currentTime }}
        </p>
      </div>
      <div class="quick-actions">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleQuickAction('add-dish')"
        >
          新增菜品
        </el-button>
        <el-button
          type="success"
          :icon="View"
          @click="handleQuickAction('view-orders')"
        >
          查看订单
        </el-button>
        <el-button
          type="warning"
          :icon="Message"
          @click="handleQuickAction('view-messages')"
        >
          查看留言
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-6">
      <el-col
        :xs="24"
        :sm="12"
        :md="6"
        v-for="(stat, index) in statistics"
        :key="index"
      >
        <div class="stats-card" :class="`stats-card--${stat.type}`">
          <div class="stats-card__icon">
            <el-icon :size="32">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stats-card__content">
            <div class="stats-card__value">{{ stat.value }}</div>
            <div class="stats-card__title">{{ stat.title }}</div>
            <div class="stats-card__trend" :class="stat.trend?.type">
              <el-icon v-if="stat.trend?.type === 'up'"><ArrowUp /></el-icon>
              <el-icon v-if="stat.trend?.type === 'down'"
                ><ArrowDown
              /></el-icon>
              <span>{{ stat.trend?.value }}</span>
              <span class="trend-desc">{{ stat.trend?.desc }}</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb-6">
      <el-col :xs="24" :lg="16">
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <el-icon><TrendCharts /></el-icon>
              <h3>订单趋势分析</h3>
            </div>
            <div class="chart-controls">
              <el-radio-group
                v-model="chartPeriod"
                size="small"
                @change="updateOrderChart"
              >
                <el-radio-button value="7d">近7天</el-radio-button>
                <el-radio-button value="30d">近30天</el-radio-button>
                <el-radio-button value="90d">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div
            ref="orderChartRef"
            class="chart-container"
            v-loading="chartLoading"
          ></div>
        </div>
      </el-col>
      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <div class="chart-title">
              <el-icon><PieChart /></el-icon>
              <h3>菜品分类占比</h3>
            </div>
          </div>
          <div ref="categoryChartRef" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 最新数据 -->
    <el-row :gutter="20">
      <el-col :xs="24" :lg="14">
        <div class="data-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon><ShoppingCart /></el-icon>
              <h3>最新订单</h3>
            </div>
            <div class="card-actions">
              <el-button
                type="text"
                :icon="Refresh"
                @click="refreshOrders"
                :loading="ordersLoading"
              >
                刷新
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="handleQuickAction('view-orders')"
              >
                查看全部
              </el-button>
            </div>
          </div>
          <div class="table-container">
            <el-table
              :data="recentOrders"
              style="width: 100%"
              size="small"
              v-loading="ordersLoading"
              empty-text="暂无订单数据"
            >
              <el-table-column prop="id" label="订单号" width="100" />
              <el-table-column prop="userName" label="用户" width="100" />
              <el-table-column
                prop="items"
                label="菜品"
                show-overflow-tooltip
              />
              <el-table-column prop="totalAmount" label="金额" width="80">
                <template #default="{ row }">
                  <span class="amount">¥{{ row.totalAmount }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <el-tag :type="getStatusType(row.status)" size="small">
                    {{ getStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="时间" width="120">
                <template #default="{ row }">
                  <span class="time">{{ formatTime(row.createdAt) }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :lg="10">
        <div class="data-card">
          <div class="card-header">
            <div class="card-title">
              <el-icon><Message /></el-icon>
              <h3>最新留言</h3>
            </div>
            <div class="card-actions">
              <el-button
                type="text"
                :icon="Refresh"
                @click="refreshMessages"
                :loading="messagesLoading"
              >
                刷新
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="handleQuickAction('view-messages')"
              >
                查看全部
              </el-button>
            </div>
          </div>
          <div class="message-list" v-loading="messagesLoading">
            <div
              v-for="message in recentMessages"
              :key="message.id"
              class="message-item"
            >
              <div class="message-avatar">
                <el-avatar :size="32" :src="message.userAvatar">
                  {{ message.userName?.charAt(0) }}
                </el-avatar>
              </div>
              <div class="message-content">
                <div class="message-header">
                  <span class="message-user">{{ message.userName }}</span>
                  <span class="message-time">{{
                    formatTime(message.createdAt)
                  }}</span>
                </div>
                <div class="message-text">{{ message.content }}</div>
                <div class="message-rating" v-if="message.rating">
                  <el-rate v-model="message.rating" disabled size="small" />
                </div>
              </div>
            </div>
            <el-empty
              v-if="!recentMessages.length"
              description="暂无留言"
              :image-size="80"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch, computed } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import * as echarts from "echarts";
import { menuApi } from "@/api/menu";
import { orderApi } from "@/api/order";
import { messageApi } from "@/api/message";
import dayjs from "dayjs";
import {
  ShoppingCart,
  Bowl,
  User,
  TrendCharts,
  PieChart,
  Message,
  Plus,
  View,
  Refresh,
  ArrowUp,
  ArrowDown,
  Sunny
} from "@element-plus/icons-vue";

// 路由和用户信息
const router = useRouter();
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 时间显示
const currentDate = ref('');
const currentTime = ref('');

// 图表引用
const orderChartRef = ref();
const categoryChartRef = ref();
const chartPeriod = ref("7d");

// 加载状态
const chartLoading = ref(false);
const ordersLoading = ref(false);
const messagesLoading = ref(false);

// 统计数据
const statistics = ref([
  {
    title: "今日订单",
    value: 0,
    description: "今日新增订单数",
    icon: ShoppingCart,
    type: "primary",
    trend: { value: 12, unit: "%", label: "较昨日", type: "up" }
  },
  {
    title: "菜品总数",
    value: 0,
    description: "当前可用菜品",
    icon: Bowl,
    type: "success",
    trend: { value: 3, unit: "个", label: "新增", type: "up" }
  },
  {
    title: "活跃用户",
    value: 0,
    description: "本周活跃用户",
    icon: User,
    type: "warning",
    trend: { value: 8, unit: "%", label: "较上周", type: "up" }
  },
  {
    title: "月访问量",
    value: 0,
    description: "本月总访问次数",
    icon: TrendCharts,
    type: "info",
    trend: { value: 15, unit: "%", label: "较上月", type: "up" }
  }
]);

const recentOrders = ref([]);
const recentMessages = ref([]);

// 更新时间显示
const updateTime = () => {
  const now = dayjs();
  currentDate.value = now.format('YYYY年MM月DD日 dddd');
  currentTime.value = now.format('HH:mm:ss');
};

// 快速操作处理
const handleQuickAction = (action) => {
  switch (action) {
    case 'add-dish':
      router.push('/menu/dishes');
      break;
    case 'view-orders':
      router.push('/order/list');
      break;
    case 'view-messages':
      router.push('/message/list');
      break;
    default:
      console.log('未知操作:', action);
  }
};

// 更新订单图表
const updateOrderChart = () => {
  chartLoading.value = true;
  // 模拟加载延迟
  setTimeout(() => {
    initOrderChart();
    chartLoading.value = false;
  }, 1000);
};

// 加载统计数据
const loadStatistics = async () => {
  try {
    const stats = await menuApi.getStatistics();
    if (stats.data) {
      statistics.value[0].value = stats.data.todayOrders || 0;
      statistics.value[1].value = stats.data.totalDishes || 0;
      statistics.value[2].value = stats.data.activeUsers || 0;
      statistics.value[3].value = stats.data.monthlyVisits || 0;
    }
  } catch (error) {
    console.error("加载统计数据失败:", error);
    // 使用模拟数据
    statistics.value[0].value = 28;
    statistics.value[1].value = 156;
    statistics.value[2].value = 1240;
    statistics.value[3].value = 8650;
  }
};

// 加载最新订单
const loadRecentOrders = async () => {
  try {
    const orders = await orderApi.getTodayOrders();
    if (orders.data) {
      recentOrders.value = orders.data.slice(0, 5).map(order => ({
        id: order.id,
        userName: order.user?.name || "未知用户",
        items: JSON.parse(order.items || "[]")
          .map(item => item.dishName)
          .join(", "),
        status: order.status
      }));
    }
  } catch (error) {
    console.error("加载最新订单失败:", error);
    // 使用模拟数据
    recentOrders.value = [
      { id: "001", userName: "张三", items: "红烧肉, 米饭", status: "pending" },
      {
        id: "002",
        userName: "李四",
        items: "西红柿鸡蛋, 紫菜蛋花汤",
        status: "completed"
      },
      { id: "003", userName: "王五", items: "凉拌黄瓜", status: "pending" }
    ];
  }
};

// 加载最新留言
const loadRecentMessages = async () => {
  try {
    const messages = await messageApi.getMessages({ limit: 5 });
    if (messages.data) {
      // 确保 messages.data 是数组
      const messageList = Array.isArray(messages.data) ? messages.data : messages.data.list || [];
      recentMessages.value = messageList.slice(0, 5);
    }
  } catch (error) {
    console.error("加载最新留言失败:", error);
    // 使用模拟数据
    recentMessages.value = [
      {
        id: 1,
        userName: "张三",
        content: "菜品很好吃，下次还会再来！",
        createdAt: new Date()
      },
      {
        id: 2,
        userName: "李四",
        content: "服务态度很好，推荐！",
        createdAt: new Date(Date.now() - 3600000)
      },
      {
        id: 3,
        userName: "王五",
        content: "菜单丰富，价格实惠！",
        createdAt: new Date(Date.now() - 7200000)
      }
    ];
  }
};

// 初始化订单趋势图表
const initOrderChart = () => {
  if (!orderChartRef.value) return;

  const chart = echarts.init(orderChartRef.value);
  const option = {
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(50, 50, 50, 0.9)",
      borderColor: "#333",
      textStyle: { color: "#fff" }
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true
    },
    xAxis: {
      type: "category",
      data:
        chartPeriod.value === "7d"
          ? ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
          : Array.from({ length: 30 }, (_, i) => `${i + 1}日`),
      axisLine: { lineStyle: { color: "#e0e0e0" } }
    },
    yAxis: {
      type: "value",
      axisLine: { lineStyle: { color: "#e0e0e0" } },
      splitLine: { lineStyle: { color: "#f0f0f0" } }
    },
    series: [
      {
        data:
          chartPeriod.value === "7d"
            ? [12, 19, 3, 5, 2, 3, 8]
            : Array.from(
                { length: 30 },
                () => Math.floor(Math.random() * 20) + 5
              ),
        type: "line",
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: { width: 3 },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "rgba(24, 144, 255, 0.3)" },
            { offset: 1, color: "rgba(24, 144, 255, 0.1)" }
          ])
        },
        itemStyle: { color: "#1890ff" }
      }
    ]
  };
  chart.setOption(option);

  // 响应式
  window.addEventListener("resize", () => chart.resize());
};

// 初始化分类统计图表
const initCategoryChart = () => {
  if (!categoryChartRef.value) return;

  const chart = echarts.init(categoryChartRef.value);
  const option = {
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)"
    },
    legend: {
      orient: "vertical",
      left: "left"
    },
    series: [
      {
        name: "菜品分类",
        type: "pie",
        radius: ["40%", "70%"],
        center: ["60%", "50%"],
        data: [
          { value: 35, name: "热菜", itemStyle: { color: "#5470c6" } },
          { value: 25, name: "凉菜", itemStyle: { color: "#91cc75" } },
          { value: 20, name: "汤品", itemStyle: { color: "#fac858" } },
          { value: 15, name: "主食", itemStyle: { color: "#ee6666" } },
          { value: 5, name: "甜品", itemStyle: { color: "#73c0de" } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)"
          }
        },
        label: {
          show: true,
          formatter: "{b}: {d}%"
        }
      }
    ]
  };
  chart.setOption(option);

  // 响应式
  window.addEventListener("resize", () => chart.resize());
};

// 获取状态类型
const getStatusType = status => {
  const statusMap = {
    pending: "warning",
    completed: "success",
    cancelled: "danger"
  };
  return statusMap[status] || "info";
};

// 获取状态文本
const getStatusText = status => {
  const statusMap = {
    pending: "待处理",
    completed: "已完成",
    cancelled: "已取消"
  };
  return statusMap[status] || "未知";
};

// 格式化时间
const formatTime = time => {
  return dayjs(time).format("MM-DD HH:mm");
};

// 刷新订单
const refreshOrders = async () => {
  ordersLoading.value = true;
  try {
    await loadRecentOrders();
  } finally {
    ordersLoading.value = false;
  }
};

// 刷新留言
const refreshMessages = async () => {
  messagesLoading.value = true;
  try {
    await loadRecentMessages();
  } finally {
    messagesLoading.value = false;
  }
};

// 监听图表周期变化
watch(chartPeriod, () => {
  nextTick(() => {
    initOrderChart();
  });
});

onMounted(async () => {
  // 初始化时间显示
  updateTime();
  // 每秒更新时间
  setInterval(updateTime, 1000);

  await loadStatistics();
  await loadRecentOrders();
  await loadRecentMessages();

  nextTick(() => {
    initOrderChart();
    initCategoryChart();
  });
});
</script>

<style scoped lang="scss">
.dashboard {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 60px);
}

/* 页面头部 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.welcome-section {
  flex: 1;
}

.welcome-title {
  display: flex;
  align-items: center;
  font-size: 28px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.welcome-icon {
  margin-right: 12px;
  color: #f6ad55;
}

.welcome-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

/* 统计卡片 */
.stats-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 120px;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.stats-card--primary {
  --card-color: #3182ce;
  --card-color-light: #63b3ed;
}

.stats-card--success {
  --card-color: #38a169;
  --card-color-light: #68d391;
}

.stats-card--warning {
  --card-color: #d69e2e;
  --card-color-light: #f6e05e;
}

.stats-card--info {
  --card-color: #3182ce;
  --card-color-light: #63b3ed;
}

.stats-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--card-color), var(--card-color-light));
  color: white;
  margin-right: 20px;
}

.stats-card__content {
  flex: 1;
}

.stats-card__value {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-card__title {
  font-size: 16px;
  color: #718096;
  margin-bottom: 8px;
}

.stats-card__trend {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
}

.stats-card__trend.up {
  color: #38a169;
}

.stats-card__trend.down {
  color: #e53e3e;
}

.trend-desc {
  margin-left: 4px;
  color: #718096;
}

.mb-6 {
  margin-bottom: 24px;
}

/* 图表和数据卡片 */
.chart-card,
.data-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: 100%;
  transition: all 0.3s ease;
}

.chart-card:hover,
.data-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.chart-header,
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.chart-title,
.card-title {
  display: flex;
  align-items: center;

  .el-icon {
    margin-right: 8px;
    color: #3182ce;
  }

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1a202c;
  }
}

.chart-controls,
.card-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  height: 320px;
}

.table-container {
  border-radius: 8px;
  overflow: hidden;
}

.amount {
  font-weight: 600;
  color: #38a169;
}

.time {
  font-size: 12px;
  color: #718096;
}

/* 留言列表 */
.message-list {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.message-item {
  display: flex;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.message-item:hover {
  background: #f7fafc;
  border-radius: 8px;
  margin: 0 -8px;
  padding: 16px 8px;
}

.message-item:last-child {
  border-bottom: none;
}

.message-avatar {
  margin-right: 12px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-user {
  font-weight: 600;
  color: #1a202c;
  font-size: 14px;
}

.message-time {
  font-size: 12px;
  color: #718096;
}

.message-text {
  color: #4a5568;
  line-height: 1.6;
  font-size: 14px;
  margin-bottom: 8px;
  word-break: break-word;
}

.message-rating {
  margin-top: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }

  .chart-card,
  .data-card {
    padding: 16px;
  }

  .chart-container {
    height: 250px;
  }
}
</style>
