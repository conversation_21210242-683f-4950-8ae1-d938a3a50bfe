.load-more-container {
  padding: 40rpx 20rpx;
}

.load-more-item {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
  
  &.loading {
    gap: 16rpx;
    
    .load-more-text {
      font-size: 28rpx;
      color: #6366f1;
    }
  }
  
  &.more {
    gap: 12rpx;
    padding: 20rpx;
    background: #f8fafc;
    border-radius: 12rpx;
    border: 2rpx solid #e2e8f0;
    transition: all 0.3s ease;
    cursor: pointer;
    
    .load-more-text {
      font-size: 28rpx;
      color: #6366f1;
      font-weight: 500;
    }
    
    &:active {
      background: #e2e8f0;
      transform: scale(0.98);
    }
  }
  
  &.nomore {
    .divider {
      display: flex;
      align-items: center;
      width: 100%;
      gap: 20rpx;
      
      .divider-line {
        flex: 1;
        height: 2rpx;
        background: #e2e8f0;
      }
      
      .divider-text {
        font-size: 24rpx;
        color: #94a3b8;
        white-space: nowrap;
        padding: 0 16rpx;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .load-more-container {
    padding: 32rpx 16rpx;
  }
  
  .load-more-item {
    min-height: 64rpx;
    
    &.loading .load-more-text,
    &.more .load-more-text {
      font-size: 26rpx;
    }
    
    &.nomore .divider .divider-text {
      font-size: 22rpx;
    }
  }
}
