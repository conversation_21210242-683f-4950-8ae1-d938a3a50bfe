// 用户相关类型
export interface User {
  id: number
  name: string
  phone: string
  role: 'admin' | 'member'
  createdAt: string
  updatedAt: string
}

export interface LoginForm {
  username: string
  password: string
  loginType: 'password' | 'wechat'
}

// 菜品相关类型
export interface Dish {
  id: number
  name: string
  category: string
  description: string
  ingredients: string
  cookingMethod: string
  image?: string
  isAvailable: boolean
  createdAt: string
  updatedAt: string
}

export interface DishForm {
  name: string
  category: string
  description: string
  ingredients: string
  cookingMethod: string
  image?: string
  isAvailable: boolean
}

// 订单相关类型
export interface Order {
  id: number
  userId: number
  user?: User
  items: string
  status: 'pending' | 'completed' | 'cancelled'
  mealTime: string
  remark?: string
  createdAt: string
  updatedAt: string
}

export interface OrderItem {
  dishId: number
  dishName: string
  count: number
}

// 消息相关类型
export interface Message {
  id: number
  userId: number
  userName: string
  content: string
  read: boolean
  createdAt: string
  updatedAt: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

export interface PaginationData<T = any> {
  list: T[]
  total: number
  page: number
  size: number
}

// 表格相关类型
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  sortable?: boolean
  formatter?: (row: any) => string
  showOverflowTooltip?: boolean
  slot?: string
}

export interface SearchField {
  prop: string
  label: string
  type: 'input' | 'select' | 'date'
  placeholder: string
  options?: Array<{ label: string; value: any }>
}

export interface Pagination {
  page: number
  size: number
  total: number
}

// 统计数据类型
export interface StatCard {
  title: string
  value: string | number
  desc: string
  type: 'primary' | 'success' | 'warning' | 'danger'
}
