/* 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.container {
	@include page-container;
	@include page-container-safe;
}

.page-header {
	@include flex;
	@include items-center;
	@include justify-center;
	margin-bottom: 30rpx;
}

.page-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #00f2ea;
	text-align: center;
}

.icon-margin {
	margin-right: 16rpx;
}

/* 空购物篮样式 */
.empty-basket {
	@include flex;
	@include flex-col;
	@include items-center;
	@include justify-center;
	margin-top: 100rpx;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 30rpx;
	opacity: 0.8;
}

.empty-text {
	color: #4b5563;
	font-size: 32rpx;
	margin-bottom: 40rpx;
}

.go-order-btn {
	@include modern-btn;
	@include btn-primary;
	@include font-bold;
	padding: 20rpx 60rpx;
	border-radius: 40rpx;
	@include text-lg;
}

/* 订单已提交样式 */
.order-submitted {
	@include flex;
	@include flex-col;
	@include items-center;
	@include justify-center;
	margin-top: 100rpx;
}

.submitted-text {
	color: #00f2ea;
	font-size: 36rpx;
	font-weight: bold;
	margin-top: 30rpx;
	margin-bottom: 40rpx;
}

.submitted-actions {
	@include flex;
	gap: 30rpx;
}

.view-menu-btn,
.continue-order-btn {
	padding: 20rpx 40rpx;
	border-radius: 40rpx;
	font-size: 30rpx;
	font-weight: bold;
	@include shadow-md;
}

.view-menu-btn {
	background: #00f2ea;
	color: #181a20;
}

.continue-order-btn {
	background: #fe2c55;
	color: #111827;
}

/* 订单内容样式 */
.order-content {
	@include flex;
	@include flex-col;
}

.order-list {
	margin-bottom: 30rpx;
}

.order-item {
	background: linear-gradient(135deg, rgba($white, 0.95) 0%, rgba($gray-50, 0.9) 100%);
	border: 2rpx solid rgba($primary-solid, 0.15);
	border-radius: $radius-xl;
	padding: 24rpx;
	margin-bottom: 20rpx;
	@include flex;
	@include items-center;
	@include shadow-md;
	gap: 20rpx;
	@include transition;
	backdrop-filter: blur(8rpx);
	position: relative;

	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba($primary-solid, 0.05) 0%, transparent 50%);
		border-radius: $radius-xl;
		pointer-events: none;
	}

	&:hover {
		transform: translateY(-2rpx);
		@include shadow-lg;
		border-color: rgba($primary-solid, 0.25);
	}
}

.order-item-img {
	width: 120rpx;
	height: 120rpx;
	border-radius: $radius-lg;
	object-fit: cover;
	@include shadow-md;
	border: 3rpx solid rgba($primary-solid, 0.2);
	@include transition;

	&:hover {
		transform: scale(1.02);
		@include shadow-lg;
		border-color: rgba($primary-solid, 0.3);
	}
}

.order-item-info {
	flex: 1;
	min-width: 0;
	@include flex;
	@include flex-col;
	gap: 6rpx;
}

.order-item-name {
	@include text-lg;
	@include font-bold;
	color: $gray-900;
	margin-bottom: 0;
	line-height: 1.3;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.order-item-desc {
	@include text-sm;
	color: $gray-600;
	margin-bottom: 0;
	line-height: 1.4;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.order-item-remark {
	@include text-xs;
	color: $primary-solid;
	@include font-medium;
	background: linear-gradient(135deg, rgba($primary-solid, 0.1) 0%, rgba($primary-solid, 0.05) 100%);
	padding: 4rpx 8rpx;
	border-radius: $radius-sm;
	border: 1rpx solid rgba($primary-solid, 0.2);
	word-break: break-all;
	line-height: 1.3;
}

.order-item-right {
	@include flex;
	@include flex-col;
	align-items: flex-end;
	gap: 12rpx;
	min-width: 80rpx;
}

.order-item-count {
	@include text-lg;
	@include font-bold;
	color: $primary-solid;
	padding: 8rpx 16rpx;
	border-radius: $radius-xl;
	min-width: 56rpx;
	text-align: center;
	position: relative;

	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba($white, 0.2) 0%, transparent 50%);
		border-radius: $radius-xl;
		pointer-events: none;
	}
}

.delete-btn {
	@include text-primary;
	font-size: 36rpx;
	padding: 10rpx;
}

/* 备注和用餐时间 */
.remark-section {
	margin: 30rpx 0;
}

.remark-label {
	color: #4b5563;
	font-size: 30rpx;
	margin-bottom: 10rpx;
}

.remark-input {
	width: 100%;
	background: #f3f4f6;
	color: #111827;
	border: 2rpx solid #00f2ea;
	border-radius: 20rpx;
	padding: 20rpx 24rpx;
	font-size: 28rpx;
	box-sizing: border-box;
	min-height: 120rpx;
	margin-bottom: 30rpx;
}

.time-picker {
	width: 100%;
	background: #f3f4f6;
	color: #111827;
	border: 2rpx solid #00f2ea;
	border-radius: 20rpx;
	padding: 20rpx 24rpx;
	font-size: 28rpx;
	box-sizing: border-box;
	@include flex;
	@include justify-between;
	@include items-center;
	margin-bottom: 30rpx;
}

.picker-value {
	color: #111827;
}

.placeholder-style {
	color: rgba(255, 255, 255, 0.5);
}

/* 提交按钮 */
.submit-btn {
	@include modern-btn;
	@include btn-primary;
	@include font-bold;
	@include btn-full;
	padding: 20rpx 0;
	border-radius: 40rpx;
	@include text-lg;
	@include text-center;
	margin-bottom: 30rpx;
}

/* 历史菜单入口 */
.history-entry {
	background: #20232a;
	border-radius: 20rpx;
	padding: 30rpx;
	@include flex;
	@include justify-between;
	@include items-center;
	box-shadow: 0 4rpx 16rpx rgba(0, 242, 234, 0.13);
	margin-top: 10rpx;
}

.history-title {
	color: #00f2ea;
	font-weight: bold;
	font-size: 30rpx;
	@include flex;
	@include items-center;
}

/* 对话框样式 */
.custom-dialog {
	border-radius: 20rpx !important;
	overflow: hidden !important;
	width: 85% !important;
	/* 控制对话框宽度 */
	max-width: 600rpx !important;
}

.custom-dialog-title {
	padding: 0 !important;
	margin: 0 !important;
	height: 0 !important;
}

.custom-dialog-button {
	font-weight: bold !important;
	font-size: 30rpx !important;
	height: 88rpx !important;
	line-height: 88rpx !important;
	flex: 1 !important;
	max-width: 45% !important;
	/* 控制按钮最大宽度 */
	padding: 0 !important;
	margin: 0 10rpx !important;
}

/* 修复按钮容器样式 */
:host {
	--dialog-width: 85% !important;
	--dialog-max-width: 600rpx !important;
}

/* 修复按钮区域样式 */
.van-dialog__footer {
	display: flex !important;
	justify-content: center !important;
	padding: 20rpx 0 !important;
}

/* 对话框内容样式 */
.dialog-content {
	padding: 40rpx 30rpx 30rpx;
	background: #f8f8f8;
	border-radius: 0 0 24rpx 24rpx;
}

.dialog-header {
	@include flex;
	@include items-center;
	@include justify-center;
	margin-bottom: 30rpx;
	background: #fff;
	padding: 0;
	@include rounded-md;
	margin: -20rpx -10rpx 30rpx;
	@include shadow-md;
}

.dialog-title {
	font-size: 34rpx;
	font-weight: bold;
	color: #333;
	margin-left: 12rpx;
	text-align: center;
}

.dialog-items {
	background: #fff;
	@include rounded-md;
	padding: 20rpx;
	margin-bottom: 30rpx;
	@include shadow-md;
}

.dialog-item {
	@include flex;
	@include justify-between;
	@include items-center;
	padding: 10rpx;
	font-size: 30rpx;
	color: #333;
	border-bottom: 1px solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.dialog-item-name {
	@include flex;
	@include items-center;
}

.dialog-item-dot {
	display: inline-block;
	width: 12rpx;
	height: 12rpx;
	border-radius: 50%;
	background: #00f2ea;
	margin-right: 12rpx;
}

.dialog-item-count {
	@include text-primary;
	font-weight: bold;
}

.dialog-info {
	background: #fff;
	@include rounded-md;
	padding: 20rpx;
	@include shadow-md;
}

.dialog-notice {
	@include flex;
	@include items-center;
	margin-top: 20rpx;
	padding: 16rpx;
	background: #fff3e0;
	border-radius: 12rpx;
	border-left: 6rpx solid #ff9800;
}

.dialog-notice-text {
	margin-left: 12rpx;
	font-size: 24rpx;
	color: #e65100;
	line-height: 1.4;
}

.auth-success {
	color: #2e7d32;
	font-weight: 500;
}

.auth-failed {
	color: #d32f2f;
	font-weight: 500;
}

.auth-unknown {
	color: #f57c00;
	font-weight: 500;
}

/* 用户选择器样式 */
.user-selector {
	width: 100%;
	background: #f3f4f6;
	color: #111827;
	border: 2rpx solid #00f2ea;
	border-radius: 20rpx;
	padding: 20rpx 24rpx;
	font-size: 28rpx;
	box-sizing: border-box;
	@include flex;
	@include justify-between;
	@include items-center;
	margin-bottom: 30rpx;
	cursor: pointer;
}

.user-info {
	@include flex;
	@include items-center;
	gap: 16rpx;
}

.user-avatar {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	object-fit: cover;
	border: 2rpx solid #00f2ea;
}

.user-name {
	color: #111827;
	font-size: 28rpx;
	font-weight: 500;
}

.user-placeholder {
	color: rgba(255, 255, 255, 0.5);
}

/* 用户选择弹窗样式 */
.user-selector-popup {
	background: #fff;
	border-radius: 24rpx 24rpx 0 0;
	overflow: hidden;
}

.popup-header {
	@include flex;
	@include justify-between;
	@include items-center;
	@include p-4;
	border-bottom: 2rpx solid #f0f0f0;
}

.popup-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.user-list {
	padding: 0;
	max-height: 60vh;
	overflow-y: auto;

	/* 用户缺省页样式 */
	.empty-users-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80rpx 40rpx;
		text-align: center;

		.empty-icon {
			margin-bottom: 24rpx;
			opacity: 0.6;
		}

		.empty-text {
			font-size: 28rpx;
			color: #6b7280;
			font-weight: 500;
			margin-bottom: 12rpx;
		}

		.empty-desc {
			font-size: 24rpx;
			color: #9ca3af;
			line-height: 1.5;
			margin-bottom: 40rpx;
		}

		.empty-action {
			/* 按钮样式由van-button组件控制 */
		}
	}
}

.user-item {
	@include flex;
	@include items-center;
	padding: 32rpx;
	cursor: pointer;
	transition: background-color 0.2s;
}

.user-item:active {
	background-color: #f5f5f5;
}

.user-item.selected {
	background-color: #f0f9ff;
}

.user-item-avatar {
	width: 64rpx;
	height: 64rpx;
	border-radius: 50%;
	object-fit: cover;
	margin-right: 24rpx;
	border: 2rpx solid #e5e7eb;
}

.user-item.selected .user-item-avatar {
	border-color: #00f2ea;
}

.user-item-info {
	flex: 1;
	@include flex;
	@include flex-col;
	gap: 8rpx;
}

.user-item-name {
	font-size: 30rpx;
	font-weight: 500;
	color: #333;
}

.user-item.selected .user-item-name {
	color: #00f2ea;
}

.user-item-role {
	font-size: 24rpx;
	color: #666;
}

.dialog-remark,
.dialog-time,
.dialog-user {
	font-size: 28rpx;
	color: #666;
	@include mb-2;
	@include flex;
}

.dialog-remark-label,
.dialog-time-label,
.dialog-user-label {
	font-weight: bold;
	color: #333;
	min-width: 120rpx;
}

.dialog-remark-content,
.dialog-time-content,
.dialog-user-content {
	flex: 1;
	word-break: break-all;
}

/* 今日订单页面特定样式 */
.order-form {
	@include modern-card;
	@include p-4;
	@include mb-4;
}

.submit-btn {
	@include modern-btn;
	@include btn-primary;
	@include btn-full;
	@include btn-lg;
}

.user-selector {
	@include modern-card;
	@include card-flat;
	@include p-3;
	@include mb-3;
}
