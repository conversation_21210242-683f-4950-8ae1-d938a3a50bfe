<template>
  <div class="space-y-1">
    <label 
      v-if="label" 
      :for="selectId"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {{ label }}
      <span v-if="required" class="text-error-500 ml-1">*</span>
    </label>
    
    <Listbox 
      :model-value="modelValue" 
      @update:model-value="handleChange"
      :disabled="disabled"
      :multiple="multiple"
    >
      <div class="relative">
        <ListboxButton
          :id="selectId"
          :class="buttonClasses"
        >
          <span class="block truncate text-left">
            {{ displayValue }}
          </span>
          <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
          </span>
        </ListboxButton>

        <transition
          leave-active-class="transition duration-100 ease-in"
          leave-from-class="opacity-100"
          leave-to-class="opacity-0"
        >
          <ListboxOptions
            class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-800 dark:ring-gray-700 sm:text-sm"
          >
            <ListboxOption
              v-for="option in options"
              :key="getOptionValue(option)"
              :value="getOptionValue(option)"
              v-slot="{ active, selected }"
              as="template"
            >
              <li
                :class="[
                  active ? 'bg-primary-100 text-primary-900 dark:bg-primary-900 dark:text-primary-100' : 'text-gray-900 dark:text-gray-100',
                  'relative cursor-default select-none py-2 pl-3 pr-9'
                ]"
              >
                <span
                  :class="[
                    selected ? 'font-semibold' : 'font-normal',
                    'block truncate'
                  ]"
                >
                  {{ getOptionLabel(option) }}
                </span>

                <span
                  v-if="selected"
                  :class="[
                    active ? 'text-primary-600 dark:text-primary-400' : 'text-primary-600 dark:text-primary-400',
                    'absolute inset-y-0 right-0 flex items-center pr-4'
                  ]"
                >
                  <CheckIcon class="h-5 w-5" aria-hidden="true" />
                </span>
              </li>
            </ListboxOption>
            
            <!-- 空状态 -->
            <li v-if="!options.length" class="relative cursor-default select-none py-2 pl-3 pr-9 text-gray-500 dark:text-gray-400">
              {{ emptyText }}
            </li>
          </ListboxOptions>
        </transition>
      </div>
    </Listbox>
    
    <!-- 帮助文本 -->
    <p 
      v-if="helpText" 
      class="text-sm text-gray-500 dark:text-gray-400"
    >
      {{ helpText }}
    </p>
    
    <!-- 错误信息 -->
    <p 
      v-if="error" 
      class="text-sm text-error-600 dark:text-error-400"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption,
} from '@headlessui/vue'
import { CheckIcon, ChevronUpDownIcon } from '@heroicons/vue/20/solid'

const props = defineProps({
  modelValue: {
    type: [String, Number, Array, Object],
    default: null
  },
  options: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  multiple: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  valueKey: {
    type: String,
    default: 'value'
  },
  labelKey: {
    type: String,
    default: 'label'
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const selectId = ref(`select-${Math.random().toString(36).substr(2, 9)}`)

const buttonClasses = computed(() => {
  const baseClasses = [
    'relative w-full cursor-default rounded-lg border-0 bg-white py-2 pl-3 pr-10 text-left shadow-sm ring-1 ring-inset ring-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-600',
    'disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200',
    'dark:bg-gray-900 dark:text-white dark:ring-gray-700 dark:focus:ring-primary-500'
  ]

  const sizeClasses = {
    sm: 'py-1.5 text-sm',
    md: 'py-2 text-sm',
    lg: 'py-3 text-base'
  }

  const errorClasses = props.error ? 'ring-error-300 focus:ring-error-600' : ''

  return [
    ...baseClasses,
    sizeClasses[props.size],
    errorClasses
  ].filter(Boolean).join(' ')
})

const displayValue = computed(() => {
  if (props.multiple) {
    if (Array.isArray(props.modelValue) && props.modelValue.length > 0) {
      return `已选择 ${props.modelValue.length} 项`
    }
    return props.placeholder
  }

  if (props.modelValue !== null && props.modelValue !== undefined && props.modelValue !== '') {
    const option = props.options.find(opt => getOptionValue(opt) === props.modelValue)
    return option ? getOptionLabel(option) : props.modelValue
  }

  return props.placeholder
})

const getOptionValue = (option) => {
  if (typeof option === 'object' && option !== null) {
    return option[props.valueKey]
  }
  return option
}

const getOptionLabel = (option) => {
  if (typeof option === 'object' && option !== null) {
    return option[props.labelKey]
  }
  return option
}

const handleChange = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}
</script>
