const path = require('path');

// 根据环境加载不同的配置文件
if (process.env.NODE_ENV === 'test') {
  require('dotenv').config({path: path.join(__dirname, '../.env.test')});
} else {
  require('dotenv').config({path: path.join(__dirname, '../.env')});
}

// 调试：打印环境变量 (非测试环境)
if (process.env.NODE_ENV !== 'test') {
  console.log('🔧 环境变量检查:');
  console.log(`   PICX_TOKEN: ${process.env.PICX_TOKEN ? '已配置' : '未配置'}`);
  console.log(`   PICX_REPO: ${process.env.PICX_REPO || '未配置'}`);
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || '未配置'}`);
  console.log(`   PORT: ${process.env.PORT || '未配置'}`);
  console.log('');
}
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const errorHandler = require('./middlewares/errorHandler');

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const menuRoutes = require('./routes/menus');
const dishRoutes = require('./routes/dishes');
const orderRoutes = require('./routes/orders');
const messageRoutes = require('./routes/messages');
const notificationRoutes = require('./routes/notifications');
const uploadRoutes = require('./routes/upload');
const connectionRoutes = require('./routes/connections');
const subscriptionRoutes = require('./routes/subscriptions');
const orderPushRoutes = require('./routes/orderPush');
const wechatCallbackRoutes = require('./routes/wechatCallback');

// 创建 Express 应用
const app = express();

// 中间件
app.use(helmet()); // 安全头
app.use(cors()); // 跨域
app.use(express.json()); // 解析 JSON
app.use(morgan('dev')); // 日志

// API 路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/menus', menuRoutes);
app.use('/api/dishes', dishRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/connections', connectionRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/order-push', orderPushRoutes);
app.use('/api/wechat', wechatCallbackRoutes);

// 根路由
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to wx-nan API',
    version: '1.0.0'
  });
});

// 健康检查路由
app.get('/api/health', (req, res) => {
  res.json({
    code: 200,
    message: 'OK',
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    }
  });
});

// 404 处理
app.use((req, res, next) => {
  res.status(404).json({
    code: 404,
    message: 'Not Found',
    data: null
  });
});

// 错误处理中间件
app.use(errorHandler);

// 启动定时任务服务
const schedulerService = require('./services/schedulerService');

// 启动服务器
const PORT = process.env.PORT || 3000;
const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);

  // 启动定时任务
  schedulerService.start();
});

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('🛑 收到 SIGTERM 信号，开始优雅关闭...');

  // 停止定时任务
  schedulerService.stop();

  // 关闭服务器
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 收到 SIGINT 信号，开始优雅关闭...');

  // 停止定时任务
  schedulerService.stop();

  // 关闭服务器
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

module.exports = app;
