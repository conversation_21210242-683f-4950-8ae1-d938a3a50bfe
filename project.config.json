{"appid": "wx82283b353918af82", "compileType": "miniprogram", "libVersion": "3.8.6", "packOptions": {"ignore": [{"value": "webs", "type": "folder"}], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmManually": true, "useCompilerPlugins": ["sass"], "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": ""}], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreDevUnusedFiles": true, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true, "useNpm": true, "bundle": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "projectname": "nannan", "simulatorPluginLibVersion": {}}