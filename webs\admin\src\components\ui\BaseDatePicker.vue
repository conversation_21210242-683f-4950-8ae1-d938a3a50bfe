<template>
  <div class="space-y-1">
    <label 
      v-if="label" 
      :for="inputId"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {{ label }}
      <span v-if="required" class="text-error-500 ml-1">*</span>
    </label>
    
    <div class="relative">
      <input
        :id="inputId"
        type="date"
        :value="formattedValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :class="inputClasses"
        v-bind="$attrs"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <!-- 日历图标 -->
      <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
        <CalendarDaysIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
      </div>
    </div>
    
    <!-- 帮助文本 -->
    <p 
      v-if="helpText" 
      class="text-sm text-gray-500 dark:text-gray-400"
    >
      {{ helpText }}
    </p>
    
    <!-- 错误信息 -->
    <p 
      v-if="error" 
      class="text-sm text-error-600 dark:text-error-400"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { CalendarDaysIcon } from '@heroicons/vue/24/outline'
import dayjs from 'dayjs'

const props = defineProps({
  modelValue: {
    type: [String, Date],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择日期'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  format: {
    type: String,
    default: 'YYYY-MM-DD'
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus', 'change'])

const inputId = ref(`date-picker-${Math.random().toString(36).substr(2, 9)}`)

const inputClasses = computed(() => {
  const baseClasses = [
    'block w-full rounded-lg border-0 shadow-sm ring-1 ring-inset transition-colors duration-200',
    'placeholder:text-gray-400 focus:ring-2 focus:ring-inset pr-10',
    'disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200',
    'dark:bg-gray-900 dark:text-white dark:ring-gray-700 dark:placeholder:text-gray-500',
    'dark:focus:ring-primary-500 dark:disabled:bg-gray-800 dark:disabled:text-gray-400'
  ]

  // 尺寸样式
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  // 状态样式
  const stateClasses = [
    'bg-white ring-gray-300 focus:ring-primary-600',
    props.error ? 'ring-error-300 focus:ring-error-600' : ''
  ]

  return [
    ...baseClasses,
    sizeClasses[props.size],
    ...stateClasses
  ].filter(Boolean).join(' ')
})

const formattedValue = computed(() => {
  if (!props.modelValue) return ''
  
  if (typeof props.modelValue === 'string') {
    return dayjs(props.modelValue).format('YYYY-MM-DD')
  }
  
  return dayjs(props.modelValue).format('YYYY-MM-DD')
})

const handleInput = (event) => {
  const value = event.target.value
  if (value) {
    const date = dayjs(value).format(props.format)
    emit('update:modelValue', date)
    emit('change', date)
  } else {
    emit('update:modelValue', '')
    emit('change', '')
  }
}

const handleBlur = (event) => {
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}
</script>
