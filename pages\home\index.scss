/* 首页 - 小程序兼容设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  @include page-container;
  @include page-container-safe;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 消息通知
.notice-bar-wrap {
  position: relative;
  margin-bottom: 24rpx;

  .van-notice-bar {
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    font-size: 28rpx;

    // 自定义图标颜色
    .van-icon {
      margin-right: 16rpx;
    }
  }

  // 轮播指示器
  .notice-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 12rpx;
    gap: 12rpx;

    .indicator-dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background-color: #ddd;
      transition: all 0.3s ease;
      cursor: pointer;

      &.active {
        background-color: #2563eb;
        transform: scale(1.3);
        box-shadow: 0 0 8rpx rgba(37, 99, 235, 0.4);
      }

      &:hover {
        background-color: #93c5fd;
      }
    }
  }
}

/* 欢迎卡片 */
.home-welcome-card {
  @include modern-card;
  @include card-primary;
  @include flex;
  @include items-center;
  @include gap-3;
  padding: 40rpx 32rpx;
  margin-bottom: 32rpx;
  @include card-enter(0.1s);
  @include hover-lift;

  .home-welcome-left {
    @include flex-1;
    min-width: 0;
  }

  .home-welcome-title {
    @include text-xl;
    @include font-bold;
    @include text-primary;
    margin-bottom: 8rpx;
    text-align: left;
  }

  .home-welcome-desc {
    @include text-sm;
    @include text-gray-600;
    text-align: left;
    line-height: 1.6;
  }

  .home-welcome-img {
    width: 80rpx;
    height: 80rpx;
    @include rounded-lg;
    object-fit: cover;
    @include shadow-md;
    flex-shrink: 0;
    border: 2rpx solid $primary-solid;
  }
}

/* 今日菜单卡片 */
.home-menu-card {
  @include modern-card;
  @include card-primary;
  @include shadow-lg;
  margin-bottom: 32rpx;
  padding: 32rpx;
  @include card-enter(0.2s);
  @include hover-lift;

  .home-menu-header {
    @include flex;
    @include justify-between;
    @include items-start;
    @include mb-3;

    .home-menu-title {
      @include text-primary;
      @include text-lg;
      @include font-semibold;
      @include flex;
      @include flex-col;

      .title-content {
        @include flex;
        @include items-center;
        @include gap-2;
        height: 40rpx;
        line-height: 40rpx;
      }

      .menu-date {
        @include text-xs;
        @include text-secondary;
        font-weight: normal;
        margin-top: 4rpx;

        &.recommended {
          @include text-error;
          font-style: italic;
        }

        .menu-creator {
          color: #666;
          font-size: 24rpx;
          margin-left: 10rpx;
          font-weight: normal;
        }
      }
    }

    .theme-link {
      @include text-gray-600;
      @include text-xs;
      @include font-medium;
      @include transition;
      text-decoration: none;
      padding: 4rpx 12rpx;
      width: 140rpx;
      height: 60rpx;
      @include rounded-full;
      border: 1rpx solid $gray-300;
      background: $white;
      @include flex;
      @include items-center;
      @include justify-center;

      .van-icon {
        margin-left: 4rpx;
        @include transition-transform;
        font-size: 16rpx;
      }

      &:active {
        @include text-primary;
        border-color: $primary-solid;
        background: rgba($primary-solid, 0.05);

        .van-icon {
          transform: translateX(2rpx);
        }
      }
    }
  }

  .home-menu-list {
    @include flex;
    @include gap-3;
    @include overflow-auto;
    white-space: nowrap;
    padding-bottom: 12rpx;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    .home-menu-food-card {
      min-width: 180rpx;
      max-width: 200rpx;
      @include modern-card;
      @include shadow-md;
      background: linear-gradient(135deg, $white 0%, $gray-50 100%);
      padding: 24rpx 16rpx;
      height: 180rpx;
      @include flex;
      @include flex-col;
      @include items-center;
      @include relative;
      @include transition;
      justify-content: flex-start;
      @include button-press;
      @include hover-lift;

      &:active {
        @include shadow-lg;
        transform: translateY(-4rpx) scale(1.05);
      }

      .home-menu-food-img {
        width: 64rpx;
        height: 64rpx;
        @include rounded-full;
        object-fit: cover;
        border: 3rpx solid $primary-solid;
        margin-bottom: 12rpx;
        @include shadow-sm;
      }

      .home-menu-food-name {
        @include text-gray-900;
        @include font-semibold;
        @include text-sm;
        @include text-center;
        margin-bottom: 4rpx;
        width: 100%;
        @include overflow-hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .home-menu-food-count {
        @include modern-badge;
        @include badge-primary;
        @include text-xs;
        @include absolute;
        top: 12rpx;
        right: 12rpx;
        min-width: 36rpx;
        height: 36rpx;
        line-height: 36rpx;
      }
    }
  }
}

// 留言卡片
.home-message-card {
  @include modern-card;
  @include card-primary;
  @include shadow-lg;
  margin-bottom: 0;
  padding: 32rpx 24rpx;
  @include card-enter(0.3s);
  @include hover-lift;

  .home-message-header {
    @include flex;
    @include justify-between;
    @include items-center;
    @include mb-2;

    .theme-link {
      @include text-gray-600;
      @include text-xs;
      @include font-medium;
      @include transition;
      text-decoration: none;
      padding: 4rpx 12rpx;
      width: 140rpx;
      height: 60rpx;
      @include rounded-full;
      border: 1rpx solid $gray-300;
      background: $white;
      @include flex;
      @include items-center;
      @include justify-center;

      .van-icon {
        margin-left: 4rpx;
        @include transition-transform;
        font-size: 16rpx;
      }

      &:active {
        @include text-primary;
        border-color: $primary-solid;
        background: rgba($primary-solid, 0.05);

        .van-icon {
          transform: translateX(2rpx);
        }
      }
    }
  }

  .home-message-title {
    @include text-primary;
    @include text-lg;
    @include font-semibold;
    @include flex;
    @include items-center;
    @include gap-2;

    .placeholder-hint {
      color: #9ca3af;
      font-size: 24rpx;
      font-weight: normal;
      margin-left: 8rpx;
    }
  }

  .home-message-swiper {
    height: 64rpx;
    margin-top: 12rpx;
    @include modern-card;
    @include card-flat;
    @include rounded-lg;
    padding: 0 16rpx;
    @include flex;
    @include items-center;

    .home-message-swipe {
      height: 64rpx;
      width: 100%;
      @include flex;
      @include items-center;
    }

    swiper-item {
      height: 64rpx;
      @include flex;
      @include items-center;
      @include overflow-hidden;
    }

    .home-message-text {
      @include text-gray-600;
      @include text-sm;
      line-height: 1.4;
      white-space: nowrap;
      @include overflow-hidden;
      text-overflow: ellipsis;
      @include flex;
      @include items-center;
      height: 100%;

      &.placeholder-text {
        color: #9ca3af;
        font-style: italic;
        opacity: 0.8;
      }
    }
  }

  // 无留言时的占位样式
  .home-message-placeholder {
    @include flex;
    @include items-center;
    @include justify-center;
    min-height: 120rpx;
    padding: 32rpx 24rpx;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2rpx dashed #e2e8f0;
    border-radius: 16rpx;
    transition: all 0.3s ease;

    &:active {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      transform: scale(0.98);
    }

    .placeholder-content {
      @include flex;
      @include flex-col;
      @include items-center;
      @include gap-2;
      text-align: center;

      .placeholder-main {
        color: #64748b;
        font-size: 28rpx;
        font-weight: 500;
        margin-top: 8rpx;
      }

      .placeholder-sub {
        color: #94a3b8;
        font-size: 24rpx;
        opacity: 0.8;
      }
    }
  }
}

// 快捷操作样式
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

  .action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15rpx;
    padding: 20rpx;
    border-radius: 12rpx;
    transition: all 0.3s;

    &:active {
      background-color: #f5f5f5;
      transform: scale(0.95);
    }

    text {
      font-size: 24rpx;
      color: #333;
      font-weight: 500;
    }
  }
}
