const {dishApi} = require('../../services/api');
const {imageOptimizer} = require('../../utils/imageOptimizer');
const app = getApp();

// 标签映射 - 将英文标签转换为中文显示
const TAG_LABELS = {
  spicy: '辣',
  numbing: '麻',
  sweet: '甜',
  sour: '酸',
  salty: '咸',
  light: '清淡',
  rich: '浓郁',
  vegetarian: '素食'
};
Page({
  data: {
    currentType: 'all',
    currentTypeIndex: 0,
    currentCategoryName: '全部', // 🔥 新增：当前分类名称
    basketCount: 0,
    categories: [
      {
        type: 'all',
        name: '全部'
      },
      {
        type: 'hot',
        name: '热菜'
      },
      {
        type: 'hotpot',
        name: '火锅'
      },
      {
        type: 'jianghu',
        name: '江湖菜'
      },
      {
        type: 'noodles',
        name: '小面'
      },
      {
        type: 'sichuan',
        name: '川菜'
      },
      {
        type: 'hunan',
        name: '湘菜'
      },
      {
        type: 'cantonese',
        name: '粤菜'
      },
      {
        type: 'cold',
        name: '凉菜'
      },
      {
        type: 'soup',
        name: '汤品'
      },
      {
        type: 'staple',
        name: '主食'
      },
      {
        type: 'vegetarian',
        name: '素菜'
      },
      {
        type: 'dessert',
        name: '甜品'
      },
      {
        type: 'drinks',
        name: '饮品'
      },
      {
        type: 'alcohol',
        name: '酒水'
      },
      {
        type: 'snacks',
        name: '小吃'
      }
    ],
    loading: true,
    foodData: {},
    foodList: []
  },

  async onLoad() {
    // 检查用户登录状态
    await this.checkLoginStatus();
    // 加载菜品数据
    await this.loadDishes();
    // 初始化默认分类的菜品列表（显示全部）
    this.renderFoodList('all');
    // 更新购物篮数量
    this.updateBasketCount();
    // 预加载图片
    this.preloadImages();
  },

  // 检查用户登录状态
  async checkLoginStatus() {
    const token =
      wx.getStorageSync('accessToken') || wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');

    if (!token || !userInfo) {
      // 用户未登录，跳转到登录页
      wx.showModal({
        title: '需要登录',
        content: '请先登录后再查看菜品',
        showCancel: false,
        success: () => {
          wx.redirectTo({
            url: '/pages/login/index'
          });
        }
      });
      return false;
    }

    return true;
  },

  // 加载菜品数据
  async loadDishes() {
    try {
      // wx.showLoading({
      // 	title: '加载菜品中...'
      // });

      const result = await dishApi.getDishesByCategory();
      console.log('result', result);

      if (result.code === 200) {
        // 🔥 新增：处理新的菜品数据格式，包含创建者信息
        const foodData = result.data || {};

        // 统计总菜品数
        let totalDishes = 0;
        Object.values(foodData).forEach(dishes => {
          if (Array.isArray(dishes)) {
            totalDishes += dishes.length;
          }
        });

        this.setData({
          foodData,
          loading: false
        });
      } else {
        console.error('加载菜品失败:', result);
        // wx.showToast({
        //   title: result.message || '加载失败',
        //   icon: 'none'
        // });
      }
    } catch (error) {
      // 认证失败已在请求拦截器中统一处理
      // 只处理其他类型的错误
      if (error.code !== 401) {
        wx.showToast({
          title: error.message || '网络错误',
          icon: 'error'
        });
      }
    } finally {
      wx.hideLoading();
    }
  },

  async onShow() {
    // 🔥 新增：页面显示时检查登录状态
    const isLoggedIn = await this.checkLoginStatus();
    if (!isLoggedIn) {
      return;
    }

    // 页面显示时更新购物篮数量
    this.updateBasketCount();

    // 🔥 修复：每次显示页面都重新加载数据，确保显示最新的菜品状态
    await this.loadDishes();
    this.renderFoodList(this.data.currentType);
  },

  // 🔥 优化：切换分类 - 更新分类名称
  switchCategory(e) {
    const type = e.currentTarget.dataset.type;
    const currentTypeIndex = this.data.categories.findIndex(
      cat => cat.type === type
    );
    const currentCategory = this.data.categories.find(cat => cat.type === type);

    this.setData({
      currentType: type,
      currentTypeIndex: currentTypeIndex >= 0 ? currentTypeIndex : 0,
      currentCategoryName: currentCategory ? currentCategory.name : '全部'
    });
    this.renderFoodList(type);
  },

  // 🔥 优化：渲染菜品列表 - 处理新的数据格式，支持"全部"分类
  renderFoodList(type) {
    let foodList = [];

    if (type === 'all') {
      // 如果选择"全部"，合并所有分类的菜品
      foodList = [];
      Object.keys(this.data.foodData).forEach(categoryType => {
        if (categoryType !== 'all') {
          // 避免循环引用
          const categoryFoods = this.data.foodData[categoryType] || [];
          foodList = foodList.concat(categoryFoods);
        }
      });
      console.log(`📋 全部分类包含 ${foodList.length} 个菜品`);
    } else {
      // 特定分类的菜品
      foodList = this.data.foodData[type] || [];
    }

    // 🔥 修复：为每个菜品添加增强数据和控制属性
    const enhancedFoodList = foodList.map(item => {
      // console.log('🔍 处理菜品数据:', item); // 🔥 调试：查看原始数据

      // 🔥 增强：检查日期字段
      const dateField =
        item.createdAt || item.created_at || item.createTime || item.createDate;
      // console.log('📅 日期字段检查:', {
      //   createdAt: item.createdAt,
      //   created_at: item.created_at,
      //   createTime: item.createTime,
      //   createDate: item.createDate,
      //   selected: dateField
      // });

      const processedItem = {
        ...item,
        isAdding: false,
        // 🔥 修复：处理标签数据 - 确保是数组
        tags: this.processTags(item.tags),
        // 🔥 增强：格式化创建日期 - 更多字段兼容
        createdDate: this.formatDate(dateField),
        // 🔥 修复：确保分类信息 - 兼容多种数据结构
        category: item.category || {
          name: item.categoryName || '未分类'
        },
        // 🔥 修复：标识是否为我的菜品
        isMyDish:
          item.creator && item.creator.id === app.globalData.userInfo?.id
      };

      // console.log('✅ 处理后的菜品:', processedItem);
      return processedItem;
    });

    this.setData({
      foodList: enhancedFoodList
    });
  },

  // 🔥 新增：处理标签数据
  processTags(tags) {
    if (!tags) return [];

    try {
      let tagArray = [];
      // 如果是字符串，尝试解析JSON
      if (typeof tags === 'string') {
        tagArray = JSON.parse(tags);
      }
      // 如果已经是数组，直接使用
      else if (Array.isArray(tags)) {
        tagArray = tags;
      }

      // 将英文标签转换为中文显示
      return tagArray.map(tag => this.getTagLabel(tag));
    } catch (e) {
      console.warn('解析标签失败:', e);
    }

    return [];
  },

  // 获取标签显示名称
  getTagLabel(tag) {
    return TAG_LABELS[tag] || tag;
  },

  // 🔥 新增：格式化日期
  formatDate(dateString) {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = now - date;
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        return '今天';
      } else if (diffDays === 1) {
        return '昨天';
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (e) {
      console.warn('格式化日期失败:', e);
      return '';
    }
  },

  // 跳转到菜品详情页
  async goToDetail(e) {
    const id = e.currentTarget.dataset.id;

    try {
      wx.showLoading({
        title: '加载中...'
      });

      const result = await dishApi.getDishDetail(id);

      if (result.code === 200) {
        // 将详情数据存入缓存
        wx.setStorageSync('detailData', result.data);

        // 跳转到详情页
        wx.navigateTo({
          url: '/pages/detail/index'
        });
      } else {
        wx.showToast({
          title: '获取详情失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('获取菜品详情失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 添加到购物篮
  addToBasket(e) {
    const {id, name, remark, img, index} = e.currentTarget.dataset;

    // 获取购物篮数据
    let basket = wx.getStorageSync('basket') || {};

    if (!basket[id]) {
      basket[id] = {
        id,
        name,
        remark,
        img,
        count: 1
      };
    } else {
      basket[id].count += 1;
    }

    // 保存购物篮数据
    wx.setStorageSync('basket', basket);

    // 更新购物篮数量
    this.updateBasketCount();

    // 显示添加动画
    const foodList = [...this.data.foodList];
    foodList[index].isAdding = true;
    this.setData({
      foodList
    });

    // 动画结束后恢复
    setTimeout(() => {
      foodList[index].isAdding = false;
      this.setData({
        foodList
      });
    }, 600);

    // 提示用户
    wx.showToast({
      title: '已添加到购物篮',
      icon: 'success',
      duration: 1000
    });
  },

  // 更新购物篮数量
  updateBasketCount() {
    const basket = wx.getStorageSync('basket') || {};
    const total = Object.values(basket).reduce(
      (sum, item) => sum + item.count,
      0
    );

    this.setData({
      basketCount: total
    });
  },

  // 跳转到购物篮页面
  goToBasket() {
    wx.navigateTo({
      url: '/pages/today_order/index'
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  },

  /**
   * 预加载图片
   */
  preloadImages() {
    // 获取当前显示的菜品列表
    const currentFoodList = this.data.foodList || [];

    if (currentFoodList.length > 0) {
      // 预加载当前页面的图片
      imageOptimizer.preloadPageImages(currentFoodList, {
        maxCount: 8, // 只预加载前8张
        imageType: 'dish',
        priority: 'high'
      });

      console.log(
        `🖼️ 开始预加载 ${Math.min(8, currentFoodList.length)} 张菜品图片`
      );
    }
  },

  /**
   * 获取优化后的图片URL
   */
  getOptimizedImageUrl(src) {
    return imageOptimizer.optimizeUrl(src, {
      width: 400,
      height: 300,
      quality: 80
    });
  }
});
