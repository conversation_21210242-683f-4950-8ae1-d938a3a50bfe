const {dishApi} = require('../../services/api');
const app = getApp();

// 标签映射 - 将英文标签转换为中文显示
const TAG_LABELS = {
  spicy: '辣',
  numbing: '麻',
  sweet: '甜',
  sour: '酸',
  salty: '咸',
  light: '清淡',
  rich: '浓郁',
  vegetarian: '素食'
};

Page({
  data: {
    menuItem: {},
    imageLoaded: false,
    pageLoaded: false,
    basketCount: 0
  },

  async onLoad(options) {
    // 检查是否有菜品ID参数，如果有则直接调用API获取
    if (options.id) {
      await this.loadDishDetail(options.id);
    } else {
      // 从缓存中获取详情数据（兼容旧的跳转方式）
      const detailData = wx.getStorageSync('detailData');

      if (detailData) {
        // 处理菜品数据，添加格式化信息
        const processedItem = this.processMenuItemData(detailData);

        this.setData({
          menuItem: processedItem
        });

        console.log('📋 菜品详情数据:', processedItem);

        // 启动进入动画
        this.startEnterAnimations();
      } else {
        // 如果没有数据，显示提示并返回
        wx.showToast({
          title: '菜品信息获取失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    }

    // 初始化页面状态
    this.updateBasketCount();
  },

  // 🔥 新增：直接调用API获取菜品详情
  async loadDishDetail(dishId) {
    try {
      wx.showLoading({
        title: '加载中...'
      });

      const result = await dishApi.getDishDetail(dishId);

      if (result.code === 200) {
        // 处理菜品数据，添加格式化信息
        const processedItem = this.processMenuItemData(result.data);

        this.setData({
          menuItem: processedItem
        });

        console.log('📋 菜品详情数据:', processedItem);

        // 启动进入动画
        this.startEnterAnimations();
      } else {
        wx.showToast({
          title: result.message || '获取详情失败',
          icon: 'error'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    } catch (error) {
      console.error('获取菜品详情失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    } finally {
      wx.hideLoading();
    }
  },

  onReady() {
    // 页面渲染完成
    this.setData({
      pageLoaded: true
    });
  },

  // 🔥 新增：处理菜品数据
  processMenuItemData(item) {
    return {
      ...item,
      // 🔥 处理标签数据
      tags: this.processTags(item.tags),
      // 🔥 格式化创建日期
      createdDate: this.formatDate(
        item.createdAt || item.created_at || item.createTime
      ),
      // 🔥 确保分类信息
      category: item.category || {name: item.categoryName || '未分类'},
      // 🔥 标识是否为我的菜品
      isMyDish: item.creator && item.creator.id === app.globalData.userInfo?.id
    };
  },

  // 🔥 新增：处理标签数据
  processTags(tags) {
    if (!tags) return [];

    try {
      let tagArray = [];
      // 如果是字符串，尝试解析JSON
      if (typeof tags === 'string') {
        tagArray = JSON.parse(tags);
      }
      // 如果已经是数组，直接使用
      else if (Array.isArray(tags)) {
        tagArray = tags;
      }

      // 将英文标签转换为中文显示
      return tagArray.map(tag => this.getTagLabel(tag));
    } catch (e) {
      console.warn('解析标签失败:', e);
    }

    return [];
  },

  // 获取标签显示名称
  getTagLabel(tag) {
    return TAG_LABELS[tag] || tag;
  },

  // 🔥 新增：格式化日期
  formatDate(dateString) {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = now - date;
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

      if (diffDays === 0) {
        return '今天';
      } else if (diffDays === 1) {
        return '昨天';
      } else if (diffDays < 7) {
        return `${diffDays}天前`;
      } else {
        return date.toLocaleDateString('zh-CN', {
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (e) {
      console.warn('格式化日期失败:', e);
      return '';
    }
  },

  // 🎬 启动进入动画
  startEnterAnimations() {
    // 延迟启动动画，确保DOM渲染完成
    setTimeout(() => {
      this.animateElements();
    }, 100);
  },

  // 🎭 元素动画控制
  animateElements() {
    const query = wx.createSelectorQuery();

    // 为所有带动画属性的元素添加动画类
    query
      .selectAll('[data-animate]')
      .boundingClientRect(rects => {
        if (rects && rects.length > 0) {
          rects.forEach((rect, index) => {
            const element = rect;
            const animationType = element.dataset?.animate || 'fadeInUp';
            const delay = parseInt(element.dataset?.delay || 0);

            setTimeout(() => {
              // 通过类名控制动画
              const selector = `[data-animate="${animationType}"][data-delay="${delay}"]`;
              wx.createSelectorQuery()
                .select(selector)
                .node(res => {
                  if (res && res.node) {
                    res.node.classList.add(`animate-${animationType}`);
                  }
                })
                .exec();
            }, delay);
          });
        }
      })
      .exec();

    // 标记页面加载完成，触发CSS动画
    setTimeout(() => {
      this.setData({
        pageLoaded: true
      });
    }, 200);
  },

  // 🖼️ 图片加载完成
  onImageLoad() {
    this.setData({
      imageLoaded: true
    });
  },

  // 🔍 图片预览
  onImagePreview() {
    const menuItem = this.data.menuItem;
    const imageUrl = menuItem.img || menuItem.image;

    if (imageUrl) {
      wx.previewImage({
        current: imageUrl,
        urls: [imageUrl],
        success: () => {},
        fail: error => {
          console.error('图片预览失败:', error);
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无图片',
        icon: 'none'
      });
    }
  },

  // 🛒 添加到购物车
  addToCart() {
    const menuItem = this.data.menuItem;

    if (!menuItem.id) {
      wx.showToast({
        title: '菜品信息错误',
        icon: 'error'
      });
      return;
    }

    // 获取购物篮数据
    let basket = wx.getStorageSync('basket') || {};

    if (!basket[menuItem.id]) {
      basket[menuItem.id] = {
        id: menuItem.id,
        name: menuItem.name,
        remark: menuItem.remark || menuItem.description,
        img: menuItem.img || menuItem.image,
        count: 1
      };
    } else {
      basket[menuItem.id].count += 1;
    }

    // 保存购物篮数据
    wx.setStorageSync('basket', basket);

    // 更新购物篮数量
    this.updateBasketCount();

    // 提示用户
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success',
      duration: 1500
    });
  },

  // 🔥 更新购物篮数量
  updateBasketCount() {
    const basket = wx.getStorageSync('basket') || {};
    const total = Object.values(basket).reduce(
      (sum, item) => sum + item.count,
      0
    );

    this.setData({
      basketCount: total
    });
  },

  // 🔥 跳转到购物篮页面
  goToBasket() {
    wx.navigateTo({
      url: '/pages/today_order/index'
    });
  },

  // 🔥 分享菜品
  onShareAppMessage() {
    const menuItem = this.data.menuItem;
    return {
      title: `推荐一道美味菜品：${menuItem.name}`,
      path: `/pages/detail/index?id=${menuItem.id}`,
      imageUrl: menuItem.img || menuItem.image
    };
  }
});
