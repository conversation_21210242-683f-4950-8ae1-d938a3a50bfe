var z=(L,t,m)=>new Promise((l,b)=>{var S=o=>{try{d(m.next(o))}catch(n){b(n)}},u=o=>{try{d(m.throw(o))}catch(n){b(n)}},d=o=>o.done?l(o.value):Promise.resolve(o.value).then(S,u);d((m=m.apply(L,t)).next())});import{_ as de,r as p,c as A,b as y,d as r,w as i,g,G as k,h as T,B,C as Y,j as P,D as Z,a1 as $,Q as ee,o as te,n as ae,s as se,E,R as ce,l as C,H as F,I as j,p as I,a as e,m as D,t as f,y as ue,a2 as _e}from"./index-Cy8N1eGd.js";import{S as me,i as N,e as he}from"./StatsCard-SUxJzo4F.js";import{m as re}from"./menu-BDmPj0yF.js";import{o as oe}from"./order-D36AVmfp.js";import{u as ie}from"./user-DGiv-qs1.js";const fe={__name:"overview",setup(L,{expose:t}){t();const m=g(),l=g(),b=g(),S=g("7d"),u=g(!0),d=g(k().format("HH:mm:ss"));let o=null,n=null,c=null,h=null;const w=T([{title:"今日订单",value:0,icon:B,type:"primary",trend:"+12%",description:"较昨日"},{title:"菜品总数",value:0,icon:Y,type:"success",trend:"+5%",description:"较上周"},{title:"活跃用户",value:0,icon:P,type:"warning",trend:"+8%",description:"较上月"},{title:"月访问量",value:0,icon:Z,type:"danger",trend:"+15%",description:"较上月"}]),R=g([]),a=T([{label:"在线用户",value:0,icon:P,type:"primary"},{label:"今日订单",value:0,icon:B,type:"success"},{label:"待处理",value:0,icon:$,type:"warning"},{label:"页面浏览",value:0,icon:ee,type:"info"}]),v=()=>z(this,null,function*(){try{const[s,_,x]=yield Promise.all([re.getStatistics(),oe.getOrderStatistics(),ie.getUserStatistics()]);x.code===200&&(w[2].value=x.data.active||0),_.code===200&&(w[0].value=_.data.today||0),s.code===200&&(w[1].value=s.data.totalDishes||0,w[3].value=s.data.monthlyVisits||0)}catch(s){console.error("加载核心统计数据失败:",s),E.error("加载统计数据失败")}}),U=()=>z(this,null,function*(){try{const s=yield dishApi.getHotDishes({limit:10});s.code===200?R.value=s.data||[]:E.error(s.message||"加载热门菜品失败")}catch(s){console.error("加载热门菜品失败:",s),E.error("加载热门菜品失败")}}),G=()=>{m.value&&(o=N(m.value),Q())},Q=()=>{if(!o)return;const s=S.value==="7d"?7:S.value==="30d"?30:90,_=[],x=[];for(let V=s-1;V>=0;V--)_.push(k().subtract(V,"day").format("MM-DD")),x.push(Math.floor(Math.random()*50)+10);const M={tooltip:{trigger:"axis",axisPointer:{type:"cross"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:_,axisLine:{lineStyle:{color:"#E4E7ED"}}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#E4E7ED"}}},series:[{name:"订单数",type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{color:"#409EFF",width:3},itemStyle:{color:"#409EFF"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}},data:x}]};o.setOption(M)},W=()=>{if(!l.value)return;n=N(l.value);const _={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"菜品分类",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:"18",fontWeight:"bold"}},labelLine:{show:!1},data:[{value:35,name:"热菜"},{value:25,name:"凉菜"},{value:20,name:"汤品"},{value:15,name:"主食"},{value:5,name:"甜品"}]}]};n.setOption(_)},H=()=>{if(!b.value)return;c=N(b.value);const s=[],_=[];for(let M=0;M<24;M++)s.push(M+":00"),_.push(Math.floor(Math.random()*100)+20);const x={tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:s,axisLine:{lineStyle:{color:"#E4E7ED"}}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#E4E7ED"}}},series:[{name:"活跃用户",type:"bar",data:_,itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#67C23A"},{offset:1,color:"#85CE61"}]}}}]};c.setOption(x)},le=()=>{U(),E.success("热门菜品数据已刷新")},ne=()=>{H(),E.success("用户活跃度数据已刷新")},q=()=>{a[0].value=Math.floor(Math.random()*50)+20,a[1].value=Math.floor(Math.random()*100)+50,a[2].value=Math.floor(Math.random()*10)+2,a[3].value=Math.floor(Math.random()*1e3)+500,d.value=k().format("HH:mm:ss")},J=()=>{h=setInterval(q,5e3)},K=()=>{h&&(clearInterval(h),h=null)},O=()=>{o&&o.resize(),n&&n.resize(),c&&c.resize()};te(()=>z(this,null,function*(){yield v(),yield U(),ae(()=>{G(),W(),H()}),J(),window.addEventListener("resize",O)})),se(()=>{K(),window.removeEventListener("resize",O),o&&(o.dispose(),o=null),n&&(n.dispose(),n=null),c&&(c.dispose(),c=null)});const X={orderChartRef:m,categoryChartRef:l,activityChartRef:b,orderPeriod:S,realtimeStatus:u,lastUpdateTime:d,get orderChart(){return o},set orderChart(s){o=s},get categoryChart(){return n},set categoryChart(s){n=s},get activityChart(){return c},set activityChart(s){c=s},get realtimeTimer(){return h},set realtimeTimer(s){h=s},coreStats:w,hotDishes:R,realtimeData:a,loadCoreStats:v,loadHotDishes:U,initOrderChart:G,updateOrderChart:Q,initCategoryChart:W,initActivityChart:H,refreshHotDishes:le,refreshUserActivity:ne,updateRealtimeData:q,startRealtimeUpdate:J,stopRealtimeUpdate:K,handleResize:O,ref:g,reactive:T,onMounted:te,nextTick:ae,onUnmounted:se,get echarts(){return he},get ElMessage(){return E},get Refresh(){return ce},get ShoppingCart(){return B},get Bowl(){return Y},get User(){return P},get TrendCharts(){return Z},get Clock(){return $},get View(){return ee},StatsCard:me,get menuApi(){return re},get orderApi(){return oe},get userApi(){return ie},get dayjs(){return k}};return Object.defineProperty(X,"__isScriptSetup",{enumerable:!1,value:!0}),X}},ve={class:"analytics-overview"},pe={class:"chart-card"},ye={class:"chart-header"},ge={ref:"orderChartRef",class:"chart-container"},Ce={class:"chart-card"},be={ref:"categoryChartRef",class:"chart-container"},we={class:"data-card"},xe={class:"card-header"},Se={class:"hot-dishes-list"},Ee={class:"dish-rank"},De={class:"dish-image"},Me={class:"dish-info"},Ae={class:"dish-name"},Re={class:"dish-category"},ze={class:"dish-stats"},ke={class:"order-count"},Le={class:"revenue"},Ue={class:"data-card"},He={class:"card-header"},Oe={class:"activity-chart"},Ve={ref:"activityChartRef",class:"chart-container"},Te={class:"realtime-card"},Be={class:"card-header"},Pe={class:"realtime-status"},Fe={class:"update-time"},je={class:"realtime-item"},Ie={class:"item-info"},Ne={class:"item-value"},Ge={class:"item-label"};function Qe(L,t,m,l,b,S){const u=p("el-col"),d=p("el-row"),o=p("el-radio-button"),n=p("el-radio-group"),c=p("el-icon"),h=p("el-button"),w=p("el-image"),R=p("el-tag");return C(),A("div",ve,[y(" 核心指标卡片 "),r(d,{gutter:20,class:"stats-row"},{default:i(()=>[(C(!0),A(F,null,j(l.coreStats,(a,v)=>(C(),I(u,{xs:24,sm:12,md:6,key:v},{default:i(()=>[r(l.StatsCard,{title:a.title,value:a.value,icon:a.icon,type:a.type,trend:a.trend,description:a.description,animated:!0},null,8,["title","value","icon","type","trend","description"])]),_:2},1024))),128))]),_:1}),y(" 图表区域 "),r(d,{gutter:20,class:"charts-row"},{default:i(()=>[y(" 订单趋势图 "),r(u,{xs:24,lg:12},{default:i(()=>[e("div",pe,[e("div",ye,[t[4]||(t[4]=e("h3",null,"订单趋势",-1)),r(n,{modelValue:l.orderPeriod,"onUpdate:modelValue":t[0]||(t[0]=a=>l.orderPeriod=a),size:"small",onChange:l.updateOrderChart},{default:i(()=>[r(o,{label:"7d"},{default:i(()=>t[1]||(t[1]=[D("7天",-1)])),_:1,__:[1]}),r(o,{label:"30d"},{default:i(()=>t[2]||(t[2]=[D("30天",-1)])),_:1,__:[2]}),r(o,{label:"90d"},{default:i(()=>t[3]||(t[3]=[D("90天",-1)])),_:1,__:[3]})]),_:1},8,["modelValue"])]),e("div",ge,null,512)])]),_:1}),y(" 菜品分类分布 "),r(u,{xs:24,lg:12},{default:i(()=>[e("div",Ce,[t[5]||(t[5]=e("div",{class:"chart-header"},[e("h3",null,"菜品分类分布")],-1)),e("div",be,null,512)])]),_:1})]),_:1}),y(" 热门菜品和用户活跃度 "),r(d,{gutter:20,class:"data-row"},{default:i(()=>[y(" 热门菜品排行 "),r(u,{xs:24,lg:12},{default:i(()=>[e("div",we,[e("div",xe,[t[7]||(t[7]=e("h3",null,"热门菜品排行",-1)),r(h,{size:"small",onClick:l.refreshHotDishes},{default:i(()=>[r(c,null,{default:i(()=>[r(l.Refresh)]),_:1}),t[6]||(t[6]=D(" 刷新 ",-1))]),_:1,__:[6]})]),e("div",Se,[(C(!0),A(F,null,j(l.hotDishes,(a,v)=>(C(),A("div",{key:a.id,class:"dish-item"},[e("div",Ee,f(v+1),1),e("div",De,[r(w,{src:a.image,fit:"cover"},null,8,["src"])]),e("div",Me,[e("h4",Ae,f(a.name),1),e("p",Re,f(a.category),1)]),e("div",ze,[e("div",ke,f(a.orderCount)+"次",1),e("div",Le,"¥"+f(a.revenue),1)])]))),128))])])]),_:1}),y(" 用户活跃度 "),r(u,{xs:24,lg:12},{default:i(()=>[e("div",Ue,[e("div",He,[t[9]||(t[9]=e("h3",null,"用户活跃度",-1)),r(h,{size:"small",onClick:l.refreshUserActivity},{default:i(()=>[r(c,null,{default:i(()=>[r(l.Refresh)]),_:1}),t[8]||(t[8]=D(" 刷新 ",-1))]),_:1,__:[8]})]),e("div",Oe,[e("div",Ve,null,512)])])]),_:1})]),_:1}),y(" 实时数据 "),r(d,{gutter:20,class:"realtime-row"},{default:i(()=>[r(u,{span:24},{default:i(()=>[e("div",Te,[e("div",Be,[t[10]||(t[10]=e("h3",null,"实时数据",-1)),e("div",Pe,[r(R,{type:l.realtimeStatus?"success":"danger",size:"small"},{default:i(()=>[D(f(l.realtimeStatus?"在线":"离线"),1)]),_:1},8,["type"]),e("span",Fe,"最后更新: "+f(l.lastUpdateTime),1)])]),r(d,{gutter:20,class:"realtime-stats"},{default:i(()=>[(C(!0),A(F,null,j(l.realtimeData,(a,v)=>(C(),I(u,{xs:12,sm:6,key:v},{default:i(()=>[e("div",je,[e("div",{class:ue(["item-icon",`item-icon--${a.type}`])},[(C(),I(_e(a.icon)))],2),e("div",Ie,[e("div",Ne,f(a.value),1),e("div",Ge,f(a.label),1)])])]),_:2},1024))),128))]),_:1})])]),_:1})]),_:1})])}const Ze=de(fe,[["render",Qe],["__scopeId","data-v-2401b5be"],["__file","E:/wx-nan/webs/admin/src/views/analytics/overview.vue"]]);export{Ze as default};
