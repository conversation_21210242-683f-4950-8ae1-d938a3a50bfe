@import "../../styles/miniprogram-design.scss";

/* 🎨 现代卡片式菜品详情页面 */

/* 主容器 */
.detail-page {
  background: #f8fafc;
  position: relative;
}

/* 内容滚动区域 */
.detail-content {
  height: 100vh;
  overflow: hidden;
  padding-top: 24rpx;
  padding-bottom: 120rpx;
  ::-webkit-scrollbar {
    display: none;
  }
}

/* 🎨 现代卡片式主要信息区域 */
.hero-card {
  background: white;
  border-radius: 20rpx;
  margin: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f1f5f9;
  transition: all 0.2s ease;

  &:active {
    transform: translateY(-1rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  }
}

.card-layout {
  display: flex;
  padding: 24rpx;
  gap: 20rpx;
}

/* 左侧图片区域 */
.image-section {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  border-radius: 16rpx;
  overflow: hidden;
  flex-shrink: 0;
  background: #f3f4f6;
}

.dish-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;

  &:active {
    transform: scale(1.05);
  }
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  padding: 12rpx;
}

/* 右侧信息区域 */
.info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dish-name {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.dish-description {
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 12rpx;
}

.tag-item {
  padding: 4rpx 12rpx;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 4rpx rgba(59, 130, 246, 0.2);
}

/* 基本信息 */
.meta-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.category-badge {
  padding: 6rpx 12rpx;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.meta-text {
  font-size: 22rpx;
  color: #9ca3af;

  &.creator-name {
    color: #6366f1;
    font-weight: 600;
  }
}

/* 📱 内容区域 */
.content-section {
  padding: 0 20rpx 40rpx;
}

/* 🎨 信息卡片通用样式 */
.info-card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #f3f4f6;
  margin: 0 20rpx 16rpx;
  overflow: hidden;
  transition: all 0.2s ease;

  &:active {
    transform: translateY(-1rpx);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  }
}

/* � 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 18rpx 20rpx 14rpx;
  background: #fafbfc;
  border-bottom: 1rpx solid #f1f5f9;
}

.header-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 10rpx;
  background: rgba(99, 102, 241, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #374151;
  flex: 1;
}

.card-content {
  padding: 18rpx 20rpx 20rpx;
}

/* 创建者信息样式 */
.creator-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.creator-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  border: 2rpx solid #f3f4f6;
  flex-shrink: 0;
}

.creator-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.creator-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

/* creator-name-large 样式已移除，因为创建者卡片已删除 */

.creator-time {
  font-size: 22rpx;
  color: #9ca3af;
}

/* 🎨 统一的卡片样式已移除，使用 cooking-card 作为通用样式 */

/* 卡片内容文本样式 */
.description-text,
.ingredients-text,
.cooking-text {
  font-size: 26rpx;
  color: #374151;
  line-height: 1.6;
  white-space: pre-wrap;
}

.description-text {
  line-height: 1.7;
  font-style: italic;
  color: #4b5563;
}

.cooking-text {
  line-height: 1.8;
}

/* 📊 元信息网格 */
.meta-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f1f5f9;

  &:last-child {
    border-bottom: none;
  }
}

.meta-label {
  font-size: 26rpx;
  color: #64748b;
  font-weight: 500;
}

.meta-value {
  font-size: 26rpx;
  color: #1e293b;
  font-weight: 600;
}

.creator-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.creator-avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #e2e8f0;
  flex-shrink: 0;
}

/* 🛒 食材卡片特殊样式 */
.ingredients-card {
  .header-icon {
    background: rgba(16, 185, 129, 0.1);
  }
}

.ingredients-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.7;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 4rpx solid #10b981;
  position: relative;

  &::before {
    content: "🥬";
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    font-size: 28rpx;
    opacity: 0.4;
  }
}

/* 🎨 统一的卡片样式 - cooking-card */
.cooking-card {
  /* 通用卡片样式，适用于所有内容卡片 */

  .header-icon {
    /* 默认图标背景 */
    background: rgba(0, 0, 0, 0.06);
    transition: background-color 0.2s ease;
  }
}

.cooking-text {
  font-size: 28rpx;
  color: #374151;
  line-height: 1.8;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(245, 158, 11, 0.02));
  padding: 24rpx;
  border-radius: 16rpx;
  border-left: 4rpx solid #f59e0b;
  position: relative;
  white-space: pre-wrap;
  word-break: break-all;

  &::before {
    content: "👨‍🍳";
    position: absolute;
    top: 16rpx;
    right: 16rpx;
    font-size: 28rpx;
    opacity: 0.4;
  }
}

/* 底部安全区域 */
.safe-bottom {
  height: 40rpx;
}

/* 🔥 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  border-top: 1rpx solid #f1f5f9;
  padding: 16rpx 24rpx;
  padding-bottom: calc(16rpx + env(safe-area-inset-bottom));
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
  z-index: 1000;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.action-right {
  display: flex;
  justify-content: flex-end;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 120rpx;

  &.secondary {
    background: #f9fafb;
    border: 1rpx solid #e5e7eb;
    color: #6b7280;

    &:active {
      background: #f3f4f6;
      transform: scale(0.95);
    }

    .btn-text {
      color: #6b7280;
    }
  }

  &.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
    min-width: 160rpx;

    &:active {
      background: linear-gradient(135deg, #2563eb, #1e40af);
      transform: scale(0.95);
      box-shadow: 0 2rpx 8rpx rgba(59, 130, 246, 0.4);
    }

    .btn-text {
      color: white;
    }
  }
}

.btn-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* ✨ 简化的动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页面进入动画 */
.hero-card {
  animation: fadeInUp 0.4s ease-out;
}

.info-card {
  animation: fadeInUp 0.4s ease-out;
  animation-fill-mode: both;
}

.info-card:nth-child(1) {
  animation-delay: 0.1s;
}
.info-card:nth-child(2) {
  animation-delay: 0.2s;
}
.info-card:nth-child(3) {
  animation-delay: 0.3s;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .card-layout {
    flex-direction: column;
    gap: 16rpx;
  }

  .image-section {
    width: 100%;
    height: 200rpx;
    align-self: center;
    max-width: 300rpx;
  }

  .dish-name {
    font-size: 32rpx;
  }

  .dish-description {
    font-size: 24rpx;
  }

  .bottom-actions {
    padding: 12rpx 16rpx;
  }

  .action-btn {
    padding: 10rpx 16rpx;
    font-size: 24rpx;
    min-width: 100rpx;

    &.primary {
      min-width: 140rpx;
    }
  }
}
