/**
 * 页面混入 - 提供全局Loading等通用功能
 */

const pageMixin = {
  data: {
    // 全局Loading状态
    globalLoading: {
      show: false,
      text: '',
      type: 'spinner',
      color: '#6366F1',
      mask: true
    }
  },

  onLoad: function(options) {
    // 初始化全局Loading状态
    const app = getApp();
    this.setData({
      globalLoading: app.globalData.loading
    });

    // 调用原始的onLoad方法
    if (this._originalOnLoad) {
      this._originalOnLoad.call(this, options);
    }
  },

  onShow: function() {
    // 页面显示时同步全局Loading状态
    const app = getApp();
    this.setData({
      globalLoading: app.globalData.loading
    });

    // 调用原始的onShow方法
    if (this._originalOnShow) {
      this._originalOnShow.call(this);
    }
  },

  /**
   * 便捷的Loading方法
   */
  showLoading: function(options) {
    const app = getApp();
    return app.loading.show(options);
  },

  hideLoading: function() {
    const app = getApp();
    return app.loading.hide();
  },

  setLoadingText: function(text) {
    const app = getApp();
    return app.loading.setText(text);
  }
};

/**
 * 应用页面混入
 * @param {Object} pageOptions 页面配置对象
 * @returns {Object} 混入后的页面配置
 */
function applyPageMixin(pageOptions) {
  // 保存原始的生命周期方法
  if (pageOptions.onLoad) {
    pageOptions._originalOnLoad = pageOptions.onLoad;
  }
  if (pageOptions.onShow) {
    pageOptions._originalOnShow = pageOptions.onShow;
  }

  // 合并数据
  pageOptions.data = {
    ...pageMixin.data,
    ...pageOptions.data
  };

  // 合并方法
  Object.keys(pageMixin).forEach(key => {
    if (key !== 'data' && typeof pageMixin[key] === 'function') {
      pageOptions[key] = pageMixin[key];
    }
  });

  return pageOptions;
}

module.exports = {
  pageMixin,
  applyPageMixin
};
