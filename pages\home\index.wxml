<view class="container">
	<!-- 主卡片 独立样式 -->
	<view class="home-welcome-card flex-between">
		<view class="home-welcome-left">
			<view class="home-welcome-title">{{userInfo.name}}，欢迎</view>
			<view class="home-welcome-desc">今日菜单已开放点菜，快来选你喜欢的菜吧～</view>
		</view>
		<image class="home-welcome-img" src="{{userInfo.avatar}}" mode="aspectFill" />
	</view>

	<!-- 消息通知 -->
	<view class="notice-bar-wrap mb-2">
		<!-- 正常状态 -->
		<van-notice-bar wx:if="{{!noticeLoading && !noticeError}}" left-icon="volume-o" text="{{currentNotice.text}}" color="#2563eb" background="#dbeafe" scrollable="{{true}}" bindtap="onNoticeClick" />

		<!-- 加载状态 -->
		<van-notice-bar wx:elif="{{noticeLoading}}" left-icon="loading" text="正在加载通知..." color="#666" background="#f5f5f5" />

		<!-- 错误状态 -->
		<van-notice-bar wx:elif="{{noticeError}}" left-icon="warning-o" text="{{currentNotice.text}}" color="#f56c6c" background="#fef0f0" bindtap="onNoticeRetry" />
	</view>

	<!-- 今日菜单 独立卡片样式 -->
	<view class="home-menu-card">
		<view class="home-menu-header flex-between">
			<view class="home-menu-title">
				<view class="title-content">
					<van-icon name="hot-o" size="32rpx" color="#FE2C55" />
					<text wx:if="{{!showRecommended}}">今日菜单</text>
					<text wx:else>推荐菜单</text>
				</view>
				<view wx:if="{{todayMenuDate && !showRecommended}}" class="menu-date">
					{{todayMenuDate}}
				</view>
				<view wx:if="{{showRecommended}}" class="menu-date recommended">
					<text wx:if="{{menuCreator === '系统推荐'}}">热门菜品推荐</text>
					<text wx:else>为您精选的家常菜</text>
				</view>
			</view>
			<van-button size="small" custom-class="theme-link" bind:click="goToOrder">去点菜
				<van-icon name="arrow" />
			</van-button>
		</view>
		<scroll-view scroll-x="true">
			<view class="home-menu-list">
				<block wx:for="{{menu}}" wx:key="id">
					<view class="home-menu-food-card" bindtap="goToFoodDetail" data-id="{{item.id}}" data-name="{{item.name}}" data-image="{{item.image}}">
						<image class="home-menu-food-img" src="{{item.image}}" mode="aspectFill" />
						<view class="home-menu-food-name">{{item.name}}</view>
					</view>
				</block>
			</view>
		</scroll-view>
	</view>

	<!-- 留言 独立卡片样式 -->
	<view class="home-message-card">
		<view class="home-message-header flex-between">
			<view class="home-message-title">
				<van-icon name="comment-o" size="32rpx" color="#FE2C55" />留言
				<text wx:if="{{!hasRealMessages}}" class="placeholder-hint">（暂无留言）</text>
			</view>
			<van-button size="small" custom-class="theme-link" bind:click="goToMessage">更多
				<van-icon name="arrow" />
			</van-button>
		</view>
		<!-- 有真实留言时显示轮播 -->
		<view wx:if="{{hasRealMessages}}" class="home-message-swiper" bindtap="goToMessage">
			<swiper vertical="{{true}}" autoplay="{{true}}" interval="{{2200}}" circular="{{true}}" class="home-message-swipe">
				<swiper-item wx:for="{{messages}}" wx:key="index">
					<view class="home-message-text">{{item}}</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 无真实留言时显示简洁提示 -->
		<view wx:else class="home-message-placeholder" bindtap="goToMessage">
			<view class="placeholder-content">
				<van-icon name="chat-o" size="40rpx" color="#cbd5e1" />
				<text class="placeholder-main">暂无留言</text>
				<text class="placeholder-sub">点击开始第一条留言</text>
			</view>
		</view>
	</view>
</view>