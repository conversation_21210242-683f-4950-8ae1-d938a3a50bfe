<template>
  <div class="menu-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="日期" prop="date">
        <el-date-picker
          v-model="form.date"
          type="date"
          placeholder="选择日期"
          style="width: 100%"
        />
      </el-form-item>
      
      <el-form-item label="是否今日菜单" prop="isToday">
        <el-switch
          v-model="form.isToday"
          active-text="是"
          inactive-text="否"
        />
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注"
        />
      </el-form-item>
      
      <el-form-item label="菜品选择">
        <div class="dish-selection">
          <el-button @click="showDishDialog = true" type="primary">
            选择菜品
          </el-button>
          <div class="selected-dishes" v-if="selectedDishes.length > 0">
            <el-tag
              v-for="dish in selectedDishes"
              :key="dish.id"
              closable
              @close="removeDish(dish.id)"
              class="dish-tag"
            >
              {{ dish.name }} x{{ dish.count }}
            </el-tag>
          </div>
        </div>
      </el-form-item>
      
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-form>
    
    <!-- 菜品选择对话框 -->
    <el-dialog
      v-model="showDishDialog"
      title="选择菜品"
      width="800px"
    >
      <DishSelector
        :selected-dishes="selectedDishes"
        @confirm="handleDishSelect"
        @cancel="showDishDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import DishSelector from './DishSelector.vue'

export default defineComponent({
  name: 'MenuForm',
  components: {
    DishSelector
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    const formRef = ref()
    const showDishDialog = ref(false)
    const selectedDishes = ref([])
    
    const form = reactive({
      date: new Date(),
      isToday: false,
      remark: '',
      dishes: []
    })
    
    const rules = {
      date: [
        { required: true, message: '请选择日期', trigger: 'change' }
      ]
    }
    
    // 监听表单数据变化
    watch(() => props.formData, (newData) => {
      if (newData && Object.keys(newData).length > 0) {
        Object.assign(form, {
          date: new Date(newData.date),
          isToday: newData.isToday || false,
          remark: newData.remark || '',
          dishes: newData.dishes || []
        })
        
        // 设置已选择的菜品
        if (newData.dishes && newData.dishes.length > 0) {
          selectedDishes.value = newData.dishes.map(item => ({
            id: item.dishId || item.dish?.id,
            name: item.dish?.name || item.name,
            count: item.count
          }))
        }
      }
    }, { immediate: true })
    
    // 处理菜品选择
    const handleDishSelect = (dishes) => {
      selectedDishes.value = dishes
      form.dishes = dishes.map(dish => ({
        dishId: dish.id,
        count: dish.count
      }))
      showDishDialog.value = false
    }
    
    // 移除菜品
    const removeDish = (dishId) => {
      selectedDishes.value = selectedDishes.value.filter(dish => dish.id !== dishId)
      form.dishes = form.dishes.filter(dish => dish.dishId !== dishId)
    }
    
    // 提交表单
    const handleSubmit = async () => {
      if (!formRef.value) return
      
      try {
        const valid = await formRef.value.validate()
        if (!valid) return
        
        if (selectedDishes.value.length === 0) {
          ElMessage.warning('请至少选择一道菜品')
          return
        }
        
        const submitData = {
          ...form,
          items: form.dishes
        }
        
        emit('submit', submitData)
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    }
    
    // 取消
    const handleCancel = () => {
      emit('cancel')
    }
    
    return {
      formRef,
      form,
      rules,
      showDishDialog,
      selectedDishes,
      handleDishSelect,
      removeDish,
      handleSubmit,
      handleCancel
    }
  }
})
</script>

<style scoped>
.menu-form {
  padding: 20px;
}

.dish-selection {
  width: 100%;
}

.selected-dishes {
  margin-top: 10px;
}

.dish-tag {
  margin: 4px 8px 4px 0;
}

.form-actions {
  text-align: center;
  margin-top: 30px;
}

.form-actions .el-button {
  margin: 0 10px;
}
</style>
