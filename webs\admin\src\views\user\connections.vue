<template>
  <div class="user-connections-container">
    <div class="page-header">
      <h2>用户关联管理</h2>
      <p>管理用户之间的关联关系，查看关联状态和解除关联</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名或手机号"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="searchForm.status" placeholder="关联状态" clearable @change="handleSearch">
            <el-option label="待确认" value="pending" />
            <el-option label="已接受" value="accepted" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 关联列表 -->
    <div class="connections-list">
      <el-table
        v-loading="loading"
        :data="connections"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="发起用户" min-width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32" :src="row.sender?.avatar">
                {{ row.sender?.name?.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="user-name">{{ row.sender?.name }}</div>
                <div class="user-phone">{{ row.sender?.phone || '未绑定' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="接收用户" min-width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32" :src="row.receiver?.avatar">
                {{ row.receiver?.name?.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="user-name">{{ row.receiver?.name }}</div>
                <div class="user-phone">{{ row.receiver?.phone || '未绑定' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="关联状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="申请消息" min-width="150">
          <template #default="{ row }">
            <span class="message-text">{{ row.message || '无' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="关联备注" min-width="120">
          <template #default="{ row }">
            <span>{{ row.remark || '无' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="所属分组" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.group" size="small" :color="row.group.color">
              {{ row.group.name }}
            </el-tag>
            <span v-else class="text-gray-400">未分组</span>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending'"
              type="success"
              size="small"
              @click="handleApprove(row)"
            >
              通过
            </el-button>
            <el-button
              v-if="row.status === 'pending'"
              type="warning"
              size="small"
              @click="handleReject(row)"
            >
              拒绝
            </el-button>
            <el-button
              v-if="row.status === 'accepted'"
              type="danger"
              size="small"
              @click="handleDisconnect(row)"
            >
              解除关联
            </el-button>
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedConnections.length > 0" class="batch-actions">
      <el-button type="success" @click="handleBatchApprove">
        批量通过 ({{ selectedConnections.length }})
      </el-button>
      <el-button type="warning" @click="handleBatchReject">
        批量拒绝 ({{ selectedConnections.length }})
      </el-button>
      <el-button type="danger" @click="handleBatchDisconnect">
        批量解除 ({{ selectedConnections.length }})
      </el-button>
    </div>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialog.visible"
      title="编辑关联信息"
      width="500px"
    >
      <el-form
        ref="editFormRef"
        :model="editDialog.form"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="关联备注" prop="remark">
          <el-input
            v-model="editDialog.form.remark"
            placeholder="请输入关联备注"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="所属分组" prop="groupId">
          <el-select
            v-model="editDialog.form.groupId"
            placeholder="选择分组"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="group in groups"
              :key="group.id"
              :label="group.name"
              :value="group.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { formatDate } from '@/utils/common'

// 响应式数据
const loading = ref(false)
const connections = ref([])
const groups = ref([])
const selectedConnections = ref([])

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 编辑对话框
const editDialog = reactive({
  visible: false,
  form: {
    id: '',
    remark: '',
    groupId: ''
  }
})

const editFormRef = ref()
const editRules = {
  remark: [
    { max: 100, message: '备注不能超过100个字符', trigger: 'blur' }
  ]
}

// 方法
const loadConnections = async () => {
  loading.value = true
  try {
    // 这里调用API获取关联数据
    // const response = await connectionApi.getList({
    //   ...searchForm,
    //   page: pagination.page,
    //   size: pagination.size
    // })
    
    // 模拟数据
    connections.value = [
      {
        id: '1',
        senderId: 'user1',
        receiverId: 'user2',
        status: 'pending',
        message: '希望能够关联，方便查看菜单',
        remark: '家人',
        groupId: 'group1',
        sender: {
          id: 'user1',
          name: '张三',
          phone: '13800138001',
          avatar: ''
        },
        receiver: {
          id: 'user2',
          name: '李四',
          phone: '13800138002',
          avatar: ''
        },
        group: {
          id: 'group1',
          name: '家庭成员',
          color: '#409EFF'
        },
        createdAt: new Date()
      }
    ]
    pagination.total = 1
  } catch (error) {
    ElMessage.error('加载关联数据失败')
  } finally {
    loading.value = false
  }
}

const loadGroups = async () => {
  try {
    // 这里调用API获取分组数据
    groups.value = [
      { id: 'group1', name: '家庭成员' },
      { id: 'group2', name: '朋友' },
      { id: 'group3', name: '同事' }
    ]
  } catch (error) {
    console.error('加载分组数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadConnections()
}

const handleSelectionChange = (selection) => {
  selectedConnections.value = selection
}

const getStatusType = (status) => {
  const types = {
    pending: 'warning',
    accepted: 'success',
    rejected: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待确认',
    accepted: '已接受',
    rejected: '已拒绝'
  }
  return texts[status] || '未知'
}

const handleApprove = async (row) => {
  try {
    await ElMessageBox.confirm('确定要通过这个关联申请吗？', '确认操作')
    // 调用API通过申请
    ElMessage.success('关联申请已通过')
    loadConnections()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleReject = async (row) => {
  try {
    await ElMessageBox.confirm('确定要拒绝这个关联申请吗？', '确认操作')
    // 调用API拒绝申请
    ElMessage.success('关联申请已拒绝')
    loadConnections()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleDisconnect = async (row) => {
  try {
    await ElMessageBox.confirm('确定要解除这个用户关联吗？', '确认操作', {
      type: 'warning'
    })
    // 调用API解除关联
    ElMessage.success('用户关联已解除')
    loadConnections()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleEdit = (row) => {
  editDialog.form.id = row.id
  editDialog.form.remark = row.remark || ''
  editDialog.form.groupId = row.groupId || ''
  editDialog.visible = true
}

const handleSaveEdit = async () => {
  try {
    await editFormRef.value.validate()
    // 调用API保存编辑
    ElMessage.success('关联信息已更新')
    editDialog.visible = false
    loadConnections()
  } catch (error) {
    console.error('保存失败:', error)
  }
}

const handleBatchApprove = async () => {
  try {
    await ElMessageBox.confirm(`确定要批量通过 ${selectedConnections.value.length} 个关联申请吗？`, '确认操作')
    // 调用API批量通过
    ElMessage.success('批量操作成功')
    loadConnections()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

const handleBatchReject = async () => {
  try {
    await ElMessageBox.confirm(`确定要批量拒绝 ${selectedConnections.value.length} 个关联申请吗？`, '确认操作')
    // 调用API批量拒绝
    ElMessage.success('批量操作成功')
    loadConnections()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

const handleBatchDisconnect = async () => {
  try {
    await ElMessageBox.confirm(`确定要批量解除 ${selectedConnections.value.length} 个用户关联吗？`, '确认操作', {
      type: 'warning'
    })
    // 调用API批量解除
    ElMessage.success('批量操作成功')
    loadConnections()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量操作失败')
    }
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadConnections()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadConnections()
}

// 生命周期
onMounted(() => {
  loadConnections()
  loadGroups()
})
</script>

<style scoped>
.user-connections-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.connections-list {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.user-phone {
  font-size: 12px;
  color: #909399;
}

.message-text {
  color: #606266;
  font-size: 14px;
}

.pagination-container {
  padding: 20px;
  text-align: right;
  border-top: 1px solid #ebeef5;
}

.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: #fff;
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
}

.text-gray-400 {
  color: #c0c4cc;
}
</style>
