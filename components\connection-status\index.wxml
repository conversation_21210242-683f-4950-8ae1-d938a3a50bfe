<view class="connection-status {{_sizeClass}}" bind:tap="onStatusClick">
  <!-- 状态图标和文字 -->
  <view class="connection-status__main">
    <view class="connection-status__icon" style="color: {{_currentConfig.color}}">
      <van-icon name="{{_currentConfig.icon}}" size="{{size === 'small' ? '24rpx' : size === 'large' ? '40rpx' : '32rpx'}}" />
    </view>
    
    <view class="connection-status__content">
      <view class="connection-status__text" style="color: {{_currentConfig.color}}">
        {{_displayText}}
      </view>
      
      <view wx:if="{{showTime && _formattedTime}}" class="connection-status__time">
        {{_formattedTime}}
      </view>
    </view>
  </view>
  
  <!-- 进度条 -->
  <view wx:if="{{showProgress}}" class="connection-status__progress">
    <view class="connection-status__progress-track">
      <view 
        class="connection-status__progress-fill" 
        style="width: {{_currentConfig.progress}}%; background-color: {{_currentConfig.color}}"
      ></view>
    </view>
    
    <!-- 进度步骤 -->
    <view class="connection-status__steps">
      <view class="connection-status__step {{status === 'pending' || status === 'accepted' || status === 'rejected' ? 'active' : ''}}" style="border-color: {{_currentConfig.color}}">
        <van-icon name="user-o" size="16rpx" color="{{status === 'pending' || status === 'accepted' || status === 'rejected' ? _currentConfig.color : '#c8c9cc'}}" />
      </view>
      
      <view class="connection-status__step {{status === 'accepted' ? 'active' : status === 'rejected' ? 'rejected' : ''}}" style="border-color: {{status === 'accepted' ? _currentConfig.color : status === 'rejected' ? '#ee0a24' : '#c8c9cc'}}">
        <van-icon name="{{status === 'accepted' ? 'success' : status === 'rejected' ? 'close' : 'clock-o'}}" size="16rpx" color="{{status === 'accepted' ? _currentConfig.color : status === 'rejected' ? '#ee0a24' : '#c8c9cc'}}" />
      </view>
    </view>
  </view>
</view>
