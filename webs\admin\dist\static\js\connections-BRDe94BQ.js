var h=(N,o,y)=>new Promise((a,V)=>{var k=r=>{try{_(y.next(r))}catch(g){V(g)}},B=r=>{try{_(y.throw(r))}catch(g){V(g)}},_=r=>r.done?a(r.value):Promise.resolve(r.value).then(k,B);_((y=y.apply(N,o)).next())});import{_ as Z,r as c,Y as q,c as M,a as i,b,d as t,w as n,Z as J,p as E,g as I,h as T,o as Y,E as d,$ as K,k as D,l as v,m as u,t as m,H as Q,I as W}from"./index-Cy8N1eGd.js";import{a as X}from"./common-CIDIMsc8.js";const $={__name:"connections",setup(N,{expose:o}){o();const y=I(!1),a=I([]),V=I([]),k=I([]),B=T({keyword:"",status:""}),_=T({page:1,size:20,total:0}),r=T({visible:!1,form:{id:"",remark:"",groupId:""}}),g=I(),F={remark:[{max:100,message:"备注不能超过100个字符",trigger:"blur"}]},s=()=>h(this,null,function*(){y.value=!0;try{a.value=[{id:"1",senderId:"user1",receiverId:"user2",status:"pending",message:"希望能够关联，方便查看菜单",remark:"家人",groupId:"group1",sender:{id:"user1",name:"张三",phone:"13800138001",avatar:""},receiver:{id:"user2",name:"李四",phone:"13800138002",avatar:""},group:{id:"group1",name:"家庭成员",color:"#409EFF"},createdAt:new Date}],_.total=1}catch(l){d.error("加载关联数据失败")}finally{y.value=!1}}),j=()=>h(this,null,function*(){try{V.value=[{id:"group1",name:"家庭成员"},{id:"group2",name:"朋友"},{id:"group3",name:"同事"}]}catch(l){console.error("加载分组数据失败:",l)}}),p=()=>{_.page=1,s()},A=l=>{k.value=l},R=l=>({pending:"warning",accepted:"success",rejected:"danger"})[l]||"info",P=l=>({pending:"待确认",accepted:"已接受",rejected:"已拒绝"})[l]||"未知",G=l=>h(this,null,function*(){try{yield D.confirm("确定要通过这个关联申请吗？","确认操作"),d.success("关联申请已通过"),s()}catch(z){z!=="cancel"&&d.error("操作失败")}}),U=l=>h(this,null,function*(){try{yield D.confirm("确定要拒绝这个关联申请吗？","确认操作"),d.success("关联申请已拒绝"),s()}catch(z){z!=="cancel"&&d.error("操作失败")}}),H=l=>h(this,null,function*(){try{yield D.confirm("确定要解除这个用户关联吗？","确认操作",{type:"warning"}),d.success("用户关联已解除"),s()}catch(z){z!=="cancel"&&d.error("操作失败")}}),L=l=>{r.form.id=l.id,r.form.remark=l.remark||"",r.form.groupId=l.groupId||"",r.visible=!0},O=()=>h(this,null,function*(){try{yield g.value.validate(),d.success("关联信息已更新"),r.visible=!1,s()}catch(l){console.error("保存失败:",l)}}),e=()=>h(this,null,function*(){try{yield D.confirm(`确定要批量通过 ${k.value.length} 个关联申请吗？`,"确认操作"),d.success("批量操作成功"),s()}catch(l){l!=="cancel"&&d.error("批量操作失败")}}),f=()=>h(this,null,function*(){try{yield D.confirm(`确定要批量拒绝 ${k.value.length} 个关联申请吗？`,"确认操作"),d.success("批量操作成功"),s()}catch(l){l!=="cancel"&&d.error("批量操作失败")}}),w=()=>h(this,null,function*(){try{yield D.confirm(`确定要批量解除 ${k.value.length} 个用户关联吗？`,"确认操作",{type:"warning"}),d.success("批量操作成功"),s()}catch(l){l!=="cancel"&&d.error("批量操作失败")}}),S=l=>{_.size=l,s()},x=l=>{_.page=l,s()};Y(()=>{s(),j()});const C={loading:y,connections:a,groups:V,selectedConnections:k,searchForm:B,pagination:_,editDialog:r,editFormRef:g,editRules:F,loadConnections:s,loadGroups:j,handleSearch:p,handleSelectionChange:A,getStatusType:R,getStatusText:P,handleApprove:G,handleReject:U,handleDisconnect:H,handleEdit:L,handleSaveEdit:O,handleBatchApprove:e,handleBatchReject:f,handleBatchDisconnect:w,handleSizeChange:S,handlePageChange:x,ref:I,reactive:T,onMounted:Y,get ElMessage(){return d},get ElMessageBox(){return D},get Search(){return K},get formatDate(){return X}};return Object.defineProperty(C,"__isScriptSetup",{enumerable:!1,value:!0}),C}},ee={class:"user-connections-container"},ae={class:"search-section"},te={class:"connections-list"},ne={class:"user-info"},oe={class:"user-details"},le={class:"user-name"},se={class:"user-phone"},re={class:"user-info"},ie={class:"user-details"},ce={class:"user-name"},de={class:"user-phone"},ue={class:"message-text"},me={key:1,class:"text-gray-400"},_e={class:"pagination-container"},ge={key:0,class:"batch-actions"};function pe(N,o,y,a,V,k){const B=c("el-icon"),_=c("el-input"),r=c("el-col"),g=c("el-option"),F=c("el-select"),s=c("el-button"),j=c("el-row"),p=c("el-table-column"),A=c("el-avatar"),R=c("el-tag"),P=c("el-table"),G=c("el-pagination"),U=c("el-form-item"),H=c("el-form"),L=c("el-dialog"),O=q("loading");return v(),M("div",ee,[o[15]||(o[15]=i("div",{class:"page-header"},[i("h2",null,"用户关联管理"),i("p",null,"管理用户之间的关联关系，查看关联状态和解除关联")],-1)),b(" 搜索和筛选 "),i("div",ae,[t(j,{gutter:20},{default:n(()=>[t(r,{span:6},{default:n(()=>[t(_,{modelValue:a.searchForm.keyword,"onUpdate:modelValue":o[0]||(o[0]=e=>a.searchForm.keyword=e),placeholder:"搜索用户名或手机号",clearable:"",onInput:a.handleSearch},{prefix:n(()=>[t(B,null,{default:n(()=>[t(a.Search)]),_:1})]),_:1},8,["modelValue"])]),_:1}),t(r,{span:4},{default:n(()=>[t(F,{modelValue:a.searchForm.status,"onUpdate:modelValue":o[1]||(o[1]=e=>a.searchForm.status=e),placeholder:"关联状态",clearable:"",onChange:a.handleSearch},{default:n(()=>[t(g,{label:"待确认",value:"pending"}),t(g,{label:"已接受",value:"accepted"}),t(g,{label:"已拒绝",value:"rejected"})]),_:1},8,["modelValue"])]),_:1}),t(r,{span:4},{default:n(()=>[t(s,{type:"primary",onClick:a.handleSearch},{default:n(()=>[t(B,null,{default:n(()=>[t(a.Search)]),_:1}),o[8]||(o[8]=u(" 搜索 ",-1))]),_:1,__:[8]})]),_:1})]),_:1})]),b(" 关联列表 "),i("div",te,[J((v(),E(P,{data:a.connections,style:{width:"100%"},onSelectionChange:a.handleSelectionChange},{default:n(()=>[t(p,{type:"selection",width:"55"}),t(p,{label:"发起用户","min-width":"120"},{default:n(({row:e})=>{var f,w,S;return[i("div",ne,[t(A,{size:32,src:(f=e.sender)==null?void 0:f.avatar},{default:n(()=>{var x,C;return[u(m((C=(x=e.sender)==null?void 0:x.name)==null?void 0:C.charAt(0)),1)]}),_:2},1032,["src"]),i("div",oe,[i("div",le,m((w=e.sender)==null?void 0:w.name),1),i("div",se,m(((S=e.sender)==null?void 0:S.phone)||"未绑定"),1)])])]}),_:1}),t(p,{label:"接收用户","min-width":"120"},{default:n(({row:e})=>{var f,w,S;return[i("div",re,[t(A,{size:32,src:(f=e.receiver)==null?void 0:f.avatar},{default:n(()=>{var x,C;return[u(m((C=(x=e.receiver)==null?void 0:x.name)==null?void 0:C.charAt(0)),1)]}),_:2},1032,["src"]),i("div",ie,[i("div",ce,m((w=e.receiver)==null?void 0:w.name),1),i("div",de,m(((S=e.receiver)==null?void 0:S.phone)||"未绑定"),1)])])]}),_:1}),t(p,{label:"关联状态",width:"100"},{default:n(({row:e})=>[t(R,{type:a.getStatusType(e.status),size:"small"},{default:n(()=>[u(m(a.getStatusText(e.status)),1)]),_:2},1032,["type"])]),_:1}),t(p,{label:"申请消息","min-width":"150"},{default:n(({row:e})=>[i("span",ue,m(e.message||"无"),1)]),_:1}),t(p,{label:"关联备注","min-width":"120"},{default:n(({row:e})=>[i("span",null,m(e.remark||"无"),1)]),_:1}),t(p,{label:"所属分组",width:"100"},{default:n(({row:e})=>[e.group?(v(),E(R,{key:0,size:"small",color:e.group.color},{default:n(()=>[u(m(e.group.name),1)]),_:2},1032,["color"])):(v(),M("span",me,"未分组"))]),_:1}),t(p,{label:"创建时间",width:"160"},{default:n(({row:e})=>[u(m(a.formatDate(e.createdAt)),1)]),_:1}),t(p,{label:"操作",width:"200",fixed:"right"},{default:n(({row:e})=>[e.status==="pending"?(v(),E(s,{key:0,type:"success",size:"small",onClick:f=>a.handleApprove(e)},{default:n(()=>o[9]||(o[9]=[u(" 通过 ",-1)])),_:2,__:[9]},1032,["onClick"])):b("v-if",!0),e.status==="pending"?(v(),E(s,{key:1,type:"warning",size:"small",onClick:f=>a.handleReject(e)},{default:n(()=>o[10]||(o[10]=[u(" 拒绝 ",-1)])),_:2,__:[10]},1032,["onClick"])):b("v-if",!0),e.status==="accepted"?(v(),E(s,{key:2,type:"danger",size:"small",onClick:f=>a.handleDisconnect(e)},{default:n(()=>o[11]||(o[11]=[u(" 解除关联 ",-1)])),_:2,__:[11]},1032,["onClick"])):b("v-if",!0),t(s,{type:"primary",size:"small",onClick:f=>a.handleEdit(e)},{default:n(()=>o[12]||(o[12]=[u(" 编辑 ",-1)])),_:2,__:[12]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[O,a.loading]]),b(" 分页 "),i("div",_e,[t(G,{"current-page":a.pagination.page,"onUpdate:currentPage":o[2]||(o[2]=e=>a.pagination.page=e),"page-size":a.pagination.size,"onUpdate:pageSize":o[3]||(o[3]=e=>a.pagination.size=e),total:a.pagination.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:a.handleSizeChange,onCurrentChange:a.handlePageChange},null,8,["current-page","page-size","total"])])]),b(" 批量操作 "),a.selectedConnections.length>0?(v(),M("div",ge,[t(s,{type:"success",onClick:a.handleBatchApprove},{default:n(()=>[u(" 批量通过 ("+m(a.selectedConnections.length)+") ",1)]),_:1}),t(s,{type:"warning",onClick:a.handleBatchReject},{default:n(()=>[u(" 批量拒绝 ("+m(a.selectedConnections.length)+") ",1)]),_:1}),t(s,{type:"danger",onClick:a.handleBatchDisconnect},{default:n(()=>[u(" 批量解除 ("+m(a.selectedConnections.length)+") ",1)]),_:1})])):b("v-if",!0),b(" 编辑对话框 "),t(L,{modelValue:a.editDialog.visible,"onUpdate:modelValue":o[7]||(o[7]=e=>a.editDialog.visible=e),title:"编辑关联信息",width:"500px"},{footer:n(()=>[t(s,{onClick:o[6]||(o[6]=e=>a.editDialog.visible=!1)},{default:n(()=>o[13]||(o[13]=[u("取消",-1)])),_:1,__:[13]}),t(s,{type:"primary",onClick:a.handleSaveEdit},{default:n(()=>o[14]||(o[14]=[u("保存",-1)])),_:1,__:[14]})]),default:n(()=>[t(H,{ref:"editFormRef",model:a.editDialog.form,rules:a.editRules,"label-width":"80px"},{default:n(()=>[t(U,{label:"关联备注",prop:"remark"},{default:n(()=>[t(_,{modelValue:a.editDialog.form.remark,"onUpdate:modelValue":o[4]||(o[4]=e=>a.editDialog.form.remark=e),placeholder:"请输入关联备注",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),_:1}),t(U,{label:"所属分组",prop:"groupId"},{default:n(()=>[t(F,{modelValue:a.editDialog.form.groupId,"onUpdate:modelValue":o[5]||(o[5]=e=>a.editDialog.form.groupId=e),placeholder:"选择分组",clearable:"",style:{width:"100%"}},{default:n(()=>[(v(!0),M(Q,null,W(a.groups,e=>(v(),E(g,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}const ye=Z($,[["render",pe],["__scopeId","data-v-de987686"],["__file","E:/wx-nan/webs/admin/src/views/user/connections.vue"]]);export{ye as default};
