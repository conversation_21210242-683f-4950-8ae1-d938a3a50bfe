const {connectionApi} = require('../../services/api');
const {applyPageMixin} = require('../../utils/page-mixin');

Page(
  applyPageMixin({
    data: {
      activeTab: 'my',
      loading: false,

      // 我的关联
      myConnections: [],

      // 可关联用户
      availableUsers: [],
      searchKeyword: '',
      searchError: false, // 搜索错误状态
      searchEmpty: false, // 搜索无结果状态

      // 待处理申请
      pendingRequests: [],

      // 发送申请弹窗
      showRequestDialog: false,
      selectedUser: {},
      requestMessage: '',

      // 操作loading状态
      respondingConnectionId: '', // 正在处理的申请ID
      sendingRequestUserId: '' // 正在发送申请的用户ID
    },

    onLoad() {
      this.loadMyConnections();
      this.loadAvailableUsers();
      this.loadPendingRequests();
    },

    onShow() {
      // 页面显示时刷新数据
      this.loadMyConnections();
      this.loadPendingRequests();
    },

    /**
     * 标签页切换
     */
    onTabChange(event) {
      const {name} = event.detail;
      this.setData({
        activeTab: name
      });

      // 根据标签页加载对应数据
      if (name === 'my') {
        this.loadMyConnections();
      } else if (name === 'add') {
        // 切换到添加关联时清空搜索框
        this.setData({
          searchKeyword: ''
        });
        this.loadAvailableUsers();
      } else if (name === 'requests') {
        this.loadPendingRequests();
      }
    },

    /**
     * 加载我的关联列表
     */
    async loadMyConnections() {
      try {
        this.setData({
          loading: true
        });
        this.showLoading({
          text: '加载关联列表...'
        });
        const res = await connectionApi.getMyConnections('accepted');

        if (res.code === 200) {
          // 格式化日期
          const myConnections = (res.data || []).map(item => ({
            ...item,
            createdAt: this.formatTime(item.createdAt),
            updatedAt: this.formatTime(item.updatedAt)
          }));

          this.setData({
            myConnections
          });
        } else {
          wx.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('Load my connections error:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      } finally {
        this.setData({
          loading: false
        });
        this.hideLoading();
      }
    },

    /**
     * 加载可关联用户列表
     */
    async loadAvailableUsers(search = '') {
      try {
        this.setData({
          loading: true,
          searchError: false,
          searchEmpty: false
        });
        this.showLoading({
          text: search ? '搜索用户中...' : '加载用户列表...'
        });
        const params = {
          page: 1,
          size: 50
        };

        if (search) {
          params.search = search;
        }

        const res = await connectionApi.getAvailableUsers(params);

        if (res.code === 200) {
          const userList = res.data.list || [];
          this.setData({
            availableUsers: userList,
            searchEmpty: search && userList.length === 0,
            searchError: false
          });
        } else {
          // 区分不同的错误类型
          this.handleSearchError(res, search);
        }
      } catch (error) {
        this.handleSearchError(error, search);
      } finally {
        this.setData({
          loading: false
        });
        this.hideLoading();
      }
    },

    /**
     * 处理搜索错误
     */
    handleSearchError(error, search) {
      let errorMessage = '';
      let isNetworkError = false;

      if (error.code) {
        // 后端返回的业务错误
        switch (error.code) {
          case 500:
            if (search) {
              errorMessage = `搜索"${search}"时出现问题，请尝试其他关键词`;
            } else {
              errorMessage = '服务器繁忙，请稍后重试';
            }
            break;
          case 404:
            errorMessage = search
              ? `未找到"${search}"相关用户`
              : '暂无可关联用户';
            break;
          case 403:
            errorMessage = '没有权限查看用户列表';
            break;
          default:
            errorMessage = error.message || '加载失败，请重试';
        }
      } else {
        // 网络错误或其他异常
        isNetworkError = true;
        errorMessage = '网络连接异常，请检查网络后重试';
      }

      this.setData({
        searchError: true,
        searchEmpty: false,
        availableUsers: []
      });

      // 只在真正的网络错误时显示 Toast
      if (isNetworkError) {
        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      }
    },

    /**
     * 加载待处理申请
     */
    async loadPendingRequests() {
      try {
        this.setData({
          loading: true
        });
        this.showLoading({
          text: '加载申请列表...'
        });
        const res = await connectionApi.getMyConnections('pending');

        if (res.code === 200) {
          // 只显示我作为接收者的申请
          const pendingRequests = (res.data || []).filter(
            item => !item.isSender
          );
          this.setData({
            pendingRequests: pendingRequests.map(item => ({
              ...item,
              createdAt: this.formatTime(item.createdAt),
              updatedAt: this.formatTime(item.updatedAt)
            }))
          });
        } else {
          wx.showToast({
            title: res.message || '加载失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('Load pending requests error:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      } finally {
        this.setData({
          loading: false
        });
        this.hideLoading();
      }
    },

    /**
     * 搜索用户
     */
    onSearch(event) {
      const keyword = event.detail.trim();
      this.setData({
        searchKeyword: keyword,
        searchError: false,
        searchEmpty: false
      });

      if (keyword) {
        this.loadAvailableUsers(keyword);
      } else {
        // 清空搜索时重新加载所有用户
        this.loadAvailableUsers();
      }
    },

    onSearchChange(event) {
      const keyword = event.detail.trim();
      this.setData({
        searchKeyword: keyword
      });

      // 如果搜索框被清空，重置状态
      if (!keyword) {
        this.setData({
          searchError: false,
          searchEmpty: false
        });
      }
    },

    onSearchClear() {
      this.setData({
        searchKeyword: '',
        searchError: false,
        searchEmpty: false
      });
      this.loadAvailableUsers();
    },

    /**
     * 重试搜索
     */
    retrySearch() {
      if (this.data.searchKeyword) {
        this.loadAvailableUsers(this.data.searchKeyword);
      } else {
        this.loadAvailableUsers();
      }
    },

    /**
     * 发送关联申请
     */
    sendConnectionRequest(event) {
      const {id, name} = event.currentTarget.dataset;

      // 检查是否正在发送申请
      if (this.data.sendingRequestUserId === id) {
        return;
      }

      // 检查是否是当前用户自己
      const currentUser = wx.getStorageSync('userInfo');
      if (currentUser && currentUser.id === id) {
        wx.showToast({
          title: '不能关联自己',
          icon: 'none'
        });
        return;
      }

      this.setData({
        selectedUser: {
          id,
          name
        },
        showRequestDialog: true,
        requestMessage: `请求添加为关联用户`
      });
    },

    onRequestMessageChange(event) {
      this.setData({
        requestMessage: event.detail
      });
    },

    async confirmSendRequest() {
      try {
        const {selectedUser, requestMessage} = this.data;

        // 设置发送loading状态
        this.setData({
          sendingRequestUserId: selectedUser.id,
          showRequestDialog: false
        });

        this.showLoading({
          text: '发送中...',
          mask: true
        });

        const res = await connectionApi.sendConnectionRequest({
          receiverId: selectedUser.id,
          message: requestMessage
        });

        if (res.code === 201) {
          wx.showToast({
            title: '申请发送成功！',
            icon: 'none',
            duration: 2000
          });

          // 刷新用户列表
          this.loadAvailableUsers(this.data.searchKeyword);
        } else {
          wx.showToast({
            title: res.message || '发送失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('Send connection request error:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      } finally {
        this.hideLoading();
        this.setData({
          sendingRequestUserId: '',
          selectedUser: {},
          requestMessage: ''
        });
      }
    },

    cancelSendRequest() {
      this.setData({
        showRequestDialog: false,
        selectedUser: {},
        requestMessage: ''
      });
    },

    /**
     * 处理关联申请
     */
    async respondToRequest(event) {
      const {id, action} = event.currentTarget.dataset;

      // 检查是否正在处理
      if (this.data.respondingConnectionId === id) {
        return;
      }

      try {
        // 设置处理loading状态
        this.setData({
          respondingConnectionId: id
        });

        this.showLoading({
          text: action === 'accept' ? '同意中...' : '拒绝中...',
          mask: true
        });

        const res = await connectionApi.respondToConnection(id, action);

        if (res.code === 200) {
          // 显示成功提示
          wx.showToast({
            title: action === 'accept' ? '关联成功！' : '已拒绝申请',
            icon: action === 'accept' ? 'success' : 'none',
            duration: 2000
          });

          // 刷新数据
          this.loadPendingRequests();
          this.loadMyConnections();
          // 刷新可关联用户列表（移除已关联的用户）
          this.loadAvailableUsers(this.data.searchKeyword);
        } else {
          wx.showToast({
            title: res.message || '操作失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('Respond to request error:', error);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      } finally {
        this.hideLoading();
        this.setData({
          respondingConnectionId: ''
        });
      }
    },

    /**
     * 解除关联
     */
    removeConnection(event) {
      const {id} = event.currentTarget.dataset;

      wx.showModal({
        title: '确认解除',
        content: '确定要解除与该用户的关联关系吗？',
        success: async res => {
          if (res.confirm) {
            try {
              const result = await connectionApi.removeConnection(id);

              if (result.code === 200) {
                wx.showToast({
                  title: '已解除关联',
                  icon: 'success'
                });

                // 刷新列表
                this.loadMyConnections();
              } else {
                wx.showToast({
                  title: result.message || '操作失败',
                  icon: 'none'
                });
              }
            } catch (error) {
              console.error('Remove connection error:', error);
              wx.showToast({
                title: '网络错误',
                icon: 'none'
              });
            }
          }
        }
      });
    },

    /**
     * 格式化时间为 YYYY-MM-DD 格式
     */
    formatTime(timeString) {
      if (!timeString) return '';

      try {
        const date = new Date(timeString);

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          return '';
        }

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
      } catch (error) {
        console.error('日期格式化错误:', error);
        return '';
      }
    },

    /**
     * 跳转到申请历史页面
     */
    goToHistory() {
      wx.navigateTo({
        url: '/pages/connection_history/index'
      });
    },

    /**
     * 关联状态点击处理
     */
    onConnectionStatusClick(event) {
      const {status, isSender} = event.detail;

      let message = '';
      switch (status) {
        case 'pending':
          message = isSender
            ? '申请已发送，等待对方确认'
            : '请及时处理关联申请';
          break;
        case 'accepted':
          message = '关联已建立，可以进行菜单推送';
          break;
        case 'rejected':
          message = '申请已被拒绝';
          break;
        default:
          message = '未知状态';
      }

      wx.showToast({
        title: message,
        icon: 'none',
        duration: 2000
      });
    }
  })
);
