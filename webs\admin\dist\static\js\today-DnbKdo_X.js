var g=(t,a,n)=>new Promise((c,l)=>{var p=e=>{try{o(n.next(e))}catch(s){l(s)}},r=e=>{try{o(n.throw(e))}catch(s){l(s)}},o=e=>e.done?c(e.value):Promise.resolve(e.value).then(p,r);o((n=n.apply(t,a)).next())});import{_ as b,V as C,r as m,c as T,d as i,w as d,g as _,o as v,E as u,l as w,m as f,t as $}from"./index-Cy8N1eGd.js";import{C as h}from"./CustomTable-DWghEWMD.js";import{o as k}from"./order-D36AVmfp.js";const M=C({name:"TodayOrders",components:{CustomTable:h},setup(){const t=_(!1),a=_([]),n=[{prop:"id",label:"订单号",width:100},{prop:"userName",label:"用户"},{prop:"items",label:"菜品数量"},{prop:"status",label:"状态",slot:"status"},{prop:"mealTime",label:"用餐时间"}],c=()=>g(this,null,function*(){t.value=!0;try{const e=yield k.getTodayOrders();e.code===200?a.value=e.data||[]:u.error(e.message||"加载数据失败")}catch(e){console.error("加载今日订单失败:",e),u.error("加载数据失败")}finally{t.value=!1}}),l=e=>({pending:"warning",completed:"success",cancelled:"danger"})[e]||"info",p=e=>({pending:"待处理",completed:"已完成",cancelled:"已取消"})[e]||"未知",r=e=>{u.info(`查看订单: ${e.id}`)},o=e=>{u.success(`订单 ${e.id} 已完成`)};return v(()=>{c()}),{loading:t,tableData:a,columns:n,getStatusType:l,getStatusText:p,handleView:r,handleComplete:o}}}),S={class:"today-orders"};function V(t,a,n,c,l,p){const r=m("el-tag"),o=m("el-button"),e=m("CustomTable");return w(),T("div",S,[i(e,{title:"今日订单",data:t.tableData,columns:t.columns,loading:t.loading,"show-search":!1},{status:d(({row:s})=>[i(r,{type:t.getStatusType(s.status)},{default:d(()=>[f($(t.getStatusText(s.status)),1)]),_:2},1032,["type"])]),actions:d(({row:s})=>[i(o,{size:"small",onClick:y=>t.handleView(s)},{default:d(()=>a[0]||(a[0]=[f("查看",-1)])),_:2,__:[0]},1032,["onClick"]),i(o,{size:"small",type:"primary",onClick:y=>t.handleComplete(s)},{default:d(()=>a[1]||(a[1]=[f("完成",-1)])),_:2,__:[1]},1032,["onClick"])]),_:1},8,["data","columns","loading"])])}const B=b(M,[["render",V],["__file","E:/wx-nan/webs/admin/src/views/order/today.vue"]]);export{B as default};
