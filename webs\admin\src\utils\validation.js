/**
 * 表单验证工具
 * 提供常用的验证规则和验证方法
 */

// 常用正则表达式
export const REGEX = {
  // 手机号
  phone: /^1[3-9]\d{9}$/,
  // 邮箱
  email: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  // 身份证号
  idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  // 密码（6-20位，包含字母和数字）
  password: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,20}$/,
  // 用户名（4-16位字母数字下划线）
  username: /^[a-zA-Z0-9_]{4,16}$/,
  // 中文姓名
  chineseName: /^[\u4e00-\u9fa5]{2,10}$/,
  // URL
  url: /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i,
  // IP地址
  ip: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
  // 数字
  number: /^\d+$/,
  // 小数
  decimal: /^\d+(\.\d+)?$/
}

// 验证器函数
export const validators = {
  // 必填验证
  required: (message = '此项为必填项') => ({
    required: true,
    message,
    trigger: 'blur'
  }),

  // 长度验证
  length: (min, max, message) => ({
    min,
    max,
    message: message || `长度在 ${min} 到 ${max} 个字符`,
    trigger: 'blur'
  }),

  // 手机号验证
  phone: (message = '请输入正确的手机号') => ({
    pattern: REGEX.phone,
    message,
    trigger: 'blur'
  }),

  // 邮箱验证
  email: (message = '请输入正确的邮箱地址') => ({
    pattern: REGEX.email,
    message,
    trigger: 'blur'
  }),

  // 密码验证
  password: (message = '密码必须包含字母和数字，长度6-20位') => ({
    pattern: REGEX.password,
    message,
    trigger: 'blur'
  }),

  // 用户名验证
  username: (message = '用户名只能包含字母、数字和下划线，长度4-16位') => ({
    pattern: REGEX.username,
    message,
    trigger: 'blur'
  }),

  // 中文姓名验证
  chineseName: (message = '请输入正确的中文姓名') => ({
    pattern: REGEX.chineseName,
    message,
    trigger: 'blur'
  }),

  // 数字验证
  number: (message = '请输入数字') => ({
    pattern: REGEX.number,
    message,
    trigger: 'blur'
  }),

  // 自定义验证
  custom: (validator, message, trigger = 'blur') => ({
    validator,
    message,
    trigger
  }),

  // 确认密码验证
  confirmPassword: (passwordField, message = '两次输入的密码不一致') => ({
    validator: (rule, value, callback) => {
      if (value !== passwordField) {
        callback(new Error(message))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  })
}

// 常用验证规则组合
export const commonRules = {
  // 用户名规则
  username: [
    validators.required('请输入用户名'),
    validators.username()
  ],

  // 密码规则
  password: [
    validators.required('请输入密码'),
    validators.password()
  ],

  // 手机号规则
  phone: [
    validators.required('请输入手机号'),
    validators.phone()
  ],

  // 邮箱规则
  email: [
    validators.required('请输入邮箱'),
    validators.email()
  ],

  // 姓名规则
  name: [
    validators.required('请输入姓名'),
    validators.length(2, 20, '姓名长度在2-20个字符')
  ],

  // 中文姓名规则
  chineseName: [
    validators.required('请输入姓名'),
    validators.chineseName()
  ]
}

// 验证工具函数
export const validationUtils = {
  // 验证手机号
  isValidPhone: (phone) => REGEX.phone.test(phone),

  // 验证邮箱
  isValidEmail: (email) => REGEX.email.test(email),

  // 验证身份证号
  isValidIdCard: (idCard) => REGEX.idCard.test(idCard),

  // 验证密码强度
  getPasswordStrength: (password) => {
    let strength = 0
    if (password.length >= 8) strength++
    if (/[a-z]/.test(password)) strength++
    if (/[A-Z]/.test(password)) strength++
    if (/\d/.test(password)) strength++
    if (/[^a-zA-Z0-9]/.test(password)) strength++
    
    if (strength <= 2) return 'weak'
    if (strength <= 3) return 'medium'
    return 'strong'
  },

  // 验证URL
  isValidUrl: (url) => REGEX.url.test(url),

  // 验证IP地址
  isValidIp: (ip) => REGEX.ip.test(ip),

  // 验证是否为空
  isEmpty: (value) => {
    if (value === null || value === undefined) return true
    if (typeof value === 'string') return value.trim() === ''
    if (Array.isArray(value)) return value.length === 0
    if (typeof value === 'object') return Object.keys(value).length === 0
    return false
  },

  // 验证数字范围
  isInRange: (value, min, max) => {
    const num = Number(value)
    return !isNaN(num) && num >= min && num <= max
  }
}

// 创建表单验证器
export const createFormValidator = (formRef) => {
  return {
    // 验证整个表单
    async validate() {
      if (!formRef.value) return false
      try {
        await formRef.value.validate()
        return true
      } catch (error) {
        return false
      }
    },

    // 验证指定字段
    async validateField(field) {
      if (!formRef.value) return false
      try {
        await formRef.value.validateField(field)
        return true
      } catch (error) {
        return false
      }
    },

    // 清除验证
    clearValidate(fields) {
      if (formRef.value) {
        formRef.value.clearValidate(fields)
      }
    },

    // 重置表单
    resetFields() {
      if (formRef.value) {
        formRef.value.resetFields()
      }
    }
  }
}

// 防抖验证
export const debounceValidator = (validator, delay = 300) => {
  let timer = null
  return (rule, value, callback) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      validator(rule, value, callback)
    }, delay)
  }
}
