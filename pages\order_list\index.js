const {
	orderApi
} = require('../../services/api');
const {
	formatTime,
} = require('../../utils/timeUtils');

Page({
	data: {
		orders: [],
		loading: true,
		refreshing: false,
		page: 1,
		size: 20,
		hasMore: true
	},

	onLoad() {
		this.loadOrders();
	},

	onShow() {
		// 页面显示时刷新数据
		this.refreshOrders();
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.refreshOrders();
	},

	// 上拉加载更多
	onReachBottom() {
		if (this.data.hasMore && !this.data.loading) {
			this.loadMoreOrders();
		}
	},

	// 刷新订单数据
	async refreshOrders() {
		this.setData({
			page: 1,
			hasMore: true,
			refreshing: true
		});

		await this.loadOrders();

		this.setData({
			refreshing: false
		});
		wx.stopPullDownRefresh();
	},

	// 加载更多订单
	async loadMoreOrders() {
		const nextPage = this.data.page + 1;
		this.setData({
			page: nextPage
		});
		await this.loadOrders(false);
	},

	// 加载订单数据
	async loadOrders(reset = true) {
		try {
			if (reset) {
				this.setData({
					loading: true
				});
			}

			const res = await orderApi.getVisibleOrders({
				page: this.data.page,
				size: this.data.size
			});

			if (res.code === 200) {
				const newOrders = res.data.list.map(order => ({
					...order,
					items: Array.isArray(order.items) ?
						order.items : JSON.parse(order.items || '[]'),
					isPushedToMe: order.isPushedToMe || false,
					pushedBy: order.pushedBy,
					menuDeleted: order.menu?.deleted || false,
					// 格式化时间字段
					createdAtFormatted: formatTime(order.createdAt),
					updatedAtFormatted: formatTime(order.updatedAt),
					diningTimeFormatted: order.diningTime ?
						formatTime(order.diningTime) : null,
					// 格式化菜单时间
					menu: order.menu ? {
						...order.menu,
						dateFormatted: formatTime(order.menu.date)
					} : null
				}));

				this.setData({
					orders: reset ? newOrders : [...this.data.orders, ...newOrders],
					hasMore: newOrders.length === this.data.size
				});
			} else {
				wx.showToast({
					title: res.message || '加载失败',
					icon: 'none'
				});
			}
		} catch (error) {
			console.error('加载订单失败:', error);
			wx.showToast({
				title: '网络错误',
				icon: 'none'
			});
		} finally {
			this.setData({
				loading: false
			});
		}
	},

	// 查看订单详情
	viewOrderDetail(e) {
		try {
			const dataset = e.currentTarget.dataset;

			let order = dataset.order;

			// 如果没有order数据，尝试通过orderId和orderIndex获取
			if (!order) {
				const {
					orderId,
					orderIndex
				} = dataset;


				if (orderIndex !== undefined && this.data.orders[orderIndex]) {
					order = this.data.orders[orderIndex];
				} else if (orderId) {
					order = this.data.orders.find(item => item.id === orderId);
				}
			}

			// 验证订单数据
			if (!order) {
				console.error('❌ 无法获取订单数据');
				wx.showToast({
					title: '订单数据错误',
					icon: 'error'
				});
				return;
			}


			// 将订单数据存储到缓存
			wx.setStorageSync('selectedOrderDetail', order);

			// 跳转到订单详情页
			wx.navigateTo({
				url: '/pages/order_detail/index'
			});
		} catch (error) {
			console.error('❌ 查看订单详情失败:', error);
			wx.showToast({
				title: '打开详情失败',
				icon: 'error'
			});
		}
	},

	// 删除订单
	async deleteOrder(e) {
		const orderId = e.currentTarget.dataset.orderId;
		const orderIndex = e.currentTarget.dataset.orderIndex;

		if (!orderId) {
			wx.showToast({
				title: '订单ID不存在',
				icon: 'none'
			});
			return;
		}

		// 显示确认对话框
		const result = await new Promise(resolve => {
			wx.showModal({
				title: '确认删除',
				content: '确定要删除这个订单吗？删除后无法恢复。',
				confirmText: '删除',
				cancelText: '取消',
				confirmColor: '#ee0a24',
				success: res => {
					resolve(res.confirm);
				},
				fail: () => {
					resolve(false);
				}
			});
		});

		if (!result) {
			return;
		}

		try {
			wx.showLoading({
				title: '删除中...'
			});

			// 调用删除API
			const response = await orderApi.deleteOrder(orderId);

			if (response.code === 200) {
				// 从本地数据中移除订单
				const orders = [...this.data.orders];
				orders.splice(orderIndex, 1);

				this.setData({
					orders: orders
				});

				wx.showToast({
					title: '删除成功',
					icon: 'success'
				});
			} else {
				wx.showToast({
					title: response.message || '删除失败',
					icon: 'none'
				});
			}
		} catch (error) {
			console.error('删除订单失败:', error);
			wx.showToast({
				title: '网络错误',
				icon: 'none'
			});
		} finally {
			wx.hideLoading();
		}
	},

	// 获取订单状态文本
	getStatusText(status) {
		const statusMap = {
			pending: '待处理',
			confirmed: '已确认',
			preparing: '准备中',
			ready: '已完成',
			cancelled: '已取消'
		};
		return statusMap[status] || status;
	},

	// 获取订单状态颜色
	getStatusColor(status) {
		const colorMap = {
			pending: '#ff9500',
			confirmed: '#1989fa',
			preparing: '#ff9500',
			ready: '#07c160',
			cancelled: '#ee0a24'
		};
		return colorMap[status] || '#666';
	},

	// 计算订单总价（如果有价格信息）
	calculateTotal(items) {
		return items.reduce((total, item) => {
			const price = item.price || 0;
			const count = item.count || 1;
			return total + price * count;
		}, 0);
	}
});