/**
 * 通用下拉刷新和上拉加载更多组件
 */

Component({
  options: {
    multipleSlots: true // 启用多个插槽
  },

  properties: {
    // 容器高度
    containerHeight: {
      type: Number,
      value: 600
    },

    // 下拉刷新配置
    refresherEnabled: {
      type: Bo<PERSON>an,
      value: true
    },
    refresherBackground: {
      type: String,
      value: '#f7f7f7'
    },
    refresherStyle: {
      type: String,
      value: 'white'
    },
    refresherThreshold: {
      type: Number,
      value: 80
    },

    // 上拉加载更多配置
    hasMore: {
      type: Boolean,
      value: true
    },
    loadingMore: {
      type: Boolean,
      value: false
    },
    showLoadMore: {
      type: Boolean,
      value: true
    },

    // 文本配置
    loadMoreText: {
      type: String,
      value: '点击加载更多'
    },
    loadingText: {
      type: String,
      value: '加载中...'
    },
    noMoreText: {
      type: String,
      value: '没有更多数据了'
    },
    refreshingText: {
      type: String,
      value: ''
    },

    // 样式配置
    loadingColor: {
      type: String,
      value: '#6366F1'
    },
    loadingSize: {
      type: String,
      value: '20px'
    },
    showScrollbar: {
      type: Boolean,
      value: false
    },

    // 空状态配置
    isEmpty: {
      type: Boolean,
      value: false
    },
    emptyIcon: {
      type: String,
      value: 'warning-o'
    },
    emptyText: {
      type: String,
      value: '暂无数据'
    },
    emptyTip: {
      type: String,
      value: '下拉刷新试试'
    },

    // 请求配置
    requestUrl: {
      type: String,
      value: ''
    },
    requestParams: {
      type: Object,
      value: {}
    },
    pageSize: {
      type: Number,
      value: 10
    },
    autoLoad: {
      type: Boolean,
      value: true
    },
    // 数据字段映射
    dataField: {
      type: String,
      value: 'data'
    },
    listField: {
      type: String,
      value: 'list'
    },
    totalField: {
      type: String,
      value: 'total'
    },

    // 请求头配置
    requestHeaders: {
      type: Object,
      value: {}
    },
    // 请求方法
    requestMethod: {
      type: String,
      value: 'GET'
    }
  },

  data: {
    refreshing: false,
    scrollTop: 0,
    // 内部数据管理
    list: [],
    currentPage: 1,
    totalCount: 0,
    internalHasMore: true,
    internalLoadingMore: false,
    internalIsEmpty: false,
    loading: false
  },

  methods: {
    /**
     * 发起网络请求
     */
    async makeRequest(isRefresh = false) {
      const {
        requestUrl,
        requestParams,
        pageSize,
        requestHeaders,
        requestMethod
      } = this.properties;
      const {currentPage} = this.data;

      if (!requestUrl) {
        console.error('requestUrl is required');
        return;
      }

      const page = isRefresh ? 1 : currentPage;
      const params = {
        ...requestParams,
        page,
        size: pageSize
      };

      // 获取认证token
      const token =
        wx.getStorageSync('token') || wx.getStorageSync('userToken');

      // 构建请求头
      const headers = {
        'Content-Type': 'application/json',
        ...requestHeaders
      };

      // 添加认证头
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      try {
        const response = await new Promise((resolve, reject) => {
          wx.request({
            url: requestUrl,
            data: params,
            method: requestMethod.toUpperCase(),
            header: headers,
            success: res => {
              if (res.statusCode === 200) {
                resolve(res.data);
              } else if (res.statusCode === 401) {
                reject(new Error('身份验证失败，请重新登录'));
              } else {
                const errorMsg = res.data?.message || `HTTP ${res.statusCode}`;
                reject(new Error(errorMsg));
              }
            },
            fail: reject
          });
        });

        return this.processResponse(response, isRefresh);
      } catch (error) {
        console.error('请求失败:', error);
        throw error;
      }
    },

    /**
     * 处理响应数据
     */
    processResponse(response, isRefresh) {
      const {dataField, listField, totalField, pageSize} = this.properties;
      const {list: currentList} = this.data;
      // 提取数据
      let data = response;
      if (dataField && response[dataField]) {
        data = response[dataField];
      }

      let newList = [];
      let total = 0;

      if (Array.isArray(data)) {
        // 直接是数组
        newList = data;
        total = data.length;
      } else if (data[listField]) {
        // 有list字段
        newList = data[listField] || [];
        total = data[totalField] || 0;
      } else {
        newList = [];
      }

      // 合并数据
      const finalList = isRefresh ? newList : [...currentList, ...newList];
      const hasMore = newList.length >= pageSize;
      const isEmpty = finalList.length === 0;

      // 更新状态
      this.setData({
        list: finalList,
        currentPage: isRefresh ? 2 : this.data.currentPage + 1,
        totalCount: total,
        internalHasMore: hasMore,
        internalIsEmpty: isEmpty,
        refreshing: false,
        internalLoadingMore: false,
        loading: false
      });

      // 触发数据更新事件
      this.triggerEvent('datachange', {
        list: newList, // 传递新数据，让页面自己处理合并逻辑
        total,
        hasMore,
        isEmpty,
        isRefresh,
        originalResponse: response
      });

      return {
        list: finalList,
        total,
        hasMore,
        isEmpty
      };
    },

    /**
     * 下拉刷新事件
     */
    async onRefresh() {
      this.setData({
        refreshing: true
      });
      try {
        await this.makeRequest(true);
      } catch (error) {
        console.error('刷新失败:', error);
        // 失败时显示为空状态
        this.setData({
          refreshing: false,
          list: [],
          internalIsEmpty: true,
          loading: false
        });
      }
    },

    /**
     * 上拉加载更多事件
     */
    async onLoadMore() {
      const {internalHasMore, internalLoadingMore} = this.data;

      if (!internalHasMore || internalLoadingMore) {
        return;
      }

      this.setData({
        internalLoadingMore: true
      });

      try {
        await this.makeRequest(false);
      } catch (error) {
        console.error('加载更多失败:', error);
        // 加载更多失败时停止loading状态
        this.setData({
          internalLoadingMore: false,
          internalHasMore: false
        });
      }
    },

    /**
     * 初始加载数据
     */
    async loadData() {
      if (!this.properties.requestUrl) {
        return;
      }

      this.setData({
        loading: true,
        currentPage: 1,
        list: []
      });

      try {
        await this.makeRequest(true);
      } catch (error) {
        console.error('初始加载失败:', error);
        // 初始加载失败时显示为空状态
        this.setData({
          loading: false,
          list: [],
          internalIsEmpty: true
        });
      }
    },

    /**
     * 重新加载数据
     */
    async reload() {
      this.setData({
        currentPage: 1,
        list: [],
        internalHasMore: true,
        internalIsEmpty: false
      });

      await this.loadData();
    },

    /**
     * 停止下拉刷新
     */
    stopRefresh() {
      this.setData({
        refreshing: false
      });
    },

    /**
     * 滚动到顶部
     */
    scrollToTop() {
      this.setData({
        scrollTop: 0
      });
    },

    /**
     * 重置滚动位置
     */
    resetScroll() {
      this.setData({
        scrollTop: 0,
        refreshing: false
      });
    }
  },

  lifetimes: {
    attached() {
      // 自动加载数据
      if (this.properties.autoLoad) {
        setTimeout(() => {
          this.loadData();
        }, 100);
      }
      // 处理是否传入插槽
      const slots = this.getSlots?.() || {};
      this.setData({
        hasEmptySlot: !!slots.empty
      });
    },

    detached() {
      // 组件卸载时的清理工作
    }
  },

  observers: {
    'requestUrl, requestParams': function (requestUrl, requestParams) {
      // 当请求参数变化时重新加载数据
      if (requestUrl && this.properties.autoLoad) {
        this.reload();
      }
    },

    containerHeight: function (newHeight) {
      // 监听容器高度变化
      if (newHeight && newHeight > 0) {
        // 高度变化后的处理逻辑可以在这里添加
      }
    }
  }
});
