const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const {auth} = require('../middlewares/auth');

// 基础认证接口（保持不变，确保小程序正常工作）
// 登录
router.post('/login', authController.login);

// 注册
router.post('/register', authController.register);

// 获取当前用户信息
router.get('/me', auth, authController.getMe);

// 登出
router.post('/logout', auth, authController.logout);

// 刷新令牌
router.post('/refresh', authController.refreshToken);

// 管理后台专用接口（新增，不影响小程序）
// 管理员注册（支持邮箱注册）
router.post('/admin-register', authController.adminRegister);

// 发送重置密码邮件
router.post(
  '/send-reset-password-email',
  authController.sendResetPasswordEmail
);

// 验证重置密码token
router.post(
  '/verify-reset-password-token',
  authController.verifyResetPasswordToken
);

// 重置密码
router.post('/reset-password', authController.resetPassword);

// 通过验证码重置密码
router.post('/reset-password-with-code', authController.resetPasswordWithCode);

// 修改密码
router.post('/change-password', auth, authController.changePassword);

// 检查用户名是否可用
router.post('/check-username', authController.checkUsername);

// 检查邮箱是否可用
router.post('/check-email', authController.checkEmail);

// 检查手机号是否可用
router.post('/check-phone', authController.checkPhone);

module.exports = router;
