import {defineStore} from 'pinia';
import {ref, computed} from 'vue';
import {authApi} from '@/api/auth';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('admin_token') || '');
  const userInfo = ref(null);

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const username = computed(() => userInfo.value?.name || '');
  const userRole = computed(() => userInfo.value?.role || 'user');
  const avatar = computed(() => userInfo.value?.avatar || '');

  // 设置token
  const setToken = newToken => {
    token.value = newToken;
    if (newToken) {
      localStorage.setItem('admin_token', newToken);
    } else {
      localStorage.removeItem('admin_token');
    }
  };

  // 设置用户信息
  const setUserInfo = info => {
    userInfo.value = info;
  };

  // 登录
  const login = async credentials => {
    try {
      // 调用登录API
      const result = await authApi.login({
        username: credentials.username,
        password: credentials.password,
        loginType: 'password'
      });

      if (result.code === 200 && result.data) {
        token.value = result.data.token;
        userInfo.value = result.data.user;

        // 保存到本地存储
        localStorage.setItem('admin_token', token.value);
        localStorage.setItem('admin_user', JSON.stringify(userInfo.value));

        return {success: true};
      } else {
        return {success: false, message: result.message || '登录失败'};
      }
    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        message: error.message || '登录失败，请检查网络连接'
      };
    }
  };

  // 登出
  const logout = () => {
    token.value = '';
    userInfo.value = null;
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_user');
  };

  // 初始化用户信息
  const initUserInfo = () => {
    const userStr = localStorage.getItem('admin_user');
    if (userStr) {
      try {
        userInfo.value = JSON.parse(userStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        logout();
      }
    }
  };

  // 初始化
  initUserInfo();

  return {
    token,
    userInfo,
    isLoggedIn,
    username,
    userRole,
    avatar,
    setToken,
    setUserInfo,
    login,
    logout,
    initUserInfo
  };
});

// 导出hook函数以保持兼容性
export const useUserStoreHook = () => {
  return useUserStore();
};
