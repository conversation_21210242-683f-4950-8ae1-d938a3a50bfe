import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('admin_token') || '');
  const userInfo = ref(null);

  // 计算属性
  const isLoggedIn = computed(() => !!token.value);
  const username = computed(() => userInfo.value?.name || '');
  const userRole = computed(() => userInfo.value?.role || 'user');
  const avatar = computed(() => userInfo.value?.avatar || '');

  // 设置token
  const setToken = (newToken) => {
    token.value = newToken;
    if (newToken) {
      localStorage.setItem('admin_token', newToken);
    } else {
      localStorage.removeItem('admin_token');
    }
  };

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info;
  };

  // 登出
  const logout = () => {
    token.value = '';
    userInfo.value = null;
    localStorage.removeItem('admin_token');
  };

  return {
    token,
    userInfo,
    isLoggedIn,
    username,
    userRole,
    avatar,
    setToken,
    setUserInfo,
    logout
  };
});

// 导出hook函数以保持兼容性
export const useUserStoreHook = () => {
  return useUserStore();
};
