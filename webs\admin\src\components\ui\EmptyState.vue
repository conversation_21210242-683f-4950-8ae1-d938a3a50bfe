<template>
  <div :class="containerClasses">
    <div class="text-center">
      <!-- 图标或图片 -->
      <div class="mx-auto mb-4">
        <component
          v-if="icon"
          :is="icon"
          :class="iconClasses"
          aria-hidden="true"
        />
        <img
          v-else-if="image"
          :src="image"
          :alt="title"
          :class="imageClasses"
        />
        <div
          v-else
          :class="defaultIconClasses"
        >
          <DocumentIcon class="h-12 w-12 text-gray-400" />
        </div>
      </div>
      
      <!-- 标题 -->
      <h3 :class="titleClasses">
        {{ title }}
      </h3>
      
      <!-- 描述 -->
      <p v-if="description" :class="descriptionClasses">
        {{ description }}
      </p>
      
      <!-- 操作按钮 -->
      <div v-if="$slots.actions || actionText" class="mt-6">
        <slot name="actions">
          <BaseButton
            v-if="actionText"
            :variant="actionVariant"
            :prefix-icon="actionIcon"
            @click="$emit('action')"
          >
            {{ actionText }}
          </BaseButton>
        </slot>
      </div>
      
      <!-- 额外内容 -->
      <div v-if="$slots.extra" class="mt-4">
        <slot name="extra"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { DocumentIcon } from '@heroicons/vue/24/outline'
import BaseButton from './BaseButton.vue'

const props = defineProps({
  // 标题
  title: {
    type: String,
    default: '暂无数据'
  },
  // 描述
  description: {
    type: String,
    default: ''
  },
  // 图标
  icon: {
    type: [Object, Function],
    default: null
  },
  // 图片
  image: {
    type: String,
    default: ''
  },
  // 操作按钮文本
  actionText: {
    type: String,
    default: ''
  },
  // 操作按钮变体
  actionVariant: {
    type: String,
    default: 'primary'
  },
  // 操作按钮图标
  actionIcon: {
    type: [Object, Function],
    default: null
  },
  // 尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  // 内边距
  padding: {
    type: String,
    default: 'normal',
    validator: (value) => ['none', 'sm', 'normal', 'lg'].includes(value)
  }
})

defineEmits(['action'])

const containerClasses = computed(() => {
  const classes = ['flex items-center justify-center']
  
  // 内边距
  const paddingClasses = {
    none: '',
    sm: 'py-8',
    normal: 'py-12',
    lg: 'py-16'
  }
  
  classes.push(paddingClasses[props.padding])
  
  return classes.join(' ')
})

const iconClasses = computed(() => {
  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-16 w-16',
    lg: 'h-20 w-20'
  }
  
  return [
    'mx-auto text-gray-400',
    sizeClasses[props.size]
  ].join(' ')
})

const imageClasses = computed(() => {
  const sizeClasses = {
    sm: 'h-24 w-24',
    md: 'h-32 w-32',
    lg: 'h-40 w-40'
  }
  
  return [
    'mx-auto object-cover',
    sizeClasses[props.size]
  ].join(' ')
})

const defaultIconClasses = computed(() => {
  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-16 w-16',
    lg: 'h-20 w-20'
  }
  
  return [
    'mx-auto flex items-center justify-center rounded-full bg-gray-100 dark:bg-gray-800',
    sizeClasses[props.size]
  ].join(' ')
})

const titleClasses = computed(() => {
  const sizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  }
  
  return [
    'font-medium text-gray-900 dark:text-white',
    sizeClasses[props.size]
  ].join(' ')
})

const descriptionClasses = computed(() => {
  const sizeClasses = {
    sm: 'text-sm mt-1',
    md: 'text-sm mt-2',
    lg: 'text-base mt-2'
  }
  
  return [
    'text-gray-500 dark:text-gray-400',
    sizeClasses[props.size]
  ].join(' ')
})
</script>
