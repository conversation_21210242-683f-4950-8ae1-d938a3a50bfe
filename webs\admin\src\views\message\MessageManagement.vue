<template>
  <PageContainer
    title="消息管理"
    subtitle="管理系统中的用户消息和通知"
    :breadcrumb="breadcrumbItems"
  >
    <template #header-actions>
      <BaseButton
        variant="primary"
        prefix-icon="PlusIcon"
        @click="handleCreateNotification"
      >
        发送通知
      </BaseButton>
    </template>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="总消息数"
        :value="stats.totalMessages"
        :icon="ChatBubbleLeftRightIcon"
        color="blue"
        :change="stats.messageGrowth"
        description="较上月"
      />
      <StatsCard
        title="未读消息"
        :value="stats.unreadMessages"
        :icon="ExclamationCircleIcon"
        color="yellow"
        description="待处理消息"
      />
      <StatsCard
        title="今日消息"
        :value="stats.todayMessages"
        :icon="CalendarDaysIcon"
        color="green"
        :change="stats.todayGrowth"
        description="较昨日"
      />
      <StatsCard
        title="系统通知"
        :value="stats.systemNotifications"
        :icon="BellIcon"
        color="purple"
        description="系统通知数"
      />
    </div>

    <!-- 消息类型标签页 -->
    <div class="mb-6">
      <div class="border-b border-gray-200 dark:border-gray-700">
        <nav class="-mb-px flex space-x-8">
          <button
            v-for="tab in messageTabs"
            :key="tab.key"
            :class="getTabClass(tab.key)"
            @click="activeTab = tab.key"
          >
            <component :is="tab.icon" class="h-5 w-5 mr-2" />
            {{ tab.label }}
            <span
              v-if="tab.count"
              :class="getTabBadgeClass(tab.key)"
              class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
            >
              {{ tab.count }}
            </span>
          </button>
        </nav>
      </div>
    </div>

    <!-- 搜索表单 -->
    <SearchForm
      v-model="searchParams"
      :fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 消息表格 -->
    <BaseTable
      :data="messages"
      :columns="tableColumns"
      :selectable="true"
      v-model:selected-rows="selectedMessages"
      :loading="loading"
      @row-click="handleRowClick"
      @sort-change="handleSort"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            {{ getTabLabel(activeTab) }} ({{ pagination.total }})
          </h3>
          <div class="flex items-center space-x-3">
            <BaseButton
              v-if="selectedMessages.length && activeTab === 'messages'"
              variant="outline"
              size="sm"
              @click="handleBatchMarkRead"
            >
              标记已读 ({{ selectedMessages.length }})
            </BaseButton>
            <BaseButton
              v-if="selectedMessages.length"
              variant="error"
              size="sm"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedMessages.length }})
            </BaseButton>
          </div>
        </div>
      </template>

      <!-- 用户信息 -->
      <template #cell-user="{ row }">
        <div class="flex items-center">
          <img
            v-if="row.user?.avatar"
            :src="row.user.avatar"
            :alt="row.user.name"
            class="h-8 w-8 rounded-full object-cover mr-2"
          />
          <div
            v-else
            class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-2"
          >
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ row.user?.name?.charAt(0)?.toUpperCase() }}
            </span>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ row.user?.name }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ row.user?.phone }}
            </div>
          </div>
        </div>
      </template>

      <!-- 消息内容 -->
      <template #cell-content="{ row }">
        <div class="max-w-xs">
          <div class="text-sm text-gray-900 dark:text-white truncate">
            {{ row.content || row.title }}
          </div>
          <div v-if="row.content && row.title" class="text-xs text-gray-500 dark:text-gray-400 truncate">
            {{ row.content }}
          </div>
        </div>
      </template>

      <!-- 消息类型 -->
      <template #cell-type="{ row }">
        <span
          :class="getTypeBadgeClass(row.type)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ getTypeText(row.type) }}
        </span>
      </template>

      <!-- 读取状态 -->
      <template #cell-read="{ row }">
        <span
          :class="row.read ? 'text-green-600' : 'text-yellow-600'"
          class="inline-flex items-center"
        >
          <component
            :is="row.read ? CheckCircleIcon : ExclamationCircleIcon"
            class="h-4 w-4 mr-1"
          />
          {{ row.read ? '已读' : '未读' }}
        </span>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center space-x-2">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleView(row)"
          >
            查看
          </BaseButton>
          <BaseButton
            v-if="!row.read && activeTab === 'messages'"
            variant="ghost"
            size="sm"
            @click="handleMarkRead(row)"
            class="text-green-600"
          >
            标记已读
          </BaseButton>
          <BaseButton
            v-if="activeTab === 'messages'"
            variant="ghost"
            size="sm"
            @click="handleReply(row)"
            class="text-blue-600"
          >
            回复
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleDelete(row)"
            class="text-red-600 hover:text-red-700"
          >
            删除
          </BaseButton>
        </div>
      </template>

      <!-- 分页 -->
      <template #footer>
        <Pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </template>
    </BaseTable>

    <!-- 消息详情模态框 -->
    <MessageDetailModal
      v-model="showDetailModal"
      :message="currentMessage"
      :type="activeTab"
    />

    <!-- 回复消息模态框 -->
    <MessageReplyModal
      v-model="showReplyModal"
      :message="currentMessage"
      @success="handleReplySuccess"
    />

    <!-- 发送通知模态框 -->
    <NotificationCreateModal
      v-model="showNotificationModal"
      @success="handleNotificationSuccess"
    />
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { 
  ChatBubbleLeftRightIcon,
  ExclamationCircleIcon,
  CalendarDaysIcon,
  BellIcon,
  CheckCircleIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import MessageDetailModal from './components/MessageDetailModal.vue'
import MessageReplyModal from './components/MessageReplyModal.vue'
import NotificationCreateModal from './components/NotificationCreateModal.vue'
import { messageApi, notificationApi } from '@/api/message'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const messages = ref([])
const selectedMessages = ref([])
const showDetailModal = ref(false)
const showReplyModal = ref(false)
const showNotificationModal = ref(false)
const currentMessage = ref(null)
const activeTab = ref('messages')

// 搜索参数
const searchParams = reactive({
  keyword: '',
  type: '',
  read: '',
  dateRange: []
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 统计数据
const stats = reactive({
  totalMessages: 0,
  unreadMessages: 0,
  todayMessages: 0,
  systemNotifications: 0,
  messageGrowth: 0,
  todayGrowth: 0
})

// 面包屑导航
const breadcrumbItems = [
  { text: '首页', to: '/' },
  { text: '消息管理', to: '/message' },
  { text: '消息列表' }
]

// 消息类型标签页
const messageTabs = computed(() => [
  {
    key: 'messages',
    label: '用户留言',
    icon: ChatBubbleLeftRightIcon,
    count: stats.totalMessages
  },
  {
    key: 'notifications',
    label: '系统通知',
    icon: BellIcon,
    count: stats.systemNotifications
  }
])

// 搜索字段配置
const searchFields = computed(() => {
  const baseFields = [
    {
      key: 'keyword',
      type: 'input',
      label: '关键词',
      placeholder: activeTab.value === 'messages' ? '搜索消息内容' : '搜索通知标题、内容',
      prefixIcon: 'MagnifyingGlassIcon'
    },
    {
      key: 'dateRange',
      type: 'daterange',
      label: '时间范围',
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期'
    }
  ]

  if (activeTab.value === 'messages') {
    baseFields.splice(1, 0, {
      key: 'read',
      type: 'select',
      label: '读取状态',
      placeholder: '请选择状态',
      options: [
        { label: '全部', value: '' },
        { label: '已读', value: 'true' },
        { label: '未读', value: 'false' }
      ]
    })
  } else {
    baseFields.splice(1, 0, {
      key: 'type',
      type: 'select',
      label: '通知类型',
      placeholder: '请选择类型',
      options: [
        { label: '全部', value: '' },
        { label: '系统通知', value: 'system' },
        { label: '订单通知', value: 'order' },
        { label: '菜单通知', value: 'menu' }
      ]
    })
  }

  return baseFields
})

// 表格列配置
const tableColumns = computed(() => {
  const baseColumns = [
    {
      key: 'user',
      title: '用户信息'
    },
    {
      key: 'content',
      title: activeTab.value === 'messages' ? '消息内容' : '通知内容'
    }
  ]

  if (activeTab.value === 'notifications') {
    baseColumns.splice(1, 0, {
      key: 'type',
      title: '类型'
    })
  }

  if (activeTab.value === 'messages') {
    baseColumns.push({
      key: 'read',
      title: '状态'
    })
  }

  baseColumns.push({
    key: 'createdAt',
    title: '时间',
    sortable: true,
    render: (value) => formatDate(value)
  })

  return baseColumns
})

// 计算属性和方法
const getTabClass = (tabKey) => {
  const baseClass = 'flex items-center py-2 px-1 border-b-2 font-medium text-sm'
  return tabKey === activeTab.value
    ? `${baseClass} border-primary-500 text-primary-600 dark:text-primary-400`
    : `${baseClass} border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300`
}

const getTabBadgeClass = (tabKey) => {
  return tabKey === activeTab.value
    ? 'bg-primary-100 text-primary-600 dark:bg-primary-900 dark:text-primary-200'
    : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
}

const getTabLabel = (tabKey) => {
  const tab = messageTabs.value.find(t => t.key === tabKey)
  return tab ? tab.label : ''
}

const getTypeBadgeClass = (type) => {
  const classes = {
    system: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    order: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    menu: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
  }
  return classes[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getTypeText = (type) => {
  const texts = {
    system: '系统通知',
    order: '订单通知',
    menu: '菜单通知'
  }
  return texts[type] || '未知'
}

// 数据获取方法
const fetchMessages = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      type: activeTab.value,
      ...searchParams
    }
    
    const api = activeTab.value === 'messages' ? messageApi : notificationApi
    const response = await api.getList(params)
    messages.value = response.data.list
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取消息列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    const response = await messageApi.getStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 事件处理方法
const handleSearch = () => {
  pagination.current = 1
  fetchMessages()
}

const handleReset = () => {
  pagination.current = 1
  fetchMessages()
}

const handlePageChange = () => {
  fetchMessages()
}

const handleSort = (sortInfo) => {
  console.log('排序:', sortInfo)
  fetchMessages()
}

const handleView = (message) => {
  currentMessage.value = message
  showDetailModal.value = true
}

const handleMarkRead = async (message) => {
  try {
    await messageApi.markRead(message.id)
    fetchMessages()
    fetchStats()
  } catch (error) {
    console.error('标记已读失败:', error)
  }
}

const handleBatchMarkRead = async () => {
  try {
    const messageIds = selectedMessages.value.map(msg => msg.id)
    await messageApi.batchMarkRead(messageIds)
    selectedMessages.value = []
    fetchMessages()
    fetchStats()
  } catch (error) {
    console.error('批量标记已读失败:', error)
  }
}

const handleReply = (message) => {
  currentMessage.value = message
  showReplyModal.value = true
}

const handleReplySuccess = () => {
  showReplyModal.value = false
  fetchMessages()
}

const handleDelete = async (message) => {
  if (confirm('确定要删除这条消息吗？')) {
    try {
      const api = activeTab.value === 'messages' ? messageApi : notificationApi
      await api.delete(message.id)
      fetchMessages()
      fetchStats()
    } catch (error) {
      console.error('删除消息失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (confirm(`确定要删除选中的 ${selectedMessages.value.length} 条消息吗？`)) {
    try {
      const messageIds = selectedMessages.value.map(msg => msg.id)
      const api = activeTab.value === 'messages' ? messageApi : notificationApi
      await api.batchDelete(messageIds)
      selectedMessages.value = []
      fetchMessages()
      fetchStats()
    } catch (error) {
      console.error('批量删除消息失败:', error)
    }
  }
}

const handleCreateNotification = () => {
  showNotificationModal.value = true
}

const handleNotificationSuccess = () => {
  showNotificationModal.value = false
  if (activeTab.value === 'notifications') {
    fetchMessages()
  }
  fetchStats()
}

const handleRowClick = (message) => {
  console.log('点击消息:', message)
}

// 监听标签页变化
watch(activeTab, () => {
  pagination.current = 1
  selectedMessages.value = []
  fetchMessages()
})

// 生命周期
onMounted(() => {
  fetchMessages()
  fetchStats()
})
</script>
