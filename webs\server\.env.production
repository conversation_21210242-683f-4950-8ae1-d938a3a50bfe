# 生产环境配置文件
# 部署到云服务器时使用

# 环境标识
NODE_ENV=production

# 数据库配置 - MySQL
DATABASE_URL="mysql://nannan_user:5201314hl@*************:3306/nannan_db"

# 服务器配置
PORT=3001
HOST=0.0.0.0

# JWT配置 (生产环境使用强密钥)
JWT_SECRET="生产环境超强密钥请修改这个密钥"
JWT_EXPIRES_IN="7d"

# 微信小程序配置 (正式环境)
WECHAT_APPID="wx82283b353918af82"
WECHAT_SECRET="5fddc9bde3ddfe39c1b792cbd3fe3ac9"
WECHAT_TEMPLATE_ID="kDusapKJllk0UrDuT86oUfFoVID7eHDiQ4AK7i0esNc"

# PicX图床配置 (与测试环境共用)
PICX_TOKEN="****************************************"
PICX_REPO="2497462726/picx-images-hosting"
GITHUB_API_BASE="https://api.github.com"

# 域名配置 (临时使用IP，备案后改回域名)
DOMAIN="http://www.huanglun.asia"
ADMIN_URL="http://www.huanglun.asia/admin"
API_URL="http://www.huanglun.asia/api"

# 日志配置
LOG_LEVEL="info"
LOG_FILE="./logs/app.log"
