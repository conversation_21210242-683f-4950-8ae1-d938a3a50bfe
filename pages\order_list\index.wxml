<view class="order-list-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">我的订单</text>
  </view>

  <!-- 加载状态 -->
  <view
    wx:if="{{loading && orders.length === 0}}"
    style="width: 100vw;display: flex; justify-content: center;"
  >
    <van-loading type="spinner" color="#1989fa" size="24px" vertical>
      加载中...
    </van-loading>
  </view>

  <!-- 订单列表 -->
  <view wx:elif="{{!loading && orders.length > 0}}" class="order-list">
    <van-swipe-cell
      wx:for="{{orders}}"
      wx:key="id"
      wx:for-index="orderIndex"
      right-width="{{ 65 }}"
      data-order-id="{{item.id}}"
      data-order-index="{{orderIndex}}"
    >
      <view
        class="order-card"
        data-order="{{item}}"
        data-order-id="{{item.id}}"
        data-order-index="{{orderIndex}}"
        bindtap="viewOrderDetail"
      >
        <!-- 订单头部信息 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-id">订单 #{{item.id.slice(-8)}}</text>
            <text class="order-time">{{item.createdAtFormatted}}</text>
          </view>
          <view class="order-status">
            <van-tag color="{{getStatusColor(item.status)}}" size="small">
              {{getStatusText(item.status)}}
            </van-tag>
          </view>
        </view>

        <!-- 推送信息 -->
        <view wx:if="{{item.isPushedToMe}}" class="push-info">
          <van-icon name="share-o" size="16px" color="#1989fa" />
          <text class="push-text">{{item.pushedBy.name}} 推送给您</text>
        </view>

        <!-- 菜单信息 -->
        <view class="menu-info">
          <view class="menu-details">
            <text class="menu-title">{{item.menu.remark || '菜单'}}</text>
            <text wx:if="{{item.menuDeleted}}" class="menu-deleted"
              >（原菜单已删除）</text
            >
            <text class="menu-date">{{item.menu.dateFormatted}}</text>
          </view>
          <text class="menu-creator">by {{item.menu.creator.name}}</text>
        </view>

        <!-- 订单项目 -->
        <view class="order-items">
          <view
            wx:for="{{item.items}}"
            wx:for-item="orderItem"
            wx:key="dishId"
            class="order-item"
          >
            <text class="item-name">{{orderItem.name}}</text>
            <text class="item-count">x{{orderItem.count}}</text>
          </view>
        </view>

        <!-- 订单备注 -->
        <view wx:if="{{item.remark}}" class="order-remark">
          <van-icon name="chat-o" size="14px" color="#666" />
          <text class="remark-text">{{item.remark}}</text>
        </view>

        <!-- 用餐时间 -->
        <view wx:if="{{item.diningTimeFormatted}}" class="dining-time">
          <van-icon name="clock-o" size="14px" color="#666" />
          <text class="time-text">用餐时间：{{item.diningTimeFormatted}}</text>
        </view>

        <!-- 订单操作 -->
        <view class="order-actions">
          <view class="order-user">
            <image
              class="user-avatar"
              src="{{item.user.avatar || '/assets/image/default-avatar.png'}}"
              mode="aspectFill"
            />
            <text class="user-name">{{item.user.name}}</text>
          </view>

          <button
            class="detail-btn"
            data-order="{{item}}"
            catchtap="viewOrderDetail"
          >
            查看详情
          </button>
        </view>
      </view>

      <!-- 滑动删除按钮 -->
      <view slot="right" class="delete-button">
        <button
          class="delete-btn"
          data-order-id="{{item.id}}"
          data-order-index="{{orderIndex}}"
          catchtap="deleteOrder"
        >
          <van-icon name="delete-o" size="18px" />
          删除
        </button>
      </view>
    </van-swipe-cell>

    <!-- 加载更多提示 -->
    <view wx:if="{{loading && orders.length > 0}}" class="loading-more">
      <van-loading type="spinner" size="16px" />
      <text>加载中...</text>
    </view>

    <!-- 没有更多数据提示 -->
    <view wx:if="{{!hasMore && orders.length > 0}}" class="no-more">
      <text>没有更多订单了</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{!loading && orders.length === 0}}" class="empty-state">
    <van-icon name="orders-o" class="empty-icon" />
    <text class="empty-text">暂无订单</text>
    <button class="refresh-btn" bind:tap="refreshOrders">刷新</button>
  </view>
</view>
