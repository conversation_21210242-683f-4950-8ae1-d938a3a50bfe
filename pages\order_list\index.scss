/* 订单列表页面 - 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.order-list-page {
  @include page-container;
  @include page-container-safe;

  .page-header {
    @include modern-card;
    @include card-primary;
    @include text-center;
    @include mb-4;
    padding: 32rpx;

    .page-title {
      @include text-xl;
      @include font-bold;
      @include text-gray-900;
    }
  }

  .order-list {
    .order-card {
      @include modern-card;
      @include card-elevated;
      @include mb-4;
      @include transition;
      padding: 32rpx;

      &:active {
        transform: scale(0.98);
        @include shadow-lg;
      }

      .order-header {
        @include flex;
        @include justify-between;
        @include items-center;
        @include mb-3;

        .order-info {
          @include flex-1;

          .order-id {
            @include text-lg;
            @include font-bold;
            @include text-gray-900;
            @include mb-1;
            display: block;
          }

          .order-time {
            @include text-xs;
            @include text-gray-500;
          }
        }
      }

      .push-info {
        @include flex;
        @include items-center;
        @include gap-2;
        @include mb-3;
        @include p-3;
        @include rounded-md;
        background: linear-gradient(135deg, rgba($primary-solid, 0.1) 0%, rgba($primary-solid, 0.05) 100%);
        border: 2rpx solid rgba($primary-solid, 0.2);

        .push-text {
          @include text-sm;
          @include text-primary;
          @include font-medium;
        }
      }

      .menu-info {
        @include flex;
        @include justify-between;
        @include items-start;
        @include mb-3;
        @include p-3;
        @include rounded-md;
        @include bg-gray-50;
        border: 2rpx solid $gray-200;

        .menu-details {
          @include flex-1;

          .menu-title {
            @include text-base;
            @include font-bold;
            @include text-gray-900;
            @include mb-1;
            display: block;
          }

          .menu-deleted {
            @include text-xs;
            @include text-error;
            margin-left: 8rpx;
          }

          .menu-date {
            @include text-xs;
            @include text-gray-600;
            display: block;
          }
        }

        .menu-creator {
          @include text-xs;
          @include text-primary;
          @include font-medium;
        }
      }

      .order-items {
        @include mb-3;

        .order-item {
          @include flex;
          @include justify-between;
          @include items-center;
          padding: 16rpx 20rpx;
          margin-bottom: 12rpx;
          background: linear-gradient(135deg, rgba($white, 0.9) 0%, rgba($gray-50, 0.8) 100%);
          border: 2rpx solid rgba($primary-solid, 0.1);
          border-radius: $radius-lg;
          @include shadow-sm;
          @include transition;
          backdrop-filter: blur(8rpx);

          &:last-child {
            margin-bottom: 0;
          }

          &:hover {
            transform: translateY(-2rpx);
            @include shadow-md;
            border-color: rgba($primary-solid, 0.2);
          }

          .item-name {
            @include text-base;
            @include font-medium;
            @include text-gray-900;
            @include flex-1;
            margin-right: 16rpx;
            line-height: 1.4;
          }

          .item-count {
            @include text-sm;
            @include font-bold;
            background: linear-gradient(135deg, $primary-solid 0%, lighten($primary-solid, 10%) 100%);
            color: $white;
            border-radius: $radius-xl;
            padding: 8rpx 16rpx;
            min-width: 56rpx;
            text-align: center;
            @include shadow-sm;
            position: relative;

            &::before {
              content: "";
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba($white, 0.2) 0%, transparent 50%);
              border-radius: $radius-xl;
              pointer-events: none;
            }
          }
        }
      }

      .order-remark {
        @include flex;
        @include items-center;
        @include gap-2;
        @include mb-3;
        @include p-3;
        @include rounded-md;
        background: linear-gradient(135deg, rgba($warning, 0.1) 0%, rgba($warning, 0.05) 100%);
        border: 2rpx solid rgba($warning, 0.2);

        .remark-text {
          @include text-sm;
          @include text-gray-700;
          line-height: 1.4;
        }
      }

      .dining-time {
        @include flex;
        @include items-center;
        @include gap-2;
        @include mb-3;

        .time-text {
          @include text-sm;
          @include text-gray-600;
        }
      }

      .order-actions {
        @include flex;
        @include justify-between;
        @include items-center;
        padding-top: 24rpx;
        border-top: 2rpx solid $gray-200;

        .order-user {
          @include flex;
          @include items-center;
          @include gap-2;

          .user-avatar {
            width: 56rpx;
            height: 56rpx;
            @include rounded-full;
            @include shadow-sm;
            border: 2rpx solid $gray-200;
          }

          .user-name {
            @include text-sm;
            @include text-gray-900;
            @include font-medium;
          }
        }

        .detail-btn {
          @include modern-btn;
          @include btn-primary;
          @include btn-sm;
          @include text-xs;
          padding: 8rpx 16rpx;
        }
      }
    }

    .loading-more {
      @include flex;
      @include items-center;
      @include justify-center;
      @include gap-3;
      @include p-4;
      @include text-gray-500;
      @include text-sm;
    }

    .no-more {
      @include text-center;
      @include p-4;
      @include text-gray-500;
      @include text-sm;
    }
  }

  // 空状态样式
  .empty-state {
    @include modern-card;
    @include text-center;
    @include p-4;
    @include mt-4;

    .empty-icon {
      @include text-gray-400;
      font-size: 120rpx;
      @include mb-3;
    }

    .empty-text {
      @include text-base;
      @include text-gray-600;
      @include mb-4;
    }

    .refresh-btn {
      @include modern-btn;
      @include btn-primary;
      @include btn-sm;
    }
  }

  // 滑动删除按钮样式
  .delete-button {
    height: 100%;
    @include flex;
    @include items-center;
    @include justify-center;
    background-color: #ee0a24;
    width: 65px;
  }

  .delete-btn {
    height: 100%;
    width: 100%;
    background-color: #ee0a24;
    color: white;
    border: none;
    @include flex;
    @include flex-col;
    @include items-center;
    @include justify-center;
    @include text-xs;
    padding: 0;
    border-radius: 0;

    &::after {
      border: none;
    }

    &:active {
      background-color: #c8102e;
    }
  }
}
