<template>
  <div class="menu-history">
    <CustomTable
      title="历史菜单"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>

      <template #dishCount="{ row }">
        <el-tag type="info">{{ row.dishCount }}道菜</el-tag>
      </template>

      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-button size="small" @click="handleView(row)">查看</el-button>
        <el-button size="small" type="danger" @click="handleDelete(row)"
          >删除</el-button
        >
      </template>
    </CustomTable>

    <!-- 菜单详情对话框 -->
    <el-dialog v-model="detailVisible" title="菜单详情" width="900px">
      <div class="menu-detail" v-if="selectedMenu">
        <div class="detail-header">
          <h3>{{ selectedMenu.date }} 菜单</h3>
          <div class="menu-stats">
            <el-tag>共 {{ selectedMenu.dishCount }} 道菜</el-tag>
            <el-tag type="success" class="ml-2">
              总价值 ¥{{ calculateTotalPrice(selectedMenu.dishes) }}
            </el-tag>
          </div>
        </div>

        <div class="dish-categories">
          <div
            v-for="category in categoriesWithDishes"
            :key="category.key"
            class="category-section"
          >
            <h4 class="category-title">
              {{ category.name }} ({{ category.dishes.length }})
            </h4>
            <div class="dish-grid">
              <div
                v-for="dish in category.dishes"
                :key="dish.id"
                class="dish-card"
              >
                <div class="dish-image">
                  <el-image :src="dish.image" fit="cover" />
                </div>
                <div class="dish-info">
                  <h5 class="dish-name">{{ dish.name }}</h5>
                  <p class="dish-price">¥{{ dish.price }}</p>
                  <p class="dish-desc" v-if="dish.description">
                    {{ dish.description }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="detailVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleCopyFromDetail">
          复制为今日菜单
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import { menuApi } from '@/api/menu'
import dayjs from 'dayjs'

const loading = ref(false)
const tableData = ref([])
const detailVisible = ref(false)
const selectedMenu = ref(null)

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  date: '',
  status: ''
})

const categories = ref([
  { key: 'hot', name: '热菜' },
  { key: 'cold', name: '凉菜' },
  { key: 'soup', name: '汤品' },
  { key: 'staple', name: '主食' },
  { key: 'dessert', name: '甜品' }
])

// 表格列配置
const columns = [
  { prop: 'date', label: '日期', minWidth: 120, formatter: (row) => formatDate(row.date) },
  { prop: 'dishCount', label: '菜品数量', minWidth: 100, slot: true },
  { prop: 'totalPrice', label: '总价值', minWidth: 100, formatter: (row) => `¥${row.totalPrice || 0}` },
  { prop: 'status', label: '状态', minWidth: 100, slot: true },
  { prop: 'createdBy', label: '创建人', minWidth: 150, showOverflowTooltip: true },
  { prop: 'createdAt', label: '创建时间', minWidth: 160, formatter: (row) => formatTime(row.createdAt) },
  { prop: 'updatedAt', label: '更新时间', minWidth: 160, formatter: (row) => formatTime(row.updatedAt) },
  { label: '操作', width: 120, slot: 'operation', fixed: 'right' }
]

// 搜索字段配置
const searchFields = [
  { prop: 'date', label: '日期', type: 'date' },
  { prop: 'status', label: '状态', type: 'select', options: [
    { label: '今日菜单', value: 'today' },
    { label: '已过期', value: 'expired' },
    { label: '正常', value: 'active' }
  ]}
]

// 计算属性
const categoriesWithDishes = computed(() => {
  if (!selectedMenu.value || !selectedMenu.value.dishes) return []

  return categories.value.map(category => ({
    ...category,
    dishes: selectedMenu.value.dishes.filter(dish => dish.category === category.key)
  })).filter(category => category.dishes.length > 0)
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }
    const res = await menuApi.getHistoryMenus(params)
    if (res.code === 200 && res.data) {
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    } else {
      ElMessage.error(res.message || '加载数据失败')
      tableData.value = []
      pagination.total = 0
    }
  } catch (error) {
    console.error('加载历史菜单失败:', error)
    ElMessage.error('加载数据失败')
    // API 失败时显示空数据
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// Mock 数据已移除，使用真实 API 数据

const formatDate = (date) => {
  return dayjs(date).format('MM-DD')
}

const formatTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const getStatusType = (status) => {
  const types = {
    today: 'success',
    active: 'primary',
    expired: 'warning',
    deleted: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    today: '今日菜单',
    active: '正常',
    expired: '已过期',
    deleted: '已删除'
  }
  return texts[status] || status
}

const calculateTotalPrice = (dishes) => {
  if (!dishes || !Array.isArray(dishes)) return 0
  return dishes.reduce((total, dish) => total + (dish.price || 0), 0)
}

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleView = (row) => {
  selectedMenu.value = row
  detailVisible.value = true
}

const handleCopy = async (row) => {
  try {
    await ElMessageBox.confirm('确定要将此菜单复制为今日菜单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    const menuData = {
      date: dayjs().format('YYYY-MM-DD'),
      dishes: row.dishes.map(dish => ({
        dishId: dish.id,
        dishName: dish.name,
        category: dish.category,
        price: dish.price,
        image: dish.image
      }))
    }

    await menuApi.createMenu(menuData)
    ElMessage.success('菜单复制成功')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('复制菜单失败:', error)
      ElMessage.error('复制菜单失败')
    }
  }
}

const handleCopyFromDetail = async () => {
  try {
    await handleCopy(selectedMenu.value)
    detailVisible.value = false
  } catch (error) {
    // 错误已在handleCopy中处理
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个菜单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await menuApi.deleteMenu(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除菜单失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleExport = async () => {
  try {
    ElMessage.info('导出功能开发中...')
    // TODO: 实现导出功能
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.menu-history {
  @apply p-6 bg-gray-50 min-h-screen;
}

.menu-detail {
  .detail-header {
    @apply flex justify-between items-center mb-6 pb-4 border-b border-gray-200;

    h3 {
      @apply text-xl font-bold text-gray-900;
    }

    .menu-stats {
      @apply flex items-center space-x-2;
    }
  }
}

.category-section {
  @apply mb-6;

  .category-title {
    @apply text-lg font-semibold text-gray-900 mb-4 pb-2 border-b border-gray-100;
  }
}

.dish-grid {
  @apply grid grid-cols-3 gap-4;
}

.dish-card {
  @apply border border-gray-200 rounded-lg overflow-hidden bg-white hover:shadow-md transition-shadow;

  .dish-image {
    @apply h-24;
  }

  .dish-info {
    @apply p-3;

    .dish-name {
      @apply text-sm font-medium text-gray-900 mb-1;
    }

    .dish-price {
      @apply text-sm text-blue-600 font-semibold mb-1;
    }

    .dish-desc {
      @apply text-xs text-gray-500;
    }
  }
}
</style>
