/* 历史菜单页面 - 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.container {
	@include page-container;
	@include page-container-safe;
	background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
	min-height: 100vh;
}

/* 顶部标题栏 */
.header {
	background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
	padding: 40rpx 32rpx 60rpx;
	margin-bottom: -30rpx;
	position: relative;

	&::after {
		content: "";
		position: absolute;
		bottom: -30rpx;
		left: 0;
		right: 0;
		height: 30rpx;
		background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
		border-radius: 0 0 30rpx 30rpx;
	}
}

.header-content {
	text-align: center;
	color: white;
}

.header-title {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.title-icon {
	font-size: 48rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.title-text {
	font-size: 48rpx;
	font-weight: 700;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.header-subtitle {
	font-size: 28rpx;
	opacity: 0.9;
	font-weight: 400;
}

/* 菜单列表 */
.menu-list {
	padding: 50rpx 32rpx 120rpx;
	display: flex;
	flex-direction: column;
	gap: 32rpx;
}

.menu-card {
	background: white;
	border-radius: 24rpx;
	padding: 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(59, 130, 246, 0.12);
	border: 1rpx solid rgba(59, 130, 246, 0.15);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;

	&::before {
		content: "";
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
		background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
	}

	&.today-menu {
		border-color: rgba(14, 165, 233, 0.3);
		box-shadow: 0 8rpx 32rpx rgba(14, 165, 233, 0.15);

		&::before {
			background: linear-gradient(90deg, #0ea5e9 0%, #38bdf8 100%);
		}
	}

	&:active {
		transform: scale(0.98);
	}
}

/* 卡片头部 */
.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
}

.date-section {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.date-icon {
	font-size: 32rpx;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #1f2937;
}

.today-badge {
	background: linear-gradient(135deg, #0ea5e9 0%, #38bdf8 100%);
	color: white;
	font-size: 20rpx;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(14, 165, 233, 0.3);
}

.creator-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.creator-avatar {
	font-size: 24rpx;
}

.creator-name {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
}

/* 摘要部分 */
.summary-section {
	margin-bottom: 24rpx;
}

.summary-label {
	font-size: 24rpx;
	color: #6b7280;
	margin-bottom: 8rpx;
	font-weight: 500;
}

.summary-text {
	font-size: 28rpx;
	color: #374151;
	line-height: 1.5;
	background: #f0f9ff;
	padding: 16rpx;
	border-radius: 12rpx;
	border-left: 4rpx solid #3b82f6;
}

/* 菜品详情部分 */
.dishes-section {
	margin-bottom: 24rpx;
}

.dishes-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.dishes-icon {
	font-size: 28rpx;
}

.dishes-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #1f2937;
	flex: 1;
	margin-left: 8rpx;
}

.dishes-count {
	font-size: 24rpx;
	color: #3b82f6;
	background: rgba(59, 130, 246, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-weight: 500;
}

.dishes-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 12rpx;
}

.dish-tag {
	background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
	border: 1rpx solid rgba(59, 130, 246, 0.2);
	border-radius: 16rpx;
	padding: 12rpx 16rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	transition: all 0.2s ease;

	&:active {
		transform: scale(0.95);
		background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
	}
}

.dish-name {
	font-size: 26rpx;
	color: #1f2937;
	font-weight: 500;
}

.dish-count {
	font-size: 22rpx;
	color: #3b82f6;
	font-weight: 600;
	background: rgba(59, 130, 246, 0.1);
	padding: 2rpx 8rpx;
	border-radius: 8rpx;
}

/* 备注部分 */
.remark-section {
	display: flex;
	align-items: flex-start;
	gap: 12rpx;
	background: #fef3c7;
	padding: 16rpx;
	border-radius: 12rpx;
	border-left: 4rpx solid #f59e0b;
	margin-bottom: 24rpx;
}

.remark-icon {
	font-size: 28rpx;
	margin-top: 2rpx;
}

.remark-text {
	font-size: 26rpx;
	color: #92400e;
	line-height: 1.4;
	flex: 1;
}

/* 统计信息部分 */
.stats-section {
	display: flex;
	gap: 24rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid #e5e7eb;
}

.stat-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	flex: 1;
}

.stat-icon {
	font-size: 24rpx;
}

.stat-text {
	font-size: 24rpx;
	color: #6b7280;
	font-weight: 500;
}

/* 浮动返回按钮 */
.floating-btn {
	position: fixed;
	right: 32rpx;
	bottom: 32rpx;
	background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
	color: white;
	border-radius: 50rpx;
	padding: 20rpx 32rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.4);
	z-index: 100;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
	}
}

.btn-icon {
	font-size: 32rpx;
	font-weight: bold;
}

.btn-text {
	font-size: 28rpx;
	font-weight: 600;
}
