<template>
  <component
    :is="tag"
    :type="tag === 'button' ? type : undefined"
    :to="tag === 'router-link' ? to : undefined"
    :href="tag === 'a' ? href : undefined"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="handleClick"
  >
    <!-- 加载图标 -->
    <svg
      v-if="loading"
      class="animate-spin -ml-1 mr-2 h-4 w-4"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        class="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        stroke-width="4"
      ></circle>
      <path
        class="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>

    <!-- 前置图标 -->
    <component
      v-else-if="prefixIcon"
      :is="prefixIcon"
      :class="iconClasses"
    />

    <!-- 按钮文本 -->
    <span v-if="$slots.default || text">
      <slot>{{ text }}</slot>
    </span>

    <!-- 后置图标 -->
    <component
      v-if="suffixIcon && !loading"
      :is="suffixIcon"
      :class="iconClasses"
    />
  </component>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 按钮文本
  text: {
    type: String,
    default: ''
  },
  // 按钮类型
  type: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'submit', 'reset'].includes(value)
  },
  // 按钮变体
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => [
      'primary', 'secondary', 'success', 'warning', 'error', 
      'outline', 'ghost', 'link'
    ].includes(value)
  },
  // 按钮尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  // 是否为块级按钮
  block: {
    type: Boolean,
    default: false
  },
  // 是否为圆形按钮
  round: {
    type: Boolean,
    default: false
  },
  // 前置图标
  prefixIcon: {
    type: [Object, Function],
    default: null
  },
  // 后置图标
  suffixIcon: {
    type: [Object, Function],
    default: null
  },
  // 标签类型
  tag: {
    type: String,
    default: 'button',
    validator: (value) => ['button', 'a', 'router-link'].includes(value)
  },
  // 链接地址 (tag为a时使用)
  href: {
    type: String,
    default: ''
  },
  // 路由地址 (tag为router-link时使用)
  to: {
    type: [String, Object],
    default: ''
  }
})

const emit = defineEmits(['click'])

const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none'
  ]

  // 尺寸样式
  const sizeClasses = {
    xs: 'px-2.5 py-1.5 text-xs rounded',
    sm: 'px-3 py-2 text-sm rounded-md',
    md: 'px-4 py-2 text-sm rounded-md',
    lg: 'px-4 py-2 text-base rounded-md',
    xl: 'px-6 py-3 text-base rounded-md'
  }

  // 变体样式
  const variantClasses = {
    primary: [
      'bg-primary-600 text-white shadow-sm',
      'hover:bg-primary-700 focus:ring-primary-500',
      'dark:bg-primary-500 dark:hover:bg-primary-600'
    ],
    secondary: [
      'bg-gray-600 text-white shadow-sm',
      'hover:bg-gray-700 focus:ring-gray-500',
      'dark:bg-gray-500 dark:hover:bg-gray-600'
    ],
    success: [
      'bg-success-600 text-white shadow-sm',
      'hover:bg-success-700 focus:ring-success-500'
    ],
    warning: [
      'bg-warning-600 text-white shadow-sm',
      'hover:bg-warning-700 focus:ring-warning-500'
    ],
    error: [
      'bg-error-600 text-white shadow-sm',
      'hover:bg-error-700 focus:ring-error-500'
    ],
    outline: [
      'bg-white text-gray-700 border border-gray-300 shadow-sm',
      'hover:bg-gray-50 focus:ring-primary-500',
      'dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700'
    ],
    ghost: [
      'bg-transparent text-gray-700',
      'hover:bg-gray-100 focus:ring-primary-500',
      'dark:text-gray-300 dark:hover:bg-gray-800'
    ],
    link: [
      'bg-transparent text-primary-600 p-0 shadow-none',
      'hover:text-primary-700 focus:ring-0 underline-offset-4 hover:underline',
      'dark:text-primary-400 dark:hover:text-primary-300'
    ]
  }

  // 圆形样式
  const roundClasses = props.round ? 'rounded-full' : ''

  // 块级样式
  const blockClasses = props.block ? 'w-full' : ''

  return [
    ...baseClasses,
    sizeClasses[props.size],
    ...variantClasses[props.variant],
    roundClasses,
    blockClasses
  ].filter(Boolean).join(' ')
})

const iconClasses = computed(() => {
  const hasText = props.text || props.$slots?.default
  const sizeMap = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
    xl: 'h-5 w-5'
  }

  const spacingClasses = []
  if (hasText) {
    if (props.prefixIcon) spacingClasses.push('mr-2')
    if (props.suffixIcon) spacingClasses.push('ml-2')
  }

  return [sizeMap[props.size], ...spacingClasses].join(' ')
})

const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>
