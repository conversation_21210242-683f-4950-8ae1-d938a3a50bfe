<template>
  <div class="today-menu">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1 class="page-title">今日菜单</h1>
          <p class="page-subtitle">查看和管理小程序用户看到的今日菜单</p>
          <div class="date-info">
            <el-icon><Calendar /></el-icon>
            <span>{{ currentDate }}</span>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-card">
            <div class="stat-number">{{ menuDishes.length }}</div>
            <div class="stat-label">菜品数量</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ totalPrice.toFixed(2) }}</div>
            <div class="stat-label">总价值(¥)</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ categoryCount }}</div>
            <div class="stat-label">涵盖分类</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          @change="handleDateChange"
          size="large"
        />
        <el-tag v-if="isToday" type="success" class="today-tag">今天</el-tag>
      </div>
      <div class="action-right">
        <el-button @click="handleRefresh" size="large">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button
          @click="handleClearAll"
          type="danger"
          plain
          size="large"
          :disabled="menuDishes.length === 0"
        >
          <el-icon><Delete /></el-icon>
          清空菜单
        </el-button>
      </div>
    </div>

    <!-- 菜单展示区域 -->
    <div class="menu-display">
      <div v-if="menuDishes.length === 0" class="empty-menu">
        <el-empty description="今日暂无菜单">
          <el-button type="primary" @click="handleRefresh">刷新数据</el-button>
        </el-empty>
      </div>

      <div v-else class="menu-content">
        <!-- 分类展示 -->
        <div
          v-for="category in categoriesWithDishes"
          :key="category.name"
          class="category-section"
        >
          <div class="category-header">
            <h3 class="category-title">{{ category.name }}</h3>
            <span class="category-count">{{ category.dishes.length }}道菜</span>
          </div>

          <div class="dishes-grid">
            <div
              v-for="dish in category.dishes"
              :key="dish.id"
              class="dish-card"
            >
              <div class="dish-image">
                <el-image :src="dish.image" :alt="dish.name" fit="cover">
                  <template #error>
                    <div class="image-slot">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>

              <div class="dish-info">
                <h4 class="dish-name">{{ dish.name }}</h4>
                <p class="dish-description" v-if="dish.description">
                  {{ dish.description }}
                </p>
                <div class="dish-meta">
                  <span class="dish-price">¥{{ dish.price || '0' }}</span>
                  <el-button
                    type="danger"
                    size="small"
                    plain
                    @click="handleRemoveDish(dish)"
                  >
                    <el-icon><Delete /></el-icon>
                    移除
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 菜单预览对话框 -->
    <el-dialog v-model="previewVisible" title="菜单预览" width="800px">
      <div class="menu-preview">
        <div class="preview-header">
          <h2>{{ selectedDate }} 菜单</h2>
          <p>共 {{ selectedDishes.length }} 道菜</p>
        </div>

        <div class="preview-categories">
          <div
            v-for="category in categoriesWithDishes"
            :key="category.key"
            class="preview-category"
          >
            <h3 class="category-title">{{ category.name }}</h3>
            <div class="category-dishes">
              <div
                v-for="dish in category.dishes"
                :key="dish.id"
                class="preview-dish"
              >
                <el-image
                  :src="dish.image"
                  class="preview-dish-image"
                  fit="cover"
                />
                <div class="preview-dish-info">
                  <h4>{{ dish.name }}</h4>
                  <p>¥{{ dish.price }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Calendar, Refresh, Picture } from '@element-plus/icons-vue'
import { dishApi } from '@/api/menu'
import { menuApi } from '@/api/menu'
import dayjs from 'dayjs'

const selectedDate = ref(dayjs().format('YYYY-MM-DD'))
const menuDishes = ref([])
const saveLoading = ref(false)
const previewVisible = ref(false)

// 计算属性
const currentDate = computed(() => {
  return dayjs().format('YYYY年MM月DD日 dddd')
})

const totalPrice = computed(() => {
  return menuDishes.value.reduce((sum, dish) => sum + (parseFloat(dish.price) || 0), 0)
})

const categoryCount = computed(() => {
  const categories = new Set(menuDishes.value.map(dish => dish.category))
  return categories.size
})

const isToday = computed(() => {
  return selectedDate.value === dayjs().format('YYYY-MM-DD')
})

const categoriesWithDishes = computed(() => {
  const categoryMap = new Map()

  menuDishes.value.forEach(dish => {
    if (!categoryMap.has(dish.category)) {
      categoryMap.set(dish.category, {
        name: dish.category,
        dishes: []
      })
    }
    categoryMap.get(dish.category).dishes.push(dish)
  })

  return Array.from(categoryMap.values())
})

// 方法
const loadTodayMenu = async () => {
  try {
    const res = await menuApi.getTodayMenu(selectedDate.value)
    if (res.code === 200 && res.data && res.data.dishes) {
      menuDishes.value = res.data.dishes
    } else {
      menuDishes.value = []
    }
  } catch (error) {
    console.error('加载今日菜单失败:', error)
    menuDishes.value = []
  }
}

const handleRemoveDish = async (dish) => {
  try {
    await ElMessageBox.confirm(`确定要从今日菜单中移除"${dish.name}"吗？`, '确认移除', {
      type: 'warning'
    })

    // 调用API移除菜品
    const res = await menuApi.removeDishFromMenu(selectedDate.value, dish.id)
    if (res.code === 200) {
      ElMessage.success('移除成功')
      loadTodayMenu() // 重新加载菜单
    } else {
      ElMessage.error(res.message || '移除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('移除菜品失败:', error)
      ElMessage.error('移除失败')
    }
  }
}

const handleDateChange = (date) => {
  loadTodayMenu()
}

const handleRefresh = () => {
  loadTodayMenu()
}

const handleClearAll = async () => {
  if (menuDishes.value.length === 0) {
    ElMessage.warning('当前没有菜单可清空')
    return
  }

  try {
    await ElMessageBox.confirm('确定要清空今日菜单吗？此操作不可恢复！', '确认清空', {
      type: 'warning'
    })

    const res = await menuApi.clearTodayMenu(selectedDate.value)
    if (res.code === 200) {
      ElMessage.success('菜单已清空')
      loadTodayMenu() // 重新加载菜单
    } else {
      ElMessage.error(res.message || '清空失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空菜单失败:', error)
      ElMessage.error('清空失败')
    }
  }
}

onMounted(() => {
  loadTodayMenu()
})
</script>

<style scoped lang="scss">
.today-menu {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 60px);
}

.page-header {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-info {
      flex: 1;

      .page-title {
        font-size: 28px;
        font-weight: 700;
        color: #1a202c;
        margin: 0 0 8px 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .page-subtitle {
        font-size: 16px;
        color: #718096;
        margin: 0 0 16px 0;
        line-height: 1.6;
      }

      .date-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #4a5568;
        font-size: 14px;

        .el-icon {
          color: #667eea;
        }
      }
    }

    .header-stats {
      display: flex;
      gap: 24px;

      .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        min-width: 100px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 12px;
          opacity: 0.9;
        }
      }
    }
  }
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px 32px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);

  .action-left,
  .action-right {
    display: flex;
    gap: 12px;
  }
}

.date-selector {
  @apply flex items-center space-x-3 mb-6;

  .today-tag {
    @apply ml-2;
  }
}

.dish-selection {
  .selection-panel {
    @apply bg-white rounded-lg shadow-sm h-full;

    .panel-header {
      @apply flex justify-between items-center p-4 border-b border-gray-200;

      h3 {
        @apply text-lg font-semibold text-gray-900;
      }

      .search-box {
        @apply w-64;
      }
    }

    .category-tabs {
      @apply p-4;

      :deep(.el-tabs__header) {
        @apply mb-4;
      }
    }
  }
}

.menu-display {
  @apply bg-white rounded-2xl p-6 shadow-lg;

  .empty-menu {
    @apply text-center py-16;
  }

  .menu-content {
    .category-section {
      @apply mb-8;

      &:last-child {
        @apply mb-0;
      }

      .category-header {
        @apply flex justify-between items-center mb-4 pb-3 border-b-2 border-gray-100;

        .category-title {
          @apply text-xl font-semibold text-gray-800 m-0;
        }

        .category-count {
          @apply text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full;
        }
      }

      .dishes-grid {
        @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5;

        .dish-card {
          @apply bg-gray-50 rounded-xl overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-lg hover:-translate-y-1;

          .dish-image {
            @apply w-full h-48 overflow-hidden;

            :deep(.el-image) {
              @apply w-full h-full;
            }

            .image-slot {
              @apply flex justify-center items-center w-full h-full bg-gray-200 text-gray-400 text-3xl;
            }
          }

          .dish-info {
            @apply p-4;

            .dish-name {
              @apply text-base font-semibold text-gray-800 mb-2 truncate;
            }

            .dish-description {
              @apply text-sm text-gray-600 mb-3 line-clamp-2;
            }

            .dish-meta {
              @apply flex justify-between items-center;

              .dish-price {
                @apply text-lg font-bold text-red-500;
              }
            }
          }
        }
      }
    }
  }
}

.menu-preview {
  .preview-header {
    @apply text-center mb-6 pb-4 border-b border-gray-200;

    h2 {
      @apply text-xl font-bold text-gray-900 mb-2;
    }

    p {
      @apply text-gray-600;
    }
  }

  .preview-category {
    @apply mb-6;

    .category-title {
      @apply text-lg font-semibold text-gray-900 mb-3;
    }

    .category-dishes {
      @apply grid grid-cols-3 gap-4;
    }
  }
}

.preview-dish {
  @apply border border-gray-200 rounded-lg overflow-hidden;

  .preview-dish-image {
    @apply w-full h-20;
  }

  .preview-dish-info {
    @apply p-3;

    h4 {
      @apply text-sm font-medium text-gray-900 mb-1;
    }

    p {
      @apply text-sm text-blue-600 font-semibold;
    }
  }
}
</style>
