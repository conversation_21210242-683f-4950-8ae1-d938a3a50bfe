const prisma = require('../utils/prisma');
const {
	success,
	error
} = require('../utils/response');
const notificationService = require('../services/notificationService');

/**
 * 获取菜单列表
 * @route GET /api/menus
 */
const getMenus = async (req, res) => {
	try {
		// 分页参数
		const page = parseInt(req.query.page, 10) || 1;
		const size = parseInt(req.query.size, 10) || 10;
		const skip = (page - 1) * size;
		const take = size;

		// 搜索参数
		const {
			date,
			isToday
		} = req.query;
		const where = {};
		if (date) {
			// 支持模糊匹配或精确匹配
			where.date = new Date(date);
		}
		if (isToday !== undefined && isToday !== '') {
			where.isToday = isToday === 'true' || isToday === true;
		}

		// 获取总数
		const total = await prisma.menu.count({
			where
		});

		// 获取菜单数据
		const menus = await prisma.menu.findMany({
			where,
			include: {
				items: {
					include: {
						dish: true
					}
				}
			},
			orderBy: {
				date: 'desc'
			},
			skip,
			take
		});

		// 格式化菜单数据
		const list = menus.map(menu => ({
			id: menu.id,
			date: menu.date,
			remark: menu.remark,
			isToday: menu.isToday,
			dishes: menu.items.map(item => ({
				id: item.dish.id,
				name: item.dish.name,
				description: item.dish.description,
				image: item.dish.image,
				count: item.count,
				isPublished: item.dish.isPublished, // 添加上架状态
				isUnavailable: !item.dish.isPublished, // 标记是否不可用
				unavailableReason: !item.dish.isPublished ? '菜品已下架' : null
			}))
		}));

		return success(res, {
			list,
			total
		});
	} catch (err) {
		console.error('Get menus error:', err);
		return error(res, 'Failed to get menus', 500);
	}
};

/**
 * 获取今日菜单
 * @route GET /api/menus/today
 */
const getTodayMenu = async (req, res) => {
	try {
		// 获取今天的日期（不包含时间）
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		const tomorrow = new Date(today);
		tomorrow.setDate(tomorrow.getDate() + 1);

		// 查找今日菜单
		const todayMenu = await prisma.menu.findFirst({
			where: {
				date: {
					gte: today,
					lt: tomorrow
				},
				isToday: true
			},
			include: {
				items: {
					include: {
						dish: true
					}
				}
			}
		});

		if (!todayMenu) {
			return success(res, null, 'No menu for today');
		}

		// 格式化菜单数据
		const formattedMenu = {
			id: todayMenu.id,
			date: todayMenu.date,
			remark: todayMenu.remark,
			dishes: todayMenu.items.map(item => ({
				id: item.dish.id,
				name: item.dish.name,
				description: item.dish.description,
				image: item.dish.image,
				count: item.count
			}))
		};

		return success(res, formattedMenu);
	} catch (err) {
		console.error('Get today menu error:', err);
		return error(res, 'Failed to get today menu', 500);
	}
};

/**
 * 获取历史菜单
 * @route GET /api/menus/history
 */
const getHistoryMenus = async (req, res) => {
	try {
		const {
			page = 1, size = 10, userId
		} = req.query;
		const pageNum = parseInt(page);
		const pageSize = parseInt(size);
		const skip = (pageNum - 1) * pageSize;

		// 构建查询条件
		const where = {
			deleted: false
		};

		// 如果指定了用户ID，只查询该用户的菜单
		if (userId) {
			where.createdBy = userId;
		}

		// 获取总数
		const total = await prisma.menu.count({
			where
		});

		// 获取历史菜单
		const historyMenus = await prisma.menu.findMany({
			where,
			include: {
				creator: {
					select: {
						id: true,
						name: true,
						avatar: true
					}
				},
				items: {
					include: {
						dish: {
							select: {
								id: true,
								name: true,
								description: true,
								image: true
							}
						}
					}
				},
				orders: {
					select: {
						id: true,
						status: true,
						createdAt: true
					}
				}
			},
			orderBy: {
				createdAt: 'desc'
			},
			skip,
			take: pageSize
		});

		// 格式化菜单数据
		const formattedMenus = historyMenus.map(menu => ({
			id: menu.id,
			createdBy: menu.createdBy,
			creator: menu.creator,
			date: menu.date,
			isToday: menu.isToday,
			remark: menu.remark,
			dishes: menu.items.map(item => ({
				id: item.dish.id,
				name: item.dish.name,
				description: item.dish.description,
				image: item.dish.image,
				count: item.count
			})),
			orderCount: menu.orders.length,
			createdAt: menu.createdAt
		}));

		return success(res, {
			list: formattedMenus,
			total,
			page: pageNum,
			size: pageSize,
			totalPages: Math.ceil(total / pageSize)
		});
	} catch (err) {
		console.error('Get history menus error:', err);
		return error(res, 'Failed to get history menus', 500);
	}
};

/**
 * 获取今日菜单
 * @route GET /api/menus/today
 */
const getTodayMenus = async (req, res) => {
	try {
		const {
			userId
		} = req.query;
		const currentUserId = req.user.id;

		// 构建查询条件
		const where = {
			deleted: false,
			isToday: true
		};

		// 如果指定了用户ID，只查询该用户的菜单
		if (userId) {
			where.createdBy = userId;
		} else {
			// 默认查询当前用户的今日菜单
			where.createdBy = currentUserId;
		}

		// 获取今日菜单
		const todayMenus = await prisma.menu.findMany({
			where,
			include: {
				creator: {
					select: {
						id: true,
						name: true,
						avatar: true
					}
				},
				items: {
					include: {
						dish: {
							select: {
								id: true,
								name: true,
								description: true,
								image: true
							}
						}
					}
				},
				orders: {
					select: {
						id: true,
						status: true,
						diningTime: true,
						createdAt: true
					}
				}
			},
			orderBy: {
				createdAt: 'desc'
			}
		});

		// 格式化菜单数据
		const formattedMenus = todayMenus.map(menu => ({
			id: menu.id,
			createdBy: menu.createdBy,
			creator: menu.creator,
			date: menu.date,
			isToday: menu.isToday,
			remark: menu.remark,
			dishes: menu.items.map(item => ({
				id: item.dish.id,
				name: item.dish.name,
				description: item.dish.description,
				image: item.dish.image,
				count: item.count
			})),
			orders: menu.orders,
			createdAt: menu.createdAt
		}));

		return success(res, formattedMenus);
	} catch (err) {
		console.error('Get today menus error:', err);
		return error(res, 'Failed to get today menus', 500);
	}
};

/**
 * 获取指定菜单
 * @route GET /api/menus/:id
 */
const getMenuById = async (req, res) => {
	try {
		const {
			id
		} = req.params;

		const menu = await prisma.menu.findUnique({
			where: {
				id
			},
			include: {
				items: {
					include: {
						dish: true
					}
				}
			}
		});

		if (!menu) {
			return error(res, 'Menu not found', 404);
		}

		// 格式化菜单数据
		const formattedMenu = {
			id: menu.id,
			date: menu.date,
			remark: menu.remark,
			dishes: menu.items.map(item => ({
				id: item.dish.id,
				name: item.dish.name,
				description: item.dish.description,
				image: item.dish.image,
				count: item.count
			}))
		};

		return success(res, formattedMenu);
	} catch (err) {
		console.error('Get menu error:', err);
		return error(res, 'Failed to get menu', 500);
	}
};

/**
 * 创建菜单
 * @route POST /api/menus
 */
const createMenu = async (req, res) => {
	try {
		const {
			date,
			dishes,
			remark,
			isToday
		} = req.body;

		if (!date || !dishes || !Array.isArray(dishes) || dishes.length === 0) {
			return error(res, 'Date and dishes are required', 400);
		}

		// 创建菜单
		const menu = await prisma.menu.create({
			data: {
				createdBy: req.user.id,
				date: new Date(date),
				remark,
				isToday: isToday || false
			}
		});

		// 创建菜单项
		const menuItems = [];

		for (const dish of dishes) {
			// 检查菜品是否存在
			const existingDish = await prisma.dish.findUnique({
				where: {
					id: dish.id
				}
			});

			if (!existingDish) {
				continue; // 跳过不存在的菜品
			}

			const menuItem = await prisma.menuItem.create({
				data: {
					menuId: menu.id,
					dishId: dish.id,
					count: dish.count || 1
				}
			});

			menuItems.push(menuItem);
		}

		// 如果是今日菜单，将其他菜单的 isToday 设为 false
		if (isToday) {
			await prisma.menu.updateMany({
				where: {
					id: {
						not: menu.id
					},
					isToday: true
				},
				data: {
					isToday: false
				}
			});
		}

		// 获取完整的菜单信息用于推送
		const fullMenu = await prisma.menu.findUnique({
			where: {
				id: menu.id
			},
			include: {
				items: {
					include: {
						dish: true
					}
				}
			}
		});

		// 生成推荐菜单给关联用户
		try {
			await generateRecommendedMenus(menu.id, req.user.id);
		} catch (recommendError) {
			console.error('Failed to generate recommended menus:', recommendError);
			// 不影响主要功能，只记录错误
		}

		// 如果是今日菜单，发送推送通知给关联用户
		if (isToday && fullMenu) {
			try {
				// 发送应用内通知
				await notificationService.notifyMenuUpdate(fullMenu, req.user);
			} catch (notifyError) {
				console.error('Failed to send menu notification:', notifyError);
				// 不影响主要功能，只记录错误
			}

			// 这里可以添加小程序订阅消息推送逻辑（如果需要）
			// 目前暂时移除了公众号推送功能
		}

		return success(
			res, {
				id: menu.id,
				date: menu.date,
				remark: menu.remark,
				isToday: menu.isToday,
				items: menuItems
			},
			'Menu created successfully',
			201
		);
	} catch (err) {
		console.error('Create menu error:', err);
		return error(res, 'Failed to create menu', 500);
	}
};

/**
 * 更新菜单
 * @route PUT /api/menus/:id
 */
const updateMenu = async (req, res) => {
	try {
		const {
			id
		} = req.params;
		const {
			date,
			dishes,
			remark,
			isToday
		} = req.body;

		// 检查菜单是否存在
		const existingMenu = await prisma.menu.findUnique({
			where: {
				id
			}
		});

		if (!existingMenu) {
			return error(res, 'Menu not found', 404);
		}

		// 更新菜单
		const updateData = {};

		if (date) updateData.date = new Date(date);
		if (remark !== undefined) updateData.remark = remark;
		if (isToday !== undefined) updateData.isToday = isToday;

		const updatedMenu = await prisma.menu.update({
			where: {
				id
			},
			data: updateData
		});

		// 如果提供了菜品，更新菜单项
		if (dishes && Array.isArray(dishes)) {
			// 删除现有菜单项
			await prisma.menuItem.deleteMany({
				where: {
					menuId: id
				}
			});

			// 创建新菜单项
			for (const dish of dishes) {
				// 检查菜品是否存在
				const existingDish = await prisma.dish.findUnique({
					where: {
						id: dish.id
					}
				});

				if (!existingDish) {
					continue; // 跳过不存在的菜品
				}

				await prisma.menuItem.create({
					data: {
						menuId: id,
						dishId: dish.id,
						count: dish.count || 1
					}
				});
			}
		}

		// 如果是今日菜单，将其他菜单的 isToday 设为 false
		if (isToday) {
			await prisma.menu.updateMany({
				where: {
					id: {
						not: id
					},
					isToday: true
				},
				data: {
					isToday: false
				}
			});
		}

		// 获取完整的菜单信息用于推送
		const fullUpdatedMenu = await prisma.menu.findUnique({
			where: {
				id
			},
			include: {
				items: {
					include: {
						dish: true
					}
				}
			}
		});

		// 如果是今日菜单，发送推送通知给关联用户
		if (isToday && fullUpdatedMenu) {
			try {
				// 发送应用内通知
				await notificationService.notifyMenuUpdate(fullUpdatedMenu, req.user);
			} catch (notifyError) {
				console.error('Failed to send menu update notification:', notifyError);
				// 不影响主要功能，只记录错误
			}

			// 这里可以添加小程序订阅消息推送逻辑（如果需要）
			// 目前暂时移除了公众号推送功能
		}

		return success(res, updatedMenu, 'Menu updated successfully');
	} catch (err) {
		console.error('Update menu error:', err);
		return error(res, 'Failed to update menu', 500);
	}
};

/**
 * 删除菜单（软删除）
 * @route DELETE /api/menus/:id
 */
const deleteMenu = async (req, res) => {
	try {
		const {
			id
		} = req.params;
		const currentUserId = req.user.id;

		// 检查菜单是否存在
		const existingMenu = await prisma.menu.findUnique({
			where: {
				id
			}
		});

		if (!existingMenu) {
			return error(res, 'Menu not found', 404);
		}

		// 检查权限：只能删除自己创建的菜单
		if (existingMenu.createdBy !== currentUserId) {
			return error(
				res,
				'Permission denied: You can only delete your own menus',
				403
			);
		}

		// 检查菜单是否已经被删除
		if (existingMenu.deleted) {
			return error(res, 'Menu already deleted', 400);
		}

		// 软删除菜单
		await prisma.menu.update({
			where: {
				id
			},
			data: {
				deleted: true,
				deletedAt: new Date()
			}
		});

		return success(res, null, 'Menu deleted successfully');
	} catch (err) {
		console.error('Delete menu error:', err);
		return error(res, 'Failed to delete menu', 500);
	}
};

/**
 * 获取菜品分类
 * @route GET /api/menus/categories
 */
const getCategories = async (req, res) => {
	try {
		const categories = await prisma.category.findMany({
			include: {
				dishes: {
					select: {
						id: true,
						name: true,
						description: true,
						image: true
					}
				}
			},
			orderBy: {
				createdAt: 'asc'
			}
		});

		return success(res, categories);
	} catch (err) {
		console.error('Get categories error:', err);
		return error(res, 'Failed to get categories', 500);
	}
};

/**
 * 获取推荐菜单（从推荐菜单表中获取）
 * @route GET /api/menus/recommended
 */
const getRecommendedMenu = async (req, res) => {
	try {
		const currentUserId = req.user.id;

		console.log(`📋 获取用户 ${currentUserId} 的推荐菜单...`);

		// 1. 从推荐菜单表中获取用户的推荐菜单
		const recommendedMenus = await prisma.recommendedMenu.findMany({
			where: {
				userId: currentUserId,
				isActive: true
			},
			include: {
				sourceMenu: {
					include: {
						items: {
							include: {
								dish: {
									select: {
										id: true,
										name: true,
										description: true,
										image: true
									}
								}
							}
						}
					}
				},
				sourceUser: {
					select: {
						id: true,
						name: true,
						avatar: true
					}
				}
			},
			orderBy: [{
					priority: 'desc'
				}, // 按优先级排序
				{
					createdAt: 'desc'
				} // 再按创建时间排序
			],
			take: 10 // 最多返回10个推荐菜单
		});

		console.log(`📊 找到 ${recommendedMenus.length} 个推荐菜单`);

		// 2. 格式化推荐菜单数据（过滤无效数据）
		const formattedMenus = recommendedMenus
			.filter(recommend => {
				// 过滤掉无效的推荐菜单
				if (!recommend.sourceMenu) {
					console.warn(`⚠️  推荐菜单 ${recommend.id} 的源菜单不存在`);
					return false;
				}
				if (!recommend.sourceMenu.id) {
					console.warn(`⚠️  推荐菜单 ${recommend.id} 的源菜单ID为空`);
					return false;
				}
				if (
					!recommend.sourceMenu.items ||
					recommend.sourceMenu.items.length === 0
				) {
					console.warn(`⚠️  推荐菜单 ${recommend.id} 的源菜单没有菜品`);
					return false;
				}
				return true;
			})
			.map(recommend => ({
				id: recommend.sourceMenu.id,
				createdBy: recommend.sourceUserId,
				creator: recommend.sourceUser,
				date: recommend.sourceMenu.date,
				isToday: recommend.sourceMenu.isToday,
				remark: recommend.sourceMenu.remark || recommend.reason,
				dishes: recommend.sourceMenu.items
					.filter(item => item.dish) // 过滤掉菜品不存在的项
					.map(item => ({
						id: item.dish.id,
						name: item.dish.name,
						description: item.dish.description,
						image: item.dish.image,
						count: item.count
					})),
				createdAt: recommend.sourceMenu.createdAt,
				recommendReason: recommend.reason,
				priority: recommend.priority,
				isRead: recommend.isRead
			}));

		// 3. 标记推荐菜单为已读（可选）
		if (recommendedMenus.length > 0) {
			try {
				await prisma.recommendedMenu.updateMany({
					where: {
						userId: currentUserId,
						isActive: true,
						isRead: false
					},
					data: {
						isRead: true
					}
				});
				console.log('✅ 推荐菜单已标记为已读');
			} catch (updateError) {
				console.error('⚠️  标记已读失败:', updateError);
				// 不影响主要功能
			}
		}

		console.log(`✅ 返回 ${formattedMenus.length} 个有效推荐菜单`);

		// 调试：打印第一个菜单的结构
		if (formattedMenus.length > 0) {
			console.log('📋 第一个推荐菜单结构:', {
				id: formattedMenus[0].id,
				creator: formattedMenus[0].creator?.name,
				dishCount: formattedMenus[0].dishes?.length
			});
		}

		return success(res, formattedMenus);
	} catch (err) {
		console.error('Get recommended menu error:', err);
		return error(res, 'Failed to get recommended menu', 500);
	}
};

/**
 * 获取菜品统计信息
 * @route GET /api/menus/statistics
 */
const getStatistics = async (req, res) => {
	try {
		// 获取今日订单数
		const today = new Date();
		today.setHours(0, 0, 0, 0);
		const tomorrow = new Date(today);
		tomorrow.setDate(tomorrow.getDate() + 1);

		// 今日订单数
		const todayOrders = await prisma.order.count({
			where: {
				createdAt: {
					gte: today,
					lt: tomorrow
				}
			}
		});

		// 菜品总数
		const totalDishes = await prisma.dish.count();

		// 活跃用户（本月下单用户数）
		const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
		const activeUsers = await prisma.order.findMany({
			where: {
				createdAt: {
					gte: monthStart,
					lt: tomorrow
				}
			},
			select: {
				userId: true
			}
		});
		const activeUserCount = new Set(activeUsers.map(u => u.userId)).size;

		// 月访问量（模拟）
		const monthlyVisits = Math.floor(Math.random() * 500) + 200;

		return success(res, {
			todayOrders,
			totalDishes,
			activeUsers: activeUserCount,
			monthlyVisits
		});
	} catch (err) {
		console.error('Get statistics error:', err);
		return error(res, 'Failed to get statistics', 500);
	}
};

/**
 * 生成推荐菜单给关联用户
 * @param {string} menuId - 新创建的菜单ID
 * @param {string} creatorId - 菜单创建者ID
 */
async function generateRecommendedMenus(menuId, creatorId) {
	try {
		console.log(`🤖 开始为菜单 ${menuId} 生成推荐菜单...`);

		// 1. 获取创建者的关联用户
		const connections = await prisma.userConnection.findMany({
			where: {
				OR: [{
						senderId: creatorId,
						status: 'accepted'
					},
					{
						receiverId: creatorId,
						status: 'accepted'
					}
				]
			},
			include: {
				sender: {
					select: {
						id: true,
						name: true
					}
				},
				receiver: {
					select: {
						id: true,
						name: true
					}
				}
			}
		});

		if (connections.length === 0) {
			console.log('⚠️  没有关联用户，跳过推荐菜单生成');
			return;
		}

		// 2. 提取关联用户ID
		const connectedUserIds = connections.map(conn =>
			conn.senderId === creatorId ? conn.receiverId : conn.senderId
		);

		console.log(`👥 找到 ${connectedUserIds.length} 个关联用户`);

		// 3. 获取菜单信息用于生成推荐理由
		const menu = await prisma.menu.findUnique({
			where: {
				id: menuId
			},
			include: {
				creator: {
					select: {
						name: true
					}
				},
				items: {
					include: {
						dish: {
							select: {
								name: true,
								category: {
									select: {
										name: true
									}
								}
							}
						}
					}
				}
			}
		});

		if (!menu) {
			console.log('❌ 菜单不存在，无法生成推荐');
			return;
		}

		// 4. 生成推荐理由
		const dishNames = menu.items.map(item => item.dish.name).slice(0, 3); // 取前3个菜品
		const reason = `${menu.creator.name}推荐了${dishNames.join('、')}${
      dishNames.length > 3 ? '等菜品' : ''
    }`;

		// 5. 计算优先级（基于菜单的特征）
		let priority = 10; // 基础优先级

		// 今日菜单优先级更高
		if (menu.isToday) {
			priority += 20;
		}

		// 菜品数量多的菜单优先级更高
		priority += Math.min(menu.items.length * 2, 10);

		// 6. 为每个关联用户创建推荐菜单记录
		const recommendPromises = connectedUserIds.map(async userId => {
			try {
				// 检查是否已经存在相同的推荐
				const existingRecommend = await prisma.recommendedMenu.findFirst({
					where: {
						userId: userId,
						sourceMenuId: menuId,
						sourceUserId: creatorId
					}
				});

				if (existingRecommend) {
					console.log(`⏭️  用户 ${userId} 已有此菜单的推荐，跳过`);
					return null;
				}

				// 创建推荐菜单记录
				const recommendedMenu = await prisma.recommendedMenu.create({
					data: {
						userId: userId,
						sourceMenuId: menuId,
						sourceUserId: creatorId,
						priority: priority,
						reason: reason,
						isRead: false,
						isActive: true
					}
				});

				console.log(`✅ 为用户 ${userId} 创建推荐菜单: ${recommendedMenu.id}`);
				return recommendedMenu;
			} catch (error) {
				console.error(`❌ 为用户 ${userId} 创建推荐菜单失败:`, error);
				return null;
			}
		});

		// 7. 等待所有推荐菜单创建完成
		const results = await Promise.all(recommendPromises);
		const successCount = results.filter(r => r !== null).length;

		console.log(
			`🎉 推荐菜单生成完成！成功: ${successCount}/${connectedUserIds.length}`
		);
	} catch (error) {
		console.error('❌ 生成推荐菜单失败:', error);
		throw error;
	}
}

/**
 * 获取首页菜单数据（自己和关联用户的今日菜单，或热门菜品）
 * @route GET /api/menus/home
 */
const getHomeMenus = async (req, res) => {
	try {
		const userId = req.user.id;
		const today = new Date();
		today.setHours(0, 0, 0, 0);

		console.log(`📱 获取用户 ${userId} 的首页菜单数据`);

		// 1. 获取当前用户的关联用户ID列表
		const connections = await prisma.userConnection.findMany({
			where: {
				OR: [{
						senderId: userId,
						status: 'accepted'
					},
					{
						receiverId: userId,
						status: 'accepted'
					}
				]
			},
			select: {
				senderId: true,
				receiverId: true
			}
		});

		// 提取关联用户ID（包括自己）
		const connectedUserIds = new Set([userId]);
		connections.forEach(conn => {
			connectedUserIds.add(conn.senderId);
			connectedUserIds.add(conn.receiverId);
		});

		console.log(`🔗 关联用户ID列表:`, Array.from(connectedUserIds));

		// 2. 获取今日菜单（自己和关联用户创建的）
		const todayMenus = await prisma.menu.findMany({
			where: {
				createdBy: {
					in: Array.from(connectedUserIds)
				},
				isToday: true,
				deleted: false,
				date: {
					gte: today
				}
			},
			include: {
				creator: {
					select: {
						id: true,
						name: true
					}
				},
				items: {
					include: {
						dish: {
							select: {
								id: true,
								name: true,
								image: true
							}
						}
					}
				}
			},
			orderBy: {
				createdAt: 'desc'
			}
		});

		console.log(`📋 找到 ${todayMenus.length} 个今日菜单`);

		if (todayMenus.length > 0) {
			// 有今日菜单，返回菜单数据
			const menuData = todayMenus.map(menu => ({
				id: menu.id,
				creatorName: menu.creator.name,
				date: menu.date,
				dishes: menu.items.map(item => ({
					id: item.dish.id,
					name: item.dish.name,
					count: item.count,
					image: item.dish.image
				}))
			}));

			return success(
				res, {
					type: 'today_menu',
					menus: menuData,
					message: '今日菜单'
				},
				'获取今日菜单成功'
			);
		}

		// 3. 没有今日菜单，获取热门菜品（按订单数量排序）
		console.log('📊 没有今日菜单，获取热门菜品...');

		const hotDishes = await prisma.dish.findMany({
			where: {
				isPublished: true
			},
			select: {
				id: true,
				name: true,
				image: true,
				_count: {
					select: {
						menuItems: true
					}
				}
			},
			orderBy: {
				menuItems: {
					_count: 'desc'
				}
			},
			take: 3
		});

		console.log(`🔥 找到 ${hotDishes.length} 个热门菜品`);

		const hotDishesData = hotDishes.map(dish => ({
			id: dish.id,
			name: dish.name,
			count: dish._count.menuItems,
			image: dish.image
		}));

		return success(
			res, {
				type: 'hot_dishes',
				dishes: hotDishesData,
				message: '热门菜品推荐'
			},
			'获取热门菜品成功'
		);
	} catch (err) {
		console.error('获取首页菜单失败:', err);
		return error(res, '获取首页菜单失败', 500);
	}
};

module.exports = {
	getMenus,
	getTodayMenu,
	getTodayMenus,
	getHistoryMenus,
	getMenuById,
	createMenu,
	updateMenu,
	deleteMenu,
	getCategories,
	getRecommendedMenu,
	getStatistics,
	getHomeMenus
};