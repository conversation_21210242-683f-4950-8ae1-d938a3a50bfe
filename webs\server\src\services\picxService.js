const axios = require('axios');

/**
 * PicX 图床服务
 * 用于上传图片到 GitHub 仓库
 */
class PicxService {
  constructor() {
    this.token = process.env.PICX_TOKEN || process.env.GITHUB_TOKEN;
    this.repo = process.env.PICX_REPO || '2497462726/picx-images-hosting';
    this.baseUrl = 'https://api.github.com';
  }

  /**
   * 上传图片到 GitHub 仓库
   * @param {Buffer} imageBuffer - 图片 buffer
   * @param {string|object} filenameOrConfig - 文件名或配置对象
   * @returns {Promise<object>} 包含图片 URL 和删除信息的对象
   */
  async uploadImage(imageBuffer, filenameOrConfig) {
    try {
      let config;

      // 兼容旧的字符串参数和新的配置对象
      if (typeof filenameOrConfig === 'string') {
        // 旧格式：直接传文件名
        const uniqueFilename = `${Date.now()}-${filenameOrConfig}`;
        config = {
          folder: 'images',
          filename: uniqueFilename,
          originalname: filenameOrConfig
        };
      } else {
        // 新格式：配置对象
        config = filenameOrConfig;
      }

      const path = `${config.folder}/${config.filename}`;

      // 将图片转换为 Base64
      const content = imageBuffer.toString('base64');

      // 检查文件是否已存在，获取SHA值
      let existingSha = null;
      try {
        const existingFile = await axios.get(
          `${this.baseUrl}/repos/${this.repo}/contents/${path}`,
          {
            headers: {
              Authorization: `token ${this.token}`,
              Accept: 'application/vnd.github.v3+json'
            }
          }
        );
        existingSha = existingFile.data.sha;
        console.log(`文件已存在，将更新: ${path}`);
      } catch (error) {
        if (error.response && error.response.status === 404) {
          console.log(`文件不存在，将创建: ${path}`);
        } else {
          console.warn(`检查文件存在性时出错: ${error.message}`);
        }
      }

      // 构建请求数据
      const requestData = {
        message: `Upload ${config.folder}: ${config.filename}`,
        content,
        branch: 'master'
      };

      // 如果文件已存在，添加SHA值
      if (existingSha) {
        requestData.sha = existingSha;
      }

      // 上传到 GitHub
      const response = await axios.put(
        `${this.baseUrl}/repos/${this.repo}/contents/${path}`,
        requestData,
        {
          headers: {
            Authorization: `token ${this.token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      // 返回图片信息
      return {
        url: `https://cdn.jsdelivr.net/gh/${this.repo}/${path}`,
        path: path,
        sha: response.data.content.sha,
        filename: config.filename,
        folder: config.folder,
        type:
          config.folder === 'avatars'
            ? 'avatar'
            : config.folder === 'menus'
            ? 'menu'
            : 'general'
      };
    } catch (error) {
      console.error('Error uploading image to PicX:', error);
      throw new Error('Failed to upload image');
    }
  }

  /**
   * 删除 GitHub 仓库中的图片
   * @param {string} path - 图片路径
   * @param {string} sha - 文件的 SHA 值
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteImage(path, sha) {
    try {
      // 从 GitHub 删除文件
      const response = await axios.delete(
        `${this.baseUrl}/repos/${this.repo}/contents/${path}`,
        {
          data: {
            message: `Delete image: ${path}`,
            sha: sha,
            branch: 'master'
          },
          headers: {
            Authorization: `token ${this.token}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log(`图片删除成功: ${path}`);
      return true;
    } catch (error) {
      console.error('Error deleting image from PicX:', error);
      return false;
    }
  }

  /**
   * 从 URL 中提取图片路径
   * @param {string} url - 图片 URL
   * @returns {string|null} 图片路径
   */
  extractPathFromUrl(url) {
    try {
      if (!url || typeof url !== 'string') {
        return null;
      }

      // 🔥 修复：匹配 jsDelivr CDN URL 格式
      // https://cdn.jsdelivr.net/gh/user/repo@branch/path
      const cdnMatch = url.match(
        /https:\/\/cdn\.jsdelivr\.net\/gh\/[^\/]+\/[^\/]+@[^\/]+\/(.+)/
      );
      if (cdnMatch) {
        return cdnMatch[1];
      }

      // 🔥 修复：匹配 raw.githubusercontent.com URL 格式
      // https://raw.githubusercontent.com/user/repo/branch/path
      const rawMatch = url.match(
        /https:\/\/raw\.githubusercontent\.com\/[^\/]+\/[^\/]+\/[^\/]+\/(.+)/
      );
      if (rawMatch) {
        return rawMatch[1];
      }

      // 🔥 修复：匹配 GitHub blob URL 格式
      // https://github.com/user/repo/blob/branch/path
      const blobMatch = url.match(
        /https:\/\/github\.com\/[^\/]+\/[^\/]+\/blob\/[^\/]+\/(.+)/
      );
      if (blobMatch) {
        return blobMatch[1];
      }

      // 🔥 修复：匹配所有支持的目录格式
      const pathMatch = url.match(
        /(menus|images|avatars)\/[^\/]+\.(jpg|jpeg|png|gif|webp)$/i
      );
      if (pathMatch) {
        return pathMatch[0];
      }

      return null;
    } catch (error) {
      console.error('Error extracting path from URL:', error);
      return null;
    }
  }
}

module.exports = new PicxService();
