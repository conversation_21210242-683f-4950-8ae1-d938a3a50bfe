const fs = require('fs');
const path = require('path');
const picxService = require('./picxService');

/**
 * 上传服务 - 封装图片上传功能
 */
class UploadService {
  /**
   * 上传菜单图片
   * @param {string} filePath - 本地文件路径
   * @param {string} menuId - 菜单ID (如 caidan-uuid)
   * @returns {Promise<object>} 上传结果
   */
  async uploadMenuImage(filePath, menuId) {
    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        throw new Error('File not found: ' + filePath);
      }

      // 读取文件
      const imageBuffer = fs.readFileSync(filePath);

      // 获取文件扩展名
      const ext = path.extname(filePath).toLowerCase() || '.jpg';

      // 生成文件名：tupian-{menuId}.jpg
      const filename = `tupian-${menuId}${ext}`;

      // 配置上传参数
      const uploadConfig = {
        folder: 'images',
        filename: filename,
        originalname: path.basename(filePath)
      };

      console.log('开始上传菜单图片:', {
        filePath,
        menuId,
        filename,
        size: imageBuffer.length
      });

      // 调用picxService上传
      const result = await picxService.uploadImage(imageBuffer, uploadConfig);

      console.log('菜单图片上传成功:', result.url);

      return {
        url: result.url,
        path: result.path,
        sha: result.sha,
        filename: result.filename,
        menuId: menuId
      };
    } catch (error) {
      console.error('上传菜单图片失败:', error);
      throw new Error(`Failed to upload menu image: ${error.message}`);
    }
  }

  /**
   * 检查删除配置
   * @returns {boolean} 配置是否完整
   */
  checkDeleteConfig() {
    const token = process.env.PICX_TOKEN || process.env.GITHUB_TOKEN;
    const repo = process.env.PICX_REPO || '2497462726/picx-images-hosting';

    if (!token) {
      console.warn('⚠️ GitHub Token未配置，无法删除云端图片');
      return false;
    }

    if (!repo) {
      console.warn('⚠️ GitHub仓库未配置，无法删除云端图片');
      return false;
    }

    return true;
  }

  /**
   * 删除图片
   * @param {string} imageUrl - 图片URL
   * @returns {Promise<boolean>} 删除是否成功
   */
  async deleteImage(imageUrl) {
    try {
      if (!imageUrl || typeof imageUrl !== 'string') {
        console.warn('Invalid image URL for deletion:', imageUrl);
        return false;
      }

      // 检查配置
      if (!this.checkDeleteConfig()) {
        console.warn('云端图片删除配置不完整，跳过删除');
        return false;
      }

      // 从URL提取路径
      const imagePath = picxService.extractPathFromUrl(imageUrl);
      if (!imagePath) {
        console.warn('Cannot extract path from URL:', imageUrl);
        return false;
      }

      console.log('开始删除图片:', {
        url: imageUrl,
        path: imagePath
      });

      // 获取文件信息以获取SHA
      const axios = require('axios');
      const token = process.env.PICX_TOKEN || process.env.GITHUB_TOKEN;
      const repo = process.env.PICX_REPO || '2497462726/picx-images-hosting';

      try {
        const fileInfo = await axios.get(
          `https://api.github.com/repos/${repo}/contents/${imagePath}`,
          {
            headers: {
              Authorization: `token ${token}`,
              Accept: 'application/vnd.github.v3+json'
            }
          }
        );

        const sha = fileInfo.data.sha;

        // 调用picxService删除
        const result = await picxService.deleteImage(imagePath, sha);

        if (result) {
          console.log('图片删除成功:', imageUrl);
        } else {
          console.warn('图片删除失败:', imageUrl);
        }

        return result;
      } catch (getFileError) {
        if (getFileError.response && getFileError.response.status === 404) {
          console.log('图片文件不存在，可能已被删除:', imageUrl);
          return true; // 文件不存在也算删除成功
        }
        throw getFileError;
      }
    } catch (error) {
      console.error('删除图片失败:', error);
      return false;
    }
  }

  /**
   * 批量删除图片
   * @param {string[]} imageUrls - 图片URL数组
   * @returns {Promise<object>} 删除结果统计
   */
  async deleteImages(imageUrls) {
    const results = {
      total: imageUrls.length,
      success: 0,
      failed: 0,
      errors: []
    };

    for (const url of imageUrls) {
      try {
        const success = await this.deleteImage(url);
        if (success) {
          results.success++;
        } else {
          results.failed++;
          results.errors.push(`Failed to delete: ${url}`);
        }
      } catch (error) {
        results.failed++;
        results.errors.push(`Error deleting ${url}: ${error.message}`);
      }
    }

    console.log('批量删除图片完成:', results);
    return results;
  }

  /**
   * 验证图片文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为有效图片
   */
  validateImageFile(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        return false;
      }

      const ext = path.extname(filePath).toLowerCase();
      const validExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

      if (!validExtensions.includes(ext)) {
        return false;
      }

      const stats = fs.statSync(filePath);
      const maxSize = 10 * 1024 * 1024; // 10MB

      if (stats.size > maxSize) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('验证图片文件失败:', error);
      return false;
    }
  }

  /**
   * 清理临时文件
   * @param {string} filePath - 临时文件路径
   */
  cleanupTempFile(filePath) {
    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log('临时文件清理成功:', filePath);
      }
    } catch (error) {
      console.error('清理临时文件失败:', error);
    }
  }

  /**
   * 创建临时目录
   * @param {string} tempDir - 临时目录路径
   */
  ensureTempDir(tempDir) {
    try {
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, {recursive: true});
        console.log('临时目录创建成功:', tempDir);
      }
    } catch (error) {
      console.error('创建临时目录失败:', error);
      throw error;
    }
  }
}

module.exports = new UploadService();
