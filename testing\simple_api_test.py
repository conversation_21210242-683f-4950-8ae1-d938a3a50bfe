#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的API测试
验证后台功能是否正常
"""

import requests
import json
from datetime import datetime

def test_api():
    """简单API测试"""
    api_base = "http://8.148.231.104:3000/api"
    
    print("🚀 开始API测试")
    print("=" * 50)
    
    # 测试API连通性
    try:
        print("🧪 测试API连通性...")
        response = requests.get(f"{api_base}/", timeout=10)
        print(f"✅ API连通性: {response.status_code}")
    except Exception as e:
        print(f"❌ API连通性失败: {e}")
        return
    
    # 测试用户注册
    try:
        print("🧪 测试用户注册...")
        user_data = {
            "name": "测试用户",
            "phone": "13800000099", 
            "password": "test123456"
        }
        
        response = requests.post(
            f"{api_base}/auth/register",
            json=user_data,
            timeout=10
        )
        
        if response.status_code == 201:
            result = response.json()
            user_id = result["data"]["user"]["id"]
            token = result["data"]["token"]
            print(f"✅ 用户注册成功: ID={user_id}")
        elif response.status_code == 409:
            print("✅ 用户已存在，尝试登录...")
            
            # 尝试登录
            login_response = requests.post(
                f"{api_base}/auth/login",
                json={
                    "username": user_data["phone"],
                    "password": user_data["password"]
                },
                timeout=10
            )
            
            if login_response.status_code == 200:
                result = login_response.json()
                user_id = result["data"]["user"]["id"]
                token = result["data"]["token"]
                print(f"✅ 用户登录成功: ID={user_id}")
            else:
                print(f"❌ 用户登录失败: {login_response.status_code}")
                return
        else:
            print(f"❌ 用户注册失败: {response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 用户注册异常: {e}")
        return
    
    # 测试获取菜品列表
    try:
        print("🧪 测试获取菜品列表...")
        response = requests.get(
            f"{api_base}/dishes",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            dishes_count = len(result.get("data", []))
            print(f"✅ 获取菜品列表成功: {dishes_count}个菜品")
        else:
            print(f"❌ 获取菜品列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取菜品列表异常: {e}")
    
    # 测试获取今日订单
    try:
        print("🧪 测试获取今日订单...")
        response = requests.get(f"{api_base}/orders/today", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            orders_count = len(result.get("data", []))
            print(f"✅ 获取今日订单成功: {orders_count}个订单")
        else:
            print(f"❌ 获取今日订单失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取今日订单异常: {e}")
    
    # 测试获取可关联用户
    try:
        print("🧪 测试获取可关联用户...")
        response = requests.get(
            f"{api_base}/connections/users",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            users_count = len(result.get("data", []))
            print(f"✅ 获取可关联用户成功: {users_count}个用户")
        else:
            print(f"❌ 获取可关联用户失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取可关联用户异常: {e}")
    
    print("=" * 50)
    print("🎉 API测试完成")

if __name__ == "__main__":
    test_api()
