/**
 * 路由工具函数
 */

/**
 * 根据路径查找路由
 * @param {string} path 路径
 * @param {Array} routes 路由数组
 * @returns {Object|null} 找到的路由
 */
export function findRouteByPath(path, routes) {
  if (!path || !routes) return null;
  
  for (const route of routes) {
    if (route.path === path) {
      return route;
    }
    
    if (route.children && route.children.length > 0) {
      const found = findRouteByPath(path, route.children);
      if (found) return found;
    }
  }
  
  return null;
}

/**
 * 获取父级路径数组
 * @param {string} path 当前路径
 * @param {Array} routes 路由数组
 * @returns {Array} 父级路径数组
 */
export function getParentPaths(path, routes) {
  const result = [];
  
  function findParent(currentPath, routeList, parents = []) {
    for (const route of routeList) {
      const newParents = [...parents, route.path];
      
      if (route.path === currentPath) {
        result.push(...parents);
        return true;
      }
      
      if (route.children && route.children.length > 0) {
        if (findParent(currentPath, route.children, newParents)) {
          return true;
        }
      }
    }
    return false;
  }
  
  findParent(path, routes);
  return result;
}

/**
 * 获取顶级菜单
 * @param {Array} routes 路由数组
 * @returns {Array} 顶级菜单数组
 */
export function getTopMenu(routes) {
  return routes.filter(route => {
    return route.meta && route.meta.title && !route.meta.hidden;
  });
}

/**
 * 扁平化路由
 * @param {Array} routes 路由数组
 * @returns {Array} 扁平化后的路由数组
 */
export function flattenRoutes(routes) {
  const result = [];
  
  function flatten(routeList) {
    routeList.forEach(route => {
      result.push(route);
      if (route.children && route.children.length > 0) {
        flatten(route.children);
      }
    });
  }
  
  flatten(routes);
  return result;
}

/**
 * 过滤隐藏的路由
 * @param {Array} routes 路由数组
 * @returns {Array} 过滤后的路由数组
 */
export function filterHiddenRoutes(routes) {
  return routes.filter(route => {
    if (route.meta && route.meta.hidden) {
      return false;
    }
    
    if (route.children && route.children.length > 0) {
      route.children = filterHiddenRoutes(route.children);
    }
    
    return true;
  });
}

/**
 * 生成面包屑
 * @param {string} path 当前路径
 * @param {Array} routes 路由数组
 * @returns {Array} 面包屑数组
 */
export function generateBreadcrumb(path, routes) {
  const breadcrumb = [];
  const parentPaths = getParentPaths(path, routes);
  
  parentPaths.forEach(parentPath => {
    const route = findRouteByPath(parentPath, routes);
    if (route && route.meta && route.meta.title) {
      breadcrumb.push({
        path: route.path,
        title: route.meta.title
      });
    }
  });
  
  // 添加当前路由
  const currentRoute = findRouteByPath(path, routes);
  if (currentRoute && currentRoute.meta && currentRoute.meta.title) {
    breadcrumb.push({
      path: currentRoute.path,
      title: currentRoute.meta.title
    });
  }
  
  return breadcrumb;
}
