<view class="category-selector-popup {{show ? 'show' : ''}}" bindtap="onMaskTap">
  <view class="popup-content" catchtap="stopPropagation">
    <!-- 弹窗标题 -->
    <view class="popup-header">
      <text class="popup-title">选择菜品分类</text>
      <van-icon name="cross" class="close-icon" bindtap="onClose" />
    </view>
    
    <!-- 分类网格 -->
    <view class="category-grid">
      <view
        wx:for="{{categories}}"
        wx:key="value"
        class="category-item {{selectedCategory === item.value ? 'selected' : ''}}"
        bindtap="onCategorySelect"
        data-category="{{item.value}}"
        data-label="{{item.label}}"
      >
        <view class="category-icon">{{item.icon}}</view>
        <text class="category-label">{{item.label}}</text>
        <view wx:if="{{selectedCategory === item.value}}" class="selected-indicator">
          <van-icon name="success" size="24rpx" color="#fff" />
        </view>
      </view>
    </view>
    
    <!-- 确认按钮 -->
    <view class="popup-footer">
      <button class="confirm-btn" bindtap="onConfirm">
        确认选择
      </button>
    </view>
  </view>
</view>
