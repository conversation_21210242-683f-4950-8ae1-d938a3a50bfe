import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import './styles/index.scss'
import './style/tailwind.css'

// 导入自定义UI组件插件
import UIComponents from './plugins/ui-components.js'

// 导入环境配置
import { getEnvConfig } from './config/env'

// 获取环境配置并打印启动信息
const envConfig = getEnvConfig()
console.log(`🚀 ${envConfig.app.name} v${envConfig.app.version}`)
console.log(`📡 当前环境: ${envConfig.server.name}`)
console.log(`🔗 API地址: ${envConfig.server.baseURL}`)
console.log(`🛠️ 调试模式: ${envConfig.features.debug ? '开启' : '关闭'}`)

// 创建应用实例
const app = createApp(App)

// 使用插件
app.use(createPinia())
app.use(router)
app.use(ElementPlus)
app.use(UIComponents)

// 挂载应用
app.mount('#app')
