import{a0 as s}from"./index-XtNpSMFt.js";const i={getDishes:e=>s.get("/dishes",{params:e}),getDishDetail:e=>s.get(`/dishes/${e}`),createDish:e=>s.post("/dishes",e),updateDish:(e,t)=>s.put(`/dishes/${e}`,t),deleteDish:e=>s.delete(`/dishes/${e}`),getCategories:()=>s.get("/dishes/categories"),createCategory:e=>s.post("/dishes/categories",e),updateCategory:(e,t)=>s.put(`/dishes/categories/${e}`,t),deleteCategory:e=>s.delete(`/dishes/categories/${e}`),getHotDishes:e=>s.get("/dishes/hot",{params:e}),getDishStatistics:()=>s.get("/dishes/statistics"),getDishAnalytics:e=>s.get("/dishes/analytics",{params:e}),exportDishes:e=>s.get("/dishes/export",{params:e}),batchOperation:e=>s.post("/dishes/batch",e),getDishesByCategory:()=>s.get("/dishes/by-category")},g={getMenus:e=>s.get("/menus",e),getTodayMenu:e=>s.get("/menus/today",{params:{date:e}}),getHistoryMenus:e=>s.get("/menus/history",e),createMenu:e=>s.post("/menus",e),updateMenu:(e,t)=>s.put(`/menus/${e}`,t),deleteMenu:e=>s.delete(`/menus/${e}`),removeDishFromMenu:(e,t)=>s.delete(`/menus/today/dishes/${t}`,{params:{date:e}}),clearTodayMenu:e=>s.delete("/menus/today",{params:{date:e}}),getStatistics:()=>s.get("/menus/statistics")};i.getCategories=e=>s.get("/dishes/categories",e);i.createCategory=e=>s.post("/dishes/categories",e);i.updateCategory=(e,t)=>s.put(`/dishes/categories/${e}`,t);i.deleteCategory=e=>s.delete(`/dishes/categories/${e}`);export{i as d,g as m};
