<template>
  <PageContainer
    title="菜品管理"
    subtitle="管理系统中的所有菜品信息"
    :breadcrumb="breadcrumbItems"
  >
    <template #header-actions>
      <BaseButton
        variant="primary"
        prefix-icon="PlusIcon"
        @click="handleCreate"
      >
        新增菜品
      </BaseButton>
    </template>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="菜品总数"
        :value="stats.totalDishes"
        :icon="CakeIcon"
        color="blue"
        :change="stats.dishGrowth"
        description="较上月"
      />
      <StatsCard
        title="已上架"
        :value="stats.publishedDishes"
        :icon="CheckCircleIcon"
        color="green"
        :change="stats.publishedGrowth"
        description="已上架菜品"
      />
      <StatsCard
        title="热门菜品"
        :value="stats.popularDishes"
        :icon="FireIcon"
        color="yellow"
        description="本月热门"
      />
      <StatsCard
        title="分类数量"
        :value="stats.totalCategories"
        :icon="TagIcon"
        color="purple"
        description="菜品分类"
      />
    </div>

    <!-- 搜索表单 -->
    <SearchForm
      v-model="searchParams"
      :fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 菜品表格 -->
    <BaseTable
      :data="dishes"
      :columns="tableColumns"
      :selectable="true"
      v-model:selected-rows="selectedDishes"
      :loading="loading"
      @row-click="handleRowClick"
      @sort-change="handleSort"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            菜品列表 ({{ pagination.total }})
          </h3>
          <div class="flex items-center space-x-3">
            <BaseButton
              v-if="selectedDishes.length"
              variant="error"
              size="sm"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedDishes.length }})
            </BaseButton>
            <BaseButton
              v-if="selectedDishes.length"
              variant="outline"
              size="sm"
              @click="handleBatchPublish"
            >
              批量上架
            </BaseButton>
            <BaseButton
              variant="outline"
              size="sm"
              @click="handleExport"
            >
              导出数据
            </BaseButton>
          </div>
        </div>
      </template>

      <!-- 菜品图片 -->
      <template #cell-image="{ row }">
        <img
          :src="row.image"
          :alt="row.name"
          class="h-12 w-12 rounded-lg object-cover"
        />
      </template>

      <!-- 菜品分类 -->
      <template #cell-category="{ row }">
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
          {{ row.category?.name }}
        </span>
      </template>

      <!-- 发布状态 -->
      <template #cell-isPublished="{ row }">
        <span
          :class="getPublishStatusClass(row.isPublished)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ row.isPublished ? '已上架' : '未上架' }}
        </span>
      </template>

      <!-- 创建者 -->
      <template #cell-creator="{ row }">
        <div class="flex items-center">
          <img
            v-if="row.creator?.avatar"
            :src="row.creator.avatar"
            :alt="row.creator.name"
            class="h-6 w-6 rounded-full object-cover mr-2"
          />
          <span class="text-sm text-gray-900 dark:text-gray-100">
            {{ row.creator?.name }}
          </span>
        </div>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center space-x-2">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleView(row)"
          >
            查看
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleEdit(row)"
          >
            编辑
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleTogglePublish(row)"
            :class="row.isPublished ? 'text-yellow-600' : 'text-green-600'"
          >
            {{ row.isPublished ? '下架' : '上架' }}
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleCopy(row)"
            class="text-blue-600"
          >
            复制
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleDelete(row)"
            class="text-red-600 hover:text-red-700"
          >
            删除
          </BaseButton>
        </div>
      </template>

      <!-- 分页 -->
      <template #footer>
        <Pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </template>
    </BaseTable>

    <!-- 菜品编辑模态框 -->
    <DishEditModal
      v-model="showEditModal"
      :dish="currentDish"
      :categories="categories"
      @success="handleEditSuccess"
    />

    <!-- 菜品详情模态框 -->
    <DishDetailModal
      v-model="showDetailModal"
      :dish="currentDish"
    />
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  CakeIcon,
  CheckCircleIcon,
  FireIcon,
  TagIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import DishEditModal from './components/DishEditModal.vue'
import DishDetailModal from './components/DishDetailModal.vue'
import { dishApi } from '@/api/dish'
import { formatDate } from '@/utils/date'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const dishes = ref([])
const categories = ref([])
const selectedDishes = ref([])
const showEditModal = ref(false)
const showDetailModal = ref(false)
const currentDish = ref(null)

// 搜索参数
const searchParams = reactive({
  keyword: '',
  categoryId: '',
  isPublished: '',
  createdBy: '',
  dateRange: []
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 统计数据
const stats = reactive({
  totalDishes: 0,
  publishedDishes: 0,
  popularDishes: 0,
  totalCategories: 0,
  dishGrowth: 0,
  publishedGrowth: 0
})

// 面包屑导航
const breadcrumbItems = [
  { text: '首页', to: '/' },
  { text: '菜单管理', to: '/menu' },
  { text: '菜品管理' }
]

// 搜索字段配置
const searchFields = [
  {
    key: 'keyword',
    type: 'input',
    label: '关键词',
    placeholder: '搜索菜品名称、描述',
    prefixIcon: 'MagnifyingGlassIcon'
  },
  {
    key: 'categoryId',
    type: 'select',
    label: '菜品分类',
    placeholder: '请选择分类',
    options: computed(() => [
      { label: '全部', value: '' },
      ...categories.value.map(cat => ({ label: cat.name, value: cat.id }))
    ])
  },
  {
    key: 'isPublished',
    type: 'select',
    label: '发布状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '已上架', value: 'true' },
      { label: '未上架', value: 'false' }
    ]
  },
  {
    key: 'dateRange',
    type: 'daterange',
    label: '创建时间',
    startPlaceholder: '开始日期',
    endPlaceholder: '结束日期'
  }
]

// 表格列配置
const tableColumns = [
  {
    key: 'image',
    title: '图片',
    width: 80
  },
  {
    key: 'name',
    title: '菜品名称',
    sortable: true
  },
  {
    key: 'category',
    title: '分类'
  },
  {
    key: 'isPublished',
    title: '状态'
  },
  {
    key: 'creator',
    title: '创建者'
  },
  {
    key: 'createdAt',
    title: '创建时间',
    sortable: true,
    render: (value) => formatDate(value)
  }
]

// 计算属性
const getPublishStatusClass = (isPublished) => {
  return isPublished
    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

// 方法
const fetchDishes = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams
    }
    
    const response = await dishApi.getDishes(params)
    dishes.value = response.data.dishes
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取菜品列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchCategories = async () => {
  try {
    const response = await dishApi.getCategories()
    categories.value = response.data
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

const fetchStats = async () => {
  try {
    const response = await dishApi.getStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  fetchDishes()
}

const handleReset = () => {
  pagination.current = 1
  fetchDishes()
}

const handlePageChange = () => {
  fetchDishes()
}

const handleSort = (sortInfo) => {
  console.log('排序:', sortInfo)
  fetchDishes()
}

const handleCreate = () => {
  currentDish.value = null
  showEditModal.value = true
}

const handleEdit = (dish) => {
  currentDish.value = dish
  showEditModal.value = true
}

const handleView = (dish) => {
  currentDish.value = dish
  showDetailModal.value = true
}

const handleDelete = async (dish) => {
  if (confirm(`确定要删除菜品 ${dish.name} 吗？`)) {
    try {
      await dishApi.deleteDish(dish.id)
      fetchDishes()
      fetchStats()
    } catch (error) {
      console.error('删除菜品失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (confirm(`确定要删除选中的 ${selectedDishes.value.length} 个菜品吗？`)) {
    try {
      const dishIds = selectedDishes.value.map(dish => dish.id)
      await dishApi.batchDeleteDishes(dishIds)
      selectedDishes.value = []
      fetchDishes()
      fetchStats()
    } catch (error) {
      console.error('批量删除菜品失败:', error)
    }
  }
}

const handleTogglePublish = async (dish) => {
  try {
    await dishApi.togglePublish(dish.id, !dish.isPublished)
    fetchDishes()
    fetchStats()
  } catch (error) {
    console.error('切换发布状态失败:', error)
  }
}

const handleBatchPublish = async () => {
  try {
    const dishIds = selectedDishes.value.map(dish => dish.id)
    await dishApi.batchPublish(dishIds, true)
    selectedDishes.value = []
    fetchDishes()
    fetchStats()
  } catch (error) {
    console.error('批量上架失败:', error)
  }
}

const handleCopy = async (dish) => {
  try {
    await dishApi.copyDish(dish.id)
    fetchDishes()
    fetchStats()
  } catch (error) {
    console.error('复制菜品失败:', error)
  }
}

const handleExport = () => {
  console.log('导出数据')
}

const handleRowClick = (dish) => {
  console.log('点击菜品:', dish)
}

const handleEditSuccess = () => {
  showEditModal.value = false
  fetchDishes()
  fetchStats()
}

// 生命周期
onMounted(() => {
  fetchDishes()
  fetchCategories()
  fetchStats()
})
</script>
