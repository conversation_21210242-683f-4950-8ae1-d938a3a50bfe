# 测试环境配置 - 端口3000
# 数据库连接 - MySQL测试数据库
DATABASE_URL="mysql://nannan_user:5201314hl@*************:3306/nannan_db_test"

# JWT 配置
JWT_SECRET="test-jwt-secret-key-for-development"
JWT_EXPIRES_IN="7d"

# 服务器配置
PORT=3000
HOST=0.0.0.0
NODE_ENV="test"

# PicX 配置 - GitHub图床服务
PICX_TOKEN="****************************************"
PICX_REPO="2497462726/picx-images-hosting"
GITHUB_API_BASE="https://api.github.com"

# 微信小程序配置
WECHAT_APPID="wx82283b353918af82"
WECHAT_SECRET="5fddc9bde3ddfe39c1b792cbd3fe3ac9"
WECHAT_TEMPLATE_ID="kDusapKJllk0UrDuT86oUfFoVID7eHDiQ4AK7i0esNc"
