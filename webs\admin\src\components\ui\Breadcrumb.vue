<template>
  <nav class="flex" aria-label="Breadcrumb">
    <ol class="flex items-center space-x-4">
      <li v-for="(item, index) in items" :key="index">
        <div class="flex items-center">
          <!-- 分隔符 -->
          <ChevronRightIcon
            v-if="index > 0"
            class="flex-shrink-0 h-4 w-4 text-gray-400 mr-4"
            aria-hidden="true"
          />
          
          <!-- 面包屑项 -->
          <component
            :is="getComponent(item, index)"
            :to="item.to"
            :href="item.href"
            :class="getItemClass(item, index)"
            @click="handleClick(item, index)"
          >
            <!-- 图标 -->
            <component
              v-if="item.icon"
              :is="item.icon"
              class="flex-shrink-0 h-4 w-4 mr-1"
              aria-hidden="true"
            />
            
            <!-- 文本 -->
            <span>{{ item.text }}</span>
          </component>
        </div>
      </li>
    </ol>
  </nav>
</template>

<script setup>
import { computed } from 'vue'
import { ChevronRightIcon } from '@heroicons/vue/20/solid'

const props = defineProps({
  // 面包屑项目列表
  items: {
    type: Array,
    default: () => [],
    validator: (items) => {
      return items.every(item => 
        typeof item === 'object' && 
        item.text && 
        typeof item.text === 'string'
      )
    }
  },
  // 分隔符
  separator: {
    type: String,
    default: '/'
  },
  // 最大显示项目数
  maxItems: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['click'])

const displayItems = computed(() => {
  if (props.maxItems > 0 && props.items.length > props.maxItems) {
    const firstItem = props.items[0]
    const lastItems = props.items.slice(-(props.maxItems - 1))
    
    return [
      firstItem,
      { text: '...', disabled: true },
      ...lastItems
    ]
  }
  
  return props.items
})

const getComponent = (item, index) => {
  if (item.disabled || index === props.items.length - 1) {
    return 'span'
  }
  
  if (item.to) {
    return 'router-link'
  }
  
  if (item.href) {
    return 'a'
  }
  
  return 'button'
}

const getItemClass = (item, index) => {
  const baseClasses = ['text-sm font-medium flex items-center']
  
  if (item.disabled) {
    baseClasses.push('text-gray-400 dark:text-gray-500 cursor-not-allowed')
  } else if (index === props.items.length - 1) {
    baseClasses.push('text-gray-500 dark:text-gray-400')
  } else {
    baseClasses.push(
      'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300',
      'transition-colors duration-200'
    )
    
    if (!item.to && !item.href) {
      baseClasses.push('cursor-pointer')
    }
  }
  
  return baseClasses.join(' ')
}

const handleClick = (item, index) => {
  if (!item.disabled && !item.to && !item.href) {
    emit('click', item, index)
  }
}
</script>
