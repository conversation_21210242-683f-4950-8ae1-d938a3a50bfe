<template>
  <div class="category-management">
    <CustomTable
      title="菜品分类管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增分类
        </el-button>
        <el-button type="success" @click="handleBatchImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="info" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>

      <template #dishCount="{ row }">
        <el-tag type="info" size="small">
          {{ row.dishCount || 0 }} 道菜
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-button size="small" type="primary" link @click="handleView(row)">
          <el-icon><View /></el-icon>
          查看
        </el-button>
        <el-button size="small" type="warning" link @click="handleEdit(row)">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-popconfirm
          title="确定要删除这个分类吗？删除后该分类下的菜品将被移动到默认分类。"
          @confirm="handleDelete(row)"
        >
          <template #reference>
            <el-button size="small" type="danger" link>
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </CustomTable>

    <!-- 分类表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input
            v-model="formData.name"
            placeholder="请输入分类名称"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="formData.sort"
            :min="0"
            :max="999"
            placeholder="排序值，数字越小越靠前"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitLoading"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 分类详情对话框 -->
    <el-dialog v-model="detailVisible" title="分类详情" width="600px">
      <div class="category-detail" v-if="currentCategory">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="分类名称">
            {{ currentCategory.name }}
          </el-descriptions-item>
          <el-descriptions-item label="菜品数量">
            <el-tag type="info"
              >{{ currentCategory.dishCount || 0 }} 道菜</el-tag
            >
          </el-descriptions-item>
          <el-descriptions-item label="排序值">
            {{ currentCategory.sort || 0 }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatTime(currentCategory.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" span="2">
            {{ formatTime(currentCategory.updatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="分类描述" span="2">
            {{ currentCategory.description || "暂无描述" }}
          </el-descriptions-item>
        </el-descriptions>

        <!-- 该分类下的菜品列表 -->
        <div class="category-dishes" v-if="categoryDishes.length">
          <h4>该分类下的菜品</h4>
          <div class="dish-list">
            <el-tag
              v-for="dish in categoryDishes"
              :key="dish.id"
              class="dish-tag"
              type="success"
            >
              {{ dish.name }}
            </el-tag>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  View,
  Edit,
  Delete,
  Upload,
  Download
} from "@element-plus/icons-vue";
import CustomTable from "@/components/CustomTable.vue";
import { dishApi } from "@/api/menu";
import { formatTime } from "@/utils/common";

const loading = ref(false);
const submitLoading = ref(false);
const tableData = ref([]);
const dialogVisible = ref(false);
const detailVisible = ref(false);
const isEdit = ref(false);
const formRef = ref();
const currentCategory = ref(null);
const categoryDishes = ref([]);

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
});

const searchParams = reactive({});

const formData = ref({
  name: "",
  description: "",
  sort: 0
});

// 表格列配置
const columns = [
  { prop: "name", label: "分类名称", minWidth: 120 },
  {
    prop: "description",
    label: "描述",
    minWidth: 150,
    showOverflowTooltip: true
  },
  { prop: "dishCount", label: "菜品数量", width: 100, slot: true },
  { prop: "sort", label: "排序", width: 80 },
  {
    prop: "createdAt",
    label: "创建时间",
    width: 150,
    formatter: row => formatTime(row.createdAt, "YYYY-MM-DD HH:mm")
  },
  { label: "操作", width: 180, slot: "operation", fixed: "right" }
];

// 搜索字段配置
const searchFields = [
  {
    prop: "name",
    label: "分类名称",
    type: "input",
    placeholder: "请输入分类名称"
  }
];

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: "请输入分类名称", trigger: "blur" },
    { min: 2, max: 20, message: "分类名称长度在 2 到 20 个字符", trigger: "blur" }
  ]
};

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? "编辑分类" : "新增分类";
});

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    };

    const response = await dishApi.getCategories(params);
    if (response.code === 200) {
      tableData.value = response.data.list || response.data || [];
      pagination.total = response.data.total || response.data.length || 0;
    } else {
      ElMessage.error(response.message || '加载数据失败');
    }
  } catch (error) {
    console.error("加载分类列表失败:", error);
    ElMessage.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = params => {
  Object.assign(searchParams, params);
  pagination.page = 1;
  loadData();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    delete searchParams[key];
  });
  pagination.page = 1;
  loadData();
};

// 分页变化
const handleCurrentChange = page => {
  pagination.page = page;
  loadData();
};

const handleSizeChange = size => {
  pagination.size = size;
  pagination.page = 1;
  loadData();
};

// 新增分类
const handleCreate = () => {
  isEdit.value = false;
  formData.value = {
    name: "",
    description: "",
    sort: 0
  };
  dialogVisible.value = true;
};

// 查看分类
const handleView = async row => {
  currentCategory.value = row;

  // 加载该分类下的菜品
  try {
    const response = await dishApi.getDishes({ categoryId: row.id });
    if (response.data) {
      categoryDishes.value = response.data.list || response.data;
    }
  } catch (error) {
    console.error("加载分类菜品失败:", error);
    categoryDishes.value = [];
  }

  detailVisible.value = true;
};

// 编辑分类
const handleEdit = row => {
  isEdit.value = true;
  formData.value = { ...row };
  dialogVisible.value = true;
};

// 删除分类
const handleDelete = async row => {
  try {
    await dishApi.deleteCategory(row.id);
    ElMessage.success("删除成功");
    loadData();
  } catch (error) {
    console.error("删除分类失败:", error);
    ElMessage.error("删除失败");
  }
};

// 批量导入
const handleBatchImport = () => {
  ElMessage.info("批量导入功能开发中...");
};

// 导出数据
const handleExport = () => {
  ElMessage.info("导出功能开发中...");
};

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitLoading.value = true;

    if (isEdit.value) {
      await dishApi.updateCategory(formData.value.id, formData.value);
      ElMessage.success("更新成功");
    } else {
      await dishApi.createCategory(formData.value);
      ElMessage.success("创建成功");
    }

    dialogVisible.value = false;
    loadData();
  } catch (error) {
    if (error.errors) {
      // 表单验证错误
      return;
    }
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  } finally {
    submitLoading.value = false;
  }
};

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false;
  formData.value = {
    name: "",
    description: "",
    sort: 0
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.category-management {
  @apply p-5;
}

.category-detail {
  .category-dishes {
    @apply mt-5;

    h4 {
      @apply mb-3 text-gray-800 text-base;
    }

    .dish-list {
      @apply flex flex-wrap gap-2;

      .dish-tag {
        @apply m-0;
      }
    }
  }
}

.dialog-footer {
  @apply flex justify-end gap-3;
}
</style>
