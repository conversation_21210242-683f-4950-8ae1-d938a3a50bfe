<template>
  <PageContainer
    title="订单管理"
    subtitle="管理系统中的所有订单信息"
    :breadcrumb="breadcrumbItems"
  >
    <template #header-actions>
      <BaseButton
        variant="outline"
        @click="handleRefresh"
      >
        刷新数据
      </BaseButton>
    </template>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="总订单数"
        :value="stats.totalOrders"
        :icon="ShoppingCartIcon"
        color="blue"
        :change="stats.orderGrowth"
        description="较上月"
      />
      <StatsCard
        title="今日订单"
        :value="stats.todayOrders"
        :icon="CalendarDaysIcon"
        color="green"
        :change="stats.todayGrowth"
        description="较昨日"
      />
      <StatsCard
        title="待处理"
        :value="stats.pendingOrders"
        :icon="ClockIcon"
        color="yellow"
        description="待处理订单"
      />
      <StatsCard
        title="已完成"
        :value="stats.completedOrders"
        :icon="CheckCircleIcon"
        color="purple"
        description="已完成订单"
      />
    </div>

    <!-- 搜索表单 -->
    <SearchForm
      v-model="searchParams"
      :fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 订单表格 -->
    <BaseTable
      :data="orders"
      :columns="tableColumns"
      :selectable="true"
      v-model:selected-rows="selectedOrders"
      :loading="loading"
      @row-click="handleRowClick"
      @sort-change="handleSort"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            订单列表 ({{ pagination.total }})
          </h3>
          <div class="flex items-center space-x-3">
            <BaseButton
              v-if="selectedOrders.length"
              variant="outline"
              size="sm"
              @click="handleBatchUpdateStatus"
            >
              批量更新状态
            </BaseButton>
            <BaseButton
              variant="outline"
              size="sm"
              @click="handleExport"
            >
              导出数据
            </BaseButton>
          </div>
        </div>
      </template>

      <!-- 用户信息 -->
      <template #cell-user="{ row }">
        <div class="flex items-center">
          <img
            v-if="row.user?.avatar"
            :src="row.user.avatar"
            :alt="row.user.name"
            class="h-8 w-8 rounded-full object-cover mr-2"
          />
          <div
            v-else
            class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-2"
          >
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ row.user?.name?.charAt(0)?.toUpperCase() }}
            </span>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ row.user?.name }}
            </div>
            <div class="text-sm text-gray-500 dark:text-gray-400">
              {{ row.user?.phone }}
            </div>
          </div>
        </div>
      </template>

      <!-- 菜单信息 -->
      <template #cell-menu="{ row }">
        <div>
          <div class="text-sm font-medium text-gray-900 dark:text-white">
            {{ formatDate(row.menu?.date, 'MM-DD') }} 菜单
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ getOrderItemsCount(row.items) }} 道菜
          </div>
        </div>
      </template>

      <!-- 订单状态 -->
      <template #cell-status="{ row }">
        <span
          :class="getStatusBadgeClass(row.status)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ getStatusText(row.status) }}
        </span>
      </template>

      <!-- 用餐时间 -->
      <template #cell-diningTime="{ row }">
        <span v-if="row.diningTime" class="text-sm text-gray-900 dark:text-gray-100">
          {{ formatDate(row.diningTime, 'MM-DD HH:mm') }}
        </span>
        <span v-else class="text-sm text-gray-500 dark:text-gray-400">
          未设置
        </span>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center space-x-2">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleView(row)"
          >
            查看
          </BaseButton>
          <BaseButton
            v-if="row.status === 'pending'"
            variant="ghost"
            size="sm"
            @click="handleConfirm(row)"
            class="text-green-600"
          >
            确认
          </BaseButton>
          <BaseButton
            v-if="row.status === 'confirmed'"
            variant="ghost"
            size="sm"
            @click="handleComplete(row)"
            class="text-blue-600"
          >
            完成
          </BaseButton>
          <BaseButton
            v-if="['pending', 'confirmed'].includes(row.status)"
            variant="ghost"
            size="sm"
            @click="handleCancel(row)"
            class="text-red-600"
          >
            取消
          </BaseButton>
        </div>
      </template>

      <!-- 分页 -->
      <template #footer>
        <Pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </template>
    </BaseTable>

    <!-- 订单详情模态框 -->
    <OrderDetailModal
      v-model="showDetailModal"
      :order="currentOrder"
    />

    <!-- 批量状态更新模态框 -->
    <BatchStatusModal
      v-model="showBatchModal"
      :orders="selectedOrders"
      @success="handleBatchSuccess"
    />
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  ShoppingCartIcon,
  CalendarDaysIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'
import OrderDetailModal from './components/OrderDetailModal.vue'
import BatchStatusModal from './components/BatchStatusModal.vue'
import { orderApi } from '@/api/order'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const orders = ref([])
const selectedOrders = ref([])
const showDetailModal = ref(false)
const showBatchModal = ref(false)
const currentOrder = ref(null)

// 搜索参数
const searchParams = reactive({
  keyword: '',
  status: '',
  dateRange: [],
  diningTimeRange: []
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 统计数据
const stats = reactive({
  totalOrders: 0,
  todayOrders: 0,
  pendingOrders: 0,
  completedOrders: 0,
  orderGrowth: 0,
  todayGrowth: 0
})

// 面包屑导航
const breadcrumbItems = [
  { text: '首页', to: '/' },
  { text: '订单管理', to: '/order' },
  { text: '订单列表' }
]

// 搜索字段配置
const searchFields = [
  {
    key: 'keyword',
    type: 'input',
    label: '关键词',
    placeholder: '搜索用户名、手机号',
    prefixIcon: 'MagnifyingGlassIcon'
  },
  {
    key: 'status',
    type: 'select',
    label: '订单状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '待处理', value: 'pending' },
      { label: '已确认', value: 'confirmed' },
      { label: '已完成', value: 'completed' },
      { label: '已取消', value: 'cancelled' }
    ]
  },
  {
    key: 'dateRange',
    type: 'daterange',
    label: '下单时间',
    startPlaceholder: '开始日期',
    endPlaceholder: '结束日期'
  },
  {
    key: 'diningTimeRange',
    type: 'daterange',
    label: '用餐时间',
    startPlaceholder: '开始日期',
    endPlaceholder: '结束日期'
  }
]

// 表格列配置
const tableColumns = [
  {
    key: 'id',
    title: '订单号',
    width: 120,
    render: (value) => `#${value.slice(-8)}`
  },
  {
    key: 'user',
    title: '用户信息'
  },
  {
    key: 'menu',
    title: '菜单信息'
  },
  {
    key: 'status',
    title: '状态'
  },
  {
    key: 'diningTime',
    title: '用餐时间',
    sortable: true
  },
  {
    key: 'createdAt',
    title: '下单时间',
    sortable: true,
    render: (value) => formatDate(value)
  }
]

// 计算属性和方法
const getStatusBadgeClass = (status) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    confirmed: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getStatusText = (status) => {
  const texts = {
    pending: '待处理',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

const getOrderItemsCount = (items) => {
  try {
    const itemsArray = typeof items === 'string' ? JSON.parse(items) : items
    return Array.isArray(itemsArray) ? itemsArray.length : 0
  } catch {
    return 0
  }
}

// 数据获取方法
const fetchOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams
    }
    
    const response = await orderApi.getOrders(params)
    orders.value = response.data.orders
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取订单列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    const response = await orderApi.getStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 事件处理方法
const handleSearch = () => {
  pagination.current = 1
  fetchOrders()
}

const handleReset = () => {
  pagination.current = 1
  fetchOrders()
}

const handleRefresh = () => {
  fetchOrders()
  fetchStats()
}

const handlePageChange = () => {
  fetchOrders()
}

const handleSort = (sortInfo) => {
  console.log('排序:', sortInfo)
  fetchOrders()
}

const handleView = (order) => {
  currentOrder.value = order
  showDetailModal.value = true
}

const handleConfirm = async (order) => {
  try {
    await orderApi.updateStatus(order.id, 'confirmed')
    fetchOrders()
    fetchStats()
  } catch (error) {
    console.error('确认订单失败:', error)
  }
}

const handleComplete = async (order) => {
  try {
    await orderApi.updateStatus(order.id, 'completed')
    fetchOrders()
    fetchStats()
  } catch (error) {
    console.error('完成订单失败:', error)
  }
}

const handleCancel = async (order) => {
  if (confirm(`确定要取消订单 #${order.id.slice(-8)} 吗？`)) {
    try {
      await orderApi.updateStatus(order.id, 'cancelled')
      fetchOrders()
      fetchStats()
    } catch (error) {
      console.error('取消订单失败:', error)
    }
  }
}

const handleBatchUpdateStatus = () => {
  showBatchModal.value = true
}

const handleBatchSuccess = () => {
  showBatchModal.value = false
  selectedOrders.value = []
  fetchOrders()
  fetchStats()
}

const handleExport = () => {
  console.log('导出数据')
}

const handleRowClick = (order) => {
  console.log('点击订单:', order)
}

// 生命周期
onMounted(() => {
  fetchOrders()
  fetchStats()
})
</script>
