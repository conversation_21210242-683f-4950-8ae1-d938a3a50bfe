<view class="recommended-menu-page">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">推荐菜单</text>
    <text class="page-subtitle">来自关联用户的美食推荐</text>
  </view>

  <!-- 加载状态 -->
  <van-loading
    wx:if="{{loading}}"
    type="spinner"
    color="#1989fa"
    size="24px"
    vertical
  >
    加载中...
  </van-loading>

  <!-- 菜单列表 -->
  <view wx:elif="{{menus.length > 0}}" class="menu-list">
    <view
      wx:for="{{menus}}"
      wx:key="id"
      class="menu-card"
      data-menu="{{item}}"
      bindtap="viewMenuDetail"
    >
      <!-- 菜单头部信息 -->
      <view class="menu-header">
        <view class="creator-info">
          <image
            class="creator-avatar"
            src="{{item.creator.avatar || '/assets/image/default-avatar.png'}}"
            mode="aspectFill"
          />
          <view class="creator-details">
            <text class="creator-name">{{item.creator.name}}</text>
            <text class="create-time">{{formatTime(item.createdAt)}}</text>
          </view>
        </view>
        <view class="menu-actions">
          <van-button
            type="primary"
            size="small"
            data-menu="{{item}}"
            catchtap="orderMenu"
          >
            点菜
          </van-button>
        </view>
      </view>

      <!-- 菜单内容 -->
      <view class="menu-content">
        <view wx:if="{{item.remark}}" class="menu-remark">
          {{item.remark}}
        </view>

        <view class="dishes-preview">
          <view
            wx:for="{{item.dishes}}"
            wx:for-item="dish"
            wx:key="id"
            class="dish-item"
          >
            <image
              class="dish-image"
              src="{{dish.image || '/assets/image/default-dish.png'}}"
              mode="aspectFill"
            />
            <view class="dish-info">
              <text class="dish-name">{{dish.name}}</text>
              <text class="dish-count">x{{dish.count}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 菜单标签 -->
      <view class="menu-tags">
        <van-tag wx:if="{{item.isToday}}" type="primary" size="small"
          >今日菜单</van-tag
        >
        <van-tag type="default" size="small"
          >{{item.dishes.length}}道菜</van-tag
        >
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <van-empty
    wx:else
    description="暂无推荐菜单"
    image="https://img.yzcdn.cn/vant/custom-empty-image.png"
  >
    <van-button type="primary" size="small" bind:click="loadRecommendedMenus">
      刷新
    </van-button>
  </van-empty>

  <!-- 订单弹窗 -->
  <van-popup
    show="{{showOrderModal}}"
    position="bottom"
    custom-style="height: 70%"
    bind:close="closeOrderModal"
  >
    <view class="order-modal">
      <view class="modal-header">
        <text class="modal-title">选择菜品</text>
        <van-icon name="cross" bind:click="closeOrderModal" />
      </view>

      <scroll-view class="modal-content" scroll-y>
        <!-- 菜品列表 -->
        <view class="order-dishes">
          <view wx:for="{{orderItems}}" wx:key="id" class="order-dish-item">
            <image
              class="dish-image"
              src="{{item.image || '/assets/image/default-dish.png'}}"
              mode="aspectFill"
            />
            <view class="dish-info">
              <text class="dish-name">{{item.name}}</text>
              <text class="dish-description">{{item.description}}</text>
            </view>
            <view class="dish-counter">
              <van-button
                type="default"
                size="small"
                icon="minus"
                data-index="{{index}}"
                data-action="decrease"
                bind:click="changeDishCount"
              />
              <text class="count-text">{{item.orderCount}}</text>
              <van-button
                type="default"
                size="small"
                icon="plus"
                data-index="{{index}}"
                data-action="increase"
                bind:click="changeDishCount"
              />
            </view>
          </view>
        </view>

        <!-- 备注输入 -->
        <view class="order-remark">
          <van-field
            label="备注"
            placeholder="请输入备注信息"
            value="{{orderRemark}}"
            bind:change="onRemarkInput"
          />
        </view>

        <!-- 推送用户选择 -->
        <view class="push-users">
          <view class="section-title">推送给关联用户</view>

          <!-- 有关联用户时显示列表 -->
          <view wx:if="{{connectedUsers.length > 0}}" class="users-list">
            <view
              wx:for="{{connectedUsers}}"
              wx:key="id"
              class="user-item {{selectedPushUsers.indexOf(item.id) > -1 ? 'selected' : ''}}"
              data-user-id="{{item.id}}"
              bind:tap="togglePushUser"
            >
              <image
                class="user-avatar"
                src="{{item.avatar || '/assets/image/default-avatar.png'}}"
                mode="aspectFill"
              />
              <text class="user-name">{{item.name}}</text>
              <van-icon
                wx:if="{{selectedPushUsers.indexOf(item.id) > -1}}"
                name="success"
                color="#1989fa"
              />
            </view>
          </view>

          <!-- 无关联用户时显示缺省页 -->
          <view wx:else class="empty-users-state">
            <view class="empty-icon">
              <van-icon name="friends-o" size="60rpx" color="#D1D5DB" />
            </view>
            <view class="empty-text">暂无关联用户</view>
            <view class="empty-desc">添加关联用户后可推送订单给他们</view>
            <view class="empty-action">
              <van-button
                size="small"
                type="primary"
                plain
                bind:click="goToUserConnection"
              >
                去添加关联用户
              </van-button>
            </view>
          </view>
        </view>
      </scroll-view>

      <!-- 提交按钮 -->
      <view class="modal-footer">
        <van-button
          type="primary"
          block
          loading="{{submitting}}"
          bind:click="submitOrder"
        >
          确认下单
        </van-button>
      </view>
    </view>
  </van-popup>
</view>
