<template>
  <BaseModal
    v-model="visible"
    title="添加微信登录权限"
    size="lg"
    :show-default-footer="true"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="space-y-4"
    >
      <el-form-item label="用户选择" prop="userId">
        <el-select
          v-model="form.userId"
          placeholder="请选择用户"
          filterable
          remote
          :remote-method="searchUsers"
          :loading="userLoading"
          class="w-full"
        >
          <el-option
            v-for="user in userOptions"
            :key="user.id"
            :label="`${user.name} (${user.phone || '无手机号'})`"
            :value="user.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="权限类型" prop="permissionType">
        <el-radio-group v-model="form.permissionType">
          <el-radio value="admin">管理员权限</el-radio>
          <el-radio value="user">普通用户权限</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="有效期" prop="expiresAt">
        <el-date-picker
          v-model="form.expiresAt"
          type="datetime"
          placeholder="选择过期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </BaseModal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import { userApi } from '@/api/user'
import { wechatPermissionApi } from '@/api/wechatPermission'
import { useToast } from '@/composables/useToast'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const { success, error } = useToast()

// 响应式数据
const visible = ref(false)
const formRef = ref()
const userLoading = ref(false)
const userOptions = ref([])

const form = reactive({
  userId: '',
  permissionType: 'user',
  expiresAt: '',
  remark: ''
})

const rules = {
  userId: [
    { required: true, message: '请选择用户', trigger: 'change' }
  ],
  permissionType: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ]
}

// 监听显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    resetForm()
    searchUsers('')
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 搜索用户
const searchUsers = async (query) => {
  try {
    userLoading.value = true
    const response = await userApi.getUsers({
      search: query,
      pageSize: 20
    })
    
    if (response.success) {
      userOptions.value = response.data.list || []
    }
  } catch (err) {
    console.error('搜索用户失败:', err)
    error('搜索用户失败')
  } finally {
    userLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(form, {
    userId: '',
    permissionType: 'user',
    expiresAt: '',
    remark: ''
  })
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const response = await wechatPermissionApi.createPermission(form)
    
    if (response.success) {
      success('添加权限成功')
      emit('success')
    } else {
      error(response.message || '添加权限失败')
    }
  } catch (err) {
    console.error('添加权限失败:', err)
    error('添加权限失败')
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
}
</script>
