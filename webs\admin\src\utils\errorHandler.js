/**
 * 错误处理工具
 * 统一处理应用中的各种错误
 */

import { ElMessage, ElNotification } from 'element-plus'

// 错误类型枚举
export const ERROR_TYPES = {
  NETWORK: 'NETWORK_ERROR',
  AUTH: 'AUTH_ERROR',
  PERMISSION: 'PERMISSION_ERROR',
  VALIDATION: 'VALIDATION_ERROR',
  BUSINESS: 'BUSINESS_ERROR',
  SYSTEM: 'SYSTEM_ERROR',
  UNKNOWN: 'UNKNOWN_ERROR'
}

// 错误级别枚举
export const ERROR_LEVELS = {
  INFO: 'info',
  WARNING: 'warning',
  ERROR: 'error',
  CRITICAL: 'critical'
}

// 错误消息映射
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: '网络连接失败，请检查网络设置',
  [ERROR_TYPES.AUTH]: '身份验证失败，请重新登录',
  [ERROR_TYPES.PERMISSION]: '权限不足，无法执行此操作',
  [ERROR_TYPES.VALIDATION]: '数据验证失败，请检查输入',
  [ERROR_TYPES.BUSINESS]: '业务处理失败',
  [ERROR_TYPES.SYSTEM]: '系统错误，请稍后重试',
  [ERROR_TYPES.UNKNOWN]: '未知错误，请联系管理员'
}

// HTTP状态码错误映射
const HTTP_ERROR_MESSAGES = {
  400: '请求参数错误',
  401: '身份验证失败，请重新登录',
  403: '权限不足，无法访问',
  404: '请求的资源不存在',
  405: '请求方法不被允许',
  408: '请求超时，请重试',
  409: '请求冲突，请刷新后重试',
  422: '数据验证失败',
  429: '请求过于频繁，请稍后重试',
  500: '服务器内部错误',
  502: '网关错误',
  503: '服务暂时不可用',
  504: '网关超时'
}

// 错误处理器类
class ErrorHandler {
  constructor() {
    this.errorQueue = []
    this.isProcessing = false
  }

  // 处理错误
  handle(error, options = {}) {
    const errorInfo = this.parseError(error)
    const config = {
      showMessage: true,
      showNotification: false,
      logError: true,
      ...options
    }

    // 记录错误日志
    if (config.logError) {
      this.logError(errorInfo)
    }

    // 显示错误消息
    if (config.showMessage) {
      this.showErrorMessage(errorInfo, config)
    }

    // 显示错误通知
    if (config.showNotification) {
      this.showErrorNotification(errorInfo, config)
    }

    return errorInfo
  }

  // 解析错误
  parseError(error) {
    let errorInfo = {
      type: ERROR_TYPES.UNKNOWN,
      level: ERROR_LEVELS.ERROR,
      message: '未知错误',
      code: null,
      details: null,
      stack: null,
      timestamp: new Date().toISOString()
    }

    if (error) {
      // HTTP错误
      if (error.response) {
        const { status, data } = error.response
        errorInfo.code = status
        errorInfo.message = data?.message || HTTP_ERROR_MESSAGES[status] || `HTTP ${status} 错误`
        errorInfo.details = data
        
        if (status === 401) {
          errorInfo.type = ERROR_TYPES.AUTH
        } else if (status === 403) {
          errorInfo.type = ERROR_TYPES.PERMISSION
        } else if (status >= 400 && status < 500) {
          errorInfo.type = ERROR_TYPES.VALIDATION
        } else if (status >= 500) {
          errorInfo.type = ERROR_TYPES.SYSTEM
          errorInfo.level = ERROR_LEVELS.CRITICAL
        }
      }
      // 网络错误
      else if (error.request) {
        errorInfo.type = ERROR_TYPES.NETWORK
        errorInfo.message = ERROR_MESSAGES[ERROR_TYPES.NETWORK]
      }
      // 业务错误
      else if (error.code && error.message) {
        errorInfo.type = error.type || ERROR_TYPES.BUSINESS
        errorInfo.code = error.code
        errorInfo.message = error.message
        errorInfo.details = error.details
      }
      // 普通错误对象
      else if (error instanceof Error) {
        errorInfo.message = error.message
        errorInfo.stack = error.stack
      }
      // 字符串错误
      else if (typeof error === 'string') {
        errorInfo.message = error
      }
    }

    return errorInfo
  }

  // 显示错误消息
  showErrorMessage(errorInfo, config) {
    const message = config.customMessage || errorInfo.message
    
    switch (errorInfo.level) {
      case ERROR_LEVELS.WARNING:
        ElMessage.warning(message)
        break
      case ERROR_LEVELS.INFO:
        ElMessage.info(message)
        break
      default:
        ElMessage.error(message)
    }
  }

  // 显示错误通知
  showErrorNotification(errorInfo, config) {
    const title = config.notificationTitle || '错误提示'
    const message = config.customMessage || errorInfo.message
    
    ElNotification({
      title,
      message,
      type: errorInfo.level === ERROR_LEVELS.WARNING ? 'warning' : 'error',
      duration: errorInfo.level === ERROR_LEVELS.CRITICAL ? 0 : 4500
    })
  }

  // 记录错误日志
  logError(errorInfo) {
    const logData = {
      ...errorInfo,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId()
    }

    // 开发环境直接打印
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Log')
      console.error('Error Info:', logData)
      if (errorInfo.stack) {
        console.error('Stack Trace:', errorInfo.stack)
      }
      console.groupEnd()
    }

    // 生产环境发送到服务器
    if (process.env.NODE_ENV === 'production') {
      this.sendErrorToServer(logData)
    }
  }

  // 获取当前用户ID
  getCurrentUserId() {
    try {
      const userInfo = JSON.parse(localStorage.getItem('admin_user') || '{}')
      return userInfo.id || null
    } catch {
      return null
    }
  }

  // 发送错误到服务器
  async sendErrorToServer(errorData) {
    try {
      // 这里可以调用错误上报API
      // await api.post('/api/errors/report', errorData)
    } catch (error) {
      console.error('Failed to send error to server:', error)
    }
  }

  // 批量处理错误
  async batchHandle(errors, options = {}) {
    const results = []
    for (const error of errors) {
      results.push(this.handle(error, options))
    }
    return results
  }

  // 清除错误队列
  clearErrorQueue() {
    this.errorQueue = []
  }
}

// 创建全局错误处理器实例
export const errorHandler = new ErrorHandler()

// 便捷方法
export const handleError = (error, options) => errorHandler.handle(error, options)

// 异步操作错误处理装饰器
export const withErrorHandling = (asyncFn, options = {}) => {
  return async (...args) => {
    try {
      return await asyncFn(...args)
    } catch (error) {
      handleError(error, options)
      throw error
    }
  }
}

// Promise错误处理
export const safePromise = (promise, options = {}) => {
  return promise.catch(error => {
    handleError(error, options)
    return Promise.reject(error)
  })
}

// 全局错误监听
export const setupGlobalErrorHandling = () => {
  // 监听未捕获的Promise错误
  window.addEventListener('unhandledrejection', event => {
    handleError(event.reason, {
      showNotification: true,
      notificationTitle: '未处理的Promise错误'
    })
  })

  // 监听JavaScript错误
  window.addEventListener('error', event => {
    handleError(event.error, {
      showNotification: true,
      notificationTitle: 'JavaScript错误'
    })
  })

  // 监听资源加载错误
  window.addEventListener('error', event => {
    if (event.target !== window) {
      handleError(`资源加载失败: ${event.target.src || event.target.href}`, {
        showMessage: false,
        showNotification: true,
        notificationTitle: '资源加载错误'
      })
    }
  }, true)
}
