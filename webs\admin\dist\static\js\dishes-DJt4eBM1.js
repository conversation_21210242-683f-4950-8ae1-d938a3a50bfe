var de=Object.defineProperty,ce=Object.defineProperties;var ue=Object.getOwnPropertyDescriptors;var R=Object.getOwnPropertySymbols;var me=Object.prototype.hasOwnProperty,fe=Object.prototype.propertyIsEnumerable;var I=(p,a,d)=>a in p?de(p,a,{enumerable:!0,configurable:!0,writable:!0,value:d}):p[a]=d,U=(p,a)=>{for(var d in a||(a={}))me.call(a,d)&&I(p,d,a[d]);if(R)for(var d of R(a))fe.call(a,d)&&I(p,d,a[d]);return p},L=(p,a)=>ce(p,ue(a));var S=(p,a,d)=>new Promise((e,h)=>{var y=g=>{try{i(d.next(g))}catch(_){h(_)}},s=g=>{try{i(d.throw(g))}catch(_){h(_)}},i=g=>g.done?e(g.value):Promise.resolve(g.value).then(y,s);i((d=d.apply(p,a)).next())});import{_ as J,r as f,c as z,d as l,w as t,a as w,H as ge,I as _e,p as pe,m,t as D,g as V,f as N,h as B,q as P,F as H,J as W,E as c,l as E,b as j,o as q,K as he,L as be,M as ve,N as ye,O as Ce,P as Ve,Q as xe,k as Y}from"./index-Cy8N1eGd.js";import{C as ke}from"./CustomTable-DWghEWMD.js";import{d as M}from"./menu-BDmPj0yF.js";import{f as G}from"./common-CIDIMsc8.js";const De="/api/upload",we={__name:"DishForm",props:{formData:{type:Object,default:()=>({})},isEdit:{type:Boolean,default:!1}},emits:["submit","cancel"],setup(p,{expose:a,emit:d}){const e=p,h=d,y=V(),s=N(),i=B({name:"",category:"",description:"",ingredients:"",cookingMethod:"",image:"",isAvailable:!0}),g={name:[{required:!0,message:"请输入菜品名称",trigger:"blur"}],category:[{required:!0,message:"请选择分类",trigger:"change"}],description:[{required:!0,message:"请输入菜品描述",trigger:"blur"}]},_=["热菜","凉菜","汤品","主食","甜品"],b=P(()=>({Authorization:`Bearer ${s.token}`}));H(()=>e.formData,r=>{r&&Object.keys(r).length>0&&Object.assign(i,{name:r.name||"",category:r.category||"",description:r.description||"",ingredients:r.ingredients||"",cookingMethod:r.cookingMethod||"",image:r.image||"",isAvailable:r.isAvailable!==void 0?r.isAvailable:!0})},{immediate:!0});const x=r=>{const T=r.type==="image/jpeg"||r.type==="image/png",F=r.size/1024/1024<2;return T?F?!0:(c.error("上传图片大小不能超过 2MB!"),!1):(c.error("上传图片只能是 JPG/PNG 格式!"),!1)},v=r=>{r.code===200?(i.image=r.data.url,c.success("图片上传成功")):c.error("图片上传失败")},C=()=>S(this,null,function*(){if(y.value)try{if(!(yield y.value.validate()))return;h("submit",U({},i))}catch(r){console.error("表单验证失败:",r)}}),A=()=>{h("cancel")},n=()=>{y.value&&y.value.resetFields(),Object.assign(i,{name:"",category:"",description:"",ingredients:"",cookingMethod:"",image:"",isAvailable:!0})};a({resetForm:n});const k={props:e,emit:h,formRef:y,userStore:s,form:i,rules:g,categories:_,uploadUrl:De,uploadHeaders:b,beforeUpload:x,handleUploadSuccess:v,handleSubmit:C,handleCancel:A,resetForm:n,ref:V,reactive:B,watch:H,computed:P,get ElMessage(){return c},get Plus(){return W},get useUserStore(){return N}};return Object.defineProperty(k,"__isScriptSetup",{enumerable:!1,value:!0}),k}},Ae={class:"dish-form"},Se=["src"],Me={class:"form-actions"};function Ee(p,a,d,e,h,y){const s=f("el-input"),i=f("el-form-item"),g=f("el-option"),_=f("el-select"),b=f("el-icon"),x=f("el-upload"),v=f("el-switch"),C=f("el-button"),A=f("el-form");return E(),z("div",Ae,[l(A,{ref:"formRef",model:e.form,rules:e.rules,"label-width":"100px"},{default:t(()=>[l(i,{label:"菜品名称",prop:"name"},{default:t(()=>[l(s,{modelValue:e.form.name,"onUpdate:modelValue":a[0]||(a[0]=n=>e.form.name=n),placeholder:"请输入菜品名称"},null,8,["modelValue"])]),_:1}),l(i,{label:"分类",prop:"category"},{default:t(()=>[l(_,{modelValue:e.form.category,"onUpdate:modelValue":a[1]||(a[1]=n=>e.form.category=n),placeholder:"请选择分类",style:{width:"100%"}},{default:t(()=>[(E(),z(ge,null,_e(e.categories,n=>l(g,{key:n,label:n,value:n},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1}),l(i,{label:"描述",prop:"description"},{default:t(()=>[l(s,{modelValue:e.form.description,"onUpdate:modelValue":a[2]||(a[2]=n=>e.form.description=n),type:"textarea",rows:3,placeholder:"请输入菜品描述"},null,8,["modelValue"])]),_:1}),l(i,{label:"食材",prop:"ingredients"},{default:t(()=>[l(s,{modelValue:e.form.ingredients,"onUpdate:modelValue":a[3]||(a[3]=n=>e.form.ingredients=n),type:"textarea",rows:2,placeholder:"请输入食材，用逗号分隔"},null,8,["modelValue"])]),_:1}),l(i,{label:"制作方法",prop:"cookingMethod"},{default:t(()=>[l(s,{modelValue:e.form.cookingMethod,"onUpdate:modelValue":a[4]||(a[4]=n=>e.form.cookingMethod=n),type:"textarea",rows:4,placeholder:"请输入制作方法"},null,8,["modelValue"])]),_:1}),l(i,{label:"菜品图片"},{default:t(()=>[l(x,{class:"dish-uploader",action:e.uploadUrl,headers:e.uploadHeaders,"show-file-list":!1,"on-success":e.handleUploadSuccess,"before-upload":e.beforeUpload},{default:t(()=>[e.form.image?(E(),z("img",{key:0,src:e.form.image,class:"dish-image"},null,8,Se)):(E(),pe(b,{key:1,class:"dish-uploader-icon"},{default:t(()=>[l(e.Plus)]),_:1}))]),_:1},8,["headers"])]),_:1}),l(i,{label:"是否可用"},{default:t(()=>[l(v,{modelValue:e.form.isAvailable,"onUpdate:modelValue":a[5]||(a[5]=n=>e.form.isAvailable=n),"active-text":"可用","inactive-text":"不可用"},null,8,["modelValue"])]),_:1}),w("div",Me,[l(C,{onClick:e.handleCancel},{default:t(()=>a[6]||(a[6]=[m("取消",-1)])),_:1,__:[6]}),l(C,{type:"primary",onClick:e.handleSubmit},{default:t(()=>[m(D(d.isEdit?"更新":"创建"),1)]),_:1})])]),_:1},8,["model"])])}const Ue=J(we,[["render",Ee],["__scopeId","data-v-44dd8453"],["__file","E:/wx-nan/webs/admin/src/views/menu/components/DishForm.vue"]]),ze={__name:"dishes",setup(p,{expose:a}){a();const d=V(!1),e=V([]),h=V(!1),y=V(!1),s=V(!1),i=V(),g=V(null),_=V([]),b=B({page:1,size:10,total:0}),x=B({}),v=V({}),C=[{prop:"image",label:"图片",width:80,slot:!0},{prop:"name",label:"菜品名称",minWidth:120},{prop:"category",label:"分类",width:100,slot:!0},{prop:"description",label:"描述",minWidth:150,showOverflowTooltip:!0},{prop:"isAvailable",label:"状态",width:80,slot:!0},{prop:"createdAt",label:"创建时间",width:150,formatter:o=>G(o.createdAt,"YYYY-MM-DD HH:mm")},{label:"操作",width:200,slot:"operation",fixed:"right"}],A=[{prop:"name",label:"菜品名称",type:"input",placeholder:"请输入菜品名称"},{prop:"category",label:"分类",type:"select",placeholder:"选择分类",options:[{label:"热菜",value:"热菜"},{label:"凉菜",value:"凉菜"},{label:"汤品",value:"汤品"},{label:"主食",value:"主食"},{label:"甜品",value:"甜品"}]},{prop:"isAvailable",label:"状态",type:"select",placeholder:"选择状态",options:[{label:"可用",value:!0},{label:"不可用",value:!1}]}],n=P(()=>s.value?"编辑菜品":"新增菜品"),k=o=>({热菜:"danger",凉菜:"success",汤品:"warning",主食:"primary",甜品:"info"})[o]||"primary",r=()=>S(this,null,function*(){d.value=!0;try{const o=U({page:b.page,size:b.size},x),u=yield M.getDishes(o);u.code===200?(e.value=u.data.list||[],b.total=u.data.total||0):c.error(u.message||"加载数据失败")}catch(o){console.error("加载菜品列表失败:",o),c.error("加载数据失败")}finally{d.value=!1}}),T=o=>{Object.assign(x,o),b.page=1,r()},F=()=>{Object.keys(x).forEach(o=>{delete x[o]}),b.page=1,r()},K=o=>{b.page=o,r()},Q=o=>{b.size=o,b.page=1,r()},X=o=>S(this,null,function*(){o.statusLoading=!0;try{const u=yield M.updateDish(o.id,{isAvailable:o.isAvailable});u.code===200?c.success("状态更新成功"):(c.error(u.message||"状态更新失败"),o.isAvailable=!o.isAvailable)}catch(u){console.error("更新状态失败:",u),c.error("状态更新失败"),o.isAvailable=!o.isAvailable}finally{o.statusLoading=!1}}),Z=()=>{s.value=!1,v.value={},h.value=!0},$=o=>{g.value=o,y.value=!0},ee=o=>{s.value=!0,v.value=U({},o),h.value=!0},le=o=>{s.value=!1,v.value=L(U({},o),{id:void 0,name:`${o.name} - 副本`}),h.value=!0},ae=o=>S(this,null,function*(){try{const u=yield M.deleteDish(o.id);u.code===200?(c.success("删除成功"),r()):c.error(u.message||"删除失败")}catch(u){console.error("删除菜品失败:",u),c.error("删除失败")}}),te=()=>S(this,null,function*(){if(_.value.length===0){c.warning("请选择要删除的菜品");return}try{yield Y.confirm(`确定要删除选中的 ${_.value.length} 个菜品吗？`,"批量删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const o=_.value.map(u=>u.id);yield M.batchOperation({operation:"delete",dishIds:o}),c.success("批量删除成功"),_.value=[],r()}catch(o){o!=="cancel"&&(console.error("批量删除失败:",o),c.error("批量删除失败"))}}),oe=o=>{_.value=o},ne=()=>{c.info("批量导入功能开发中...")},ie=()=>{c.info("导出功能开发中...")},re=o=>S(this,null,function*(){try{s.value?(yield M.updateDish(v.value.id,o),c.success("更新成功")):(yield M.createDish(o),c.success("创建成功")),h.value=!1,r()}catch(u){console.error("提交失败:",u),c.error("操作失败")}}),se=()=>{h.value=!1,v.value={},i.value&&i.value.resetForm()};q(()=>{r()});const O={loading:d,tableData:e,dialogVisible:h,detailVisible:y,isEdit:s,dishFormRef:i,currentDish:g,selectedRows:_,pagination:b,searchParams:x,formData:v,columns:C,searchFields:A,dialogTitle:n,getCategoryType:k,loadData:r,handleSearch:T,handleReset:F,handleCurrentChange:K,handleSizeChange:Q,handleStatusChange:X,handleCreate:Z,handleView:$,handleEdit:ee,handleCopy:le,handleDelete:ae,handleBatchDelete:te,handleSelectionChange:oe,handleBatchImport:ne,handleExport:ie,handleSubmit:re,handleDialogClose:se,ref:V,reactive:B,computed:P,onMounted:q,get ElMessage(){return c},get ElMessageBox(){return Y},get Plus(){return W},get View(){return xe},get Edit(){return Ve},get Delete(){return Ce},get Upload(){return ye},get Download(){return ve},get Picture(){return be},get CopyDocument(){return he},CustomTable:ke,DishForm:Ue,get dishApi(){return M},get formatTime(){return G}};return Object.defineProperty(O,"__isScriptSetup",{enumerable:!1,value:!0}),O}},Be={class:"dish-management"},Pe={class:"image-slot",style:{width:"60px",height:"60px","border-radius":"8px",background:"#f5f7fa",display:"flex","align-items":"center","justify-content":"center",color:"#909399"}},Te={key:0,class:"dish-detail"},Fe={class:"detail-header"},je={style:{width:"200px",height:"150px","border-radius":"8px",background:"#f5f7fa",display:"flex","align-items":"center","justify-content":"center",color:"#909399"}},Oe={class:"detail-info"},Re={class:"category"},Ie={class:"status"},Le={class:"detail-content"};function Ne(p,a,d,e,h,y){const s=f("el-icon"),i=f("el-button"),g=f("el-image"),_=f("el-tag"),b=f("el-switch"),x=f("el-popconfirm"),v=f("el-dialog"),C=f("el-descriptions-item"),A=f("el-descriptions");return E(),z("div",Be,[l(e.CustomTable,{title:"菜品管理",data:e.tableData,columns:e.columns,loading:e.loading,pagination:e.pagination,"show-search":!0,"show-selection":!0,"search-fields":e.searchFields,onSearch:e.handleSearch,onReset:e.handleReset,onCurrentChange:e.handleCurrentChange,onSizeChange:e.handleSizeChange,onSelectionChange:e.handleSelectionChange},{toolbar:t(()=>[l(i,{type:"primary",onClick:e.handleCreate},{default:t(()=>[l(s,null,{default:t(()=>[l(e.Plus)]),_:1}),a[2]||(a[2]=m(" 新增菜品 ",-1))]),_:1,__:[2]}),l(i,{type:"danger",disabled:e.selectedRows.length===0,onClick:e.handleBatchDelete},{default:t(()=>[l(s,null,{default:t(()=>[l(e.Delete)]),_:1}),a[3]||(a[3]=m(" 批量删除 ",-1))]),_:1,__:[3]},8,["disabled"]),l(i,{type:"success",onClick:e.handleBatchImport},{default:t(()=>[l(s,null,{default:t(()=>[l(e.Upload)]),_:1}),a[4]||(a[4]=m(" 批量导入 ",-1))]),_:1,__:[4]}),l(i,{type:"info",onClick:e.handleExport},{default:t(()=>[l(s,null,{default:t(()=>[l(e.Download)]),_:1}),a[5]||(a[5]=m(" 导出数据 ",-1))]),_:1,__:[5]})]),image:t(({row:n})=>[l(g,{src:n.image,alt:n.name,style:{width:"60px",height:"60px","border-radius":"8px"},fit:"cover","preview-src-list":n.image?[n.image]:[],"preview-teleported":!0},{error:t(()=>[w("div",Pe,[l(s,{size:"24"},{default:t(()=>[l(e.Picture)]),_:1})])]),_:2},1032,["src","alt","preview-src-list"])]),category:t(({row:n})=>[l(_,{type:e.getCategoryType(n.category),size:"small"},{default:t(()=>[m(D(n.category),1)]),_:2},1032,["type"])]),isAvailable:t(({row:n})=>[l(b,{modelValue:n.isAvailable,"onUpdate:modelValue":k=>n.isAvailable=k,onChange:k=>e.handleStatusChange(n),loading:n.statusLoading},null,8,["modelValue","onUpdate:modelValue","onChange","loading"])]),operation:t(({row:n})=>[l(i,{size:"small",type:"primary",link:"",onClick:k=>e.handleView(n)},{default:t(()=>[l(s,null,{default:t(()=>[l(e.View)]),_:1}),a[6]||(a[6]=m(" 查看 ",-1))]),_:2,__:[6]},1032,["onClick"]),l(i,{size:"small",type:"warning",link:"",onClick:k=>e.handleEdit(n)},{default:t(()=>[l(s,null,{default:t(()=>[l(e.Edit)]),_:1}),a[7]||(a[7]=m(" 编辑 ",-1))]),_:2,__:[7]},1032,["onClick"]),l(i,{size:"small",type:"success",link:"",onClick:k=>e.handleCopy(n)},{default:t(()=>[l(s,null,{default:t(()=>[l(e.CopyDocument)]),_:1}),a[8]||(a[8]=m(" 复制 ",-1))]),_:2,__:[8]},1032,["onClick"]),l(x,{title:"确定要删除这个菜品吗？",onConfirm:k=>e.handleDelete(n)},{reference:t(()=>[l(i,{size:"small",type:"danger",link:""},{default:t(()=>[l(s,null,{default:t(()=>[l(e.Delete)]),_:1}),a[9]||(a[9]=m(" 删除 ",-1))]),_:1,__:[9]})]),_:2},1032,["onConfirm"])]),_:1},8,["data","loading","pagination"]),j(" 菜品表单对话框 "),l(v,{modelValue:e.dialogVisible,"onUpdate:modelValue":a[0]||(a[0]=n=>e.dialogVisible=n),title:e.dialogTitle,width:"800px","close-on-click-modal":!1,onClose:e.handleDialogClose},{default:t(()=>[l(e.DishForm,{ref:"dishFormRef","form-data":e.formData,"is-edit":e.isEdit,onSubmit:e.handleSubmit,onCancel:e.handleDialogClose},null,8,["form-data","is-edit"])]),_:1},8,["modelValue","title"]),j(" 菜品详情对话框 "),l(v,{modelValue:e.detailVisible,"onUpdate:modelValue":a[1]||(a[1]=n=>e.detailVisible=n),title:"菜品详情",width:"600px"},{default:t(()=>[e.currentDish?(E(),z("div",Te,[w("div",Fe,[l(g,{src:e.currentDish.image,alt:e.currentDish.name,style:{width:"200px",height:"150px","border-radius":"8px"},fit:"cover"},{error:t(()=>[w("div",je,[l(s,{size:"48"},{default:t(()=>[l(e.Picture)]),_:1})])]),_:1},8,["src","alt"]),w("div",Oe,[w("h3",null,D(e.currentDish.name),1),w("p",Re,"分类："+D(e.currentDish.category),1),w("p",Ie,[a[10]||(a[10]=m(" 状态： ",-1)),l(_,{type:e.currentDish.isAvailable?"success":"danger",size:"small"},{default:t(()=>[m(D(e.currentDish.isAvailable?"可用":"不可用"),1)]),_:1},8,["type"])])])]),w("div",Le,[l(A,{column:1,border:""},{default:t(()=>[l(C,{label:"菜品描述"},{default:t(()=>[m(D(e.currentDish.description||"暂无描述"),1)]),_:1}),l(C,{label:"食材"},{default:t(()=>[m(D(e.currentDish.ingredients||"暂无食材信息"),1)]),_:1}),l(C,{label:"制作方法"},{default:t(()=>[m(D(e.currentDish.cookingMethod||"暂无制作方法"),1)]),_:1}),l(C,{label:"创建时间"},{default:t(()=>[m(D(e.formatTime(e.currentDish.createdAt)),1)]),_:1}),l(C,{label:"更新时间"},{default:t(()=>[m(D(e.formatTime(e.currentDish.updatedAt)),1)]),_:1})]),_:1})])])):j("v-if",!0)]),_:1},8,["modelValue"])])}const We=J(ze,[["render",Ne],["__scopeId","data-v-167e75fc"],["__file","E:/wx-nan/webs/admin/src/views/menu/dishes.vue"]]);export{We as default};
