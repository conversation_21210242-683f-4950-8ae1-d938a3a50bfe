const prisma = require('../utils/prisma');
const {success, error} = require('../utils/response');
const notificationService = require('../services/notificationService');
const {canAccessUserResource} = require('../middlewares/auth');

/**
 * 获取当前用户的通知列表（只显示关联用户的菜单和订单通知）
 * @route GET /api/notifications
 */
const getNotifications = async (req, res) => {
  try {
    const {page = 1, limit = 20, type, read, category, homeFilter} = req.query;
    const userId = req.user.id;

    // 如果是首页通知过滤，只显示关联用户的菜单和订单通知
    if (homeFilter === 'true') {
      return getHomeNotifications(req, res);
    }

    // 构建查询条件
    const where = {userId};

    // 根据分类筛选通知
    if (category === 'system') {
      // 系统通知：菜单相关、订单相关、系统维护等
      where.type = {
        in: [
          'menu_published',
          'menu_updated',
          'order_created',
          'order_updated',
          'system_maintenance',
          'system_announcement'
        ]
      };
    } else if (category === 'family') {
      // 家庭通知：家庭留言、关联申请等
      where.type = {
        in: [
          'family_message',
          'connection_request',
          'connection_accepted',
          'family_announcement'
        ]
      };
    } else if (type) {
      where.type = type;
    }

    if (read !== undefined) {
      where.read = read === 'true';
    }

    // 分页查询
    const notifications = await prisma.notification.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        sender: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      skip: (parseInt(page) - 1) * parseInt(limit),
      take: parseInt(limit)
    });

    // 获取总数
    const total = await prisma.notification.count({where});

    // 获取未读数量（分类统计）
    const systemUnreadCount = await prisma.notification.count({
      where: {
        userId,
        read: false,
        type: {
          in: [
            'menu_published',
            'menu_updated',
            'order_created',
            'order_updated',
            'system_maintenance',
            'system_announcement'
          ]
        }
      }
    });

    const familyUnreadCount = await prisma.notification.count({
      where: {
        userId,
        read: false,
        type: {
          in: [
            'family_message',
            'connection_request',
            'connection_accepted',
            'family_announcement'
          ]
        }
      }
    });

    return success(res, {
      notifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      },
      unreadCount: {
        total: systemUnreadCount + familyUnreadCount,
        system: systemUnreadCount,
        family: familyUnreadCount
      }
    });
  } catch (err) {
    console.error('Get notifications error:', err);
    return error(res, 'Failed to get notifications', 500);
  }
};

/**
 * 获取指定通知
 * @route GET /api/notifications/:id
 */
const getNotificationById = async (req, res) => {
  try {
    const {id} = req.params;

    const notification = await prisma.notification.findUnique({
      where: {id},
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    if (!notification) {
      return error(res, 'Notification not found', 404);
    }

    return success(res, notification);
  } catch (err) {
    console.error('Get notification error:', err);
    return error(res, 'Failed to get notification', 500);
  }
};

/**
 * 创建通知
 * @route POST /api/notifications
 */
const createNotification = async (req, res) => {
  try {
    const {title, content, type, targetUserId, data} = req.body;
    const senderId = req.user.id;

    if (!content) {
      return error(res, 'Content is required', 400);
    }

    // 确定目标用户ID
    const userId = targetUserId || senderId;

    // 创建通知数据
    const notificationData = {
      title: title || '新通知',
      content,
      type: type || 'general',
      userId,
      senderId,
      read: false
    };

    // 如果有额外数据，添加到通知中
    if (data) {
      notificationData.data = JSON.stringify(data);
    }

    // 创建通知
    const notification = await prisma.notification.create({
      data: notificationData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        sender: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    // 如果是菜单推送，记录推送日志
    if (type === 'menu_push') {
      console.log(`菜单推送成功: ${senderId} -> ${userId}, 内容: ${content}`);
    }

    return success(res, notification, 'Notification created successfully', 201);
  } catch (err) {
    console.error('Create notification error:', err);
    return error(res, 'Failed to create notification', 500);
  }
};

/**
 * 更新通知
 * @route PUT /api/notifications/:id
 */
const updateNotification = async (req, res) => {
  try {
    const {id} = req.params;
    const {content, read} = req.body;

    // 检查通知是否存在
    const existingNotification = await prisma.notification.findUnique({
      where: {id}
    });

    if (!existingNotification) {
      return error(res, 'Notification not found', 404);
    }

    // 检查权限（只有管理员或通知创建者可以更新内容）
    if (
      content &&
      req.user.role !== 'admin' &&
      req.user.id !== existingNotification.userId
    ) {
      return error(res, 'Permission denied', 403);
    }

    // 准备更新数据
    const updateData = {};

    if (content) updateData.content = content;
    if (read !== undefined) updateData.read = read;

    // 更新通知
    const updatedNotification = await prisma.notification.update({
      where: {id},
      data: updateData,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      }
    });

    return success(
      res,
      updatedNotification,
      'Notification updated successfully'
    );
  } catch (err) {
    console.error('Update notification error:', err);
    return error(res, 'Failed to update notification', 500);
  }
};

/**
 * 删除通知
 * @route DELETE /api/notifications/:id
 */
const deleteNotification = async (req, res) => {
  try {
    const {id} = req.params;

    // 检查通知是否存在
    const existingNotification = await prisma.notification.findUnique({
      where: {id}
    });

    if (!existingNotification) {
      return error(res, 'Notification not found', 404);
    }

    // 检查权限（只有管理员或通知创建者可以删除）
    if (
      req.user.role !== 'admin' &&
      req.user.id !== existingNotification.userId
    ) {
      return error(res, 'Permission denied', 403);
    }

    // 删除通知
    await prisma.notification.delete({
      where: {id}
    });

    return success(res, null, 'Notification deleted successfully');
  } catch (err) {
    console.error('Delete notification error:', err);
    return error(res, 'Failed to delete notification', 500);
  }
};

/**
 * 标记通知为已读
 * @route PUT /api/notifications/:id/read
 */
const markAsRead = async (req, res) => {
  try {
    const {id} = req.params;
    const userId = req.user.id;

    const result = await notificationService.markAsRead(id, userId);

    if (result.count === 0) {
      return error(res, 'Notification not found or access denied', 404);
    }

    return success(res, null, 'Notification marked as read');
  } catch (err) {
    console.error('Mark notification as read error:', err);
    return error(res, 'Failed to mark notification as read', 500);
  }
};

/**
 * 标记所有通知为已读
 * @route PUT /api/notifications/read-all
 */
const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;

    const result = await notificationService.markAllAsRead(userId);

    return success(
      res,
      {count: result.count},
      'All notifications marked as read'
    );
  } catch (err) {
    console.error('Mark all notifications as read error:', err);
    return error(res, 'Failed to mark all notifications as read', 500);
  }
};

/**
 * 获取未读通知数量
 * @route GET /api/notifications/unread-count
 */
const getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;

    const count = await notificationService.getUnreadCount(userId);

    return success(res, {count});
  } catch (err) {
    console.error('Get unread count error:', err);
    return error(res, 'Failed to get unread count', 500);
  }
};

/**
 * 发送家庭通知（仅家庭管理员和管理员）
 * @route POST /api/notifications/family
 */
const sendFamilyNotification = async (req, res) => {
  try {
    const {content} = req.body;
    const sender = req.user;

    if (!content) {
      return error(res, 'Content is required', 400);
    }

    // 检查权限
    if (!['admin', 'family_head'].includes(sender.role)) {
      return error(
        res,
        'Permission denied. Only family heads and admins can send family notifications',
        403
      );
    }

    const notifications = await notificationService.pushToFamily(
      content,
      'family_announcement',
      sender.id
    );

    return success(
      res,
      {count: notifications.length},
      'Family notification sent successfully',
      201
    );
  } catch (err) {
    console.error('Send family notification error:', err);
    return error(res, 'Failed to send family notification', 500);
  }
};

/**
 * 发送系统维护通知（仅管理员）
 * @route POST /api/notifications/system
 */
const sendSystemNotification = async (req, res) => {
  try {
    const {content} = req.body;
    const sender = req.user;

    if (!content) {
      return error(res, 'Content is required', 400);
    }

    // 检查权限
    if (sender.role !== 'admin') {
      return error(
        res,
        'Permission denied. Only admins can send system notifications',
        403
      );
    }

    const notifications = await notificationService.notifySystemMaintenance(
      content
    );

    return success(
      res,
      {count: notifications.length},
      'System notification sent successfully',
      201
    );
  } catch (err) {
    console.error('Send system notification error:', err);
    return error(res, 'Failed to send system notification', 500);
  }
};

/**
 * 获取首页通知（只显示关联用户的菜单和订单通知）
 * @route GET /api/notifications?homeFilter=true
 */
const getHomeNotifications = async (req, res) => {
  try {
    const {limit = 5} = req.query;
    const userId = req.user.id;

    // 1. 获取当前用户的关联用户ID列表
    const connections = await prisma.userConnection.findMany({
      where: {
        OR: [
          {senderId: userId, status: 'accepted'},
          {receiverId: userId, status: 'accepted'}
        ]
      }
    });

    // 提取关联用户ID（包括自己）
    const connectedUserIds = [userId]; // 包括自己
    connections.forEach(conn => {
      const otherUserId =
        conn.senderId === userId ? conn.receiverId : conn.senderId;
      if (!connectedUserIds.includes(otherUserId)) {
        connectedUserIds.push(otherUserId);
      }
    });

    // 2. 查询关联用户的菜单和订单通知
    const where = {
      userId, // 通知的接收者是当前用户
      type: {
        in: [
          'menu_push', // 菜单发布通知
          'menu_update', // 菜单更新通知
          'new_dish', // 新菜品通知
          'new_order', // 新订单通知
          'order_push', // 订单推送通知
          'system' // 系统通知也显示
        ]
      },
      OR: [
        // 系统通知（没有发送者）
        {senderId: null},
        // 关联用户发送的通知
        {senderId: {in: connectedUserIds}}
      ]
    };

    // 3. 查询通知
    const notifications = await prisma.notification.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        },
        sender: {
          select: {
            id: true,
            name: true,
            avatar: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: parseInt(limit)
    });

    // 4. 格式化通知数据
    const formattedNotifications = notifications.map(notification => ({
      id: notification.id,
      title: notification.title,
      content: notification.content,
      type: notification.type,
      read: notification.read,
      createdAt: notification.createdAt,
      updatedAt: notification.updatedAt,
      user: notification.user,
      sender: notification.sender,
      data: notification.data ? JSON.parse(notification.data) : null
    }));

    return success(res, formattedNotifications);
  } catch (err) {
    console.error('Get home notifications error:', err);
    return error(res, 'Failed to get home notifications', 500);
  }
};

module.exports = {
  getNotifications,
  getHomeNotifications,
  getNotificationById,
  createNotification,
  updateNotification,
  deleteNotification,
  markAsRead,
  markAllAsRead,
  getUnreadCount,
  sendFamilyNotification,
  sendSystemNotification
};
