<template>
  <div class="system-config-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h2 class="page-title">系统配置</h2>
      <p class="page-subtitle">查看和管理系统配置信息</p>
    </div>

    <div class="space-y-6">
      <!-- 服务器配置 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            服务器配置
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            当前连接的服务器信息
          </p>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <ServerIcon class="h-6 w-6 text-blue-600" />
                </div>
                <div class="ml-3">
                  <p
                    class="text-sm font-medium text-blue-900 dark:text-blue-200"
                  >
                    服务器环境
                  </p>
                  <p
                    class="text-lg font-semibold text-blue-600 dark:text-blue-400"
                  >
                    {{ envConfig.server.name }}
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <LinkIcon class="h-6 w-6 text-green-600" />
                </div>
                <div class="ml-3">
                  <p
                    class="text-sm font-medium text-green-900 dark:text-green-200"
                  >
                    API 地址
                  </p>
                  <p
                    class="text-sm font-mono text-green-600 dark:text-green-400 break-all"
                  >
                    {{ envConfig.server.baseURL }}
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <ClockIcon class="h-6 w-6 text-yellow-600" />
                </div>
                <div class="ml-3">
                  <p
                    class="text-sm font-medium text-yellow-900 dark:text-yellow-200"
                  >
                    请求超时
                  </p>
                  <p
                    class="text-lg font-semibold text-yellow-600 dark:text-yellow-400"
                  >
                    {{ envConfig.server.timeout }}ms
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 应用信息 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            应用信息
          </h3>
        </div>
        <div class="card-body">
          <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                应用名称
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.app.name }}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                版本号
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.app.version }}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                描述
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.app.description }}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                版权信息
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.app.copyright }}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- 功能开关 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            功能开关
          </h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            当前环境下的功能启用状态
          </p>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="(enabled, feature) in envConfig.features"
              :key="feature"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <span
                class="text-sm font-medium text-gray-900 dark:text-white capitalize"
              >
                {{ getFeatureName(feature) }}
              </span>
              <span
                :class="enabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              >
                {{ enabled ? '启用' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 存储配置 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            存储配置
          </h3>
        </div>
        <div class="card-body">
          <dl class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div v-for="(value, key) in envConfig.storage" :key="key">
              <dt
                class="text-sm font-medium text-gray-500 dark:text-gray-400 capitalize"
              >
                {{ getStorageName(key) }}
              </dt>
              <dd class="mt-1 text-sm font-mono text-gray-900 dark:text-white">
                {{ value }}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- 上传配置 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            上传配置
          </h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                最大文件大小
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.upload.maxSize }} MB
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                最大上传数量
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.upload.maxCount }} 个
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                多文件上传
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.upload.multiple ? '支持' : '不支持' }}
              </dd>
            </div>
          </div>

          <div class="mt-6">
            <dt
              class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3"
            >
              支持的文件类型
            </dt>
            <div class="space-y-2">
              <div
                v-for="(types, category) in envConfig.upload.allowedTypes"
                :key="category"
                class="flex items-center"
              >
                <span
                  class="text-sm font-medium text-gray-700 dark:text-gray-300 w-20 capitalize"
                  >{{ category }}:</span
                >
                <div class="flex flex-wrap gap-1">
                  <span
                    v-for="type in types"
                    :key="type"
                    class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    .{{ type }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全配置 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            安全配置
          </h3>
        </div>
        <div class="card-body">
          <dl class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                Token 过期时间
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.security.tokenExpiry }} 小时
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                密码长度范围
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.security.passwordMinLength




                }}-{{ envConfig.security.passwordMaxLength }} 位
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                最大登录失败次数
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.security.maxLoginAttempts }} 次
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">
                账户锁定时间
              </dt>
              <dd class="mt-1 text-sm text-gray-900 dark:text-white">
                {{ envConfig.security.lockoutDuration }} 分钟
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  ServerIcon,
  LinkIcon,
  ClockIcon
} from '@heroicons/vue/24/outline'
import { getEnvConfig } from '@/config/env'

// 获取环境配置
const envConfig = getEnvConfig()

// 面包屑导航
const breadcrumbItems = [
  { text: '首页', to: '/' },
  { text: '系统管理', to: '/system' },
  { text: '系统配置' }
]

// 功能名称映射
const featureNames = {
  debug: '调试模式',
  performance: '性能监控',
  errorReporting: '错误上报',
  hotReload: '热更新',
  devtools: '开发工具',
  mock: 'Mock 数据',
  cache: '缓存',
  compression: '压缩'
}

// 存储名称映射
const storageNames = {
  tokenKey: 'Token 存储键',
  userKey: '用户信息存储键',
  settingsKey: '设置存储键',
  themeKey: '主题存储键',
  localeKey: '语言存储键'
}

const getFeatureName = (feature) => {
  return featureNames[feature] || feature
}

const getStorageName = (key) => {
  return storageNames[key] || key
}
</script>

<style scoped>
.system-config-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
}

.card-body {
  padding: 20px 24px;
}

.space-y-6 > * + * {
  margin-top: 24px;
}
</style>
