<template>
  <div
    ref="chartRef"
    :style="{ width: width, height: height }"
    class="echart-container"
    v-loading="loading"
    v-motion
    :initial="{ opacity: 0, y: 50 }"
    :enter="{ opacity: 1, y: 0, transition: { duration: 800, delay: 200 } }"
  />
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  option: {
    type: Object,
    required: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '400px'
  },
  theme: {
    type: String,
    default: 'default'
  },
  loading: {
    type: Boolean,
    default: false
  },
  autoResize: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['chart-ready', 'chart-click', 'chart-hover'])

const chartRef = ref()
let chartInstance = null

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return

  await nextTick()
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose()
  }

  // 创建新实例
  chartInstance = echarts.init(chartRef.value, props.theme)
  
  // 设置配置项
  chartInstance.setOption(props.option, true)
  
  // 绑定事件
  chartInstance.on('click', (params) => {
    emit('chart-click', params)
  })
  
  chartInstance.on('mouseover', (params) => {
    emit('chart-hover', params)
  })
  
  // 自动调整大小
  if (props.autoResize) {
    window.addEventListener('resize', handleResize)
  }
  
  emit('chart-ready', chartInstance)
}

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 更新图表配置
const updateChart = () => {
  if (chartInstance && props.option) {
    chartInstance.setOption(props.option, true)
  }
}

// 显示加载动画
const showLoading = () => {
  if (chartInstance) {
    chartInstance.showLoading('default', {
      text: '加载中...',
      color: '#409eff',
      textColor: '#000',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      zlevel: 0
    })
  }
}

// 隐藏加载动画
const hideLoading = () => {
  if (chartInstance) {
    chartInstance.hideLoading()
  }
}

// 监听配置变化
watch(() => props.option, updateChart, { deep: true })

// 监听加载状态
watch(() => props.loading, (newVal) => {
  if (newVal) {
    showLoading()
  } else {
    hideLoading()
  }
})

// 监听主题变化
watch(() => props.theme, () => {
  initChart()
})

onMounted(() => {
  initChart()
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
  if (props.autoResize) {
    window.removeEventListener('resize', handleResize)
  }
})

// 暴露方法
defineExpose({
  getInstance: () => chartInstance,
  resize: handleResize,
  showLoading,
  hideLoading,
  updateChart
})
</script>

<style scoped lang="scss">
.echart-container {
  position: relative;
  
  &:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }
}
</style>
