import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

// 配置 dayjs
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

/**
 * 格式化日期
 * @param {string|Date} date - 日期
 * @param {string} format - 格式化模板
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param {string|Date} date - 日期
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return ''
  return dayjs(date).fromNow()
}

/**
 * 获取日期范围
 * @param {number} days - 天数
 * @returns {Array} [开始日期, 结束日期]
 */
export function getDateRange(days = 7) {
  const end = dayjs()
  const start = end.subtract(days - 1, 'day')
  return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]
}

/**
 * 获取本周日期范围
 * @returns {Array} [开始日期, 结束日期]
 */
export function getThisWeek() {
  const start = dayjs().startOf('week')
  const end = dayjs().endOf('week')
  return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]
}

/**
 * 获取本月日期范围
 * @returns {Array} [开始日期, 结束日期]
 */
export function getThisMonth() {
  const start = dayjs().startOf('month')
  const end = dayjs().endOf('month')
  return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]
}

/**
 * 获取上月日期范围
 * @returns {Array} [开始日期, 结束日期]
 */
export function getLastMonth() {
  const start = dayjs().subtract(1, 'month').startOf('month')
  const end = dayjs().subtract(1, 'month').endOf('month')
  return [start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD')]
}

/**
 * 判断是否为今天
 * @param {string|Date} date - 日期
 * @returns {boolean}
 */
export function isToday(date) {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 判断是否为昨天
 * @param {string|Date} date - 日期
 * @returns {boolean}
 */
export function isYesterday(date) {
  if (!date) return false
  return dayjs(date).isSame(dayjs().subtract(1, 'day'), 'day')
}

/**
 * 判断是否为本周
 * @param {string|Date} date - 日期
 * @returns {boolean}
 */
export function isThisWeek(date) {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'week')
}

/**
 * 判断是否为本月
 * @param {string|Date} date - 日期
 * @returns {boolean}
 */
export function isThisMonth(date) {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'month')
}

/**
 * 计算两个日期之间的天数差
 * @param {string|Date} date1 - 日期1
 * @param {string|Date} date2 - 日期2
 * @returns {number} 天数差
 */
export function daysBetween(date1, date2) {
  if (!date1 || !date2) return 0
  return dayjs(date2).diff(dayjs(date1), 'day')
}

/**
 * 获取指定日期的开始时间
 * @param {string|Date} date - 日期
 * @returns {string} 开始时间
 */
export function startOfDay(date) {
  if (!date) return ''
  return dayjs(date).startOf('day').format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 获取指定日期的结束时间
 * @param {string|Date} date - 日期
 * @returns {string} 结束时间
 */
export function endOfDay(date) {
  if (!date) return ''
  return dayjs(date).endOf('day').format('YYYY-MM-DD HH:mm:ss')
}

/**
 * 生成日期序列
 * @param {string|Date} start - 开始日期
 * @param {string|Date} end - 结束日期
 * @param {string} unit - 单位 (day, week, month)
 * @returns {Array} 日期序列
 */
export function generateDateSeries(start, end, unit = 'day') {
  const dates = []
  let current = dayjs(start)
  const endDate = dayjs(end)
  
  while (current.isBefore(endDate) || current.isSame(endDate)) {
    dates.push(current.format('YYYY-MM-DD'))
    current = current.add(1, unit)
  }
  
  return dates
}

/**
 * 格式化时间段
 * @param {string|Date} start - 开始时间
 * @param {string|Date} end - 结束时间
 * @returns {string} 格式化的时间段
 */
export function formatTimeRange(start, end) {
  if (!start || !end) return ''
  
  const startDate = dayjs(start)
  const endDate = dayjs(end)
  
  if (startDate.isSame(endDate, 'day')) {
    return `${startDate.format('YYYY-MM-DD')} ${startDate.format('HH:mm')} - ${endDate.format('HH:mm')}`
  }
  
  return `${startDate.format('YYYY-MM-DD HH:mm')} - ${endDate.format('YYYY-MM-DD HH:mm')}`
}

/**
 * 获取友好的时间描述
 * @param {string|Date} date - 日期
 * @returns {string} 友好的时间描述
 */
export function getFriendlyTime(date) {
  if (!date) return ''
  
  const target = dayjs(date)
  const now = dayjs()
  
  if (target.isSame(now, 'day')) {
    return `今天 ${target.format('HH:mm')}`
  }
  
  if (target.isSame(now.subtract(1, 'day'), 'day')) {
    return `昨天 ${target.format('HH:mm')}`
  }
  
  if (target.isSame(now, 'year')) {
    return target.format('MM-DD HH:mm')
  }
  
  return target.format('YYYY-MM-DD HH:mm')
}

export default {
  formatDate,
  formatRelativeTime,
  getDateRange,
  getThisWeek,
  getThisMonth,
  getLastMonth,
  isToday,
  isYesterday,
  isThisWeek,
  isThisMonth,
  daysBetween,
  startOfDay,
  endOfDay,
  generateDateSeries,
  formatTimeRange,
  getFriendlyTime
}
