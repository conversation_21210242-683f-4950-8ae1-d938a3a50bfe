var Q=Object.defineProperty;var O=Object.getOwnPropertySymbols;var W=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable;var F=(p,l,s)=>l in p?Q(p,l,{enumerable:!0,configurable:!0,writable:!0,value:s}):p[l]=s,N=(p,l)=>{for(var s in l||(l={}))W.call(l,s)&&F(p,s,l[s]);if(O)for(var s of O(l))X.call(l,s)&&F(p,s,l[s]);return p};var k=(p,l,s)=>new Promise((e,m)=>{var v=r=>{try{i(s.next(r))}catch(d){m(d)}},c=r=>{try{i(s.throw(r))}catch(d){m(d)}},i=r=>r.done?e(r.value):Promise.resolve(r.value).then(v,c);i((s=s.apply(p,l)).next())});import{_ as Y,r as g,Y as $,p as z,w as t,Z as ee,d as a,b as R,a as D,m as y,t as x,g as C,h as B,F as M,E as f,n as ae,l as T,c as te,o as q,M as le,R as oe,k as P}from"./index-Cy8N1eGd.js";import{C as se}from"./CustomTable-DWghEWMD.js";import{u as E}from"./user-DGiv-qs1.js";import{f as I}from"./common-CIDIMsc8.js";const ne={__name:"UserDialog",props:{modelValue:{type:Boolean,default:!1},userId:{type:[String,Number],default:null},isEdit:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup(p,{expose:l,emit:s}){l();const e=p,m=s,v=C(!1),c=C(!1),i=C(!1),r=C(),d=B({id:null,name:"",phone:"",role:"user",status:1,avatar:"",createdAt:null,lastLoginAt:null,orderCount:0,totalAmount:0}),V={name:[{required:!0,message:"请输入用户姓名",trigger:"blur"},{min:2,max:20,message:"姓名长度在 2 到 20 个字符",trigger:"blur"}],phone:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],role:[{required:!0,message:"请选择用户角色",trigger:"change"}],status:[{required:!0,message:"请选择用户状态",trigger:"change"}]};M(()=>e.modelValue,u=>{v.value=u,u&&e.userId&&o()}),M(v,u=>{m("update:modelValue",u),u||w()});const o=()=>k(this,null,function*(){if(e.userId){c.value=!0;try{const u=yield E.getUserDetail(e.userId);u.code===200?Object.assign(d,u.data):f.error(u.message||"获取用户信息失败")}catch(u){console.error("获取用户信息失败:",u),f.error("获取用户信息失败")}finally{c.value=!1}}}),h=()=>k(this,null,function*(){if(r.value)try{yield r.value.validate(),i.value=!0;const u=yield E.updateUser(d.id,{name:d.name,phone:d.phone,role:d.role,status:d.status});u.code===200?(f.success("用户信息更新成功"),m("success"),U()):f.error(u.message||"更新失败")}catch(u){console.error("更新用户信息失败:",u),f.error("更新失败")}finally{i.value=!1}}),U=()=>{v.value=!1},w=()=>{r.value&&r.value.resetFields(),Object.assign(d,{id:null,name:"",phone:"",role:"user",status:1,avatar:"",createdAt:null,lastLoginAt:null,orderCount:0,totalAmount:0})},S={props:e,emit:m,visible:v,loading:c,submitting:i,formRef:r,formData:d,rules:V,loadUserData:o,handleSubmit:h,handleClose:U,resetForm:w,ref:C,reactive:B,watch:M,nextTick:ae,get ElMessage(){return f},get userApi(){return E},get formatTime(){return I}};return Object.defineProperty(S,"__isScriptSetup",{enumerable:!1,value:!0}),S}},re={class:"avatar-section"},ie={class:"avatar-info"},de={class:"dialog-footer"};function ue(p,l,s,e,m,v){const c=g("el-input"),i=g("el-form-item"),r=g("el-col"),d=g("el-row"),V=g("el-option"),o=g("el-select"),h=g("el-avatar"),U=g("el-button"),w=g("el-form"),S=g("el-dialog"),u=$("loading");return T(),z(S,{modelValue:e.visible,"onUpdate:modelValue":l[4]||(l[4]=b=>e.visible=b),title:s.isEdit?"编辑用户":"用户详情",width:"600px","close-on-click-modal":!1,onClose:e.handleClose},{footer:t(()=>[D("div",de,[a(U,{onClick:e.handleClose},{default:t(()=>l[6]||(l[6]=[y("取消",-1)])),_:1,__:[6]}),s.isEdit?(T(),z(U,{key:0,type:"primary",onClick:e.handleSubmit,loading:e.submitting},{default:t(()=>l[7]||(l[7]=[y(" 保存 ",-1)])),_:1,__:[7]},8,["loading"])):R("v-if",!0)])]),default:t(()=>[ee((T(),z(w,{ref:"formRef",model:e.formData,rules:e.rules,"label-width":"100px"},{default:t(()=>[a(d,{gutter:20},{default:t(()=>[a(r,{span:12},{default:t(()=>[a(i,{label:"用户姓名",prop:"name"},{default:t(()=>[a(c,{modelValue:e.formData.name,"onUpdate:modelValue":l[0]||(l[0]=b=>e.formData.name=b),placeholder:"请输入用户姓名",disabled:!s.isEdit},null,8,["modelValue","disabled"])]),_:1})]),_:1}),a(r,{span:12},{default:t(()=>[a(i,{label:"手机号",prop:"phone"},{default:t(()=>[a(c,{modelValue:e.formData.phone,"onUpdate:modelValue":l[1]||(l[1]=b=>e.formData.phone=b),placeholder:"请输入手机号",disabled:!s.isEdit},null,8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),a(d,{gutter:20},{default:t(()=>[a(r,{span:12},{default:t(()=>[a(i,{label:"用户角色",prop:"role"},{default:t(()=>[a(o,{modelValue:e.formData.role,"onUpdate:modelValue":l[2]||(l[2]=b=>e.formData.role=b),placeholder:"请选择用户角色",disabled:!s.isEdit,style:{width:"100%"}},{default:t(()=>[a(V,{label:"普通用户",value:"user"}),a(V,{label:"管理员",value:"admin"})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1}),a(r,{span:12},{default:t(()=>[a(i,{label:"用户状态",prop:"status"},{default:t(()=>[a(o,{modelValue:e.formData.status,"onUpdate:modelValue":l[3]||(l[3]=b=>e.formData.status=b),placeholder:"请选择用户状态",disabled:!s.isEdit,style:{width:"100%"}},{default:t(()=>[a(V,{label:"正常",value:1}),a(V,{label:"禁用",value:0})]),_:1},8,["modelValue","disabled"])]),_:1})]),_:1})]),_:1}),a(d,{gutter:20},{default:t(()=>[a(r,{span:12},{default:t(()=>[a(i,{label:"注册时间"},{default:t(()=>[a(c,{value:e.formatTime(e.formData.createdAt),disabled:""},null,8,["value"])]),_:1})]),_:1}),a(r,{span:12},{default:t(()=>[a(i,{label:"最后登录"},{default:t(()=>[a(c,{value:e.formatTime(e.formData.lastLoginAt),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1}),s.isEdit?R("v-if",!0):(T(),z(d,{key:0,gutter:20},{default:t(()=>[a(r,{span:12},{default:t(()=>[a(i,{label:"订单数量"},{default:t(()=>[a(c,{value:e.formData.orderCount||0,disabled:""},null,8,["value"])]),_:1})]),_:1}),a(r,{span:12},{default:t(()=>[a(i,{label:"消费总额"},{default:t(()=>[a(c,{value:"¥"+(e.formData.totalAmount||0),disabled:""},null,8,["value"])]),_:1})]),_:1})]),_:1})),a(i,{label:"用户头像"},{default:t(()=>[D("div",re,[a(h,{src:e.formData.avatar,size:80},{default:t(()=>[y(x(e.formData.name?e.formData.name.charAt(0):"U"),1)]),_:1},8,["src"]),D("div",ie,[D("p",null,x(e.formData.avatar?"已设置头像":"未设置头像"),1),s.isEdit?(T(),z(U,{key:0,size:"small",type:"primary"},{default:t(()=>l[5]||(l[5]=[y(" 更换头像 ",-1)])),_:1,__:[5]})):R("v-if",!0)])])]),_:1})]),_:1},8,["model"])),[[u,e.loading]])]),_:1},8,["modelValue","title"])}const ce=Y(ne,[["render",ue],["__scopeId","data-v-9ff371d6"],["__file","E:/wx-nan/webs/admin/src/views/user/components/UserDialog.vue"]]),me={__name:"index",setup(p,{expose:l}){l();const s=C(!1),e=C([]),m=B({page:1,size:10,total:0}),v=B({name:"",phone:"",status:""}),c=C(!1),i=C(null),r=C(!1),d=[{prop:"user",label:"用户信息",width:200,slot:!0},{prop:"orderCount",label:"订单数量",width:120,slot:!0},{prop:"totalAmount",label:"消费总额",width:120,slot:!0},{prop:"lastLoginTime",label:"最后登录",width:160,slot:!0},{prop:"registerTime",label:"注册时间",width:160,formatter:n=>I(n.registerTime)},{prop:"status",label:"状态",width:100,slot:!0},{label:"操作",width:200,slot:"operation",fixed:"right"}],V=[{prop:"name",label:"用户姓名",type:"input"},{prop:"phone",label:"手机号",type:"input"},{prop:"status",label:"状态",type:"select",options:[{label:"全部",value:""},{label:"正常",value:"1"},{label:"禁用",value:"0"}]}],o=()=>k(this,null,function*(){s.value=!0;try{const n=N({page:m.page,size:m.size},v),_=yield E.getUsers(n);_.code===200?(e.value=_.data.list||[],m.total=_.data.total||0):f.error(_.message||"加载数据失败")}catch(n){console.error("加载用户数据失败:",n),f.error("加载数据失败")}finally{s.value=!1}}),h=n=>{Object.assign(v,n),m.page=1,o()},U=()=>{Object.keys(v).forEach(n=>{v[n]=""}),m.page=1,o()},w=n=>{m.page=n,o()},S=n=>{m.size=n,m.page=1,o()},u=()=>{o(),f.success("数据已刷新")},b=()=>{f.info("导出功能开发中...")},Z=n=>{i.value=n.id,r.value=!1,c.value=!0},G=n=>{i.value=n.id,r.value=!0,c.value=!0},H=()=>{o()},J=n=>k(this,null,function*(){try{const _=yield E.updateUser(n.id,{status:n.status});_.code===200?f.success("用户状态更新成功"):(f.error(_.message||"状态更新失败"),n.status=n.status===1?0:1)}catch(_){console.error("更新用户状态失败:",_),f.error("状态更新失败"),n.status=n.status===1?0:1}}),K=n=>k(this,null,function*(){const _=n.status===1?"禁用":"启用";try{yield P.confirm(`确定要${_}用户 ${n.name} 吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const A=n.status===1?0:1,L=yield E.updateUser(n.id,{status:A});L.code===200?(n.status=A,f.success(`用户${_}成功`)):f.error(L.message||`用户${_}失败`)}catch(A){A!=="cancel"&&(console.error(`用户${_}失败:`,A),f.error(`用户${_}失败`))}});q(()=>{o()});const j={loading:s,tableData:e,pagination:m,searchParams:v,dialogVisible:c,selectedUserId:i,isEditMode:r,columns:d,searchFields:V,loadData:o,handleSearch:h,handleReset:U,handleCurrentChange:w,handleSizeChange:S,handleRefresh:u,handleExport:b,handleViewUser:Z,handleEditUser:G,handleDialogSuccess:H,handleStatusChange:J,handleToggleStatus:K,ref:C,reactive:B,onMounted:q,get ElMessage(){return f},get ElMessageBox(){return P},get Refresh(){return oe},get Download(){return le},CustomTable:se,UserDialog:ce,get userApi(){return E},get formatTime(){return I}};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}},fe={class:"user-management"},_e={class:"user-info"},ge={class:"user-details"},pe={class:"user-name"},ve={class:"user-phone"},he={class:"amount-text"};function be(p,l,s,e,m,v){const c=g("el-icon"),i=g("el-button"),r=g("el-avatar"),d=g("el-switch"),V=g("el-tag");return T(),te("div",fe,[a(e.CustomTable,{title:"用户管理",data:e.tableData,columns:e.columns,loading:e.loading,pagination:e.pagination,"show-search":!0,"search-fields":e.searchFields,onSearch:e.handleSearch,onReset:e.handleReset,onCurrentChange:e.handleCurrentChange,onSizeChange:e.handleSizeChange},{actions:t(()=>[a(i,{onClick:e.handleRefresh},{default:t(()=>[a(c,null,{default:t(()=>[a(e.Refresh)]),_:1}),l[1]||(l[1]=y(" 刷新数据 ",-1))]),_:1,__:[1]}),a(i,{onClick:e.handleExport},{default:t(()=>[a(c,null,{default:t(()=>[a(e.Download)]),_:1}),l[2]||(l[2]=y(" 导出用户 ",-1))]),_:1,__:[2]})]),user:t(({row:o})=>[D("div",_e,[a(r,{src:o.avatar,size:40},{default:t(()=>[y(x(o.name?o.name.charAt(0):"U"),1)]),_:2},1032,["src"]),D("div",ge,[D("div",pe,x(o.name||"未知用户"),1),D("div",ve,x(o.phone||"未知手机号"),1)])])]),status:t(({row:o})=>[a(d,{modelValue:o.status,"onUpdate:modelValue":h=>o.status=h,"active-value":1,"inactive-value":0,"active-text":"正常","inactive-text":"禁用",onChange:h=>e.handleStatusChange(o)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),orderCount:t(({row:o})=>[a(V,{type:"primary"},{default:t(()=>[y(x(o.orderCount)+"单",1)]),_:2},1024)]),totalAmount:t(({row:o})=>[D("span",he,"¥"+x(o.totalAmount),1)]),lastLoginTime:t(({row:o})=>[D("span",null,x(e.formatTime(o.lastLoginTime)),1)]),operation:t(({row:o})=>[a(i,{size:"small",onClick:h=>e.handleViewUser(o)},{default:t(()=>l[3]||(l[3]=[y("查看",-1)])),_:2,__:[3]},1032,["onClick"]),a(i,{size:"small",type:"primary",onClick:h=>e.handleEditUser(o)},{default:t(()=>l[4]||(l[4]=[y("编辑",-1)])),_:2,__:[4]},1032,["onClick"]),a(i,{size:"small",type:o.status===1?"warning":"success",onClick:h=>e.handleToggleStatus(o)},{default:t(()=>[y(x(o.status===1?"禁用":"启用"),1)]),_:2},1032,["type","onClick"])]),_:1},8,["data","loading","pagination"]),R(" 用户详情/编辑对话框 "),a(e.UserDialog,{modelValue:e.dialogVisible,"onUpdate:modelValue":l[0]||(l[0]=o=>e.dialogVisible=o),"user-id":e.selectedUserId,"is-edit":e.isEditMode,onSuccess:e.handleDialogSuccess},null,8,["modelValue","user-id","is-edit"])])}const Ue=Y(me,[["render",be],["__scopeId","data-v-0f4e4cc3"],["__file","E:/wx-nan/webs/admin/src/views/user/index.vue"]]);export{Ue as default};
