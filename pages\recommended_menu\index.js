const {menuApi, orderApi, connectionApi} = require('../../services/api');

Page({
  data: {
    menus: [],
    loading: true,
    refreshing: false,
    selectedMenu: null,
    showOrderModal: false,
    orderItems: [],
    orderRemark: '',
    connectedUsers: [],
    selectedPushUsers: [],
    submitting: false
  },

  onLoad() {
    this.loadRecommendedMenus();
    this.loadConnectedUsers();
  },

  onShow() {
    // 页面显示时刷新数据
    this.loadRecommendedMenus();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({refreshing: true});
    this.loadRecommendedMenus().finally(() => {
      this.setData({refreshing: false});
      wx.stopPullDownRefresh();
    });
  },

  // 加载推荐菜单
  async loadRecommendedMenus() {
    try {
      this.setData({loading: true});

      const res = await menuApi.getRecommendedMenu();

      if (res.code === 200) {
        this.setData({
          menus: res.data || []
        });
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载推荐菜单失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({loading: false});
    }
  },

  // 加载关联用户
  async loadConnectedUsers() {
    try {
      const res = await connectionApi.getMyConnections('accepted');

      if (res.code === 200) {
        this.setData({
          connectedUsers: res.data.map(conn => ({
            id: conn.connectedUser.id,
            name: conn.connectedUser.name,
            avatar: conn.connectedUser.avatar,
            selected: false
          }))
        });
      }
    } catch (error) {
      console.error('加载关联用户失败:', error);
    }
  },

  // 查看菜单详情
  viewMenuDetail(e) {
    const {menu} = e.currentTarget.dataset;

    // 将菜单数据存储到缓存
    wx.setStorageSync('selectedMenuDetail', menu);

    // 跳转到菜单详情页
    wx.navigateTo({
      url: '/pages/menu_detail/index'
    });
  },

  // 点菜
  orderMenu(e) {
    const {menu} = e.currentTarget.dataset;

    // 调试：检查菜单ID
    console.log('🔍 点菜 - 菜单ID:', menu?.id);

    // 验证菜单数据
    if (!menu) {
      console.error('❌ 菜单数据为空');
      wx.showToast({
        title: '菜单数据异常，请重试',
        icon: 'none'
      });
      return;
    }

    if (!menu.id) {
      console.error('❌ 菜单ID为空，菜单数据:', menu);
      wx.showToast({
        title: '菜单ID异常，请重试',
        icon: 'none'
      });
      return;
    }

    if (
      !menu.dishes ||
      !Array.isArray(menu.dishes) ||
      menu.dishes.length === 0
    ) {
      console.error('❌ 菜单菜品数据异常');
      wx.showToast({
        title: '菜单菜品数据异常',
        icon: 'none'
      });
      return;
    }

    this.setData({
      selectedMenu: menu,
      orderItems: menu.dishes.map(dish => ({
        ...dish,
        orderCount: 0
      })),
      showOrderModal: true,
      orderRemark: '',
      selectedPushUsers: []
    });
  },

  // 关闭订单弹窗
  closeOrderModal() {
    this.setData({
      showOrderModal: false,
      selectedMenu: null,
      orderItems: [],
      orderRemark: '',
      selectedPushUsers: []
    });
  },

  // 修改菜品数量
  changeDishCount(e) {
    const {index, action} = e.currentTarget.dataset;
    const {orderItems} = this.data;

    if (action === 'increase') {
      orderItems[index].orderCount++;
    } else if (action === 'decrease' && orderItems[index].orderCount > 0) {
      orderItems[index].orderCount--;
    }

    this.setData({orderItems});
  },

  // 输入备注
  onRemarkInput(e) {
    this.setData({
      orderRemark: e.detail.value
    });
  },

  // 跳转到用户关联页面
  goToUserConnection() {
    wx.navigateTo({
      url: '/pages/user_connection/index',
      success: () => {
        console.log('跳转到用户关联页面成功');
      },
      fail: error => {
        console.error('跳转到用户关联页面失败:', error);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 切换推送用户选择
  togglePushUser(e) {
    const {userId} = e.currentTarget.dataset;
    const {selectedPushUsers} = this.data;

    const index = selectedPushUsers.indexOf(userId);
    if (index > -1) {
      selectedPushUsers.splice(index, 1);
    } else {
      selectedPushUsers.push(userId);
    }

    this.setData({selectedPushUsers});
  },

  // 提交订单
  async submitOrder() {
    const {selectedMenu, orderItems, orderRemark, selectedPushUsers} =
      this.data;

    // 调试：检查菜单ID
    console.log('🔍 提交订单 - selectedMenu.id:', selectedMenu?.id);

    // 检查是否选择了菜品
    const selectedItems = orderItems.filter(item => item.orderCount > 0);
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择菜品',
        icon: 'none'
      });
      return;
    }

    this.setData({submitting: true});

    try {
      // 格式化订单数据
      const orderData = {
        menuId: selectedMenu?.id || null,
        items: selectedItems.map(item => ({
          dishId: item.id,
          name: item.name,
          count: item.orderCount,
          remark: ''
        })),
        remark: orderRemark,
        pushToUsers: selectedPushUsers
      };

      const res = await orderApi.createOrderAndPush(orderData);

      if (res.code === 200 || res.code === 201) {
        wx.showToast({
          title: '下单成功',
          icon: 'success'
        });

        // 关闭弹窗
        this.closeOrderModal();

        // 可选：跳转到订单页面
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/order/index'
          });
        }, 1500);
      } else {
        wx.showToast({
          title: res.message || '下单失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('下单失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({submitting: false});
    }
  },

  // 格式化时间显示
  formatTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) {
      // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) {
      // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return `${Math.floor(diff / 86400000)}天前`;
    }
  }
});
