const {PrismaClient} = require('@prisma/client');
const {success, error} = require('../utils/response');
const wechatSubscriptionService = require('../services/wechatSubscriptionService');

const prisma = new PrismaClient();

/**
 * 发送订单通知
 * @route POST /api/subscriptions/order-notification
 */
const sendOrderNotification = async (req, res) => {
  try {
    const {orderId, customerInfo} = req.body;
    const userId = req.user.id;

    // 详细的参数验证和调试信息
    if (!orderId) {
      console.log('❌ 订单通知请求缺少orderId:', {
        body: req.body,
        userId: userId
      });
      return error(res, 'Order ID is required', 400);
    }

    // 获取订单信息
    const order = await prisma.order.findUnique({
      where: {id: orderId},
      include: {
        user: {
          select: {
            id: true,
            name: true,
            openid: true
          }
        },
        menu: {
          select: {
            id: true,
            date: true,
            remark: true
          }
        }
      }
    });

    if (!order) {
      return error(res, 'Order not found', 404);
    }

    // 获取关联的用户列表（通过用户关联系统）
    const connections = await prisma.userConnection.findMany({
      where: {
        OR: [
          {senderId: order.user.id, status: 'accepted'},
          {receiverId: order.user.id, status: 'accepted'}
        ]
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            openid: true
          }
        },
        receiver: {
          select: {
            id: true,
            name: true,
            openid: true
          }
        }
      }
    });

    // 构建关联用户列表（包括订单用户和所有关联用户）
    const connectedUsers = [order.user];
    connections.forEach(connection => {
      if (connection.senderId === order.user.id) {
        connectedUsers.push(connection.receiver);
      } else {
        connectedUsers.push(connection.sender);
      }
    });

    // 解析订单项（JSON格式）
    let orderItems = [];
    try {
      orderItems =
        typeof order.items === 'string'
          ? JSON.parse(order.items)
          : order.items || [];
    } catch (error) {
      orderItems = [];
    }

    // 格式化订单项
    const formattedOrder = {
      ...order,
      items: orderItems.map(item => ({
        dishName: item.dishName || item.name || '未知菜品',
        quantity: item.quantity || item.count || 1
      })),
      createdAt: order.createdAt
    };

    // 发送订阅消息
    const result = await wechatSubscriptionService.sendOrderNotification(
      formattedOrder,
      order.user,
      connectedUsers
    );

    if (result.success) {
      return success(
        res,
        {
          orderId,
          notificationResult: result
        },
        'Order notification sent successfully',
        200
      );
    } else {
      return error(res, `Failed to send notification: ${result.error}`, 500);
    }
  } catch (err) {
    return error(res, 'Failed to send order notification', 500);
  }
};

/**
 * 发送菜单更新通知
 * @route POST /api/subscriptions/menu-notification
 */
const sendMenuNotification = async (req, res) => {
  try {
    const {menuId} = req.body;
    const userId = req.user.id;

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: {id: userId},
      select: {
        id: true,
        name: true,
        openid: true
      }
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    // 获取关联的用户列表（通过用户关联系统）
    const connections = await prisma.userConnection.findMany({
      where: {
        OR: [
          {senderId: userId, status: 'accepted'},
          {receiverId: userId, status: 'accepted'}
        ]
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            openid: true
          }
        },
        receiver: {
          select: {
            id: true,
            name: true,
            openid: true
          }
        }
      }
    });

    // 构建关联用户列表（包括当前用户和所有关联用户）
    const connectedUsers = [user];
    connections.forEach(connection => {
      if (connection.senderId === userId) {
        connectedUsers.push(connection.receiver);
      } else {
        connectedUsers.push(connection.sender);
      }
    });

    // 构建菜单信息
    const menu = {
      id: menuId || 'today',
      updatedAt: new Date()
    };

    // 发送订阅消息
    const result = await wechatSubscriptionService.sendMenuUpdateNotification(
      menu,
      user,
      connectedUsers
    );

    if (result.success) {
      return success(
        res,
        {
          menuId,
          notificationResult: result
        },
        'Menu notification sent successfully',
        200
      );
    } else {
      return error(res, `Failed to send notification: ${result.error}`, 500);
    }
  } catch (err) {
    return error(res, 'Failed to send menu notification', 500);
  }
};

/**
 * 测试订阅消息发送
 * @route POST /api/subscriptions/test
 */
const testSubscriptionMessage = async (req, res) => {
  try {
    const {openid, message = '测试订阅消息'} = req.body;
    const userId = req.user.id;

    // 如果提供了openid，发送给指定用户
    if (openid) {
      if (openid.startsWith('mock_')) {
        return error(res, '不能向模拟openid发送消息', 400);
      }

      // 构建测试消息数据
      const testData = {
        thing1: {
          value: message
        },
        time1: {
          value: new Date().toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          })
        },
        thing2: {
          value: '关联好友'
        },
        thing3: {
          value: '测试通知'
        },
        thing4: {
          value: '测试用户'
        },
        thing5: {
          value: '红烧肉，糖醋排骨，青椒土豆丝'
        }
      };

      // 发送测试消息
      const result = await wechatSubscriptionService.sendSubscriptionMessage(
        openid,
        testData
      );

      if (result.success) {
        return success(
          res,
          {
            openid,
            result
          },
          'Test message sent successfully',
          200
        );
      } else {
        return error(res, `Failed to send test message: ${result.error}`, 500);
      }
    } else {
      // 如果没有提供openid，发送给当前用户
      return sendTestMessage(req, res);
    }
  } catch (err) {
    console.error('Send test message error:', err);
    return error(res, 'Failed to send test message', 500);
  }
};

/**
 * 发送测试订阅消息给当前用户
 * @route POST /api/subscriptions/test
 */
const sendTestMessage = async (req, res) => {
  try {
    const userId = req.user.id;
    const {message = '测试订阅消息', type = 'order_notification'} = req.body;

    console.log(`📨 发送测试订阅消息给用户 ${userId}`);

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: {id: userId},
      select: {
        id: true,
        name: true,
        phone: true,
        openid: true
      }
    });

    if (!user) {
      return error(res, '用户不存在', 404);
    }

    if (!user.openid) {
      return error(res, '用户未绑定微信，无法发送订阅消息', 400);
    }

    if (user.openid.startsWith('mock_')) {
      return error(
        res,
        '用户openid为模拟数据，请使用微信登录获取真实openid',
        400
      );
    }

    // 构造测试消息数据（按照统一字段格式）
    const messageData = {
      thing1: {
        value: message // 消息标题
      },
      time1: {
        value: new Date().toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }) // 时间
      },
      thing2: {
        value: '关联好友' // 固定文字
      },
      thing3: {
        value: '测试通知'
      },
      thing4: {
        value: user.name || '测试用户' // 用户名
      },
      thing5: {
        value: '红烧肉，糖醋排骨，青椒土豆丝' // 内容（用逗号拼接）
      }
    };

    console.log('📋 发送消息数据:', messageData);
    console.log('📱 目标用户:', {name: user.name, phone: user.phone});

    // 发送订阅消息
    const result = await wechatSubscriptionService.sendSubscriptionMessage(
      user.openid,
      messageData
    );

    if (result.success) {
      console.log('✅ 测试订阅消息发送成功');
      return success(
        res,
        {
          userId: user.id,
          userName: user.name,
          sentAt: new Date().toISOString()
        },
        '测试消息发送成功'
      );
    } else {
      console.log('❌ 测试订阅消息发送失败:', result.error);
      return error(res, `发送失败: ${result.error}`, 400);
    }
  } catch (err) {
    console.error('发送测试订阅消息失败:', err);
    return error(res, '发送测试消息失败', 500);
  }
};

module.exports = {
  sendOrderNotification,
  sendMenuNotification,
  testSubscriptionMessage,
  sendTestMessage
};
