<template>
  <BaseModal
    v-model="visible"
    title="编辑微信登录权限"
    size="lg"
    :show-default-footer="true"
    @confirm="handleSubmit"
    @cancel="handleCancel"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="space-y-4"
    >
      <el-form-item label="用户信息">
        <div class="flex items-center space-x-3">
          <el-avatar :size="40" :src="permission?.user?.avatar">
            {{ permission?.user?.name?.charAt(0) }}
          </el-avatar>
          <div>
            <div class="font-medium">{{ permission?.user?.name }}</div>
            <div class="text-sm text-gray-500">{{ permission?.user?.phone || '无手机号' }}</div>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="权限类型" prop="permissionType">
        <el-radio-group v-model="form.permissionType">
          <el-radio value="admin">管理员权限</el-radio>
          <el-radio value="user">普通用户权限</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="权限状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio value="active">启用</el-radio>
          <el-radio value="inactive">禁用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="有效期" prop="expiresAt">
        <el-date-picker
          v-model="form.expiresAt"
          type="datetime"
          placeholder="选择过期时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          class="w-full"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="form.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>
    </el-form>
  </BaseModal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import { wechatPermissionApi } from '@/api/wechatPermission'
import { useToast } from '@/composables/useToast'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  permission: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

const { success, error } = useToast()

// 响应式数据
const visible = ref(false)
const formRef = ref()

const form = reactive({
  permissionType: 'user',
  status: 'active',
  expiresAt: '',
  remark: ''
})

const rules = {
  permissionType: [
    { required: true, message: '请选择权限类型', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择权限状态', trigger: 'change' }
  ]
}

// 监听显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val && props.permission) {
    loadPermissionData()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 加载权限数据
const loadPermissionData = () => {
  if (props.permission) {
    Object.assign(form, {
      permissionType: props.permission.permissionType || 'user',
      status: props.permission.status || 'active',
      expiresAt: props.permission.expiresAt || '',
      remark: props.permission.remark || ''
    })
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    
    const response = await wechatPermissionApi.updatePermission(props.permission.id, form)
    
    if (response.success) {
      success('更新权限成功')
      emit('success')
    } else {
      error(response.message || '更新权限失败')
    }
  } catch (err) {
    console.error('更新权限失败:', err)
    error('更新权限失败')
  }
}

// 取消
const handleCancel = () => {
  visible.value = false
}
</script>
