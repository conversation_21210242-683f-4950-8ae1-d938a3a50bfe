/**
 * Store 统一导出
 */

// 导入所有store模块
import { useAppStore, useAppStoreHook } from './modules/app';
import { useUserStore, useUserStoreHook } from './modules/user';
import { usePermissionStore, usePermissionStoreHook } from './modules/permission';
import { useEpThemeStore, useEpThemeStoreHook } from './modules/epTheme';
import { useMultiTagsStore, useMultiTagsStoreHook } from './modules/multiTags';
import { useSettingStore, useSettingStoreHook } from './modules/settings';

// 导出所有store
export {
  useAppStore,
  useAppStoreHook,
  useUserStore,
  useUserStoreHook,
  usePermissionStore,
  usePermissionStoreHook,
  useEpThemeStore,
  useEpThemeStoreHook,
  useMultiTagsStore,
  useMultiTagsStoreHook,
  useSettingStore,
  useSettingStoreHook
};

// 初始化所有store的函数
export function initStores() {
  // 初始化权限store
  const permissionStore = usePermissionStoreHook();
  if (!permissionStore.isMenusLoaded) {
    permissionStore.initMenus();
  }
  
  return {
    app: useAppStoreHook(),
    user: useUserStoreHook(),
    permission: permissionStore,
    epTheme: useEpThemeStoreHook(),
    multiTags: useMultiTagsStoreHook(),
    settings: useSettingStoreHook()
  };
}
