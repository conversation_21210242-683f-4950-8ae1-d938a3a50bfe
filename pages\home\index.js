const {
  menuApi,
  messageApi,
  notificationApi,
  userApi
} = require('../../services/api');
const dayjs = require('dayjs');

Page({
  data: {
    menu: [],
    showRecommended: false,
    messages: [],
    hasRealMessages: false, // 标识是否有真实留言
    statistics: {
      todayDishes: 0,
      weeklyFavorite: '',
      totalOrders: 0,
      monthlyVisits: 0
    },
    userInfo: {
      name: '家庭厨房',
      avatar: 'https://picsum.photos/200/200'
    },
    // 通知相关数据
    notices: [], // 所有通知数据
    currentNotice: {
      // 当前显示的通知
      text: '加载中...',
      id: null,
      type: 'system'
    },
    noticeIndex: 0, // 当前通知索引
    noticeLoading: false, // 通知加载状态
    noticeError: false, // 通知错误状态

    // 轮播控制
    autoRotate: true, // 是否自动轮播
    rotateInterval: 3000, // 轮播间隔（毫秒）
    isPaused: false, // 是否暂停

    loading: true
  },

  onLoad() {
    if (!wx.getStorageSync('token')) {
      wx.reLaunch({
        url: '/pages/login/index'
      });
      return;
    }
  },

  onShow() {
    // 加载所有数据
    this.loadAllData();
    // 刷新菜单数据
    this.loadMenuData();

    // 页面显示时恢复轮播
    if (
      this.data.notices.length > 1 &&
      this.data.autoRotate &&
      !this.data.isPaused
    ) {
      this.startNoticeRotation();
    }
  },

  // 加载所有数据
  async loadAllData() {
    try {
      // 并行加载多个数据
      await Promise.all([
        this.loadMenuData(),
        this.loadMessagesData(),
        this.loadNoticeData(),
        this.loadUserData(),
        this.loadStatisticsData()
      ]);

      // 所有数据加载完成
      this.setData({
        loading: false,
        loadComplete: true
      });
    } catch (error) {
      console.error('加载数据失败:', error);
      wx.showToast({
        title: '加载数据失败',
        icon: 'none'
      });

      this.setData({
        loading: false,
        loadComplete: true
      });
    }
  },

  // 加载菜单数据
  async loadMenuData() {
    try {
      // 使用首页菜单API
      const res = await menuApi.getHomeMenus();

      if (res.code === 200 && res.data) {
        const {type, menus, dishes, message} = res.data;

        if (type === 'today_menu' && menus && menus.length > 0) {
          // 有今日菜单，显示第一个菜单的菜品
          const todayMenu = menus[0];

          this.setData({
            menu: todayMenu.dishes,
            todayMenuDate: dayjs(todayMenu.date).format('YYYY-MM-DD'),
            showRecommended: false,
            menuCreator: todayMenu.creatorName
          });
        } else if (type === 'hot_dishes' && dishes && dishes.length > 0) {
          // 没有今日菜单，显示热门菜品
          this.setData({
            menu: dishes,
            todayMenuDate: dayjs().format('YYYY-MM-DD'),
            showRecommended: true,
            menuCreator: '系统推荐'
          });
        } else {
          // 没有任何数据
          this.setData({
            menu: [],
            showRecommended: true,
            menuCreator: ''
          });
        }
      } else {
        this.setData({
          menu: [],
          showRecommended: true
        });
      }

      // 获取推荐菜单（用于推荐区域显示）
      const recRes = await menuApi.getRecommendedMenu();

      if (recRes.code === 200 && recRes.data && recRes.data.length > 0) {
        // 将推荐菜单中的所有菜品提取出来，展平为菜品列表
        const allDishes = [];
        recRes.data.forEach(menu => {
          if (menu.dishes && menu.dishes.length > 0) {
            menu.dishes.forEach(dish => {
              allDishes.push({
                id: dish.id,
                name: dish.name,
                count: dish.count,
                image: dish.image,
                creator: menu.creator, // 添加创建者信息
                isRecommended: true, // 标记为推荐菜品
                recommendReason: menu.recommendReason || '推荐菜品'
              });
            });
          }
        });

        // 如果没有今日菜单，显示推荐菜单；如果有今日菜单，将推荐菜单添加到末尾
        const currentMenu = this.data.menu || [];
        const combinedMenu = [...currentMenu, ...allDishes];

        this.setData({
          menu: combinedMenu,
          showRecommended: allDishes.length > 0
        });
      } else {
      }
    } catch (error) {
      console.error('❌ 加载菜单数据失败:', error);
      // 加载失败时显示空菜单
      this.setData({
        menu: [],
        showRecommended: true
      });
    }
  },

  // 加载消息数据
  async loadMessagesData() {
    try {
      // 请求最新的6条留言数据
      const res = await messageApi.getMessages({
        limit: 6,
        page: 1
      });
      if (res.code === 200) {
        // 处理不同的数据格式
        let messageData = res.data;

        // 确保数据是数组格式
        if (!Array.isArray(messageData)) {
          messageData =
            messageData.messages || messageData.list || messageData.data || [];
        }

        // 如果最终还不是数组，设为空数组
        if (!Array.isArray(messageData)) {
          messageData = [];
        }

        if (messageData.length > 0) {
          // 过滤掉没有内容的留言，然后格式化消息数据
          const validMessages = messageData.filter(msg => {
            const content = msg.content;
            // 过滤掉空内容、null、undefined、只有空格的内容
            return content && content.trim().length > 0;
          });

          if (validMessages.length > 0) {
            // 格式化有效的消息数据，只取最新的6条
            const formattedMessages = validMessages.slice(0, 6).map(msg => {
              const userName = msg.user?.name || msg.user_name || '用户';
              const content = msg.content.trim();
              return `${userName}：${content}`;
            });

            this.setData({
              messages: formattedMessages,
              hasRealMessages: true
            });
          } else {
            // 如果过滤后没有有效数据，显示默认消息
            this.setData({
              messages: [
                '欢迎使用家庭点餐系统',
                '快来给家人留言吧～',
                '今天想吃什么呢？'
              ],
              hasRealMessages: false
            });
          }
        } else {
          // 如果没有数据，显示默认消息
          this.setData({
            messages: [
              '欢迎使用家庭点餐系统',
              '快来给家人留言吧～',
              '今天想吃什么呢？'
            ],
            hasRealMessages: false
          });
        }
      } else {
        throw new Error(res.message || '获取消息失败');
      }
    } catch (error) {
      console.error('加载消息数据失败:', error);
      // 加载失败时显示默认消息
      this.setData({
        messages: [
          '欢迎使用家庭点餐系统',
          '快来给家人留言吧～',
          '今天想吃什么呢？'
        ],
        hasRealMessages: false
      });
    }
  },

  // 加载通知数据（支持轮播）
  async loadNoticeData() {
    try {
      this.setData({
        noticeLoading: true,
        noticeError: false
      });

      const res = await notificationApi.getNotifications({
        limit: 5, // 最多获取5条
        homeFilter: true, // 首页过滤：只显示关联用户的菜单和订单通知
        orderBy: 'createdAt',
        order: 'desc'
      });

      // 正确解析API返回的数据结构
      const notificationData =
        res.data.notifications || res.data.list || res.data || [];
      if (res.code === 200 && notificationData && notificationData.length > 0) {
        // 处理通知数据
        const notices = notificationData.map(item => ({
          id: item.id,
          text: this.formatNoticeText(item.content),
          type: item.type || 'general',
          createdAt: item.createdAt
        }));

        this.setData({
          notices,
          currentNotice: notices[0],
          noticeIndex: 0,
          noticeLoading: false
        });

        // 如果有多条通知，启动轮播
        if (notices.length > 1 && this.data.autoRotate) {
          this.startNoticeRotation();
        }
      } else {
        // 没有通知时的处理
        this.setData({
          notices: [],
          currentNotice: {
            text: '--暂无通知--',
            id: null,
            type: 'empty'
          },
          noticeLoading: false
        });
      }
    } catch (error) {
      console.error('加载通知数据失败:', error);
      this.setData({
        notices: [],
        currentNotice: {
          text: '--加载失败，点击重试--',
          id: null,
          type: 'error'
        },
        noticeLoading: false,
        noticeError: true
      });
    }
  },

  // 格式化通知文本
  formatNoticeText(content) {
    if (!content) return '通知内容为空';

    // 移除HTML标签
    const cleanText = content.replace(/<[^>]*>/g, '');

    // 限制长度，超出部分用省略号
    const maxLength = 50;
    if (cleanText.length > maxLength) {
      return cleanText.substring(0, maxLength - 3) + '...';
    }

    return cleanText;
  },

  // 加载用户数据
  async loadUserData() {
    try {
      // 获取当前用户ID
      const userId = wx.getStorageSync('userId') || 1;
      const res = await userApi.getUserInfo(userId);

      if (res.data) {
        this.setData({
          userInfo: {
            name: res.data.name,
            avatar: res.data.avatar || 'https://picsum.photos/200/200'
          }
        });
      }
    } catch (error) {
      console.error('加载用户数据失败:', error);
    }
  },

  // 加载统计数据
  async loadStatisticsData() {
    try {
      const res = await menuApi.getStatistics();

      if (res.data) {
        this.setData({
          statistics: res.data
        });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      // 使用默认数据
      this.setData({
        statistics: {
          todayDishes: 0,
          weeklyFavorite: '暂无数据',
          totalOrders: 0,
          monthlyVisits: 0
        }
      });
    }
  },

  // 跳转到点菜页面
  goToOrder() {
    wx.switchTab({
      url: '/pages/order/index',
      fail: error => {
        console.error('跳转失败:', error);
        // 如果switchTab失败（可能order不是tabBar页面），尝试使用navigateTo
        wx.navigateTo({
          url: '/pages/order/index'
        });
      }
    });
  },

  // 跳转到菜品详情页
  async goToFoodDetail(e) {
    const {id} = e.currentTarget.dataset;

    try {
      wx.showLoading({
        title: '加载中...'
      });

      const {dishApi} = require('../../services/api');
      const result = await dishApi.getDishDetail(id);

      if (result.code === 200) {
        // 将详情数据存入缓存
        wx.setStorageSync('detailData', result.data);

        // 跳转到详情页
        wx.navigateTo({
          url: '/pages/detail/index'
        });
      } else {
        wx.showToast({
          title: '获取详情失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('获取菜品详情失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 跳转到留言页面
  goToMessage() {
    wx.navigateTo({
      url: '/pages/family_message/index',
      fail: error => {
        console.error('跳转失败:', error);
      }
    });
  },

  // 跳转到推荐菜单页面
  goToRecommendedMenu() {
    wx.navigateTo({
      url: '/pages/recommended_menu/index',
      fail: error => {
        console.error('跳转失败:', error);
      }
    });
  },

  // 跳转到订单列表页面
  goToOrderList() {
    wx.navigateTo({
      url: '/pages/order_list/index',
      fail: error => {
        console.error('跳转失败:', error);
      }
    });
  },

  // ==================== 通知轮播控制方法 ====================

  // 启动通知轮播
  startNoticeRotation() {
    // 清除现有定时器
    this.stopNoticeRotation();

    this.noticeTimer = setInterval(() => {
      if (!this.data.isPaused && this.data.notices.length > 1) {
        const {notices, noticeIndex} = this.data;
        const nextIndex = (noticeIndex + 1) % notices.length;

        this.setData({
          noticeIndex: nextIndex,
          currentNotice: notices[nextIndex]
        });
      }
    }, this.data.rotateInterval);
  },

  // 停止通知轮播
  stopNoticeRotation() {
    if (this.noticeTimer) {
      clearInterval(this.noticeTimer);
      this.noticeTimer = null;
    }
  },

  // 暂停/恢复轮播
  toggleNoticeRotation() {
    this.setData({
      isPaused: !this.data.isPaused
    });
  },

  // 手动切换到下一条通知
  nextNotice() {
    if (this.data.notices.length <= 1) return;

    const {notices, noticeIndex} = this.data;
    const nextIndex = (noticeIndex + 1) % notices.length;

    this.setData({
      noticeIndex: nextIndex,
      currentNotice: notices[nextIndex]
    });

    // 重置定时器
    if (this.data.autoRotate) {
      this.startNoticeRotation();
    }
  },

  // 点击通知栏事件
  onNoticeClick() {
    if (this.data.noticeError) {
      // 如果是错误状态，重新加载
      this.loadNoticeData();
    } else {
      // 正常状态，跳转到通知中心
      wx.navigateTo({
        url: '/pages/notification_center/index'
      });
    }
  },

  // 点击重试
  onNoticeRetry() {
    this.loadNoticeData();
  },

  // 点击轮播指示器
  onIndicatorTap(e) {
    const index = e.currentTarget.dataset.index;
    if (index !== undefined && this.data.notices[index]) {
      this.setData({
        noticeIndex: index,
        currentNotice: this.data.notices[index]
      });

      // 重置定时器
      if (this.data.autoRotate) {
        this.startNoticeRotation();
      }
    }
  },

  // ==================== 页面生命周期管理 ====================

  onHide() {
    // 页面隐藏时暂停轮播
    this.stopNoticeRotation();
  },

  onUnload() {
    // 页面卸载时清理定时器
    this.stopNoticeRotation();
  }
});
