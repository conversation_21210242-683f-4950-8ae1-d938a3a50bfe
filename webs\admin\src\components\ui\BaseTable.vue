<template>
  <div class="bg-white dark:bg-gray-800 shadow-soft rounded-lg overflow-hidden">
    <!-- 表格头部 -->
    <div v-if="$slots.header || title" class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <slot name="header">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ title }}</h3>
          <div class="flex items-center space-x-3">
            <slot name="actions"></slot>
          </div>
        </div>
      </slot>
    </div>

    <!-- 表格内容 -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <!-- 表头 -->
        <thead class="bg-gray-50 dark:bg-gray-900">
          <tr>
            <!-- 选择列 -->
            <th v-if="selectable" class="px-6 py-3 text-left">
              <input
                type="checkbox"
                :checked="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </th>
            
            <!-- 数据列 -->
            <th
              v-for="column in columns"
              :key="column.key"
              :class="getHeaderClass(column)"
              @click="handleSort(column)"
            >
              <div class="flex items-center space-x-1">
                <span>{{ column.title }}</span>
                <div v-if="column.sortable" class="flex flex-col">
                  <ChevronUpIcon 
                    :class="getSortIconClass(column, 'asc')"
                    class="h-3 w-3"
                  />
                  <ChevronDownIcon 
                    :class="getSortIconClass(column, 'desc')"
                    class="h-3 w-3 -mt-1"
                  />
                </div>
              </div>
            </th>
            
            <!-- 操作列 -->
            <th v-if="$slots.actions" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        
        <!-- 表体 -->
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          <!-- 数据行 -->
          <tr
            v-for="(row, index) in data"
            :key="getRowKey(row, index)"
            :class="getRowClass(row, index)"
            @click="handleRowClick(row, index)"
          >
            <!-- 选择列 -->
            <td v-if="selectable" class="px-6 py-4">
              <input
                type="checkbox"
                :checked="isRowSelected(row)"
                @change="handleRowSelect(row, $event)"
                @click.stop
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
            </td>
            
            <!-- 数据列 -->
            <td
              v-for="column in columns"
              :key="column.key"
              :class="getCellClass(column)"
            >
              <slot
                :name="`cell-${column.key}`"
                :row="row"
                :column="column"
                :index="index"
                :value="getColumnValue(row, column)"
              >
                <span>{{ getColumnValue(row, column) }}</span>
              </slot>
            </td>
            
            <!-- 操作列 -->
            <td v-if="$slots.actions" class="px-6 py-4 text-right text-sm font-medium">
              <slot name="actions" :row="row" :index="index"></slot>
            </td>
          </tr>
          
          <!-- 空状态 -->
          <tr v-if="!data.length">
            <td :colspan="totalColumns" class="px-6 py-12 text-center">
              <slot name="empty">
                <div class="text-gray-500 dark:text-gray-400">
                  <div class="text-4xl mb-2">📝</div>
                  <div class="text-sm">{{ emptyText }}</div>
                </div>
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 表格底部 -->
    <div v-if="$slots.footer" class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/vue/20/solid'

const props = defineProps({
  // 表格数据
  data: {
    type: Array,
    default: () => []
  },
  // 表格列配置
  columns: {
    type: Array,
    default: () => []
  },
  // 表格标题
  title: {
    type: String,
    default: ''
  },
  // 是否可选择
  selectable: {
    type: Boolean,
    default: false
  },
  // 选中的行
  selectedRows: {
    type: Array,
    default: () => []
  },
  // 行键
  rowKey: {
    type: [String, Function],
    default: 'id'
  },
  // 是否可点击行
  clickableRow: {
    type: Boolean,
    default: false
  },
  // 排序配置
  sortBy: {
    type: String,
    default: ''
  },
  sortOrder: {
    type: String,
    default: 'asc',
    validator: (value) => ['asc', 'desc'].includes(value)
  },
  // 空状态文本
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  // 是否显示斑马纹
  striped: {
    type: Boolean,
    default: false
  },
  // 是否显示边框
  bordered: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:selectedRows',
  'row-click',
  'sort-change',
  'selection-change'
])

const totalColumns = computed(() => {
  let count = props.columns.length
  if (props.selectable) count++
  if (props.$slots?.actions) count++
  return count
})

const isAllSelected = computed(() => {
  return props.data.length > 0 && props.selectedRows.length === props.data.length
})

const isIndeterminate = computed(() => {
  return props.selectedRows.length > 0 && props.selectedRows.length < props.data.length
})

const getRowKey = (row, index) => {
  if (typeof props.rowKey === 'function') {
    return props.rowKey(row, index)
  }
  return row[props.rowKey] || index
}

const getRowClass = (row, index) => {
  const classes = []
  
  if (props.clickableRow) {
    classes.push('cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700')
  }
  
  if (props.striped && index % 2 === 1) {
    classes.push('bg-gray-50 dark:bg-gray-900')
  }
  
  return classes.join(' ')
}

const getHeaderClass = (column) => {
  const classes = [
    'px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider'
  ]
  
  if (column.sortable) {
    classes.push('cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800')
  }
  
  if (column.align === 'center') {
    classes.push('text-center')
  } else if (column.align === 'right') {
    classes.push('text-right')
  }
  
  return classes.join(' ')
}

const getCellClass = (column) => {
  const classes = ['px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100']
  
  if (column.align === 'center') {
    classes.push('text-center')
  } else if (column.align === 'right') {
    classes.push('text-right')
  }
  
  return classes.join(' ')
}

const getSortIconClass = (column, direction) => {
  const isActive = props.sortBy === column.key && props.sortOrder === direction
  return isActive ? 'text-primary-600' : 'text-gray-400'
}

const getColumnValue = (row, column) => {
  if (column.render && typeof column.render === 'function') {
    return column.render(row[column.key], row)
  }
  
  if (column.key.includes('.')) {
    return column.key.split('.').reduce((obj, key) => obj?.[key], row)
  }
  
  return row[column.key]
}

const isRowSelected = (row) => {
  const rowKey = getRowKey(row)
  return props.selectedRows.some(selectedRow => getRowKey(selectedRow) === rowKey)
}

const handleSelectAll = (event) => {
  const newSelectedRows = event.target.checked ? [...props.data] : []
  emit('update:selectedRows', newSelectedRows)
  emit('selection-change', newSelectedRows)
}

const handleRowSelect = (row, event) => {
  const isSelected = event.target.checked
  let newSelectedRows = [...props.selectedRows]
  
  if (isSelected) {
    if (!isRowSelected(row)) {
      newSelectedRows.push(row)
    }
  } else {
    const rowKey = getRowKey(row)
    newSelectedRows = newSelectedRows.filter(selectedRow => getRowKey(selectedRow) !== rowKey)
  }
  
  emit('update:selectedRows', newSelectedRows)
  emit('selection-change', newSelectedRows)
}

const handleRowClick = (row, index) => {
  if (props.clickableRow) {
    emit('row-click', row, index)
  }
}

const handleSort = (column) => {
  if (!column.sortable) return
  
  let newSortOrder = 'asc'
  if (props.sortBy === column.key && props.sortOrder === 'asc') {
    newSortOrder = 'desc'
  }
  
  emit('sort-change', {
    column: column.key,
    order: newSortOrder
  })
}
</script>
