const {dishApi, subscriptionApi, uploadApi} = require('../../../services/api');

// 预定义标签选项
const DISH_TAGS = [
  {
    value: 'spicy',
    label: '辣',
    color: '#ff4757'
  },
  {
    value: 'numbing',
    label: '麻',
    color: '#ff6b35'
  },
  {
    value: 'sweet',
    label: '甜',
    color: '#ff9ff3'
  },
  {
    value: 'sour',
    label: '酸',
    color: '#54a0ff'
  },
  {
    value: 'salty',
    label: '咸',
    color: '#5f27cd'
  },
  {
    value: 'light',
    label: '清淡',
    color: '#00d2d3'
  },
  {
    value: 'rich',
    label: '浓郁',
    color: '#ff9f43'
  },
  {
    value: 'vegetarian',
    label: '素食',
    color: '#1dd1a1'
  }
];

Page({
  data: {
    name: '',
    tempImagePath: '',
    imageBase64: '',
    originalImageInfo: null,
    remark: '',
    material: '',
    method: '',
    category: 'hotpot',
    categoryIndex: 0,
    categoryLabel: '火锅',
    categories: [],
    availableTags: DISH_TAGS,
    selectedTags: [],
    isPublished: false,
    loading: false,
    showCategoryPicker: false,

    // 编辑模式相关
    isEditMode: false,
    dishId: null,
    pageTitle: '新增菜品',
    uploadedImageUrl: '', // 已上传的图片URL
    uploadedImageInfo: null, // 已上传的图片信息
    hasSubmitted: false // 🔥 新增：标记是否已提交成功
  },

  async onLoad(options) {
    // 检查是否为编辑模式
    if (
      options.dishId &&
      options.dishId !== 'undefined' &&
      options.dishId !== 'null'
    ) {
      this.setData({
        isEditMode: true,
        dishId: options.dishId,
        pageTitle: '编辑菜品'
      });

      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '编辑菜品'
      });
    } else if (options.dishId === 'undefined' || options.dishId === 'null') {
      wx.showToast({
        title: '菜品ID无效，请重试',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    // 先加载分类列表，再加载菜品数据（如果是编辑模式）
    await this.loadCategories();

    if (this.data.isEditMode) {
      await this.loadDishData();
    }
  },

  // 🔥 新增：页面卸载时清理未保存的图片
  onUnload() {
    // 如果用户没有保存就离开页面，清理已上传但未保存的图片
    if (this.data.uploadedImageUrl && !this.data.isEditMode) {
      // 检查是否真的保存了菜品
      const hasSubmitted = this.data.hasSubmitted || false;
      if (!hasSubmitted) {
        uploadApi.deleteImage(this.data.uploadedImageUrl).catch(error => {
          // 静默处理清理失败
        });
      }
    }
  },

  // 加载分类列表
  async loadCategories() {
    try {
      const result = await dishApi.getCategoriesForMiniProgram();
      const categories = result.data || [];
      // 如果没有分类，使用默认分类
      if (categories.length === 0) {
        categories.push(
          {
            value: 'hotpot',
            label: '火锅',
            icon: '🍲'
          },
          {
            value: 'jianghu',
            label: '江湖菜',
            icon: '🥘'
          },
          {
            value: 'noodles',
            label: '小面',
            icon: '🍜'
          },
          {
            value: 'sichuan',
            label: '川菜',
            icon: '🌶️'
          },
          {
            value: 'hunan',
            label: '湘菜',
            icon: '🔥'
          },
          {
            value: 'cantonese',
            label: '粤菜',
            icon: '🥟'
          },
          {
            value: 'staple',
            label: '主食',
            icon: '🍚'
          },
          {
            value: 'cold',
            label: '凉菜',
            icon: '🥗'
          },
          {
            value: 'dessert',
            label: '甜品',
            icon: '🍰'
          },
          {
            value: 'drinks',
            label: '饮品',
            icon: '🥤'
          },
          {
            value: 'alcohol',
            label: '酒水',
            icon: '🍺'
          },
          {
            value: 'snacks',
            label: '小吃',
            icon: '🍿'
          }
        );
      }

      // 为从后端获取的分类添加图标
      const categoriesWithIcons = categories.map(cat => ({
        ...cat,
        icon: cat.icon || this.getCategoryIcon(cat.value)
      }));

      this.setData({
        categories: categoriesWithIcons,
        category: categoriesWithIcons[0]?.value || 'hotpot',
        categoryLabel: categoriesWithIcons[0]?.label || '火锅',
        categoryIndex: 0
      });
    } catch (error) {
      // 静默处理分类加载失败
    }
  },

  // 加载菜品数据（编辑模式）
  async loadDishData() {
    if (!this.data.dishId) return;

    try {
      wx.showLoading({
        title: '加载菜品数据...'
      });

      const result = await dishApi.getDishDetail(this.data.dishId);

      // 改进的成功判断逻辑
      const isSuccess =
        (result && result.success === true) ||
        (result && result.code >= 200 && result.code < 300) ||
        (result && result.data);

      if (isSuccess) {
        const dish = result.data;

        // 处理标签数据 - 确保是数组格式
        let tags = [];
        if (dish.tags) {
          if (Array.isArray(dish.tags)) {
            tags = dish.tags;
          } else if (typeof dish.tags === 'string') {
            try {
              tags = JSON.parse(dish.tags);
            } catch (e) {
              console.warn('标签数据解析失败:', dish.tags);
              tags = [];
            }
          }
        }

        // 处理分类数据
        let categoryName = '';
        if (dish.category) {
          if (typeof dish.category === 'string') {
            categoryName = dish.category;
          } else if (dish.category.name) {
            categoryName = dish.category.name;
          }
        }

        // 查找分类索引（确保分类数据已加载）
        const categoryIndex = this.data.categories.findIndex(
          cat => cat.label === categoryName || cat.value === categoryName
        );

        const updateData = {
          name: dish.name || '',
          uploadedImageUrl: dish.image || '',
          tempImagePath: dish.image || '', // 用于显示
          remark: dish.remark || '',
          material: dish.ingredients || '',
          method: dish.cookingMethod || '',
          selectedTags: tags,
          isPublished: !!dish.isPublished,
          categoryIndex: categoryIndex >= 0 ? categoryIndex : 0,
          category:
            categoryIndex >= 0
              ? this.data.categories[categoryIndex].value
              : this.data.categories[0]?.value || 'hotpot',
          categoryLabel:
            categoryIndex >= 0
              ? this.data.categories[categoryIndex].label
              : this.data.categories[0]?.label || '火锅'
        };

        this.setData(updateData);
      } else {
        throw new Error(result?.message || '获取菜品详情失败');
      }
    } catch (error) {
      wx.showToast({
        title: error.message || '加载失败',
        icon: 'none'
      });

      // 加载失败时返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    } finally {
      wx.hideLoading();
    }
  },

  // 输入事件处理
  onNameInput(e) {
    this.setData({
      name: e.detail.value
    });
  },

  onMaterialInput(e) {
    this.setData({
      material: e.detail.value
    });
  },

  onMethodInput(e) {
    this.setData({
      method: e.detail.value
    });
  },

  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    });
  },

  // 显示分类选择弹窗
  showCategoryModal() {
    this.setData({
      showCategoryPicker: true
    });
  },

  // 隐藏分类选择弹窗
  hideCategoryModal() {
    this.setData({
      showCategoryPicker: false
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止点击弹窗内容时关闭弹窗
  },

  // 选择分类
  selectCategory(e) {
    const {index, value, label} = e.currentTarget.dataset;
    this.setData({
      categoryIndex: parseInt(index),
      category: value,
      categoryLabel: label,
      showCategoryPicker: false
    });
  },

  // 获取分类图标
  getCategoryIcon(categoryValue) {
    const iconMap = {
      hotpot: '🍲',
      jianghu: '🥘',
      noodles: '🍜',
      sichuan: '🌶️',
      hunan: '🔥',
      cantonese: '🥟',
      staple: '🍚',
      cold: '🥗',
      dessert: '🍰',
      drinks: '🥤',
      alcohol: '🍺',
      snacks: '🍿'
    };
    return iconMap[categoryValue] || '🍽️';
  },

  // 标签切换
  toggleTag(e) {
    const tag = e.currentTarget.dataset.tag;
    const selectedTags = [...this.data.selectedTags];

    const index = selectedTags.indexOf(tag);
    if (index > -1) {
      selectedTags.splice(index, 1);
    } else {
      selectedTags.push(tag);
    }

    this.setData({
      selectedTags: selectedTags
    });
  },

  // 上架状态切换
  onPublishChange(e) {
    this.setData({
      isPublished: e.detail.value
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: res => {
        const tempFilePath = res.tempFiles[0].tempFilePath;

        // 压缩图片
        wx.compressImage({
          src: tempFilePath,
          quality: 80,
          success: compressRes => {
            this.processImage(compressRes.tempFilePath, res.tempFiles[0]);
          },
          fail: () => {
            this.processImage(tempFilePath, res.tempFiles[0]);
          }
        });
      },
      fail: error => {
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理图片 - 优化版本，支持旧图片删除
  async processImage(filePath, originalInfo) {
    try {
      wx.showLoading({
        title: '上传图片中...'
      });

      // 🔥 新增：如果有旧图片，先删除
      if (this.data.uploadedImageUrl) {
        try {
          // wx.showLoading({
          //   title: '删除旧图片...'
          // });
          await uploadApi.deleteImage(this.data.uploadedImageUrl);
          wx.showLoading({
            title: '上传新图片...'
          });
        } catch (deleteError) {
          // 不阻止新图片上传，只记录警告
          wx.showLoading({
            title: '上传新图片...'
          });
        }
      }

      // 生成菜单ID - 区分新增和编辑模式
      const menuId = this.data.isEditMode
        ? `edit_${this.data.dishId}_${Date.now()}`
        : `menu_${Date.now()}`;

      // 调用图片上传API
      const uploadResult = await uploadApi.uploadImage(filePath, {
        type: 'menu',
        entityId: menuId,
        category: 'food'
      });

      if (uploadResult.code === 201) {
        this.setData({
          tempImagePath: filePath,
          uploadedImageUrl: uploadResult.data.url,
          uploadedImageInfo: uploadResult.data,
          originalImageInfo: originalInfo
        });

        wx.hideLoading();
        wx.showToast({
          title: '图片上传成功',
          icon: 'none'
        });
      } else {
        throw new Error(uploadResult.message || '图片上传失败');
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: error.message || '图片上传失败',
        icon: 'none'
      });
    }
  },

  // 预览图片
  previewImage() {
    if (this.data.tempImagePath) {
      wx.previewImage({
        urls: [this.data.tempImagePath]
      });
    }
  },

  // 删除图片
  async deleteImage() {
    const {uploadedImageUrl} = this.data;

    // 如果有已上传的图片，先删除云端图片
    if (uploadedImageUrl) {
      try {
        await uploadApi.deleteImage(uploadedImageUrl);
      } catch (error) {
        // 不阻止本地删除操作
      }
    }

    this.setData({
      tempImagePath: '',
      uploadedImageUrl: '',
      uploadedImageInfo: null,
      originalImageInfo: null
    });
  },

  // 获取分类名称
  getCategoryName(categoryValue) {
    const category = this.data.categories.find(
      cat => cat.value === categoryValue
    );
    return category ? category.label : categoryValue;
  },

  // 提交表单
  async submitForm() {
    const {
      name,
      uploadedImageUrl,
      remark,
      material,
      method,
      category,
      selectedTags,
      isPublished,
      loading,
      isEditMode,
      dishId
    } = this.data;

    // 防止重复提交
    if (loading) return;

    // 验证必填字段
    if (!name.trim()) {
      wx.showToast({
        title: '请输入菜品名称',
        icon: 'none'
      });
      return;
    }

    if (!material.trim()) {
      wx.showToast({
        title: '请输入原材料',
        icon: 'none'
      });
      return;
    }

    if (!method.trim()) {
      wx.showToast({
        title: '请输入制作方法',
        icon: 'none'
      });
      return;
    }

    if (!uploadedImageUrl) {
      wx.showToast({
        title: '请上传菜品图片',
        icon: 'none'
      });
      return;
    }

    this.setData({
      loading: true
    });

    try {
      wx.showLoading({
        title: isEditMode ? '更新中...' : '提交中...'
      });

      // 构建菜品数据
      const dishData = {
        name: name.trim(),
        description: remark.trim() || `美味的${name.trim()}`,
        ingredients: material.trim(),
        cookingMethod: method.trim(),
        remark: remark.trim(),
        category: this.getCategoryName(category),
        tags: selectedTags,
        isPublished,
        image: uploadedImageUrl
      };

      // 调用API创建或更新菜品
      const result = isEditMode
        ? await dishApi.updateDish(dishId, dishData)
        : await dishApi.createDish(dishData);

      // 改进的成功判断逻辑
      const isSuccess =
        (result && result.success === true) ||
        (result && result.code >= 200 && result.code < 300) ||
        (result &&
          result.message &&
          result.message.toLowerCase().includes('success')) ||
        (result &&
          result.message &&
          result.message.toLowerCase().includes('created'));

      if (isSuccess) {
        // 🔥 新增：标记图片已保存，防止页面卸载时误删
        this.setData({
          hasSubmitted: true
        });

        wx.showToast({
          title: isEditMode ? '更新成功' : '添加成功',
          icon: 'none',
          duration: 2000
        });

        // 只有新增菜品时才发送菜单更新通知
        if (!isEditMode) {
          try {
            await subscriptionApi.sendMenuNotification({
              menuId: 'today',
              dishName: name.trim()
            });
          } catch (notifyError) {
            // 静默处理，不显示错误提示
          }
        }

        // 清空表单（仅新增模式）
        if (!isEditMode) {
          this.clearForm();
        }

        // 🔥 修复：统一返回上一页，避免多次点击返回
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      } else {
        throw new Error(
          result?.message ||
            result?.error ||
            (isEditMode ? '更新失败' : '添加失败')
        );
      }
    } catch (error) {
      // 显示图片上传失败提示
      if (error.message && error.message.includes('图片上传失败')) {
        wx.showToast({
          title: '图片上传失败，请重试',
          icon: 'none',
          duration: 3000
        });
      } else {
        wx.showToast({
          title: error.message || '添加失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    } finally {
      wx.hideLoading();
      this.setData({
        loading: false
      });
    }
  },

  // 清空表单
  clearForm() {
    this.setData({
      name: '',
      tempImagePath: '',
      uploadedImageUrl: '',
      uploadedImageInfo: null,
      originalImageInfo: null,
      remark: '',
      material: '',
      method: '',
      selectedTags: [],
      isPublished: false
    });
  }
});
