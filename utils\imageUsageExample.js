/**
 * 图片优化使用示例
 * 展示如何在页面中正确使用优化后的图片组件
 */

const { imageOptimizer } = require('./imageOptimizer');

// 页面使用示例
const ImageUsageExample = {
  
  /**
   * 在页面onLoad时预加载图片
   */
  onPageLoad(imageList) {
    // 预加载当前页面的图片
    imageOptimizer.preloadPageImages(imageList, {
      maxCount: 8, // 只预加载前8张
      imageType: 'dish',
      priority: 'high'
    });
  },

  /**
   * 在列表滚动时预加载下一页图片
   */
  onScrollToLower(nextPageImages) {
    imageOptimizer.preloadPageImages(nextPageImages, {
      maxCount: 5,
      imageType: 'dish', 
      priority: 'normal'
    });
  },

  /**
   * 获取优化后的图片URL
   */
  getOptimizedImageUrl(src, type = 'dish') {
    const sizeMap = {
      dish: { width: 400, height: 300, quality: 80 },
      avatar: { width: 240, height: 240, quality: 85 },
      menu: { width: 600, height: 400, quality: 75 },
      thumbnail: { width: 160, height: 160, quality: 70 }
    };

    const options = sizeMap[type] || sizeMap.dish;
    return imageOptimizer.optimizeUrl(src, options);
  },

  /**
   * 在页面卸载时清理缓存
   */
  onPageUnload() {
    // 可选：清理缓存以释放内存
    // imageOptimizer.clearCache();
  }
};

module.exports = ImageUsageExample;
