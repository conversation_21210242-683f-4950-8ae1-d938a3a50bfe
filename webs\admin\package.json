{"name": "nannan-kitchen-admin", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "cross-env NODE_ENV=development vite", "test": "cross-env NODE_ENV=test vite --mode test", "prod": "cross-env NODE_ENV=production vite", "build": "cross-env NODE_ENV=production vite build", "build:test": "cross-env NODE_ENV=test vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.jsx --fix", "vitest": "vitest", "vitest:run": "vitest run", "vitest:ui": "vitest --ui", "vitest:coverage": "vitest run --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "@vueuse/core": "^11.2.0", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.8.8", "file-saver": "^2.0.5", "js-cookie": "^3.0.5", "nprogress": "^0.2.0", "pinia": "^2.2.6", "vue": "^3.5.12", "vue-router": "^4.4.5", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vitest/ui": "^3.1.4", "@vue/test-utils": "^2.4.6", "animate.css": "^4.1.1", "autoprefixer": "^10.4.20", "cross-env": "^10.0.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.29.1", "happy-dom": "^17.5.6", "jsdom": "^26.1.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass": "^1.80.6", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0", "vitest": "^3.1.4"}}