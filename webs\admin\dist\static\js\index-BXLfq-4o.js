var b=(M,t,m)=>new Promise((a,_)=>{var i=l=>{try{u(m.next(l))}catch(p){_(p)}},c=l=>{try{u(m.throw(l))}catch(p){_(p)}},u=l=>l.done?a(l.value):Promise.resolve(l.value).then(i,c);u((m=m.apply(M,t)).next())});import{_ as Q,r as y,c as x,b as C,d as s,w as n,g as v,B as z,C as B,j as P,D as R,F as j,n as L,o as V,G as E,l as g,H,I as F,p as G,a as r,m as w,t as A}from"./index-XtNpSMFt.js";import{S as W,i as I,L as Y,e as Z}from"./StatsCard-CSWzpKrl.js";import{m as U}from"./menu-DNv9gB1J.js";import{o as J}from"./order-CU4EjPqU.js";import{m as X}from"./message-CDTf0RK9.js";const $={__name:"index",setup(M,{expose:t}){t();const m=v(),a=v(),_=v("7d"),i=v([{title:"今日订单",value:0,description:"今日新增订单数",icon:z,type:"primary",trend:{value:12,unit:"%",label:"较昨日",type:"up"}},{title:"菜品总数",value:0,description:"当前可用菜品",icon:B,type:"success",trend:{value:3,unit:"个",label:"新增",type:"up"}},{title:"活跃用户",value:0,description:"本周活跃用户",icon:P,type:"warning",trend:{value:8,unit:"%",label:"较上周",type:"up"}},{title:"月访问量",value:0,description:"本月总访问次数",icon:R,type:"info",trend:{value:15,unit:"%",label:"较上月",type:"up"}}]),c=v([]),u=v([]),l=()=>b(this,null,function*(){try{const e=yield U.getStatistics();e.data&&(i.value[0].value=e.data.todayOrders||0,i.value[1].value=e.data.totalDishes||0,i.value[2].value=e.data.activeUsers||0,i.value[3].value=e.data.monthlyVisits||0)}catch(e){console.error("加载统计数据失败:",e),i.value[0].value=28,i.value[1].value=156,i.value[2].value=1240,i.value[3].value=8650}}),p=()=>b(this,null,function*(){try{const e=yield J.getTodayOrders();e.data&&(c.value=e.data.slice(0,5).map(d=>{var k;return{id:d.id,userName:((k=d.user)==null?void 0:k.name)||"未知用户",items:JSON.parse(d.items||"[]").map(D=>D.dishName).join(", "),status:d.status}}))}catch(e){console.error("加载最新订单失败:",e),c.value=[{id:"001",userName:"张三",items:"红烧肉, 米饭",status:"pending"},{id:"002",userName:"李四",items:"西红柿鸡蛋, 紫菜蛋花汤",status:"completed"},{id:"003",userName:"王五",items:"凉拌黄瓜",status:"pending"}]}}),f=()=>b(this,null,function*(){try{const e=yield X.getMessages({limit:5});if(e.data){const d=Array.isArray(e.data)?e.data:e.data.list||[];u.value=d.slice(0,5)}}catch(e){console.error("加载最新留言失败:",e),u.value=[{id:1,userName:"张三",content:"菜品很好吃，下次还会再来！",createdAt:new Date},{id:2,userName:"李四",content:"服务态度很好，推荐！",createdAt:new Date(Date.now()-36e5)},{id:3,userName:"王五",content:"菜单丰富，价格实惠！",createdAt:new Date(Date.now()-72e5)}]}}),h=()=>{if(!m.value)return;const e=I(m.value),d={tooltip:{trigger:"axis",backgroundColor:"rgba(50, 50, 50, 0.9)",borderColor:"#333",textStyle:{color:"#fff"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:_.value==="7d"?["周一","周二","周三","周四","周五","周六","周日"]:Array.from({length:30},(k,D)=>`${D+1}日`),axisLine:{lineStyle:{color:"#e0e0e0"}}},yAxis:{type:"value",axisLine:{lineStyle:{color:"#e0e0e0"}},splitLine:{lineStyle:{color:"#f0f0f0"}}},series:[{data:_.value==="7d"?[12,19,3,5,2,3,8]:Array.from({length:30},()=>Math.floor(Math.random()*20)+5),type:"line",smooth:!0,symbol:"circle",symbolSize:6,lineStyle:{width:3},areaStyle:{color:new Y(0,0,0,1,[{offset:0,color:"rgba(24, 144, 255, 0.3)"},{offset:1,color:"rgba(24, 144, 255, 0.1)"}])},itemStyle:{color:"#1890ff"}}]};e.setOption(d),window.addEventListener("resize",()=>e.resize())},S=()=>{if(!a.value)return;const e=I(a.value),d={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},legend:{orient:"vertical",left:"left"},series:[{name:"菜品分类",type:"pie",radius:["40%","70%"],center:["60%","50%"],data:[{value:35,name:"热菜",itemStyle:{color:"#5470c6"}},{value:25,name:"凉菜",itemStyle:{color:"#91cc75"}},{value:20,name:"汤品",itemStyle:{color:"#fac858"}},{value:15,name:"主食",itemStyle:{color:"#ee6666"}},{value:5,name:"甜品",itemStyle:{color:"#73c0de"}}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}},label:{show:!0,formatter:"{b}: {d}%"}}]};e.setOption(d),window.addEventListener("resize",()=>e.resize())},N=e=>({pending:"warning",completed:"success",cancelled:"danger"})[e]||"info",o=e=>({pending:"待处理",completed:"已完成",cancelled:"已取消"})[e]||"未知",O=e=>E(e).format("MM-DD HH:mm"),q=()=>{p()},K=()=>{f()};j(_,()=>{L(()=>{h()})}),V(()=>b(this,null,function*(){yield l(),yield p(),yield f(),L(()=>{h(),S()})}));const T={orderChartRef:m,categoryChartRef:a,chartPeriod:_,statistics:i,recentOrders:c,recentMessages:u,loadStatistics:l,loadRecentOrders:p,loadRecentMessages:f,initOrderChart:h,initCategoryChart:S,getStatusType:N,getStatusText:o,formatTime:O,refreshOrders:q,refreshMessages:K,ref:v,onMounted:V,nextTick:L,watch:j,get echarts(){return Z},get menuApi(){return U},get orderApi(){return J},get messageApi(){return X},get dayjs(){return E},StatsCard:W,get ShoppingCart(){return z},get Bowl(){return B},get User(){return P},get TrendCharts(){return R}};return Object.defineProperty(T,"__isScriptSetup",{enumerable:!1,value:!0}),T}},ee={class:"dashboard"},te={class:"chart-card"},ae={class:"chart-header"},se={ref:"orderChartRef",class:"chart-container"},re={class:"chart-card"},oe={ref:"categoryChartRef",class:"chart-container"},ne={class:"data-card"},le={class:"card-header"},ie={class:"data-card"},de={class:"card-header"},ce={class:"message-list"},ue={class:"message-header"},me={class:"message-user"},pe={class:"message-time"},_e={class:"message-content"};function fe(M,t,m,a,_,i){const c=y("el-col"),u=y("el-row"),l=y("el-button"),p=y("el-button-group"),f=y("el-table-column"),h=y("el-tag"),S=y("el-table"),N=y("el-empty");return g(),x("div",ee,[C(" 统计卡片 "),s(u,{gutter:20,class:"mb-6"},{default:n(()=>[(g(!0),x(H,null,F(a.statistics,(o,O)=>(g(),G(c,{xs:24,sm:12,md:6,key:O},{default:n(()=>[s(a.StatsCard,{title:o.title,value:o.value,icon:o.icon,type:o.type,trend:o.trend,description:o.description,animated:!0},null,8,["title","value","icon","type","trend","description"])]),_:2},1024))),128))]),_:1}),C(" 图表区域 "),s(u,{gutter:20,class:"mb-6"},{default:n(()=>[s(c,{xs:24,md:12},{default:n(()=>[r("div",te,[r("div",ae,[t[4]||(t[4]=r("h3",null,"订单趋势",-1)),s(p,{size:"small"},{default:n(()=>[s(l,{type:a.chartPeriod==="7d"?"primary":"",onClick:t[0]||(t[0]=o=>a.chartPeriod="7d")},{default:n(()=>t[2]||(t[2]=[w("7天",-1)])),_:1,__:[2]},8,["type"]),s(l,{type:a.chartPeriod==="30d"?"primary":"",onClick:t[1]||(t[1]=o=>a.chartPeriod="30d")},{default:n(()=>t[3]||(t[3]=[w("30天",-1)])),_:1,__:[3]},8,["type"])]),_:1})]),r("div",se,null,512)])]),_:1}),s(c,{xs:24,md:12},{default:n(()=>[r("div",re,[t[5]||(t[5]=r("div",{class:"chart-header"},[r("h3",null,"菜品分类统计")],-1)),r("div",oe,null,512)])]),_:1})]),_:1}),C(" 最新数据 "),s(u,{gutter:20},{default:n(()=>[s(c,{xs:24,md:12},{default:n(()=>[r("div",ne,[r("div",le,[t[7]||(t[7]=r("h3",null,"最新订单",-1)),s(l,{type:"text",onClick:a.refreshOrders},{default:n(()=>t[6]||(t[6]=[w("刷新",-1)])),_:1,__:[6]})]),s(S,{data:a.recentOrders,style:{width:"100%"},size:"small"},{default:n(()=>[s(f,{prop:"id",label:"订单号",width:"80"}),s(f,{prop:"userName",label:"用户"}),s(f,{prop:"items",label:"菜品","show-overflow-tooltip":""}),s(f,{prop:"status",label:"状态",width:"80"},{default:n(({row:o})=>[s(h,{type:a.getStatusType(o.status),size:"small"},{default:n(()=>[w(A(a.getStatusText(o.status)),1)]),_:2},1032,["type"])]),_:1})]),_:1},8,["data"])])]),_:1}),s(c,{xs:24,md:12},{default:n(()=>[r("div",ie,[r("div",de,[t[9]||(t[9]=r("h3",null,"最新留言",-1)),s(l,{type:"text",onClick:a.refreshMessages},{default:n(()=>t[8]||(t[8]=[w("刷新",-1)])),_:1,__:[8]})]),r("div",ce,[(g(!0),x(H,null,F(a.recentMessages,o=>(g(),x("div",{key:o.id,class:"message-item"},[r("div",ue,[r("span",me,A(o.userName),1),r("span",pe,A(a.formatTime(o.createdAt)),1)]),r("div",_e,A(o.content),1)]))),128)),a.recentMessages.length?C("v-if",!0):(g(),G(N,{key:0,description:"暂无留言","image-size":80}))])])]),_:1})]),_:1})])}const Se=Q($,[["render",fe],["__scopeId","data-v-48f440f2"],["__file","E:/wx-nan/webs/admin/src/views/dashboard/index.vue"]]);export{Se as default};
