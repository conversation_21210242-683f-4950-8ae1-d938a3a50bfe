import request from '@/utils/request'

/**
 * 微信权限管理 API
 */
export const wechatPermissionApi = {
  /**
   * 获取权限列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 权限列表
   */
  getPermissions(params = {}) {
    return request({
      url: '/admin/wechat/permissions',
      method: 'GET',
      params
    })
  },

  /**
   * 获取权限统计数据
   * @returns {Promise} 统计数据
   */
  getStats() {
    return request({
      url: '/admin/wechat/permissions/stats',
      method: 'GET'
    })
  },

  /**
   * 添加权限
   * @param {Object} data - 权限数据
   * @param {string} data.openid - 微信openid
   * @param {string} data.permissionLevel - 权限级别
   * @param {string} data.expiresAt - 过期时间
   * @param {string} data.remark - 备注
   * @returns {Promise} 添加结果
   */
  addPermission(data) {
    return request({
      url: '/admin/wechat/permissions',
      method: 'POST',
      data
    })
  },

  /**
   * 更新权限
   * @param {number} id - 权限ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 更新结果
   */
  updatePermission(id, data) {
    return request({
      url: `/admin/wechat/permissions/${id}`,
      method: 'PUT',
      data
    })
  },

  /**
   * 撤销权限
   * @param {number} id - 权限ID
   * @returns {Promise} 撤销结果
   */
  revokePermission(id) {
    return request({
      url: `/admin/wechat/permissions/${id}/revoke`,
      method: 'POST'
    })
  },

  /**
   * 恢复权限
   * @param {number} id - 权限ID
   * @returns {Promise} 恢复结果
   */
  restorePermission(id) {
    return request({
      url: `/admin/wechat/permissions/${id}/restore`,
      method: 'POST'
    })
  },

  /**
   * 批量撤销权限
   * @param {Array} ids - 权限ID数组
   * @returns {Promise} 批量撤销结果
   */
  batchRevokePermissions(ids) {
    return request({
      url: '/admin/wechat/permissions/batch-revoke',
      method: 'POST',
      data: { ids }
    })
  },

  /**
   * 批量恢复权限
   * @param {Array} ids - 权限ID数组
   * @returns {Promise} 批量恢复结果
   */
  batchRestorePermissions(ids) {
    return request({
      url: '/admin/wechat/permissions/batch-restore',
      method: 'POST',
      data: { ids }
    })
  },

  /**
   * 获取权限详情
   * @param {number} id - 权限ID
   * @returns {Promise} 权限详情
   */
  getPermissionDetail(id) {
    return request({
      url: `/admin/wechat/permissions/${id}`,
      method: 'GET'
    })
  },

  /**
   * 检查用户权限
   * @param {string} openid - 微信openid
   * @returns {Promise} 权限检查结果
   */
  checkUserPermission(openid) {
    return request({
      url: `/admin/wechat/permissions/check/${openid}`,
      method: 'GET'
    })
  },

  /**
   * 获取权限历史记录
   * @param {string} openid - 微信openid
   * @returns {Promise} 权限历史
   */
  getPermissionHistory(openid) {
    return request({
      url: `/admin/wechat/permissions/history/${openid}`,
      method: 'GET'
    })
  },

  /**
   * 搜索微信用户
   * @param {string} keyword - 搜索关键词
   * @returns {Promise} 用户列表
   */
  searchWechatUsers(keyword) {
    return request({
      url: '/admin/wechat/users/search',
      method: 'GET',
      params: { keyword }
    })
  },

  /**
   * 获取登录日志
   * @param {Object} params - 查询参数
   * @returns {Promise} 登录日志
   */
  getLoginLogs(params = {}) {
    return request({
      url: '/admin/wechat/login-logs',
      method: 'GET',
      params
    })
  },

  /**
   * 导出权限数据
   * @param {Object} params - 导出参数
   * @returns {Promise} 导出结果
   */
  exportPermissions(params = {}) {
    return request({
      url: '/admin/wechat/permissions/export',
      method: 'GET',
      params,
      responseType: 'blob'
    })
  },

  /**
   * 导入权限数据
   * @param {FormData} formData - 文件数据
   * @returns {Promise} 导入结果
   */
  importPermissions(formData) {
    return request({
      url: '/admin/wechat/permissions/import',
      method: 'POST',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取权限模板
   * @returns {Promise} 模板文件
   */
  getPermissionTemplate() {
    return request({
      url: '/admin/wechat/permissions/template',
      method: 'GET',
      responseType: 'blob'
    })
  }
}

/**
 * 微信登录日志 API
 */
export const wechatLoginLogApi = {
  /**
   * 获取登录日志列表
   * @param {Object} params - 查询参数
   * @returns {Promise} 日志列表
   */
  getLogs(params = {}) {
    return request({
      url: '/admin/wechat/login-logs',
      method: 'GET',
      params
    })
  },

  /**
   * 获取登录统计
   * @param {Object} params - 统计参数
   * @returns {Promise} 统计数据
   */
  getLoginStats(params = {}) {
    return request({
      url: '/admin/wechat/login-logs/stats',
      method: 'GET',
      params
    })
  },

  /**
   * 清理过期日志
   * @param {number} days - 保留天数
   * @returns {Promise} 清理结果
   */
  cleanupLogs(days = 30) {
    return request({
      url: '/admin/wechat/login-logs/cleanup',
      method: 'POST',
      data: { days }
    })
  },

  /**
   * 导出登录日志
   * @param {Object} params - 导出参数
   * @returns {Promise} 导出结果
   */
  exportLogs(params = {}) {
    return request({
      url: '/admin/wechat/login-logs/export',
      method: 'GET',
      params,
      responseType: 'blob'
    })
  }
}

/**
 * 微信配置管理 API
 */
export const wechatConfigApi = {
  /**
   * 获取微信配置
   * @returns {Promise} 配置信息
   */
  getConfig() {
    return request({
      url: '/admin/wechat/config',
      method: 'GET'
    })
  },

  /**
   * 更新微信配置
   * @param {Object} data - 配置数据
   * @returns {Promise} 更新结果
   */
  updateConfig(data) {
    return request({
      url: '/admin/wechat/config',
      method: 'PUT',
      data
    })
  },

  /**
   * 测试微信配置
   * @returns {Promise} 测试结果
   */
  testConfig() {
    return request({
      url: '/admin/wechat/config/test',
      method: 'POST'
    })
  },

  /**
   * 刷新微信access_token
   * @returns {Promise} 刷新结果
   */
  refreshAccessToken() {
    return request({
      url: '/admin/wechat/config/refresh-token',
      method: 'POST'
    })
  }
}

// 默认导出
export default {
  wechatPermissionApi,
  wechatLoginLogApi,
  wechatConfigApi
}
