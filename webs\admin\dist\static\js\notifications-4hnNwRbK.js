var fe=Object.defineProperty;var I=Object.getOwnPropertySymbols;var ge=Object.prototype.hasOwnProperty,be=Object.prototype.propertyIsEnumerable;var G=(i,l,s)=>l in i?fe(i,l,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[l]=s,A=(i,l)=>{for(var s in l||(l={}))ge.call(l,s)&&G(i,s,l[s]);if(I)for(var s of I(l))be.call(l,s)&&G(i,s,l[s]);return i};var V=(i,l,s)=>new Promise((t,w)=>{var T=c=>{try{u(s.next(c))}catch(g){w(g)}},f=c=>{try{u(s.throw(c))}catch(g){w(g)}},u=c=>c.done?t(c.value):Promise.resolve(c.value).then(T,f);u((s=s.apply(i,l)).next())});import{a0 as _,_ as ye,r as m,c as U,d as o,b as O,w as n,g as v,h as S,q as J,o as W,E as p,G as K,O as he,J as _e,k as z,l as x,m as b,t as M,p as P,H,I as q}from"./index-XtNpSMFt.js";import{C as ve}from"./CustomTable-BfiU6RpM.js";import{u as Q}from"./user-bKKJkI49.js";const D={getNotifications:i=>_.get("/notifications",i),getNotificationDetail:i=>_.get(`/notifications/${i}`),createNotification:i=>_.post("/notifications",i),updateNotification:(i,l)=>_.put(`/notifications/${i}`,l),deleteNotification:i=>_.delete(`/notifications/${i}`),publishNotification:i=>_.put(`/notifications/${i}/publish`),unpublishNotification:i=>_.put(`/notifications/${i}/unpublish`),batchDelete:i=>_.delete("/notifications/batch",{data:{ids:i}}),getNotificationStatistics:()=>_.get("/notifications/statistics")},we={__name:"notifications",setup(i,{expose:l}){l();const s=v(!1),t=v([]),w=v([]),T=v(!1),f=v("add"),u=v(!1),c=v(),g=v([]),d=S({page:1,size:10,total:0}),y=S({title:"",type:"",status:"",priority:""}),h=S({id:null,title:"",content:"",type:"info",priority:"normal",targetUsers:[],attachmentUrl:"",publishTime:null,publishNow:!1}),C=[{label:"系统通知",value:"system"},{label:"菜单更新",value:"menu"},{label:"活动公告",value:"activity"},{label:"维护通知",value:"maintenance"},{label:"其他",value:"other"}],N=[{label:"低",value:"low"},{label:"普通",value:"normal"},{label:"高",value:"high"},{label:"紧急",value:"urgent"}],B=[{type:"selection",width:55},{prop:"title",label:"标题",minWidth:200,showOverflowTooltip:!0},{prop:"type",label:"类型",width:100,slot:!0},{prop:"priority",label:"优先级",width:100,slot:!0},{prop:"status",label:"状态",width:100,slot:!0},{prop:"targetCount",label:"目标用户",width:100,formatter:e=>{var r;return((r=e.targetUsers)==null?void 0:r.length)||"全部"}},{prop:"publishTime",label:"发布时间",width:160,formatter:e=>j(e.publishTime)},{prop:"createdAt",label:"创建时间",width:160,formatter:e=>j(e.createdAt)},{label:"操作",width:220,slot:"operation",fixed:"right"}],Y=[{prop:"title",label:"标题",type:"input"},{prop:"type",label:"类型",type:"select",options:C},{prop:"status",label:"状态",type:"select",options:[{label:"草稿",value:"draft"},{label:"已发布",value:"published"},{label:"已撤回",value:"revoked"}]},{prop:"priority",label:"优先级",type:"select",options:N}],E={title:[{required:!0,message:"请输入通知标题",trigger:"blur"}],content:[{required:!0,message:"请输入通知内容",trigger:"blur"}],type:[{required:!0,message:"请选择通知类型",trigger:"change"}],priority:[{required:!0,message:"请选择优先级",trigger:"change"}]},R=J(()=>({add:"新增通知",edit:"编辑通知",view:"查看通知"})[f.value]),a=()=>V(this,null,function*(){s.value=!0;try{const e=A({page:d.page,size:d.size},y),r=yield D.getNotifications(e);r.data&&(t.value=r.data.list||[],d.total=r.data.total||0)}catch(e){console.error("加载通知数据失败:",e),p.error("加载数据失败"),t.value=[],d.total=0}finally{s.value=!1}}),k=()=>V(this,null,function*(){try{const e=yield Q.getUsers({size:1e3});e.data&&(g.value=e.data.list||[])}catch(e){console.error("加载用户数据失败:",e),g.value=[{id:1,name:"张三"},{id:2,name:"李四"},{id:3,name:"王五"}]}}),j=e=>e?K(e).format("YYYY-MM-DD HH:mm"):"-",X=e=>({system:"info",menu:"success",activity:"warning",maintenance:"danger",other:""})[e]||"info",Z=e=>({system:"系统通知",menu:"菜单更新",activity:"活动公告",maintenance:"维护通知",other:"其他"})[e]||e,$=e=>({draft:"info",published:"success",revoked:"warning"})[e]||"info",ee=e=>({draft:"草稿",published:"已发布",revoked:"已撤回"})[e]||e,te=e=>({low:"info",normal:"",high:"warning",urgent:"danger"})[e]||"",ae=e=>({low:"低",normal:"普通",high:"高",urgent:"紧急"})[e]||e,le=e=>{Object.assign(y,e),d.page=1,a()},oe=()=>{Object.keys(y).forEach(e=>{y[e]=""}),d.page=1,a()},ne=e=>{d.page=e,a()},ie=e=>{d.size=e,d.page=1,a()},F=()=>{Object.assign(h,{id:null,title:"",content:"",type:"info",priority:"normal",targetUsers:[],attachmentUrl:"",publishTime:null,publishNow:!1})},re=()=>{f.value="add",F(),T.value=!0},se=e=>{f.value="view",Object.assign(h,e),T.value=!0},de=e=>{f.value="edit",Object.assign(h,e),T.value=!0},ue=e=>V(this,null,function*(){try{const r=e.status==="published"?"撤回":"发布";yield z.confirm(`确定要${r}这条通知吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),e.status==="published"?(yield D.unpublishNotification(e.id),p.success("通知已撤回")):(yield D.publishNotification(e.id),p.success("通知已发布")),a()}catch(r){r!=="cancel"&&(console.error("操作失败:",r),p.error("操作失败"))}}),ce=e=>V(this,null,function*(){try{yield z.confirm("确定要删除这条通知吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield D.deleteNotification(e.id),p.success("删除成功"),a()}catch(r){r!=="cancel"&&(console.error("删除通知失败:",r),p.error("删除失败"))}}),me=()=>V(this,null,function*(){if(!w.value.length){p.warning("请选择要删除的通知");return}try{yield z.confirm(`确定要删除选中的 ${w.value.length} 条通知吗？`,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=w.value.map(r=>r.id);yield D.batchDelete(e),p.success("批量删除成功"),a()}catch(e){e!=="cancel"&&(console.error("批量删除失败:",e),p.error("批量删除失败"))}}),pe=()=>V(this,null,function*(){if(c.value)try{yield c.value.validate(),u.value=!0;const e=A({},h);f.value==="add"?(yield D.createNotification(e),p.success("新增成功")):f.value==="edit"&&(yield D.updateNotification(h.id,e),p.success("更新成功")),T.value=!1,a()}catch(e){console.error("提交失败:",e),p.error("操作失败")}finally{u.value=!1}});W(()=>{a(),k()});const L={loading:s,tableData:t,selectedRows:w,dialogVisible:T,dialogMode:f,submitLoading:u,formRef:c,users:g,pagination:d,searchParams:y,formData:h,notificationTypes:C,priorities:N,columns:B,searchFields:Y,formRules:E,dialogTitle:R,loadData:a,loadUsers:k,formatTime:j,getTypeColor:X,getTypeText:Z,getStatusType:$,getStatusText:ee,getPriorityType:te,getPriorityText:ae,handleSearch:le,handleReset:oe,handleCurrentChange:ne,handleSizeChange:ie,resetForm:F,handleAdd:re,handleView:se,handleEdit:de,handleToggleStatus:ue,handleDelete:ce,handleBatchDelete:me,handleSubmit:pe,ref:v,reactive:S,onMounted:W,computed:J,get ElMessage(){return p},get ElMessageBox(){return z},get Plus(){return _e},get Delete(){return he},CustomTable:ve,get notificationApi(){return D},get userApi(){return Q},get dayjs(){return K}};return Object.defineProperty(L,"__isScriptSetup",{enumerable:!1,value:!0}),L}},Te={class:"notification-management"};function De(i,l,s,t,w,T){const f=m("el-icon"),u=m("el-button"),c=m("el-tag"),g=m("el-input"),d=m("el-form-item"),y=m("el-col"),h=m("el-option"),C=m("el-select"),N=m("el-row"),B=m("el-date-picker"),Y=m("el-switch"),E=m("el-form"),R=m("el-dialog");return x(),U("div",Te,[o(t.CustomTable,{title:"系统通知管理",data:t.tableData,columns:t.columns,loading:t.loading,pagination:t.pagination,"show-search":!0,"search-fields":t.searchFields,onSearch:t.handleSearch,onReset:t.handleReset,onCurrentChange:t.handleCurrentChange,onSizeChange:t.handleSizeChange},{actions:n(()=>[o(u,{type:"primary",onClick:t.handleAdd},{default:n(()=>[o(f,null,{default:n(()=>[o(t.Plus)]),_:1}),l[10]||(l[10]=b(" 新增通知 ",-1))]),_:1,__:[10]}),o(u,{onClick:t.handleBatchDelete,disabled:!t.selectedRows.length},{default:n(()=>[o(f,null,{default:n(()=>[o(t.Delete)]),_:1}),l[11]||(l[11]=b(" 批量删除 ",-1))]),_:1,__:[11]},8,["disabled"])]),type:n(({row:a})=>[o(c,{type:t.getTypeColor(a.type),size:"small"},{default:n(()=>[b(M(t.getTypeText(a.type)),1)]),_:2},1032,["type"])]),status:n(({row:a})=>[o(c,{type:t.getStatusType(a.status)},{default:n(()=>[b(M(t.getStatusText(a.status)),1)]),_:2},1032,["type"])]),priority:n(({row:a})=>[o(c,{type:t.getPriorityType(a.priority),size:"small"},{default:n(()=>[b(M(t.getPriorityText(a.priority)),1)]),_:2},1032,["type"])]),operation:n(({row:a})=>[o(u,{size:"small",onClick:k=>t.handleView(a)},{default:n(()=>l[12]||(l[12]=[b("查看",-1)])),_:2,__:[12]},1032,["onClick"]),o(u,{size:"small",type:"primary",onClick:k=>t.handleEdit(a)},{default:n(()=>l[13]||(l[13]=[b("编辑",-1)])),_:2,__:[13]},1032,["onClick"]),o(u,{size:"small",type:a.status==="published"?"warning":"success",onClick:k=>t.handleToggleStatus(a)},{default:n(()=>[b(M(a.status==="published"?"撤回":"发布"),1)]),_:2},1032,["type","onClick"]),o(u,{size:"small",type:"danger",onClick:k=>t.handleDelete(a)},{default:n(()=>l[14]||(l[14]=[b("删除",-1)])),_:2,__:[14]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),O(" 通知表单对话框 "),o(R,{modelValue:t.dialogVisible,"onUpdate:modelValue":l[9]||(l[9]=a=>t.dialogVisible=a),title:t.dialogTitle,width:"700px","close-on-click-modal":!1},{footer:n(()=>[o(u,{onClick:l[8]||(l[8]=a=>t.dialogVisible=!1)},{default:n(()=>l[15]||(l[15]=[b("取消",-1)])),_:1,__:[15]}),t.dialogMode!=="view"?(x(),P(u,{key:0,type:"primary",loading:t.submitLoading,onClick:t.handleSubmit},{default:n(()=>l[16]||(l[16]=[b(" 确定 ",-1)])),_:1,__:[16]},8,["loading"])):O("v-if",!0)]),default:n(()=>[o(E,{ref:"formRef",model:t.formData,rules:t.formRules,"label-width":"100px",class:"notification-form"},{default:n(()=>[o(N,{gutter:20},{default:n(()=>[o(y,{span:12},{default:n(()=>[o(d,{label:"通知标题",prop:"title"},{default:n(()=>[o(g,{modelValue:t.formData.title,"onUpdate:modelValue":l[0]||(l[0]=a=>t.formData.title=a),placeholder:"请输入通知标题"},null,8,["modelValue"])]),_:1})]),_:1}),o(y,{span:12},{default:n(()=>[o(d,{label:"通知类型",prop:"type"},{default:n(()=>[o(C,{modelValue:t.formData.type,"onUpdate:modelValue":l[1]||(l[1]=a=>t.formData.type=a),placeholder:"请选择类型",style:{width:"100%"}},{default:n(()=>[(x(),U(H,null,q(t.notificationTypes,a=>o(h,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(N,{gutter:20},{default:n(()=>[o(y,{span:12},{default:n(()=>[o(d,{label:"优先级",prop:"priority"},{default:n(()=>[o(C,{modelValue:t.formData.priority,"onUpdate:modelValue":l[2]||(l[2]=a=>t.formData.priority=a),placeholder:"请选择优先级",style:{width:"100%"}},{default:n(()=>[(x(),U(H,null,q(t.priorities,a=>o(h,{key:a.value,label:a.label,value:a.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),_:1})]),_:1}),o(y,{span:12},{default:n(()=>[o(d,{label:"发布时间",prop:"publishTime"},{default:n(()=>[o(B,{modelValue:t.formData.publishTime,"onUpdate:modelValue":l[3]||(l[3]=a=>t.formData.publishTime=a),type:"datetime",placeholder:"选择发布时间",format:"YYYY-MM-DD HH:mm","value-format":"YYYY-MM-DD HH:mm:ss",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1}),o(d,{label:"通知内容",prop:"content"},{default:n(()=>[o(g,{modelValue:t.formData.content,"onUpdate:modelValue":l[4]||(l[4]=a=>t.formData.content=a),type:"textarea",rows:6,placeholder:"请输入通知内容",maxlength:"1000","show-word-limit":""},null,8,["modelValue"])]),_:1}),o(d,{label:"目标用户",prop:"targetUsers"},{default:n(()=>[o(C,{modelValue:t.formData.targetUsers,"onUpdate:modelValue":l[5]||(l[5]=a=>t.formData.targetUsers=a),multiple:"",placeholder:"选择目标用户（不选则发送给所有用户）",style:{width:"100%"},clearable:""},{default:n(()=>[(x(!0),U(H,null,q(t.users,a=>(x(),P(h,{key:a.id,label:a.name,value:a.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),o(d,{label:"附件链接",prop:"attachmentUrl"},{default:n(()=>[o(g,{modelValue:t.formData.attachmentUrl,"onUpdate:modelValue":l[6]||(l[6]=a=>t.formData.attachmentUrl=a),placeholder:"可选：相关链接或附件地址"},null,8,["modelValue"])]),_:1}),t.dialogMode==="add"?(x(),P(d,{key:0,label:"立即发布",prop:"publishNow"},{default:n(()=>[o(Y,{modelValue:t.formData.publishNow,"onUpdate:modelValue":l[7]||(l[7]=a=>t.formData.publishNow=a),"active-text":"是","inactive-text":"否"},null,8,["modelValue"])]),_:1})):O("v-if",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}const Ne=ye(we,[["render",De],["__scopeId","data-v-e0f9529b"],["__file","E:/wx-nan/webs/admin/src/views/message/notifications.vue"]]);export{Ne as default};
