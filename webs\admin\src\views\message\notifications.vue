<template>
  <div class="notification-management">
    <CustomTable
      title="系统通知管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增通知
        </el-button>
        <el-button @click="handleBatchDelete" :disabled="!selectedRows.length">
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
      </template>

      <template #type="{ row }">
        <el-tag :type="getTypeColor(row.type)" size="small">
          {{ getTypeText(row.type) }}
        </el-tag>
      </template>

      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <template #priority="{ row }">
        <el-tag :type="getPriorityType(row.priority)" size="small">
          {{ getPriorityText(row.priority) }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-button size="small" @click="handleView(row)">查看</el-button>
        <el-button size="small" type="primary" @click="handleEdit(row)"
          >编辑</el-button
        >
        <el-button
          size="small"
          :type="row.status === 'published' ? 'warning' : 'success'"
          @click="handleToggleStatus(row)"
        >
          {{ row.status === 'published' ? '撤回' : '发布' }}
        </el-button>
        <el-button size="small" type="danger" @click="handleDelete(row)"
          >删除</el-button
        >
      </template>
    </CustomTable>

    <!-- 通知表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="700px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        class="notification-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="通知标题" prop="title">
              <el-input v-model="formData.title" placeholder="请输入通知标题" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通知类型" prop="type">
              <el-select
                v-model="formData.type"
                placeholder="请选择类型"
                style="width: 100%"
              >
                <el-option
                  v-for="type in notificationTypes"
                  :key="type.value"
                  :label="type.label"
                  :value="type.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select
                v-model="formData.priority"
                placeholder="请选择优先级"
                style="width: 100%"
              >
                <el-option
                  v-for="priority in priorities"
                  :key="priority.value"
                  :label="priority.label"
                  :value="priority.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间" prop="publishTime">
              <el-date-picker
                v-model="formData.publishTime"
                type="datetime"
                placeholder="选择发布时间"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="通知内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="6"
            placeholder="请输入通知内容"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="目标用户" prop="targetUsers">
          <el-select
            v-model="formData.targetUsers"
            multiple
            placeholder="选择目标用户（不选则发送给所有用户）"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="附件链接" prop="attachmentUrl">
          <el-input
            v-model="formData.attachmentUrl"
            placeholder="可选：相关链接或附件地址"
          />
        </el-form-item>

        <el-form-item
          label="立即发布"
          prop="publishNow"
          v-if="dialogMode === 'add'"
        >
          <el-switch
            v-model="formData.publishNow"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
          v-if="dialogMode !== 'view'"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import { notificationApi } from '@/api/notification'
import { userApi } from '@/api/user'
import dayjs from 'dayjs'

const loading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const dialogVisible = ref(false)
const dialogMode = ref('add') // add, edit, view
const submitLoading = ref(false)
const formRef = ref()
const users = ref([])

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  title: '',
  type: '',
  status: '',
  priority: ''
})

const formData = reactive({
  id: null,
  title: '',
  content: '',
  type: 'info',
  priority: 'normal',
  targetUsers: [],
  attachmentUrl: '',
  publishTime: null,
  publishNow: false
})

const notificationTypes = [
  { label: '系统通知', value: 'system' },
  { label: '菜单更新', value: 'menu' },
  { label: '活动公告', value: 'activity' },
  { label: '维护通知', value: 'maintenance' },
  { label: '其他', value: 'other' }
]

const priorities = [
  { label: '低', value: 'low' },
  { label: '普通', value: 'normal' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 表格列配置
const columns = [
  { type: 'selection', width: 55 },
  { prop: 'title', label: '标题', minWidth: 200, showOverflowTooltip: true },
  { prop: 'type', label: '类型', width: 100, slot: true },
  { prop: 'priority', label: '优先级', width: 100, slot: true },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'targetCount', label: '目标用户', width: 100, formatter: (row) => row.targetUsers?.length || '全部' },
  { prop: 'publishTime', label: '发布时间', width: 160, formatter: (row) => formatTime(row.publishTime) },
  { prop: 'createdAt', label: '创建时间', width: 160, formatter: (row) => formatTime(row.createdAt) },
  { label: '操作', width: 220, slot: 'operation', fixed: 'right' }
]

// 搜索字段配置
const searchFields = [
  { prop: 'title', label: '标题', type: 'input' },
  { prop: 'type', label: '类型', type: 'select', options: notificationTypes },
  { prop: 'status', label: '状态', type: 'select', options: [
    { label: '草稿', value: 'draft' },
    { label: '已发布', value: 'published' },
    { label: '已撤回', value: 'revoked' }
  ]},
  { prop: 'priority', label: '优先级', type: 'select', options: priorities }
]

// 表单验证规则
const formRules = {
  title: [{ required: true, message: '请输入通知标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入通知内容', trigger: 'blur' }],
  type: [{ required: true, message: '请选择通知类型', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }]
}

// 计算属性
const dialogTitle = computed(() => {
  const titles = { add: '新增通知', edit: '编辑通知', view: '查看通知' }
  return titles[dialogMode.value]
})

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }
    const res = await notificationApi.getNotifications(params)
    if (res.data) {
      tableData.value = res.data.list || []
      pagination.total = res.data.total || 0
    }
  } catch (error) {
    console.error('加载通知数据失败:', error)
    ElMessage.error('加载数据失败')
    // API 失败时显示空数据
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

const loadUsers = async () => {
  try {
    const res = await userApi.getUsers({ size: 1000 })
    if (res.data) {
      users.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
    // 使用模拟数据
    users.value = [
      { id: 1, name: '张三' },
      { id: 2, name: '李四' },
      { id: 3, name: '王五' }
    ]
  }
}

// Mock 数据已移除，使用真实 API 数据

const formatTime = (time) => {
  return time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-'
}

const getTypeColor = (type) => {
  const colors = {
    system: 'info',
    menu: 'success',
    activity: 'warning',
    maintenance: 'danger',
    other: ''
  }
  return colors[type] || 'info'
}

const getTypeText = (type) => {
  const texts = {
    system: '系统通知',
    menu: '菜单更新',
    activity: '活动公告',
    maintenance: '维护通知',
    other: '其他'
  }
  return texts[type] || type
}

const getStatusType = (status) => {
  const types = {
    draft: 'info',
    published: 'success',
    revoked: 'warning'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    revoked: '已撤回'
  }
  return texts[status] || status
}

const getPriorityType = (priority) => {
  const types = {
    low: 'info',
    normal: '',
    high: 'warning',
    urgent: 'danger'
  }
  return types[priority] || ''
}

const getPriorityText = (priority) => {
  const texts = {
    low: '低',
    normal: '普通',
    high: '高',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const resetForm = () => {
  Object.assign(formData, {
    id: null,
    title: '',
    content: '',
    type: 'info',
    priority: 'normal',
    targetUsers: [],
    attachmentUrl: '',
    publishTime: null,
    publishNow: false
  })
}

const handleAdd = () => {
  dialogMode.value = 'add'
  resetForm()
  dialogVisible.value = true
}

const handleView = (row) => {
  dialogMode.value = 'view'
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogMode.value = 'edit'
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleToggleStatus = async (row) => {
  try {
    const action = row.status === 'published' ? '撤回' : '发布'
    await ElMessageBox.confirm(`确定要${action}这条通知吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    if (row.status === 'published') {
      await notificationApi.unpublishNotification(row.id)
      ElMessage.success('通知已撤回')
    } else {
      await notificationApi.publishNotification(row.id)
      ElMessage.success('通知已发布')
    }

    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('操作失败:', error)
      ElMessage.error('操作失败')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await notificationApi.deleteNotification(row.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleBatchDelete = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要删除的通知')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedRows.value.length} 条通知吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const ids = selectedRows.value.map(row => row.id)
    await notificationApi.batchDelete(ids)
    ElMessage.success('批量删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitLoading.value = true

    const data = { ...formData }

    if (dialogMode.value === 'add') {
      await notificationApi.createNotification(data)
      ElMessage.success('新增成功')
    } else if (dialogMode.value === 'edit') {
      await notificationApi.updateNotification(formData.id, data)
      ElMessage.success('更新成功')
    }

    dialogVisible.value = false
    loadData()
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitLoading.value = false
  }
}

onMounted(() => {
  loadData()
  loadUsers()
})
</script>

<style scoped lang="scss">
.notification-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

.notification-form {
  @apply max-h-96 overflow-y-auto;
}
</style>
