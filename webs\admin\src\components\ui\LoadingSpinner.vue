<template>
  <div :class="containerClasses">
    <!-- 加载动画 -->
    <div :class="spinnerClasses">
      <svg
        v-if="type === 'spinner'"
        class="animate-spin"
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          class="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          stroke-width="4"
        ></circle>
        <path
          class="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        ></path>
      </svg>
      
      <div
        v-else-if="type === 'dots'"
        class="flex space-x-1"
      >
        <div
          v-for="i in 3"
          :key="i"
          class="w-2 h-2 bg-current rounded-full animate-pulse"
          :style="{ animationDelay: `${(i - 1) * 0.2}s` }"
        ></div>
      </div>
      
      <div
        v-else-if="type === 'bars'"
        class="flex space-x-1"
      >
        <div
          v-for="i in 4"
          :key="i"
          class="w-1 bg-current rounded animate-pulse"
          :class="getBarHeight(i)"
          :style="{ animationDelay: `${(i - 1) * 0.1}s` }"
        ></div>
      </div>
      
      <div
        v-else-if="type === 'pulse'"
        class="w-full h-full bg-current rounded animate-pulse"
      ></div>
    </div>
    
    <!-- 加载文本 -->
    <div v-if="text" :class="textClasses">
      {{ text }}
    </div>
    
    <!-- 进度条 -->
    <div v-if="showProgress && progress !== null" class="w-full mt-3">
      <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
        <span>{{ progressText }}</span>
        <span>{{ progress }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          class="bg-primary-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 加载动画类型
  type: {
    type: String,
    default: 'spinner',
    validator: (value) => ['spinner', 'dots', 'bars', 'pulse'].includes(value)
  },
  // 尺寸
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['xs', 'sm', 'md', 'lg', 'xl'].includes(value)
  },
  // 颜色
  color: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'white', 'gray', 'current'].includes(value)
  },
  // 加载文本
  text: {
    type: String,
    default: ''
  },
  // 是否居中显示
  center: {
    type: Boolean,
    default: false
  },
  // 是否全屏覆盖
  overlay: {
    type: Boolean,
    default: false
  },
  // 是否显示进度条
  showProgress: {
    type: Boolean,
    default: false
  },
  // 进度值 (0-100)
  progress: {
    type: Number,
    default: null
  },
  // 进度文本
  progressText: {
    type: String,
    default: '加载中...'
  }
})

const containerClasses = computed(() => {
  const classes = []
  
  if (props.overlay) {
    classes.push(
      'fixed inset-0 z-50 bg-white bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75',
      'flex items-center justify-center'
    )
  } else if (props.center) {
    classes.push('flex items-center justify-center')
  }
  
  return classes.join(' ')
})

const spinnerClasses = computed(() => {
  const classes = []
  
  // 尺寸样式
  const sizeClasses = {
    xs: 'w-4 h-4',
    sm: 'w-5 h-5',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-10 h-10'
  }
  
  // 颜色样式
  const colorClasses = {
    primary: 'text-primary-600',
    white: 'text-white',
    gray: 'text-gray-600',
    current: 'text-current'
  }
  
  classes.push(sizeClasses[props.size])
  classes.push(colorClasses[props.color])
  
  return classes.join(' ')
})

const textClasses = computed(() => {
  const classes = ['mt-2 text-sm font-medium']
  
  // 颜色样式
  const colorClasses = {
    primary: 'text-primary-600 dark:text-primary-400',
    white: 'text-white',
    gray: 'text-gray-600 dark:text-gray-400',
    current: 'text-current'
  }
  
  classes.push(colorClasses[props.color])
  
  return classes.join(' ')
})

const getBarHeight = (index) => {
  const heights = ['h-2', 'h-3', 'h-4', 'h-3']
  return heights[index - 1] || 'h-2'
}
</script>

<style scoped>
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 1.5s ease-in-out infinite;
}
</style>
