<view class="container">
  <!-- 统计卡片 -->
  <view class="stats-card">
    <view class="stat-item">
      <view class="stat-number">{{totalCount}}</view>
      <view class="stat-label">总通知</view>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <view class="stat-number unread">{{unreadCount}}</view>
      <view class="stat-label">未读</view>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <view class="stat-number">{{readCount}}</view>
      <view class="stat-label">已读</view>
    </view>
  </view>

  <!-- 筛选标签 -->
  <view class="filter-tabs">
    <view
      class="filter-tab {{activeFilter === 'all' ? 'active' : ''}}"
      bindtap="setFilter"
      data-filter="all"
    >
      全部
    </view>
    <view
      class="filter-tab {{activeFilter === 'unread' ? 'active' : ''}}"
      bindtap="setFilter"
      data-filter="unread"
    >
      未读
    </view>
    <view
      class="filter-tab {{activeFilter === 'read' ? 'active' : ''}}"
      bindtap="setFilter"
      data-filter="read"
    >
      已读
    </view>
  </view>

  <!-- 使用通用刷新组件 -->
  <refresh-scroll
    id="refresh-scroll"
    container-height="{{scrollHeight}}"
    request-url="{{requestUrl}}"
    request-params="{{requestParams}}"
    request-headers="{{requestHeaders}}"
    page-size="{{pageSize}}"
    has-more="{{hasMore}}"
    loading-more="{{loading}}"
    empty-icon="bell-o"
    empty-text="{{emptyText}}"
    empty-tip="{{emptyTip}}"
    auto-load="{{true}}"
    data-field="data"
    list-field="notifications"
    total-field="total"
    bind:datachange="onDataChange"
  >
    <view
      slot="content"
      wx:if="{{filteredNotifications.length > 0}}"
      class="notification-list"
    >
      <view
        class="notification-item {{item.read ? 'read' : 'unread'}}"
        wx:for="{{filteredNotifications}}"
        wx:key="id"
        bindtap="markAsRead"
        data-id="{{item.id}}"
      >
        <view class="notification-icon">
          <van-icon
            name="{{item.type === 'system' ? 'setting-o' : (item.type === 'order_notification' || item.type === 'menu_push') ? 'shop-o' : 'chat-o'}}"
            size="24px"
            color="{{item.read ? '#94A3B8' : '#6366F1'}}"
          />
        </view>
        <view class="notification-content">
          <view class="notification-title">{{item.title}}</view>
          <view class="notification-message">{{item.content}}</view>
          <view class="notification-time">{{item.timeText}}</view>
        </view>
        <view class="notification-status" wx:if="{{!item.read}}">
          <view class="unread-dot"></view>
        </view>
      </view>
    </view>
  </refresh-scroll>
</view>
