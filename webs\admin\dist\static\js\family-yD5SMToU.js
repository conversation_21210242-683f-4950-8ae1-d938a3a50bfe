var Y=Object.defineProperty;var N=Object.getOwnPropertySymbols;var Z=Object.prototype.hasOwnProperty,$=Object.prototype.propertyIsEnumerable;var j=(d,e,n)=>e in d?Y(d,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):d[e]=n,L=(d,e)=>{for(var n in e||(e={}))Z.call(e,n)&&j(d,n,e[n]);if(N)for(var n of N(e))$.call(e,n)&&j(d,n,e[n]);return d};var T=(d,e,n)=>new Promise((t,C)=>{var k=o=>{try{i(n.next(o))}catch(c){C(c)}},p=o=>{try{i(n.throw(o))}catch(c){C(c)}},i=o=>o.done?t(o.value):Promise.resolve(o.value).then(k,p);i((n=n.apply(d,e)).next())});import{_ as ee,r as f,c as R,d as r,b as D,w as l,g as x,h as z,o as O,E as g,G as te,M as ae,R as se,k as P,l as v,p as I,m as y,a as _,t as b,H as le,I as ne}from"./index-Cy8N1eGd.js";import{C as oe}from"./CustomTable-DWghEWMD.js";import{m as U}from"./message-DwjFCcib.js";import{f as W}from"./common-CIDIMsc8.js";const re={__name:"family",setup(d,{expose:e}){e();const n=x(!1),t=x([]),C=x(!1),k=x(!1),p=x(null),i=x(),o=z({page:1,size:10,total:0}),c=z({status:"",userName:"",dateRange:[]}),w=z({content:""}),E=[{prop:"user",label:"用户",width:150,slot:!0},{prop:"content",label:"留言内容",minWidth:300,slot:!0},{prop:"status",label:"状态",width:100,slot:!0},{prop:"reply",label:"回复",minWidth:200,slot:!0},{prop:"createdAt",label:"留言时间",width:160,formatter:s=>W(s.createdAt)},{label:"操作",width:180,slot:"operation",fixed:"right"}],F=[{prop:"userName",label:"用户姓名",type:"input"},{prop:"status",label:"状态",type:"select",options:[{label:"全部",value:""},{label:"待回复",value:"pending"},{label:"已回复",value:"replied"}]},{prop:"dateRange",label:"留言时间",type:"daterange"}],B={content:[{required:!0,message:"请输入回复内容",trigger:"blur"},{min:5,max:500,message:"回复内容长度在 5 到 500 个字符",trigger:"blur"}]},u=()=>T(this,null,function*(){n.value=!0;try{const s=L({page:o.page,size:o.size},c),h=yield U.getMessages(s);h.code===200?(t.value=h.data.list||[],o.total=h.data.total||0):g.error(h.message||"加载数据失败")}catch(s){console.error("加载留言数据失败:",s),g.error("加载数据失败")}finally{n.value=!1}}),a=s=>({pending:"warning",replied:"success"})[s]||"info",m=s=>({pending:"待回复",replied:"已回复"})[s]||"未知",M=s=>{Object.assign(c,s),o.page=1,u()},V=()=>{Object.keys(c).forEach(s=>{Array.isArray(c[s])?c[s]=[]:c[s]=""}),o.page=1,u()},S=s=>{o.page=s,u()},q=s=>{o.size=s,o.page=1,u()},G=()=>{u(),g.success("数据已刷新")},H=()=>{g.info("导出功能开发中...")},J=s=>{g.info(`查看留言：${s.content.substring(0,20)}...`)},K=s=>{p.value=s,w.content="",C.value=!0},Q=()=>T(this,null,function*(){if(i.value)try{yield i.value.validate(),k.value=!0,yield new Promise(s=>setTimeout(s,1e3)),p.value.status="replied",p.value.reply=w.content,p.value.replyTime=new Date,g.success("回复发送成功"),C.value=!1}catch(s){console.error("发送回复失败:",s),g.error("发送回复失败")}finally{k.value=!1}}),X=s=>T(this,null,function*(){try{yield P.confirm("确定要删除这条留言吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),g.success("留言删除成功"),u()}catch(h){h!=="cancel"&&g.error("删除留言失败")}});O(()=>{u()});const A={loading:n,tableData:t,replyDialogVisible:C,replyLoading:k,selectedMessage:p,replyFormRef:i,pagination:o,searchParams:c,replyForm:w,columns:E,searchFields:F,replyRules:B,loadData:u,getStatusType:a,getStatusText:m,handleSearch:M,handleReset:V,handleCurrentChange:S,handleSizeChange:q,handleRefresh:G,handleExport:H,handleViewMessage:J,handleReplyMessage:K,handleSubmitReply:Q,handleDeleteMessage:X,ref:x,reactive:z,onMounted:O,get ElMessage(){return g},get ElMessageBox(){return P},get Refresh(){return se},get Download(){return ae},CustomTable:oe,get messageApi(){return U},get formatTime(){return W},get dayjs(){return te}};return Object.defineProperty(A,"__isScriptSetup",{enumerable:!1,value:!0}),A}},ie={class:"family-messages"},ce={class:"user-info"},de={class:"ml-2"},me={class:"message-content"},pe={class:"content-text"},ue={key:0,class:"message-images"},ge={key:0,class:"more-images"},_e={key:0,class:"reply-content"},fe={class:"reply-text"},ye={class:"reply-time"},he={key:1,class:"no-reply"},ve={class:"reply-dialog"},be={class:"original-message"};function Ce(d,e,n,t,C,k){const p=f("el-icon"),i=f("el-button"),o=f("el-avatar"),c=f("el-image"),w=f("el-tag"),E=f("el-input"),F=f("el-form-item"),B=f("el-form"),u=f("el-dialog");return v(),R("div",ie,[r(t.CustomTable,{title:"家庭留言管理",data:t.tableData,columns:t.columns,loading:t.loading,pagination:t.pagination,"show-search":!0,"search-fields":t.searchFields,onSearch:t.handleSearch,onReset:t.handleReset,onCurrentChange:t.handleCurrentChange,onSizeChange:t.handleSizeChange},{actions:l(()=>[r(i,{onClick:t.handleRefresh},{default:l(()=>[r(p,null,{default:l(()=>[r(t.Refresh)]),_:1}),e[3]||(e[3]=y(" 刷新数据 ",-1))]),_:1,__:[3]}),r(i,{onClick:t.handleExport},{default:l(()=>[r(p,null,{default:l(()=>[r(t.Download)]),_:1}),e[4]||(e[4]=y(" 导出留言 ",-1))]),_:1,__:[4]})]),user:l(({row:a})=>{var m,M;return[_("div",ce,[r(o,{src:(m=a.user)==null?void 0:m.avatar,size:32},{default:l(()=>{var V,S;return[y(b((S=(V=a.user)==null?void 0:V.name)==null?void 0:S.charAt(0)),1)]}),_:2},1032,["src"]),_("span",de,b((M=a.user)==null?void 0:M.name),1)])]}),content:l(({row:a})=>[_("div",me,[_("p",pe,b(a.content),1),a.images&&a.images.length?(v(),R("div",ue,[(v(!0),R(le,null,ne(a.images.slice(0,3),(m,M)=>(v(),I(c,{key:M,src:m,"preview-src-list":a.images,class:"message-image",fit:"cover"},null,8,["src","preview-src-list"]))),128)),a.images.length>3?(v(),R("span",ge," +"+b(a.images.length-3),1)):D("v-if",!0)])):D("v-if",!0)])]),status:l(({row:a})=>[r(w,{type:t.getStatusType(a.status)},{default:l(()=>[y(b(t.getStatusText(a.status)),1)]),_:2},1032,["type"])]),reply:l(({row:a})=>[a.reply?(v(),R("div",_e,[_("p",fe,b(a.reply),1),_("p",ye,b(t.formatTime(a.replyTime)),1)])):(v(),R("span",he,"未回复"))]),operation:l(({row:a})=>[r(i,{size:"small",onClick:m=>t.handleViewMessage(a)},{default:l(()=>e[5]||(e[5]=[y("查看",-1)])),_:2,__:[5]},1032,["onClick"]),a.status==="pending"?(v(),I(i,{key:0,size:"small",type:"primary",onClick:m=>t.handleReplyMessage(a)},{default:l(()=>e[6]||(e[6]=[y(" 回复 ",-1)])),_:2,__:[6]},1032,["onClick"])):D("v-if",!0),r(i,{size:"small",type:"danger",onClick:m=>t.handleDeleteMessage(a)},{default:l(()=>e[7]||(e[7]=[y("删除",-1)])),_:2,__:[7]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),D(" 回复对话框 "),r(u,{modelValue:t.replyDialogVisible,"onUpdate:modelValue":e[2]||(e[2]=a=>t.replyDialogVisible=a),title:"回复留言",width:"600px","close-on-click-modal":!1},{footer:l(()=>[r(i,{onClick:e[1]||(e[1]=a=>t.replyDialogVisible=!1)},{default:l(()=>e[9]||(e[9]=[y("取消",-1)])),_:1,__:[9]}),r(i,{type:"primary",loading:t.replyLoading,onClick:t.handleSubmitReply},{default:l(()=>e[10]||(e[10]=[y(" 发送回复 ",-1)])),_:1,__:[10]},8,["loading"])]),default:l(()=>{var a;return[_("div",ve,[_("div",be,[e[8]||(e[8]=_("h4",null,"原留言内容：",-1)),_("p",null,b((a=t.selectedMessage)==null?void 0:a.content),1)]),r(B,{ref:"replyFormRef",model:t.replyForm,rules:t.replyRules,"label-width":"80px"},{default:l(()=>[r(F,{label:"回复内容",prop:"content"},{default:l(()=>[r(E,{modelValue:t.replyForm.content,"onUpdate:modelValue":e[0]||(e[0]=m=>t.replyForm.content=m),type:"textarea",rows:4,placeholder:"请输入回复内容",maxlength:"500","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])])]}),_:1},8,["modelValue"])])}const Ve=ee(re,[["render",Ce],["__scopeId","data-v-f8208a29"],["__file","E:/wx-nan/webs/admin/src/views/message/family.vue"]]);export{Ve as default};
