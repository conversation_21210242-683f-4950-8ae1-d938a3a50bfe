@import "../../styles/miniprogram-design.scss";

/* 🔥 优化：点菜页面 - 固定容器布局 */

/* 🎯 主容器 - 固定高度，防止整页滚动 */
.order-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $gradient-bg;
  @include overflow-hidden;
}

/* 🎯 布局容器 - 左右分栏 */
.order-layout {
  @include flex;
  height: 100vh;
  @include overflow-hidden;
}

/* 🔥 优化：左侧分类导航 - 去掉图标，文字上下排列 */
.category-sidebar {
  width: 200rpx;
  background: $white;
  border-right: 2rpx solid $gray-100;
  @include flex;
  @include flex-col;
  flex-shrink: 0;
  height: 90vh;
  @include overflow-hidden;
  // border: 1px solid red;
  margin-top: 5vh;
  border-bottom-right-radius: 40rpx;
  border-top-right-radius: 40rpx;
  overflow: hidden;
}

/* 分类标题 */
.category-header {
  @include flex;
  @include items-center;
  @include justify-center;
  height: 100rpx;
  background: $white;
  border-bottom: 2rpx solid $gray-100;

  .category-title {
    @include text-lg;
    @include font-semibold;
    @include text-gray-700;
  }
}

/* 🔥 修复：分类滚动区域 - 顶部底部留距离，彻底隐藏滚动条 */
.category-scroll {
  @include flex-1;
  @include overflow-hidden;
  padding: 20rpx 0; // 🔥 顶部底部留距离

  /* 🔥 强制隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  /* 兼容性处理 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE 10+ */
}

/* 🔥 重新设计：分类项 - 降低高度，更紧凑美观 */
.category-item {
  @include flex;
  @include items-center;
  @include justify-center;
  min-height: 80rpx; // 🔥 降低高度，更紧凑
  @include px-2;
  @include py-2;
  margin: 6rpx 12rpx;
  border-radius: $radius-md;
  @include transition;
  cursor: pointer;
  background: $gray-50;
  border: 2rpx solid transparent;
  position: relative;

  .category-name {
    @include text-sm;
    @include font-medium;
    @include text-gray-600;
    @include text-center;
    line-height: 1.3;
    word-break: break-all;
    white-space: normal;
  }

  /* 悬停效果 */
  &:active {
    transform: scale(0.96);
    background: $gray-100;
  }

  /* 🔥 重新设计：激活状态 - 更精致的设计 */
  &.active {
    background: $white;
    border-color: $primary-solid;
    @include shadow-sm;

    /* 左侧指示条 */
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 6rpx;
      height: 40rpx;
      background: $gradient-primary;
      border-radius: 0 6rpx 6rpx 0;
    }

    .category-name {
      @include text-primary;
      @include font-semibold;
    }
  }
}

/* 🔥 重新设计：右侧菜品主区域 */
.dishes-main {
  @include flex-1;
  @include flex;
  @include flex-col;
  background: $gray-50; // 🔥 改为浅灰背景，更有层次
  @include overflow-hidden;
}

/* 🔥 重新设计：菜品滚动区域 */
.dishes-scroll {
  @include flex-1;
  @include overflow-hidden;
  padding: 24rpx 0; // 🔥 顶部留一点间距
}

/* 🔥 重新设计：菜品列表 - 垂直列表布局 */
.dishes-list {
  padding: 0 20rpx;
  min-height: 100%;
}

/* 参照我的菜品页面：菜品卡片样式 */
.dish-card {
  background: white;
  border-radius: 24rpx;
  @include mb-4;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  @include transition;

  &:active {
    transform: scale(0.98);
  }

  &.adding {
    border: 2rpx solid #10b981;
    background: rgba(16, 185, 129, 0.02);
  }
}

/* 卡片头部布局 */
.dish-header-layout {
  @include flex;
  @include gap-3;
  padding: 24rpx;
  padding-bottom: 0;
}

/* 菜品图片区域 */
.dish-image-container {
  position: relative;
  width: 160rpx;
  height: 120rpx;
  @include flex-shrink-0;
  overflow: hidden;
  border-radius: 16rpx;
}

.dish-image {
  width: 160rpx;
  height: 120rpx;
  object-fit: cover;
}

.status-badge {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  @include text-xs;
  font-weight: 600;
  backdrop-filter: blur(8rpx);

  &.added {
    background: rgba(16, 185, 129, 0.9);
    color: white;
  }

  &.available {
    background: rgba(59, 130, 246, 0.9);
    color: white;
  }
}

/* 菜品信息区域 */
.dish-info {
  @include flex-1;
  @include flex;
  @include flex-col;
  @include justify-between;
  min-height: 150rpx;
}

/* 菜品标题行 */
.dish-title-row {
  @include flex;
  @include justify-between;
  @include items-start;
  @include mb-2;
}

.dish-name {
  @include text-base;
  @include font-semibold;
  color: $gray-900;
  @include flex-1;
  margin-right: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dish-category {
  @include text-sm;
  color: $primary-solid;
  // background: rgba(59, 130, 246, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

/* 内联标签区域（替代备注位置） */
.dish-tags-inline {
  @include flex;
  @include gap-2;
  @include flex-wrap;
  @include mb-3;
}

.tag-item-inline {
  padding: 4rpx 10rpx;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
  color: $primary-solid;
  border-radius: 12rpx;
  @include text-xs;
  font-weight: 500;
  border: 1rpx solid rgba(59, 130, 246, 0.2);
}

/* 菜品元信息 */
.dish-meta {
  @include flex;
  @include gap-4;
  justify-content: space-between;
  padding: 10rpx 24rpx;
}

.meta-item {
  @include flex;
  @include items-center;
  @include gap-1;
}

.meta-text {
  @include text-xs;
  color: $gray-500;
}

/* 底部标签区域 */
.dish-tags {
  @include flex;
  @include gap-2;
  @include flex-wrap;
  padding: 0 24rpx 16rpx 24rpx;
}

.tag-item {
  padding: 6rpx 12rpx;
  background: $gray-100;
  color: $gray-600;
  border-radius: 12rpx;
  @include text-xs;
  font-weight: 500;
}

/* 重新设计的添加按钮区域 */
.dish-add-section {
  padding: 16rpx 24rpx 20rpx 24rpx;
  @include flex;
  @include justify-end;
}

.add-to-cart-btn {
  position: relative;
  @include flex;
  @include items-center;
  @include justify-center;
  background: linear-gradient(135deg, #3b82f6, #6366f1);
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  min-width: 200rpx;
  @include shadow-lg;
  @include transition;
  overflow: hidden;

  &:active {
    transform: scale(0.95);
  }

  &.adding {
    background: linear-gradient(135deg, #10b981, #059669);
    animation: btn-success-pulse 0.6s ease-out;
  }
}

.btn-content {
  @include flex;
  @include items-center;
  @include gap-2;
  z-index: 2;
  position: relative;
}

.btn-text {
  @include text-sm;
  @include font-semibold;
  color: white;
  letter-spacing: 0.5rpx;
}

.btn-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: ripple-effect 0.6s ease-out;
}

/* 按钮动画 */
@keyframes btn-success-pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes ripple-effect {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 200rpx;
    height: 200rpx;
    opacity: 0;
  }
}

/* 移除旧的按钮样式，使用新的卡片样式 */

/* 移除旧的动画，保持简洁 */

/* 🔥 优化：空状态 - 更友好的设计 */
.empty-state {
  @include flex;
  @include flex-col;
  @include items-center;
  @include justify-center;
  gap: $space-4; // 🔥 修复：直接使用gap替代gap-4 mixin
  @include flex-1;
  @include py-20;
  padding-left: $space-4; // 🔥 修复：直接使用padding替代px-4
  padding-right: $space-4;

  .empty-icon {
    font-size: 120rpx;
    opacity: 0.6;
    margin-bottom: 16rpx;
  }

  .empty-title {
    @include text-lg;
    @include font-semibold;
    @include text-gray-500;
    margin-bottom: 8rpx;
  }

  .empty-desc {
    @include text-sm;
    @include text-gray-400;
    @include text-center;
    line-height: 1.5;
  }
}

/* 🔥 重新设计：购物车悬浮按钮 - 更美观醒目 */
.shopping-cart-fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  z-index: 1000;
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: $gradient-primary; // 🔥 使用主题色
  @include flex;
  @include items-center;
  @include justify-center;
  @include shadow-xl;
  @include transition;
  border: 4rpx solid $white;

  /* 🔥 增强视觉效果 */
  box-shadow: 0 8rpx 24rpx rgba($primary-solid, 0.4);

  /* 悬停效果 */
  &:active {
    transform: scale(0.95);
    @include shadow-lg;
  }

  /* 🔥 有商品时的特殊样式 */
  &.has-items {
    background: $gradient-secondary;
    box-shadow: 0 8rpx 24rpx rgba($secondary, 0.4); // 🔥 修复：使用$secondary替代$secondary-solid
    animation: cart-pulse 2s infinite;
  }

  /* 购物车图标 */
  .van-icon {
    @include text-white;
    font-size: 48rpx;
  }
}

/* 🔥 购物车脉冲动画 */
@keyframes cart-pulse {
  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

/* 🔥 优化：购物车数量徽章 - 更精致的设计 */
.cart-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  min-width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: $gradient-secondary;
  @include flex;
  @include items-center;
  @include justify-center;
  @include text-white;
  @include font-bold;
  @include text-xs;
  border: 3rpx solid $white;
  @include shadow-md;

  /* 数字动画 */
  animation: badge-bounce 0.3s ease-out;
}

@keyframes badge-bounce {
  0% {
    transform: scale(0.8);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

/* 🔥 重新设计：响应式设计 - 适配新的左右布局 */
@media (max-width: 750rpx) {
  .category-sidebar {
    width: 160rpx;
  }

  .category-item {
    min-height: 70rpx;
    margin: 4rpx 8rpx;

    .category-name {
      font-size: 22rpx;
      line-height: 1.3;
    }
  }

  .dishes-list {
    padding: 0 16rpx;
  }

  .dish-item {
    min-height: 140rpx; // 🔥 修复：最小高度，由内容撑开
    margin-bottom: 16rpx;
  }

  .dish-image-section {
    width: 120rpx; // 🔥 修复：中等屏幕图片更小
    height: 120rpx;
    margin: 12rpx;
  }

  .dish-content {
    padding: 16rpx 16rpx 16rpx 0;
    min-height: 120rpx;
  }

  .dish-title {
    @include text-base;
    margin-bottom: 8rpx;
  }

  .add-btn-elegant {
    padding: 10rpx 16rpx;

    .btn-icon {
      width: 24rpx;
      height: 24rpx;
      margin-right: 6rpx;
    }

    .btn-text {
      @include text-xs;
    }
  }

  .dish-content {
    padding: 16rpx;
  }

  .dish-name {
    @include text-base;
  }

  .dish-desc {
    @include text-xs;
  }

  .shopping-cart-fab {
    width: 96rpx;
    height: 96rpx;
    right: 24rpx;
    bottom: 100rpx;

    .van-icon {
      font-size: 36rpx;
    }
  }

  .cart-badge {
    min-width: 32rpx;
    height: 32rpx;
    font-size: 18rpx;
  }
}

/* 🔥 重新设计：超小屏幕适配 */
@media (max-width: 600rpx) {
  .category-sidebar {
    width: 140rpx;
  }

  .category-item {
    min-height: 60rpx;
    margin: 3rpx 6rpx;

    .category-name {
      font-size: 20rpx;
    }
  }

  .dishes-list {
    padding: 0 12rpx;
  }

  .dish-item {
    min-height: 120rpx; // 🔥 修复：小屏幕最小高度
    margin-bottom: 12rpx;
    border-radius: $radius-lg;
  }

  .dish-image-section {
    width: 80rpx; // 🔥 修复：小屏幕图片更小
    height: 80rpx;
    margin: 10rpx;
  }

  .dish-content {
    padding: 12rpx 12rpx 12rpx 0;
    min-height: 100rpx;
  }

  .dish-title {
    @include text-sm;
    margin-bottom: 6rpx;
  }

  .dish-tag {
    @include text-xs;
    padding: 2rpx 6rpx;
    font-size: 18rpx;
  }

  .add-btn-elegant {
    padding: 8rpx 12rpx;

    .btn-icon {
      width: 20rpx;
      height: 20rpx;
      margin-right: 4rpx;
    }

    .btn-text {
      font-size: 20rpx;
    }
  }

  .dish-content {
    padding: 12rpx;
  }

  .dish-name {
    @include text-sm;
  }

  .add-btn {
    width: 32rpx;
    height: 32rpx;
  }
}
.red {
  border: 1px solid red;
}
