<template>
  <BaseModal
    v-model="visible"
    title="权限详情"
    size="lg"
    :show-default-footer="false"
  >
    <div v-if="permission" class="space-y-6">
      <!-- 用户信息 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">用户信息</h3>
        <div class="flex items-center space-x-4">
          <el-avatar :size="60" :src="permission.user?.avatar">
            {{ permission.user?.name?.charAt(0) }}
          </el-avatar>
          <div class="flex-1">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="text-sm text-gray-500">用户名</label>
                <div class="font-medium">{{ permission.user?.name || '-' }}</div>
              </div>
              <div>
                <label class="text-sm text-gray-500">手机号</label>
                <div class="font-medium">{{ permission.user?.phone || '-' }}</div>
              </div>
              <div>
                <label class="text-sm text-gray-500">微信OpenID</label>
                <div class="font-medium text-xs">{{ permission.user?.openid || '-' }}</div>
              </div>
              <div>
                <label class="text-sm text-gray-500">用户角色</label>
                <div class="font-medium">
                  <el-tag :type="permission.user?.role === 'admin' ? 'danger' : 'primary'" size="small">
                    {{ permission.user?.role === 'admin' ? '管理员' : '普通用户' }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 权限信息 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">权限信息</h3>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="text-sm text-gray-500">权限类型</label>
            <div class="font-medium">
              <el-tag :type="permission.permissionType === 'admin' ? 'danger' : 'primary'" size="small">
                {{ permission.permissionType === 'admin' ? '管理员权限' : '普通用户权限' }}
              </el-tag>
            </div>
          </div>
          <div>
            <label class="text-sm text-gray-500">权限状态</label>
            <div class="font-medium">
              <el-tag :type="permission.status === 'active' ? 'success' : 'danger'" size="small">
                {{ permission.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </div>
          <div>
            <label class="text-sm text-gray-500">创建时间</label>
            <div class="font-medium">{{ formatDate(permission.createdAt) }}</div>
          </div>
          <div>
            <label class="text-sm text-gray-500">更新时间</label>
            <div class="font-medium">{{ formatDate(permission.updatedAt) }}</div>
          </div>
          <div>
            <label class="text-sm text-gray-500">有效期</label>
            <div class="font-medium">
              {{ permission.expiresAt ? formatDate(permission.expiresAt) : '永久有效' }}
            </div>
          </div>
          <div>
            <label class="text-sm text-gray-500">最后登录</label>
            <div class="font-medium">
              {{ permission.lastLoginAt ? formatDate(permission.lastLoginAt) : '从未登录' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div v-if="permission.remark" class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">备注信息</h3>
        <div class="text-gray-700">{{ permission.remark }}</div>
      </div>

      <!-- 登录历史 -->
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-lg font-medium mb-3">登录统计</h3>
        <div class="grid grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-600">{{ permission.loginCount || 0 }}</div>
            <div class="text-sm text-gray-500">总登录次数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-600">{{ permission.todayLoginCount || 0 }}</div>
            <div class="text-sm text-gray-500">今日登录次数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-600">{{ permission.weekLoginCount || 0 }}</div>
            <div class="text-sm text-gray-500">本周登录次数</div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </BaseModal>
</template>

<script setup>
import { ref, watch } from 'vue'
import BaseModal from '@/components/ui/BaseModal.vue'
import { formatDate } from '@/utils/date'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  permission: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)

// 监听显示状态
watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 关闭模态框
const handleClose = () => {
  visible.value = false
}
</script>
