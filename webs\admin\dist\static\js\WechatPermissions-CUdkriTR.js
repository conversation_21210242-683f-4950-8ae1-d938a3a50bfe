var an=Object.defineProperty,ln=Object.defineProperties;var sn=Object.getOwnPropertyDescriptors;var Ne=Object.getOwnPropertySymbols;var yt=Object.prototype.hasOwnProperty,wt=Object.prototype.propertyIsEnumerable;var ht=(e,t,r)=>t in e?an(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,E=(e,t)=>{for(var r in t||(t={}))yt.call(t,r)&&ht(e,r,t[r]);if(Ne)for(var r of Ne(t))wt.call(t,r)&&ht(e,r,t[r]);return e},fe=(e,t)=>ln(e,sn(t));var oe=(e,t)=>{var r={};for(var n in e)yt.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&Ne)for(var n of Ne(e))t.indexOf(n)<0&&wt.call(e,n)&&(r[n]=e[n]);return r};var ie=(e,t,r)=>new Promise((n,o)=>{var a=i=>{try{s(r.next(i))}catch(u){o(u)}},l=i=>{try{s(r.throw(i))}catch(u){o(u)}},s=i=>i.done?n(i.value):Promise.resolve(i.value).then(a,l);s((r=r.apply(e,t)).next())});import{c as P,a as m,l as b,ad as un,ae as J,af as ae,q as x,g as _,ag as dn,ah as Z,H as Ge,V as le,ai as de,o as z,s as ee,F as G,aj as cn,ak as mn,al as Ot,h as pe,n as fn,y as Y,_ as ye,p as R,w as g,b as T,a2 as Oe,a5 as Fe,m as B,t as S,d as y,am as W,an as pn,ao as Ft,r as O,I as vn,ap as Vt,aq as gn,ar as hn,G as dt}from"./index-Cy8N1eGd.js";import{u as xt}from"./user-DGiv-qs1.js";function tt(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function nt(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"})])}function bt(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z"})])}function yn(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 4.5v15m7.5-7.5h-15"})])}function wn(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"})])}function xn(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"})])}function _t(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"})])}function bn(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 18 18 6M6 6l12 12"})])}function ct(e){typeof queueMicrotask=="function"?queueMicrotask(e):Promise.resolve().then(e).catch(t=>setTimeout(()=>{throw t}))}function Ve(){let e=[],t={addEventListener(r,n,o,a){return r.addEventListener(n,o,a),t.add(()=>r.removeEventListener(n,o,a))},requestAnimationFrame(...r){let n=requestAnimationFrame(...r);t.add(()=>cancelAnimationFrame(n))},nextFrame(...r){t.requestAnimationFrame(()=>{t.requestAnimationFrame(...r)})},setTimeout(...r){let n=setTimeout(...r);t.add(()=>clearTimeout(n))},microTask(...r){let n={current:!0};return ct(()=>{n.current&&r[0]()}),t.add(()=>{n.current=!1})},style(r,n,o){let a=r.style.getPropertyValue(n);return Object.assign(r.style,{[n]:o}),this.add(()=>{Object.assign(r.style,{[n]:a})})},group(r){let n=Ve();return r(n),this.add(()=>n.dispose())},add(r){return e.push(r),()=>{let n=e.indexOf(r);if(n>=0)for(let o of e.splice(n,1))o()}},dispose(){for(let r of e.splice(0))r()}};return t}var kt;let _n=Symbol("headlessui.useid"),kn=0;const Xe=(kt=un)!=null?kt:function(){return J(_n,()=>`${++kn}`)()};function I(e){var t;if(e==null||e.value==null)return null;let r=(t=e.value.$el)!=null?t:e.value;return r instanceof Node?r:null}function ce(e,t,...r){if(e in t){let o=t[e];return typeof o=="function"?o(...r):o}let n=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map(o=>`"${o}"`).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(n,ce),n}var Cn=Object.defineProperty,Sn=(e,t,r)=>t in e?Cn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Ct=(e,t,r)=>(Sn(e,typeof t!="symbol"?t+"":t,r),r);let Tn=class{constructor(){Ct(this,"current",this.detect()),Ct(this,"currentId",0)}set(t){this.current!==t&&(this.currentId=0,this.current=t)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return this.current==="server"}get isClient(){return this.current==="client"}detect(){return typeof window=="undefined"||typeof document=="undefined"?"server":"client"}},je=new Tn;function Pe(e){if(je.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(e!=null&&e.hasOwnProperty("value")){let t=I(e);if(t)return t.ownerDocument}return document}let rt=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>`${e}:not([tabindex='-1'])`).join(",");var ge=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(ge||{}),jt=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(jt||{}),En=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(En||{});function Pn(e=document.body){return e==null?[]:Array.from(e.querySelectorAll(rt)).sort((t,r)=>Math.sign((t.tabIndex||Number.MAX_SAFE_INTEGER)-(r.tabIndex||Number.MAX_SAFE_INTEGER)))}var It=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(It||{});function Ln(e,t=0){var r;return e===((r=Pe(e))==null?void 0:r.body)?!1:ce(t,{0(){return e.matches(rt)},1(){let n=e;for(;n!==null;){if(n.matches(rt))return!0;n=n.parentElement}return!1}})}var Mn=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(Mn||{});typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{e.detail===1?delete document.documentElement.dataset.headlessuiFocusVisible:e.detail===0&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));function ke(e){e==null||e.focus({preventScroll:!0})}let Bn=["textarea","input"].join(",");function An(e){var t,r;return(r=(t=e==null?void 0:e.matches)==null?void 0:t.call(e,Bn))!=null?r:!1}function Dn(e,t=r=>r){return e.slice().sort((r,n)=>{let o=t(r),a=t(n);if(o===null||a===null)return 0;let l=o.compareDocumentPosition(a);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}function We(e,t,{sorted:r=!0,relativeTo:n=null,skipElements:o=[]}={}){var a;let l=(a=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e==null?void 0:e.ownerDocument)!=null?a:document,s=Array.isArray(e)?r?Dn(e):e:Pn(e);o.length>0&&s.length>1&&(s=s.filter(p=>!o.includes(p))),n=n!=null?n:l.activeElement;let i=(()=>{if(t&5)return 1;if(t&10)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(t&1)return 0;if(t&2)return Math.max(0,s.indexOf(n))-1;if(t&4)return Math.max(0,s.indexOf(n))+1;if(t&8)return s.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=t&32?{preventScroll:!0}:{},v=0,d=s.length,f;do{if(v>=d||v+d<=0)return 0;let p=u+v;if(t&16)p=(p+d)%d;else{if(p<0)return 3;if(p>=d)return 1}f=s[p],f==null||f.focus(c),v+=i}while(f!==l.activeElement);return t&6&&An(f)&&f.select(),2}function Rt(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function On(){return/Android/gi.test(window.navigator.userAgent)}function Fn(){return Rt()||On()}function Ue(e,t,r){je.isServer||ae(n=>{document.addEventListener(e,t,r),n(()=>document.removeEventListener(e,t,r))})}function Ht(e,t,r){je.isServer||ae(n=>{window.addEventListener(e,t,r),n(()=>window.removeEventListener(e,t,r))})}function Vn(e,t,r=x(()=>!0)){function n(a,l){if(!r.value||a.defaultPrevented)return;let s=l(a);if(s===null||!s.getRootNode().contains(s))return;let i=function u(c){return typeof c=="function"?u(c()):Array.isArray(c)||c instanceof Set?c:[c]}(e);for(let u of i){if(u===null)continue;let c=u instanceof HTMLElement?u:I(u);if(c!=null&&c.contains(s)||a.composed&&a.composedPath().includes(c))return}return!Ln(s,It.Loose)&&s.tabIndex!==-1&&a.preventDefault(),t(a,s)}let o=_(null);Ue("pointerdown",a=>{var l,s;r.value&&(o.value=((s=(l=a.composedPath)==null?void 0:l.call(a))==null?void 0:s[0])||a.target)},!0),Ue("mousedown",a=>{var l,s;r.value&&(o.value=((s=(l=a.composedPath)==null?void 0:l.call(a))==null?void 0:s[0])||a.target)},!0),Ue("click",a=>{Fn()||o.value&&(n(a,()=>o.value),o.value=null)},!0),Ue("touchend",a=>n(a,()=>a.target instanceof HTMLElement?a.target:null),!0),Ht("blur",a=>n(a,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}var qe=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(qe||{}),he=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(he||{});function me(a){var l=a,{visible:e=!0,features:t=0,ourProps:r,theirProps:n}=l,o=oe(l,["visible","features","ourProps","theirProps"]);var s;let i=Ut(n,r),u=Object.assign(o,{props:i});if(e||t&2&&i.static)return Qe(u);if(t&1){let c=(s=i.unmount)==null||s?0:1;return ce(c,{0(){return null},1(){return Qe(fe(E({},o),{props:fe(E({},i),{hidden:!0,style:{display:"none"}})}))}})}return Qe(u)}function Qe({props:e,attrs:t,slots:r,slot:n,name:o}){var a,l;let v=Yt(e,["unmount","static"]),{as:s}=v,i=oe(v,["as"]),u=(a=r.default)==null?void 0:a.call(r,n),c={};if(n){let d=!1,f=[];for(let[p,k]of Object.entries(n))typeof k=="boolean"&&(d=!0),k===!0&&f.push(p);d&&(c["data-headlessui-state"]=f.join(" "))}if(s==="template"){if(u=Nt(u!=null?u:[]),Object.keys(i).length>0||Object.keys(t).length>0){let[d,...f]=u!=null?u:[];if(!jn(d)||f.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${o} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(i).concat(Object.keys(t)).map(h=>h.trim()).filter((h,L,C)=>C.indexOf(h)===L).sort((h,L)=>h.localeCompare(L)).map(h=>`  - ${h}`).join(`
`),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map(h=>`  - ${h}`).join(`
`)].join(`
`));let p=Ut((l=d.props)!=null?l:{},i,c),k=dn(d,p,!0);for(let h in p)h.startsWith("on")&&(k.props||(k.props={}),k.props[h]=p[h]);return k}return Array.isArray(u)&&u.length===1?u[0]:u}return Z(s,Object.assign({},i,c),{default:()=>u})}function Nt(e){return e.flatMap(t=>t.type===Ge?Nt(t.children):[t])}function Ut(...e){if(e.length===0)return{};if(e.length===1)return e[0];let t={},r={};for(let n of e)for(let o in n)o.startsWith("on")&&typeof n[o]=="function"?(r[o]!=null||(r[o]=[]),r[o].push(n[o])):t[o]=n[o];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(r).map(n=>[n,void 0])));for(let n in r)Object.assign(t,{[n](o,...a){let l=r[n];for(let s of l){if(o instanceof Event&&o.defaultPrevented)return;s(o,...a)}}});return t}function Yt(e,t=[]){let r=Object.assign({},e);for(let n of t)n in r&&delete r[n];return r}function jn(e){return e==null?!1:typeof e.type=="string"||typeof e.type=="object"||typeof e.type=="function"}var Ze=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(Ze||{});let ot=le({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup(e,{slots:t,attrs:r}){return()=>{var n;let s=e,{features:o}=s,a=oe(s,["features"]),l={"aria-hidden":(o&2)===2?!0:(n=a["aria-hidden"])!=null?n:void 0,hidden:(o&4)===4?!0:void 0,style:E({position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},(o&4)===4&&(o&2)!==2&&{display:"none"})};return me({ourProps:l,theirProps:a,slot:{},attrs:r,slots:t,name:"Hidden"})}}}),zt=Symbol("Context");var $=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))($||{});function In(){return mt()!==null}function mt(){return J(zt,null)}function Rn(e){de(zt,e)}var Wt=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(Wt||{});function Hn(e){function t(){document.readyState!=="loading"&&(e(),document.removeEventListener("DOMContentLoaded",t))}typeof window!="undefined"&&typeof document!="undefined"&&(document.addEventListener("DOMContentLoaded",t),t())}let be=[];Hn(()=>{function e(t){t.target instanceof HTMLElement&&t.target!==document.body&&be[0]!==t.target&&(be.unshift(t.target),be=be.filter(r=>r!=null&&r.isConnected),be.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});function qt(e,t,r,n){je.isServer||ae(o=>{e=e!=null?e:window,e.addEventListener(t,r,n),o(()=>e.removeEventListener(t,r,n))})}var De=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))(De||{});function Nn(){let e=_(0);return Ht("keydown",t=>{t.key==="Tab"&&(e.value=t.shiftKey?1:0)}),e}function Zt(e){if(!e)return new Set;if(typeof e=="function")return new Set(e());let t=new Set;for(let r of e.value){let n=I(r);n instanceof HTMLElement&&t.add(n)}return t}var Gt=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(Gt||{});let Be=Object.assign(le({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:_(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:r,expose:n}){let o=_(null);n({el:o,$el:o});let a=x(()=>Pe(o)),l=_(!1);z(()=>l.value=!0),ee(()=>l.value=!1),Yn({ownerDocument:a},x(()=>l.value&&!!(e.features&16)));let s=zn({ownerDocument:a,container:o,initialFocus:x(()=>e.initialFocus)},x(()=>l.value&&!!(e.features&2)));Wn({ownerDocument:a,container:o,containers:e.containers,previousActiveElement:s},x(()=>l.value&&!!(e.features&8)));let i=Nn();function u(f){let p=I(o);p&&(k=>k())(()=>{ce(i.value,{[De.Forwards]:()=>{We(p,ge.First,{skipElements:[f.relatedTarget]})},[De.Backwards]:()=>{We(p,ge.Last,{skipElements:[f.relatedTarget]})}})})}let c=_(!1);function v(f){f.key==="Tab"&&(c.value=!0,requestAnimationFrame(()=>{c.value=!1}))}function d(f){if(!l.value)return;let p=Zt(e.containers);I(o)instanceof HTMLElement&&p.add(I(o));let k=f.relatedTarget;k instanceof HTMLElement&&k.dataset.headlessuiFocusGuard!=="true"&&(Xt(p,k)||(c.value?We(I(o),ce(i.value,{[De.Forwards]:()=>ge.Next,[De.Backwards]:()=>ge.Previous})|ge.WrapAround,{relativeTo:f.target}):f.target instanceof HTMLElement&&ke(f.target)))}return()=>{let f={},p={ref:o,onKeydown:v,onFocusout:d},D=e,{features:k,initialFocus:h,containers:L}=D,C=oe(D,["features","initialFocus","containers"]);return Z(Ge,[!!(k&4)&&Z(ot,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:Ze.Focusable}),me({ourProps:p,theirProps:E(E({},t),C),slot:f,attrs:t,slots:r,name:"FocusTrap"}),!!(k&4)&&Z(ot,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:Ze.Focusable})])}}}),{features:Gt});function Un(e){let t=_(be.slice());return G([e],([r],[n])=>{n===!0&&r===!1?ct(()=>{t.value.splice(0)}):n===!1&&r===!0&&(t.value=be.slice())},{flush:"post"}),()=>{var r;return(r=t.value.find(n=>n!=null&&n.isConnected))!=null?r:null}}function Yn({ownerDocument:e},t){let r=Un(t);z(()=>{ae(()=>{var n,o;t.value||((n=e.value)==null?void 0:n.activeElement)===((o=e.value)==null?void 0:o.body)&&ke(r())},{flush:"post"})}),ee(()=>{t.value&&ke(r())})}function zn({ownerDocument:e,container:t,initialFocus:r},n){let o=_(null),a=_(!1);return z(()=>a.value=!0),ee(()=>a.value=!1),z(()=>{G([t,r,n],(l,s)=>{if(l.every((u,c)=>(s==null?void 0:s[c])===u)||!n.value)return;let i=I(t);i&&ct(()=>{var u,c;if(!a.value)return;let v=I(r),d=(u=e.value)==null?void 0:u.activeElement;if(v){if(v===d){o.value=d;return}}else if(i.contains(d)){o.value=d;return}v?ke(v):We(i,ge.First|ge.NoScroll)===jt.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),o.value=(c=e.value)==null?void 0:c.activeElement})},{immediate:!0,flush:"post"})}),o}function Wn({ownerDocument:e,container:t,containers:r,previousActiveElement:n},o){var a;qt((a=e.value)==null?void 0:a.defaultView,"focus",l=>{if(!o.value)return;let s=Zt(r);I(t)instanceof HTMLElement&&s.add(I(t));let i=n.value;if(!i)return;let u=l.target;u&&u instanceof HTMLElement?Xt(s,u)?(n.value=u,ke(u)):(l.preventDefault(),l.stopPropagation(),ke(i)):ke(n.value)},!0)}function Xt(e,t){for(let r of e)if(r.contains(t))return!0;return!1}function qn(e){let t=cn(e.getSnapshot());return ee(e.subscribe(()=>{t.value=e.getSnapshot()})),t}function Zn(e,t){let r=e(),n=new Set;return{getSnapshot(){return r},subscribe(o){return n.add(o),()=>n.delete(o)},dispatch(o,...a){let l=t[o].call(r,...a);l&&(r=l,n.forEach(s=>s()))}}}function Gn(){let e;return{before({doc:t}){var r;let n=t.documentElement;e=((r=t.defaultView)!=null?r:window).innerWidth-n.clientWidth},after({doc:t,d:r}){let n=t.documentElement,o=n.clientWidth-n.offsetWidth,a=e-o;r.style(n,"paddingRight",`${a}px`)}}}function Xn(){return Rt()?{before({doc:e,d:t,meta:r}){function n(o){return r.containers.flatMap(a=>a()).some(a=>a.contains(o))}t.microTask(()=>{var o;if(window.getComputedStyle(e.documentElement).scrollBehavior!=="auto"){let s=Ve();s.style(e.documentElement,"scrollBehavior","auto"),t.add(()=>t.microTask(()=>s.dispose()))}let a=(o=window.scrollY)!=null?o:window.pageYOffset,l=null;t.addEventListener(e,"click",s=>{if(s.target instanceof HTMLElement)try{let i=s.target.closest("a");if(!i)return;let{hash:u}=new URL(i.href),c=e.querySelector(u);c&&!n(c)&&(l=c)}catch(i){}},!0),t.addEventListener(e,"touchstart",s=>{if(s.target instanceof HTMLElement)if(n(s.target)){let i=s.target;for(;i.parentElement&&n(i.parentElement);)i=i.parentElement;t.style(i,"overscrollBehavior","contain")}else t.style(s.target,"touchAction","none")}),t.addEventListener(e,"touchmove",s=>{if(s.target instanceof HTMLElement){if(s.target.tagName==="INPUT")return;if(n(s.target)){let i=s.target;for(;i.parentElement&&i.dataset.headlessuiPortal!==""&&!(i.scrollHeight>i.clientHeight||i.scrollWidth>i.clientWidth);)i=i.parentElement;i.dataset.headlessuiPortal===""&&s.preventDefault()}else s.preventDefault()}},{passive:!1}),t.add(()=>{var s;let i=(s=window.scrollY)!=null?s:window.pageYOffset;a!==i&&window.scrollTo(0,a),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{}}function Kn(){return{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}}function $n(e){let t={};for(let r of e)Object.assign(t,r(t));return t}let _e=Zn(()=>new Map,{PUSH(e,t){var r;let n=(r=this.get(e))!=null?r:{doc:e,count:0,d:Ve(),meta:new Set};return n.count++,n.meta.add(t),this.set(e,n),this},POP(e,t){let r=this.get(e);return r&&(r.count--,r.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:r}){let n={doc:e,d:t,meta:$n(r)},o=[Xn(),Gn(),Kn()];o.forEach(({before:a})=>a==null?void 0:a(n)),o.forEach(({after:a})=>a==null?void 0:a(n))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});_e.subscribe(()=>{let e=_e.getSnapshot(),t=new Map;for(let[r]of e)t.set(r,r.documentElement.style.overflow);for(let r of e.values()){let n=t.get(r.doc)==="hidden",o=r.count!==0;(o&&!n||!o&&n)&&_e.dispatch(r.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",r),r.count===0&&_e.dispatch("TEARDOWN",r)}});function Qn(e,t,r){let n=qn(_e),o=x(()=>{let a=e.value?n.value.get(e.value):void 0;return a?a.count>0:!1});return G([e,t],([a,l],[s],i)=>{if(!a||!l)return;_e.dispatch("PUSH",a,r);let u=!1;i(()=>{u||(_e.dispatch("POP",s!=null?s:a,r),u=!0)})},{immediate:!0}),o}let Je=new Map,Ae=new Map;function St(e,t=_(!0)){ae(r=>{var n;if(!t.value)return;let o=I(e);if(!o)return;r(function(){var l;if(!o)return;let s=(l=Ae.get(o))!=null?l:1;if(s===1?Ae.delete(o):Ae.set(o,s-1),s!==1)return;let i=Je.get(o);i&&(i["aria-hidden"]===null?o.removeAttribute("aria-hidden"):o.setAttribute("aria-hidden",i["aria-hidden"]),o.inert=i.inert,Je.delete(o))});let a=(n=Ae.get(o))!=null?n:0;Ae.set(o,a+1),a===0&&(Je.set(o,{"aria-hidden":o.getAttribute("aria-hidden"),inert:o.inert}),o.setAttribute("aria-hidden","true"),o.inert=!0)})}function Jn({defaultContainers:e=[],portals:t,mainTreeNodeRef:r}={}){let n=_(null),o=Pe(n);function a(){var l,s,i;let u=[];for(let c of e)c!==null&&(c instanceof HTMLElement?u.push(c):"value"in c&&c.value instanceof HTMLElement&&u.push(c.value));if(t!=null&&t.value)for(let c of t.value)u.push(c);for(let c of(l=o==null?void 0:o.querySelectorAll("html > *, body > *"))!=null?l:[])c!==document.body&&c!==document.head&&c instanceof HTMLElement&&c.id!=="headlessui-portal-root"&&(c.contains(I(n))||c.contains((i=(s=I(n))==null?void 0:s.getRootNode())==null?void 0:i.host)||u.some(v=>c.contains(v))||u.push(c));return u}return{resolveContainers:a,contains(l){return a().some(s=>s.contains(l))},mainTreeNodeRef:n,MainTreeNode(){return r!=null?null:Z(ot,{features:Ze.Hidden,ref:n})}}}let Kt=Symbol("ForcePortalRootContext");function er(){return J(Kt,!1)}let Tt=le({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup(e,{slots:t,attrs:r}){return de(Kt,e.force),()=>{let a=e,{force:n}=a,o=oe(a,["force"]);return me({theirProps:o,ourProps:{},slot:{},slots:t,attrs:r,name:"ForcePortalRoot"})}}}),$t=Symbol("StackContext");var at=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(at||{});function tr(){return J($t,()=>{})}function nr({type:e,enabled:t,element:r,onUpdate:n}){let o=tr();function a(...l){n==null||n(...l),o(...l)}z(()=>{G(t,(l,s)=>{l?a(0,e,r):s===!0&&a(1,e,r)},{immediate:!0,flush:"sync"})}),ee(()=>{t.value&&a(1,e,r)}),de($t,a)}let rr=Symbol("DescriptionContext");function or({slot:e=_({}),name:t="Description",props:r={}}={}){let n=_([]);function o(a){return n.value.push(a),()=>{let l=n.value.indexOf(a);l!==-1&&n.value.splice(l,1)}}return de(rr,{register:o,slot:e,name:t,props:r}),x(()=>n.value.length>0?n.value.join(" "):void 0)}function ar(e){let t=Pe(e);if(!t){if(e===null)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let r=t.getElementById("headlessui-portal-root");if(r)return r;let n=t.createElement("div");return n.setAttribute("id","headlessui-portal-root"),t.body.appendChild(n)}const lt=new WeakMap;function lr(e){var t;return(t=lt.get(e))!=null?t:0}function Et(e,t){let r=t(lr(e));return r<=0?lt.delete(e):lt.set(e,r),r}let sr=le({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:r}){let n=_(null),o=x(()=>Pe(n)),a=er(),l=J(Qt,null),s=_(a===!0||l==null?ar(n.value):l.resolveTarget());s.value&&Et(s.value,d=>d+1);let i=_(!1);z(()=>{i.value=!0}),ae(()=>{a||l!=null&&(s.value=l.resolveTarget())});let u=J(st,null),c=!1,v=mn();return G(n,()=>{if(c||!u)return;let d=I(n);d&&(ee(u.register(d),v),c=!0)}),ee(()=>{var d,f;let p=(d=o.value)==null?void 0:d.getElementById("headlessui-portal-root");!p||s.value!==p||Et(s.value,k=>k-1)||s.value.children.length>0||(f=s.value.parentElement)==null||f.removeChild(s.value)}),()=>{if(!i.value||s.value===null)return null;let d={ref:n,"data-headlessui-portal":""};return Z(Ot,{to:s.value},me({ourProps:d,theirProps:e,slot:{},attrs:r,slots:t,name:"Portal"}))}}}),st=Symbol("PortalParentContext");function ir(){let e=J(st,null),t=_([]);function r(a){return t.value.push(a),e&&e.register(a),()=>n(a)}function n(a){let l=t.value.indexOf(a);l!==-1&&t.value.splice(l,1),e&&e.unregister(a)}let o={register:r,unregister:n,portals:t};return[t,le({name:"PortalWrapper",setup(a,{slots:l}){return de(st,o),()=>{var s;return(s=l.default)==null?void 0:s.call(l)}}})]}let Qt=Symbol("PortalGroupContext"),ur=le({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:r}){let n=pe({resolveTarget(){return e.target}});return de(Qt,n),()=>{let l=e,{target:o}=l,a=oe(l,["target"]);return me({theirProps:a,ourProps:{},slot:{},attrs:t,slots:r,name:"PortalGroup"})}}});var dr=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(dr||{});let it=Symbol("DialogContext");function ft(e){let t=J(it,null);if(t===null){let r=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,ft),r}return t}let Ye="DC8F892D-2EBD-447C-A4C8-A03058436FF4",cr=le({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:Ye},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:t,attrs:r,slots:n,expose:o}){var a,l;let s=(a=e.id)!=null?a:`headlessui-dialog-${Xe()}`,i=_(!1);z(()=>{i.value=!0});let u=!1,c=x(()=>e.role==="dialog"||e.role==="alertdialog"?e.role:(u||(u=!0,console.warn(`Invalid role [${c}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")),v=_(0),d=mt(),f=x(()=>e.open===Ye&&d!==null?(d.value&$.Open)===$.Open:e.open),p=_(null),k=x(()=>Pe(p));if(o({el:p,$el:p}),!(e.open!==Ye||d!==null))throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if(typeof f.value!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${f.value===Ye?void 0:e.open}`);let h=x(()=>i.value&&f.value?0:1),L=x(()=>h.value===0),C=x(()=>v.value>1),D=J(it,null)!==null,[H,A]=ir(),{resolveContainers:U,mainTreeNodeRef:se,MainTreeNode:j}=Jn({portals:H,defaultContainers:[x(()=>{var M;return(M=ve.panelRef.value)!=null?M:p.value})]}),te=x(()=>C.value?"parent":"leaf"),Ce=x(()=>d!==null?(d.value&$.Closing)===$.Closing:!1),Le=x(()=>D||Ce.value?!1:L.value),Me=x(()=>{var M,F,q;return(q=Array.from((F=(M=k.value)==null?void 0:M.querySelectorAll("body > *"))!=null?F:[]).find(w=>w.id==="headlessui-portal-root"?!1:w.contains(I(se))&&w instanceof HTMLElement))!=null?q:null});St(Me,Le);let Q=x(()=>C.value?!0:L.value),we=x(()=>{var M,F,q;return(q=Array.from((F=(M=k.value)==null?void 0:M.querySelectorAll("[data-headlessui-portal]"))!=null?F:[]).find(w=>w.contains(I(se))&&w instanceof HTMLElement))!=null?q:null});St(we,Q),nr({type:"Dialog",enabled:x(()=>h.value===0),element:p,onUpdate:(M,F)=>{if(F==="Dialog")return ce(M,{[at.Add]:()=>v.value+=1,[at.Remove]:()=>v.value-=1})}});let ne=or({name:"DialogDescription",slot:x(()=>({open:f.value}))}),re=_(null),ve={titleId:re,panelRef:_(null),dialogState:h,setTitleId(M){re.value!==M&&(re.value=M)},close(){t("close",!1)}};de(it,ve);let Ie=x(()=>!(!L.value||C.value));Vn(U,(M,F)=>{M.preventDefault(),ve.close(),fn(()=>F==null?void 0:F.focus())},Ie);let Re=x(()=>!(C.value||h.value!==0));qt((l=k.value)==null?void 0:l.defaultView,"keydown",M=>{Re.value&&(M.defaultPrevented||M.key===Wt.Escape&&(M.preventDefault(),M.stopPropagation(),ve.close()))});let He=x(()=>!(Ce.value||h.value!==0||D));return Qn(k,He,M=>{var F;return{containers:[...(F=M.containers)!=null?F:[],U]}}),ae(M=>{if(h.value!==0)return;let F=I(p);if(!F)return;let q=new ResizeObserver(w=>{for(let V of w){let X=V.target.getBoundingClientRect();X.x===0&&X.y===0&&X.width===0&&X.height===0&&ve.close()}});q.observe(F),M(()=>q.disconnect())}),()=>{let X=e,{open:M,initialFocus:F}=X,q=oe(X,["open","initialFocus"]),w=fe(E({},r),{ref:p,id:s,role:c.value,"aria-modal":h.value===0?!0:void 0,"aria-labelledby":re.value,"aria-describedby":ne.value}),V={open:h.value===0};return Z(Tt,{force:!0},()=>[Z(sr,()=>Z(ur,{target:p.value},()=>Z(Tt,{force:!1},()=>Z(Be,{initialFocus:F,containers:U,features:L.value?ce(te.value,{parent:Be.features.RestoreFocus,leaf:Be.features.All&~Be.features.FocusLock}):Be.features.None},()=>Z(A,{},()=>me({ourProps:w,theirProps:E(E({},q),r),slot:V,attrs:r,slots:n,visible:h.value===0,features:qe.RenderStrategy|qe.Static,name:"Dialog"})))))),Z(j)])}}}),mr=le({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:r,expose:n}){var o;let a=(o=e.id)!=null?o:`headlessui-dialog-panel-${Xe()}`,l=ft("DialogPanel");n({el:l.panelRef,$el:l.panelRef});function s(i){i.stopPropagation()}return()=>{let i=oe(e,[]),u={id:a,ref:l.panelRef,onClick:s};return me({ourProps:u,theirProps:i,slot:{open:l.dialogState.value===0},attrs:t,slots:r,name:"DialogPanel"})}}}),fr=le({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(e,{attrs:t,slots:r}){var n;let o=(n=e.id)!=null?n:`headlessui-dialog-title-${Xe()}`,a=ft("DialogTitle");return z(()=>{a.setTitleId(o),ee(()=>a.setTitleId(null))}),()=>{let l=oe(e,[]);return me({ourProps:{id:o},theirProps:l,slot:{open:a.dialogState.value===0},attrs:t,slots:r,name:"DialogTitle"})}}});function pr(e){let t={called:!1};return(...r)=>{if(!t.called)return t.called=!0,e(...r)}}function et(e,...t){e&&t.length>0&&e.classList.add(...t)}function ze(e,...t){e&&t.length>0&&e.classList.remove(...t)}var ut=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(ut||{});function vr(e,t){let r=Ve();if(!e)return r.dispose;let{transitionDuration:n,transitionDelay:o}=getComputedStyle(e),[a,l]=[n,o].map(s=>{let[i=0]=s.split(",").filter(Boolean).map(u=>u.includes("ms")?parseFloat(u):parseFloat(u)*1e3).sort((u,c)=>c-u);return i});return a!==0?r.setTimeout(()=>t("finished"),a+l):t("finished"),r.add(()=>t("cancelled")),r.dispose}function Pt(e,t,r,n,o,a){let l=Ve(),s=a!==void 0?pr(a):()=>{};return ze(e,...o),et(e,...t,...r),l.nextFrame(()=>{ze(e,...r),et(e,...n),l.add(vr(e,i=>(ze(e,...n,...t),et(e,...o),s(i))))}),l.add(()=>ze(e,...t,...r,...n,...o)),l.add(()=>s("cancelled")),l.dispose}function xe(e=""){return e.split(/\s+/).filter(t=>t.length>1)}let pt=Symbol("TransitionContext");var gr=(e=>(e.Visible="visible",e.Hidden="hidden",e))(gr||{});function hr(){return J(pt,null)!==null}function yr(){let e=J(pt,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}function wr(){let e=J(vt,null);if(e===null)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}let vt=Symbol("NestingContext");function Ke(e){return"children"in e?Ke(e.children):e.value.filter(({state:t})=>t==="visible").length>0}function Jt(e){let t=_([]),r=_(!1);z(()=>r.value=!0),ee(()=>r.value=!1);function n(a,l=he.Hidden){let s=t.value.findIndex(({id:i})=>i===a);s!==-1&&(ce(l,{[he.Unmount](){t.value.splice(s,1)},[he.Hidden](){t.value[s].state="hidden"}}),!Ke(t)&&r.value&&(e==null||e()))}function o(a){let l=t.value.find(({id:s})=>s===a);return l?l.state!=="visible"&&(l.state="visible"):t.value.push({id:a,state:"visible"}),()=>n(a,he.Unmount)}return{children:t,register:o,unregister:n}}let en=qe.RenderStrategy,tn=le({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:r,slots:n,expose:o}){let a=_(0);function l(){a.value|=$.Opening,t("beforeEnter")}function s(){a.value&=~$.Opening,t("afterEnter")}function i(){a.value|=$.Closing,t("beforeLeave")}function u(){a.value&=~$.Closing,t("afterLeave")}if(!hr()&&In())return()=>Z(nn,fe(E({},e),{onBeforeEnter:l,onAfterEnter:s,onBeforeLeave:i,onAfterLeave:u}),n);let c=_(null),v=x(()=>e.unmount?he.Unmount:he.Hidden);o({el:c,$el:c});let{show:d,appear:f}=yr(),{register:p,unregister:k}=wr(),h=_(d.value?"visible":"hidden"),L={value:!0},C=Xe(),D={value:!1},H=Jt(()=>{!D.value&&h.value!=="hidden"&&(h.value="hidden",k(C),u())});z(()=>{let Q=p(C);ee(Q)}),ae(()=>{if(v.value===he.Hidden&&C){if(d.value&&h.value!=="visible"){h.value="visible";return}ce(h.value,{hidden:()=>k(C),visible:()=>p(C)})}});let A=xe(e.enter),U=xe(e.enterFrom),se=xe(e.enterTo),j=xe(e.entered),te=xe(e.leave),Ce=xe(e.leaveFrom),Le=xe(e.leaveTo);z(()=>{ae(()=>{if(h.value==="visible"){let Q=I(c);if(Q instanceof Comment&&Q.data==="")throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}})});function Me(Q){let we=L.value&&!f.value,ne=I(c);!ne||!(ne instanceof HTMLElement)||we||(D.value=!0,d.value&&l(),d.value||i(),Q(d.value?Pt(ne,A,U,se,j,re=>{D.value=!1,re===ut.Finished&&s()}):Pt(ne,te,Ce,Le,j,re=>{D.value=!1,re===ut.Finished&&(Ke(H)||(h.value="hidden",k(C),u()))})))}return z(()=>{G([d],(Q,we,ne)=>{Me(ne),L.value=!1},{immediate:!0})}),de(vt,H),Rn(x(()=>ce(h.value,{visible:$.Open,hidden:$.Closed})|a.value)),()=>{let V=e,{appear:Q,show:we,enter:ne,enterFrom:re,enterTo:ve,entered:Ie,leave:Re,leaveFrom:He,leaveTo:M}=V,F=oe(V,["appear","show","enter","enterFrom","enterTo","entered","leave","leaveFrom","leaveTo"]),q={ref:c},w=E(E({},F),f.value&&d.value&&je.isServer?{class:Y([r.class,F.class,...A,...U])}:{});return me({theirProps:w,ourProps:q,slot:{},slots:n,attrs:r,features:en,visible:h.value==="visible",name:"TransitionChild"})}}}),xr=tn,nn=le({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:r,slots:n}){let o=mt(),a=x(()=>e.show===null&&o!==null?(o.value&$.Open)===$.Open:e.show);ae(()=>{if(![!0,!1].includes(a.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')});let l=_(a.value?"visible":"hidden"),s=Jt(()=>{l.value="hidden"}),i=_(!0),u={show:a,appear:x(()=>e.appear||!i.value)};return z(()=>{ae(()=>{i.value=!1,a.value?l.value="visible":Ke(s)||(l.value="hidden")})}),de(vt,s),de(pt,u),()=>{let c=Yt(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),v={unmount:e.unmount};return me({ourProps:fe(E({},v),{as:"template"}),theirProps:{},slot:{},slots:fe(E({},n),{default:()=>[Z(xr,E(E(E({onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave")},r),v),c),n.default)]}),attrs:{},features:en,visible:l.value==="visible",name:"Transition"})}}});const br={__name:"BaseButton",props:{text:{type:String,default:""},type:{type:String,default:"button",validator:e=>["button","submit","reset"].includes(e)},variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","warning","error","outline","ghost","link"].includes(e)},size:{type:String,default:"md",validator:e=>["xs","sm","md","lg","xl"].includes(e)},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},block:{type:Boolean,default:!1},round:{type:Boolean,default:!1},prefixIcon:{type:[Object,Function],default:null},suffixIcon:{type:[Object,Function],default:null},tag:{type:String,default:"button",validator:e=>["button","a","router-link"].includes(e)},href:{type:String,default:""},to:{type:[String,Object],default:""}},emits:["click"],setup(e,{expose:t,emit:r}){t();const n=e,o=r,a=x(()=>{const u=["inline-flex items-center justify-center font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none"],c={xs:"px-2.5 py-1.5 text-xs rounded",sm:"px-3 py-2 text-sm rounded-md",md:"px-4 py-2 text-sm rounded-md",lg:"px-4 py-2 text-base rounded-md",xl:"px-6 py-3 text-base rounded-md"},v={primary:["bg-primary-600 text-white shadow-sm","hover:bg-primary-700 focus:ring-primary-500","dark:bg-primary-500 dark:hover:bg-primary-600"],secondary:["bg-gray-600 text-white shadow-sm","hover:bg-gray-700 focus:ring-gray-500","dark:bg-gray-500 dark:hover:bg-gray-600"],success:["bg-success-600 text-white shadow-sm","hover:bg-success-700 focus:ring-success-500"],warning:["bg-warning-600 text-white shadow-sm","hover:bg-warning-700 focus:ring-warning-500"],error:["bg-error-600 text-white shadow-sm","hover:bg-error-700 focus:ring-error-500"],outline:["bg-white text-gray-700 border border-gray-300 shadow-sm","hover:bg-gray-50 focus:ring-primary-500","dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"],ghost:["bg-transparent text-gray-700","hover:bg-gray-100 focus:ring-primary-500","dark:text-gray-300 dark:hover:bg-gray-800"],link:["bg-transparent text-primary-600 p-0 shadow-none","hover:text-primary-700 focus:ring-0 underline-offset-4 hover:underline","dark:text-primary-400 dark:hover:text-primary-300"]},d=n.round?"rounded-full":"",f=n.block?"w-full":"";return[...u,c[n.size],...v[n.variant],d,f].filter(Boolean).join(" ")}),l=x(()=>{var d;const u=n.text||((d=n.$slots)==null?void 0:d.default),c={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-5 w-5"},v=[];return u&&(n.prefixIcon&&v.push("mr-2"),n.suffixIcon&&v.push("ml-2")),[c[n.size],...v].join(" ")}),i={props:n,emit:o,buttonClasses:a,iconClasses:l,handleClick:u=>{!n.disabled&&!n.loading&&o("click",u)},computed:x};return Object.defineProperty(i,"__isScriptSetup",{enumerable:!1,value:!0}),i}},_r={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},kr={key:2};function Cr(e,t,r,n,o,a){return b(),R(Oe(r.tag),{type:r.tag==="button"?r.type:void 0,to:r.tag==="router-link"?r.to:void 0,href:r.tag==="a"?r.href:void 0,disabled:r.disabled||r.loading,class:Y(n.buttonClasses),onClick:n.handleClick},{default:g(()=>[T(" 加载图标 "),r.loading?(b(),P("svg",_r,t[0]||(t[0]=[m("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),m("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):r.prefixIcon?(b(),P(Ge,{key:1},[T(" 前置图标 "),(b(),R(Oe(r.prefixIcon),{class:Y(n.iconClasses)},null,8,["class"]))],2112)):T("v-if",!0),T(" 按钮文本 "),e.$slots.default||r.text?(b(),P("span",kr,[Fe(e.$slots,"default",{},()=>[B(S(r.text),1)])])):T("v-if",!0),T(" 后置图标 "),r.suffixIcon&&!r.loading?(b(),R(Oe(r.suffixIcon),{key:3,class:Y(n.iconClasses)},null,8,["class"])):T("v-if",!0)]),_:3},8,["type","to","href","disabled","class"])}const gt=ye(br,[["render",Cr],["__file","E:/wx-nan/webs/admin/src/components/ui/BaseButton.vue"]]),Sr={__name:"BaseModal",props:{modelValue:{type:Boolean,default:!1},title:{type:String,default:""},size:{type:String,default:"md",validator:e=>["xs","sm","md","lg","xl","2xl","3xl","4xl","5xl","6xl","7xl"].includes(e)},closable:{type:Boolean,default:!0},maskClosable:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!1},showCancelButton:{type:Boolean,default:!0},showConfirmButton:{type:Boolean,default:!0},cancelText:{type:String,default:"取消"},confirmText:{type:String,default:"确认"},confirmLoading:{type:Boolean,default:!1},fullscreen:{type:Boolean,default:!1},customClass:{type:String,default:""}},emits:["update:modelValue","close","cancel","confirm"],setup(e,{expose:t,emit:r}){t();const n=e,o=r,a=x(()=>n.fullscreen?["relative transform overflow-hidden bg-white dark:bg-gray-800 text-left shadow-xl transition-all","w-full h-full",n.customClass].filter(Boolean).join(" "):["relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all","sm:my-8 sm:w-full",{xs:"sm:max-w-xs",sm:"sm:max-w-sm",md:"sm:max-w-md",lg:"sm:max-w-lg",xl:"sm:max-w-xl","2xl":"sm:max-w-2xl","3xl":"sm:max-w-3xl","4xl":"sm:max-w-4xl","5xl":"sm:max-w-5xl","6xl":"sm:max-w-6xl","7xl":"sm:max-w-7xl"}[n.size],n.customClass].filter(Boolean).join(" ")),l=x(()=>{const v=["p-6"];return n.fullscreen&&v.push("flex-1 overflow-y-auto"),v.join(" ")}),c={props:n,emit:o,modalClasses:a,bodyClasses:l,handleClose:()=>{n.closable&&n.maskClosable&&(o("update:modelValue",!1),o("close"))},handleCancel:()=>{o("update:modelValue",!1),o("cancel")},handleConfirm:()=>{o("confirm")},computed:x,get Dialog(){return cr},get DialogPanel(){return mr},get DialogTitle(){return fr},get TransitionChild(){return tn},get TransitionRoot(){return nn},get XMarkIcon(){return bn},BaseButton:gt};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}},Tr={class:"fixed inset-0 z-10 overflow-y-auto"},Er={class:"flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0"},Pr={key:0,class:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700"},Lr={key:1,class:"flex items-center justify-end space-x-3 px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900"};function Mr(e,t,r,n,o,a){return b(),R(n.TransitionRoot,{as:"template",show:r.modelValue},{default:g(()=>[y(n.Dialog,{as:"div",class:"relative z-50",onClose:n.handleClose},{default:g(()=>[T(" 背景遮罩 "),y(n.TransitionChild,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:g(()=>t[0]||(t[0]=[m("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])),_:1,__:[0]}),m("div",Tr,[m("div",Er,[T(" 模态框内容 "),y(n.TransitionChild,{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95","enter-to":"opacity-100 translate-y-0 sm:scale-100",leave:"ease-in duration-200","leave-from":"opacity-100 translate-y-0 sm:scale-100","leave-to":"opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"},{default:g(()=>[y(n.DialogPanel,{class:Y(n.modalClasses)},{default:g(()=>[T(" 模态框头部 "),e.$slots.header||r.title?(b(),P("div",Pr,[Fe(e.$slots,"header",{},()=>[y(n.DialogTitle,{as:"h3",class:"text-lg font-medium leading-6 text-gray-900 dark:text-white"},{default:g(()=>[B(S(r.title),1)]),_:1})]),r.closable?(b(),P("button",{key:0,type:"button",class:"rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2",onClick:n.handleClose},[t[1]||(t[1]=m("span",{class:"sr-only"},"关闭",-1)),y(n.XMarkIcon,{class:"h-6 w-6","aria-hidden":"true"})])):T("v-if",!0)])):T("v-if",!0),T(" 模态框内容 "),m("div",{class:Y(n.bodyClasses)},[Fe(e.$slots,"default")],2),T(" 模态框底部 "),e.$slots.footer||r.showDefaultFooter?(b(),P("div",Lr,[Fe(e.$slots,"footer",{},()=>[r.showCancelButton?(b(),R(n.BaseButton,{key:0,variant:"outline",onClick:n.handleCancel},{default:g(()=>[B(S(r.cancelText),1)]),_:1})):T("v-if",!0),r.showConfirmButton?(b(),R(n.BaseButton,{key:1,variant:"primary",loading:r.confirmLoading,onClick:n.handleConfirm},{default:g(()=>[B(S(r.confirmText),1)]),_:1},8,["loading"])):T("v-if",!0)])])):T("v-if",!0)]),_:3},8,["class"])]),_:3})])])]),_:3})]),_:3},8,["show"])}const $e=ye(Sr,[["render",Mr],["__file","E:/wx-nan/webs/admin/src/components/ui/BaseModal.vue"]]),ue={getPermissions(e={}){return W({url:"/admin/wechat/permissions",method:"GET",params:e})},getStats(){return W({url:"/admin/wechat/permissions/stats",method:"GET"})},addPermission(e){return W({url:"/admin/wechat/permissions",method:"POST",data:e})},updatePermission(e,t){return W({url:`/admin/wechat/permissions/${e}`,method:"PUT",data:t})},revokePermission(e){return W({url:`/admin/wechat/permissions/${e}/revoke`,method:"POST"})},restorePermission(e){return W({url:`/admin/wechat/permissions/${e}/restore`,method:"POST"})},batchRevokePermissions(e){return W({url:"/admin/wechat/permissions/batch-revoke",method:"POST",data:{ids:e}})},batchRestorePermissions(e){return W({url:"/admin/wechat/permissions/batch-restore",method:"POST",data:{ids:e}})},getPermissionDetail(e){return W({url:`/admin/wechat/permissions/${e}`,method:"GET"})},checkUserPermission(e){return W({url:`/admin/wechat/permissions/check/${e}`,method:"GET"})},getPermissionHistory(e){return W({url:`/admin/wechat/permissions/history/${e}`,method:"GET"})},searchWechatUsers(e){return W({url:"/admin/wechat/users/search",method:"GET",params:{keyword:e}})},getLoginLogs(e={}){return W({url:"/admin/wechat/login-logs",method:"GET",params:e})},exportPermissions(e={}){return W({url:"/admin/wechat/permissions/export",method:"GET",params:e,responseType:"blob"})},importPermissions(e){return W({url:"/admin/wechat/permissions/import",method:"POST",data:e,headers:{"Content-Type":"multipart/form-data"}})},getPermissionTemplate(){return W({url:"/admin/wechat/permissions/template",method:"GET",responseType:"blob"})}};function Lt(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z","clip-rule":"evenodd"})])}function Mt(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495ZM10 5a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 10 5Zm0 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z","clip-rule":"evenodd"})])}function Bt(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-7-4a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM9 9a.75.75 0 0 0 0 1.5h.253a.25.25 0 0 1 .244.304l-.459 2.066A1.75 1.75 0 0 0 10.747 15H11a.75.75 0 0 0 0-1.5h-.253a.25.25 0 0 1-.244-.304l.459-2.066A1.75 1.75 0 0 0 9.253 9H9Z","clip-rule":"evenodd"})])}function At(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM8.28 7.22a.75.75 0 0 0-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 1 0 1.06 1.06L10 11.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L11.06 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L10 8.94 8.28 7.22Z","clip-rule":"evenodd"})])}function Br(e,t){return b(),P("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true","data-slot":"icon"},[m("path",{d:"M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"})])}const Ar={__name:"BaseToast",props:{type:{type:String,default:"info",validator:e=>["success","warning","error","info"].includes(e)},title:{type:String,default:""},message:{type:String,required:!0},position:{type:String,default:"top-right",validator:e=>["top-left","top-center","top-right","bottom-left","bottom-center","bottom-right"].includes(e)},duration:{type:Number,default:4e3},closable:{type:Boolean,default:!0},showAction:{type:Boolean,default:!1},actionText:{type:String,default:"操作"},icon:{type:[Object,Function],default:null}},emits:["close","action"],setup(e,{expose:t,emit:r}){const n=e,o=r,a=_(!1);let l=null;const s={success:{icon:Lt,iconClasses:"text-green-400",titleClasses:"text-green-800 dark:text-green-200",messageClasses:"text-green-700 dark:text-green-300",closeButtonClasses:"text-green-500 hover:text-green-600 focus:ring-green-600"},warning:{icon:Mt,iconClasses:"text-yellow-400",titleClasses:"text-yellow-800 dark:text-yellow-200",messageClasses:"text-yellow-700 dark:text-yellow-300",closeButtonClasses:"text-yellow-500 hover:text-yellow-600 focus:ring-yellow-600"},error:{icon:At,iconClasses:"text-red-400",titleClasses:"text-red-800 dark:text-red-200",messageClasses:"text-red-700 dark:text-red-300",closeButtonClasses:"text-red-500 hover:text-red-600 focus:ring-red-600"},info:{icon:Bt,iconClasses:"text-blue-400",titleClasses:"text-blue-800 dark:text-blue-200",messageClasses:"text-blue-700 dark:text-blue-300",closeButtonClasses:"text-blue-500 hover:text-blue-600 focus:ring-blue-600"}},i=x(()=>s[n.type]),u=x(()=>{const A=["fixed pointer-events-none"],U={"top-left":"top-0 left-0 p-6","top-center":"top-0 left-1/2 transform -translate-x-1/2 p-6","top-right":"top-0 right-0 p-6","bottom-left":"bottom-0 left-0 p-6","bottom-center":"bottom-0 left-1/2 transform -translate-x-1/2 p-6","bottom-right":"bottom-0 right-0 p-6"};return[...A,U[n.position]].join(" ")}),c=x(()=>["max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden","p-4"].join(" ")),v=x(()=>n.icon||i.value.icon),d=x(()=>["h-6 w-6",i.value.iconClasses].join(" ")),f=x(()=>["text-sm font-medium",i.value.titleClasses].join(" ")),p=x(()=>{const A=["text-sm"];return n.title&&A.push("mt-1"),[...A,i.value.messageClasses].join(" ")}),k=x(()=>["bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2",i.value.closeButtonClasses].join(" ")),h=()=>{a.value=!0,n.duration>0&&(l=setTimeout(()=>{C()},n.duration))},L=()=>{a.value=!1,l&&(clearTimeout(l),l=null)},C=()=>{L(),o("close")},D=()=>{o("action")};t({show:h,hide:L}),z(()=>{h()}),ee(()=>{l&&clearTimeout(l)});const H={props:n,emit:o,visible:a,get timer(){return l},set timer(A){l=A},typeConfig:s,config:i,containerClasses:u,toastClasses:c,iconComponent:v,iconClasses:d,titleClasses:f,messageClasses:p,closeButtonClasses:k,show:h,hide:L,handleClose:C,handleAction:D,computed:x,ref:_,onMounted:z,onUnmounted:ee,get CheckCircleIcon(){return Lt},get ExclamationTriangleIcon(){return Mt},get InformationCircleIcon(){return Bt},get XCircleIcon(){return At},get XMarkIcon(){return Br},BaseButton:gt};return Object.defineProperty(H,"__isScriptSetup",{enumerable:!1,value:!0}),H}},Dr={class:"flex"},Or={class:"flex-shrink-0"},Fr={class:"ml-3 w-0 flex-1"},Vr={key:1,class:"mt-3 flex space-x-3"},jr={key:0,class:"ml-4 flex-shrink-0 flex"};function Ir(e,t,r,n,o,a){return b(),R(Ot,{to:"body"},[n.visible?(b(),P("div",{key:0,class:Y(n.containerClasses),style:{"z-index":"9999"}},[y(pn,{"enter-active-class":"transform ease-out duration-300 transition","enter-from-class":"translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2","enter-to-class":"translate-y-0 opacity-100 sm:translate-x-0","leave-active-class":"transition ease-in duration-100","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:g(()=>[n.visible?(b(),P("div",{key:0,class:Y(n.toastClasses)},[m("div",Dr,[T(" 图标 "),m("div",Or,[(b(),R(Oe(n.iconComponent),{class:Y(n.iconClasses),"aria-hidden":"true"},null,8,["class"]))]),T(" 内容 "),m("div",Fr,[T(" 标题 "),r.title?(b(),P("p",{key:0,class:Y(n.titleClasses)},S(r.title),3)):T("v-if",!0),T(" 消息内容 "),m("p",{class:Y(n.messageClasses)},S(r.message),3),T(" 操作按钮 "),e.$slots.actions||r.showAction?(b(),P("div",Vr,[Fe(e.$slots,"actions",{},()=>[r.actionText?(b(),R(n.BaseButton,{key:0,variant:"ghost",size:"sm",onClick:n.handleAction},{default:g(()=>[B(S(r.actionText),1)]),_:1})):T("v-if",!0)])])):T("v-if",!0)]),T(" 关闭按钮 "),r.closable?(b(),P("div",jr,[m("button",{type:"button",class:Y(n.closeButtonClasses),onClick:n.handleClose},[t[0]||(t[0]=m("span",{class:"sr-only"},"关闭",-1)),y(n.XMarkIcon,{class:"h-5 w-5","aria-hidden":"true"})],2)])):T("v-if",!0)])],2)):T("v-if",!0)]),_:3})],2)):T("v-if",!0)])}const Rr=ye(Ar,[["render",Ir],["__file","E:/wx-nan/webs/admin/src/components/ui/BaseToast.vue"]]),Te=_([]);let Hr=0;class Nr{constructor(){this.container=null,this.init()}init(){typeof document!="undefined"&&(this.container=document.createElement("div"),this.container.id="toast-container",document.body.appendChild(this.container))}show(t){const r=++Hr,n=E({id:r,type:"info",position:"top-right",duration:4e3,closable:!0},t),o=Ft(Rr,fe(E({},n),{onClose:()=>this.remove(r)})),a=document.createElement("div");this.container.appendChild(a);const l=o.mount(a);return Te.value.push({id:r,app:o,instance:l,mountPoint:a,options:n}),r}remove(t){const r=Te.value.findIndex(n=>n.id===t);if(r>-1){const n=Te.value[r];n.app.unmount(),n.mountPoint&&n.mountPoint.parentNode&&n.mountPoint.parentNode.removeChild(n.mountPoint),Te.value.splice(r,1)}}clear(){Te.value.forEach(t=>{t.app.unmount(),t.mountPoint&&t.mountPoint.parentNode&&t.mountPoint.parentNode.removeChild(t.mountPoint)}),Te.value=[]}success(t,r={}){return this.show(E({type:"success",message:t},r))}error(t,r={}){return this.show(E({type:"error",message:t,duration:6e3},r))}warning(t,r={}){return this.show(E({type:"warning",message:t},r))}info(t,r={}){return this.show(E({type:"info",message:t},r))}loading(t="加载中...",r={}){return this.show(E({type:"info",message:t,duration:0,closable:!1},r))}}const K=new Nr;function Ee(){return{toast:K,success:K.success.bind(K),error:K.error.bind(K),warning:K.warning.bind(K),info:K.info.bind(K),loading:K.loading.bind(K),clear:K.clear.bind(K)}}const Ur={__name:"AddPermissionModal",props:{modelValue:{type:Boolean,default:!1}},emits:["update:modelValue","success"],setup(e,{expose:t,emit:r}){t();const n=e,o=r,{success:a,error:l}=Ee(),s=_(!1),i=_(),u=_(!1),c=_([]),v=pe({userId:"",permissionType:"user",expiresAt:"",remark:""}),d={userId:[{required:!0,message:"请选择用户",trigger:"change"}],permissionType:[{required:!0,message:"请选择权限类型",trigger:"change"}]};G(()=>n.modelValue,C=>{s.value=C,C&&(p(),f(""))}),G(s,C=>{o("update:modelValue",C)});const f=C=>ie(this,null,function*(){try{u.value=!0;const D=yield xt.getUsers({search:C,pageSize:20});D.success&&(c.value=D.data.list||[])}catch(D){console.error("搜索用户失败:",D),l("搜索用户失败")}finally{u.value=!1}}),p=()=>{i.value&&i.value.resetFields(),Object.assign(v,{userId:"",permissionType:"user",expiresAt:"",remark:""})},L={props:n,emit:o,success:a,error:l,visible:s,formRef:i,userLoading:u,userOptions:c,form:v,rules:d,searchUsers:f,resetForm:p,handleSubmit:()=>ie(this,null,function*(){try{yield i.value.validate();const C=yield ue.createPermission(v);C.success?(a("添加权限成功"),o("success")):l(C.message||"添加权限失败")}catch(C){console.error("添加权限失败:",C),l("添加权限失败")}}),handleCancel:()=>{s.value=!1},ref:_,reactive:pe,watch:G,BaseModal:$e,get userApi(){return xt},get wechatPermissionApi(){return ue},get useToast(){return Ee}};return Object.defineProperty(L,"__isScriptSetup",{enumerable:!1,value:!0}),L}};function Yr(e,t,r,n,o,a){const l=O("el-option"),s=O("el-select"),i=O("el-form-item"),u=O("el-radio"),c=O("el-radio-group"),v=O("el-date-picker"),d=O("el-input"),f=O("el-form");return b(),R(n.BaseModal,{modelValue:n.visible,"onUpdate:modelValue":t[4]||(t[4]=p=>n.visible=p),title:"添加微信登录权限",size:"lg","show-default-footer":!0,onConfirm:n.handleSubmit,onCancel:n.handleCancel},{default:g(()=>[y(f,{ref:"formRef",model:n.form,rules:n.rules,"label-width":"120px",class:"space-y-4"},{default:g(()=>[y(i,{label:"用户选择",prop:"userId"},{default:g(()=>[y(s,{modelValue:n.form.userId,"onUpdate:modelValue":t[0]||(t[0]=p=>n.form.userId=p),placeholder:"请选择用户",filterable:"",remote:"","remote-method":n.searchUsers,loading:n.userLoading,class:"w-full"},{default:g(()=>[(b(!0),P(Ge,null,vn(n.userOptions,p=>(b(),R(l,{key:p.id,label:`${p.name} (${p.phone||"无手机号"})`,value:p.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),y(i,{label:"权限类型",prop:"permissionType"},{default:g(()=>[y(c,{modelValue:n.form.permissionType,"onUpdate:modelValue":t[1]||(t[1]=p=>n.form.permissionType=p)},{default:g(()=>[y(u,{value:"admin"},{default:g(()=>t[5]||(t[5]=[B("管理员权限",-1)])),_:1,__:[5]}),y(u,{value:"user"},{default:g(()=>t[6]||(t[6]=[B("普通用户权限",-1)])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),y(i,{label:"有效期",prop:"expiresAt"},{default:g(()=>[y(v,{modelValue:n.form.expiresAt,"onUpdate:modelValue":t[2]||(t[2]=p=>n.form.expiresAt=p),type:"datetime",placeholder:"选择过期时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",class:"w-full"},null,8,["modelValue"])]),_:1}),y(i,{label:"备注",prop:"remark"},{default:g(()=>[y(d,{modelValue:n.form.remark,"onUpdate:modelValue":t[3]||(t[3]=p=>n.form.remark=p),type:"textarea",rows:3,placeholder:"请输入备注信息",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}const zr=ye(Ur,[["render",Yr],["__file","E:/wx-nan/webs/admin/src/views/system/components/AddPermissionModal.vue"]]),Wr={__name:"EditPermissionModal",props:{modelValue:{type:Boolean,default:!1},permission:{type:Object,default:()=>({})}},emits:["update:modelValue","success"],setup(e,{expose:t,emit:r}){t();const n=e,o=r,{success:a,error:l}=Ee(),s=_(!1),i=_(),u=pe({permissionType:"user",status:"active",expiresAt:"",remark:""}),c={permissionType:[{required:!0,message:"请选择权限类型",trigger:"change"}],status:[{required:!0,message:"请选择权限状态",trigger:"change"}]};G(()=>n.modelValue,h=>{s.value=h,h&&n.permission&&v()}),G(s,h=>{o("update:modelValue",h)});const v=()=>{n.permission&&Object.assign(u,{permissionType:n.permission.permissionType||"user",status:n.permission.status||"active",expiresAt:n.permission.expiresAt||"",remark:n.permission.remark||""})},k={props:n,emit:o,success:a,error:l,visible:s,formRef:i,form:u,rules:c,loadPermissionData:v,resetForm:()=>{i.value&&i.value.resetFields()},handleSubmit:()=>ie(this,null,function*(){try{yield i.value.validate();const h=yield ue.updatePermission(n.permission.id,u);h.success?(a("更新权限成功"),o("success")):l(h.message||"更新权限失败")}catch(h){console.error("更新权限失败:",h),l("更新权限失败")}}),handleCancel:()=>{s.value=!1},ref:_,reactive:pe,watch:G,BaseModal:$e,get wechatPermissionApi(){return ue},get useToast(){return Ee}};return Object.defineProperty(k,"__isScriptSetup",{enumerable:!1,value:!0}),k}},qr={class:"flex items-center space-x-3"},Zr={class:"font-medium"},Gr={class:"text-sm text-gray-500"};function Xr(e,t,r,n,o,a){const l=O("el-avatar"),s=O("el-form-item"),i=O("el-radio"),u=O("el-radio-group"),c=O("el-date-picker"),v=O("el-input"),d=O("el-form");return b(),R(n.BaseModal,{modelValue:n.visible,"onUpdate:modelValue":t[4]||(t[4]=f=>n.visible=f),title:"编辑微信登录权限",size:"lg","show-default-footer":!0,onConfirm:n.handleSubmit,onCancel:n.handleCancel},{default:g(()=>[y(d,{ref:"formRef",model:n.form,rules:n.rules,"label-width":"120px",class:"space-y-4"},{default:g(()=>[y(s,{label:"用户信息"},{default:g(()=>{var f,p,k,h,L,C;return[m("div",qr,[y(l,{size:40,src:(p=(f=r.permission)==null?void 0:f.user)==null?void 0:p.avatar},{default:g(()=>{var D,H,A;return[B(S((A=(H=(D=r.permission)==null?void 0:D.user)==null?void 0:H.name)==null?void 0:A.charAt(0)),1)]}),_:1},8,["src"]),m("div",null,[m("div",Zr,S((h=(k=r.permission)==null?void 0:k.user)==null?void 0:h.name),1),m("div",Gr,S(((C=(L=r.permission)==null?void 0:L.user)==null?void 0:C.phone)||"无手机号"),1)])])]}),_:1}),y(s,{label:"权限类型",prop:"permissionType"},{default:g(()=>[y(u,{modelValue:n.form.permissionType,"onUpdate:modelValue":t[0]||(t[0]=f=>n.form.permissionType=f)},{default:g(()=>[y(i,{value:"admin"},{default:g(()=>t[5]||(t[5]=[B("管理员权限",-1)])),_:1,__:[5]}),y(i,{value:"user"},{default:g(()=>t[6]||(t[6]=[B("普通用户权限",-1)])),_:1,__:[6]})]),_:1},8,["modelValue"])]),_:1}),y(s,{label:"权限状态",prop:"status"},{default:g(()=>[y(u,{modelValue:n.form.status,"onUpdate:modelValue":t[1]||(t[1]=f=>n.form.status=f)},{default:g(()=>[y(i,{value:"active"},{default:g(()=>t[7]||(t[7]=[B("启用",-1)])),_:1,__:[7]}),y(i,{value:"inactive"},{default:g(()=>t[8]||(t[8]=[B("禁用",-1)])),_:1,__:[8]})]),_:1},8,["modelValue"])]),_:1}),y(s,{label:"有效期",prop:"expiresAt"},{default:g(()=>[y(c,{modelValue:n.form.expiresAt,"onUpdate:modelValue":t[2]||(t[2]=f=>n.form.expiresAt=f),type:"datetime",placeholder:"选择过期时间",format:"YYYY-MM-DD HH:mm:ss","value-format":"YYYY-MM-DD HH:mm:ss",class:"w-full"},null,8,["modelValue"])]),_:1}),y(s,{label:"备注",prop:"remark"},{default:g(()=>[y(v,{modelValue:n.form.remark,"onUpdate:modelValue":t[3]||(t[3]=f=>n.form.remark=f),type:"textarea",rows:3,placeholder:"请输入备注信息",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])}const Kr=ye(Wr,[["render",Xr],["__file","E:/wx-nan/webs/admin/src/views/system/components/EditPermissionModal.vue"]]);var rn={exports:{}};(function(e,t){(function(r,n){e.exports=n()})(Vt,function(){return function(r,n,o){r=r||{};var a=n.prototype,l={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function s(u,c,v,d){return a.fromToBase(u,c,v,d)}o.en.relativeTime=l,a.fromToBase=function(u,c,v,d,f){for(var p,k,h,L=v.$locale().relativeTime||l,C=r.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],D=C.length,H=0;H<D;H+=1){var A=C[H];A.d&&(p=d?o(u).diff(v,A.d,!0):v.diff(u,A.d,!0));var U=(r.rounding||Math.round)(Math.abs(p));if(h=p>0,U<=A.r||!A.r){U<=1&&H>0&&(A=C[H-1]);var se=L[A.l];f&&(U=f(""+U)),k=typeof se=="string"?se.replace("%d",U):se(U,c,A.l,h);break}}if(c)return k;var j=h?L.future:L.past;return typeof j=="function"?j(k):j.replace("%s",k)},a.to=function(u,c){return s(u,c,this,!0)},a.from=function(u,c){return s(u,c,this)};var i=function(u){return u.$u?o.utc():o()};a.toNow=function(u){return this.to(i(this),u)},a.fromNow=function(u){return this.from(i(this),u)}}})})(rn);var $r=rn.exports;const Qr=gn($r);var Jr={exports:{}};(function(e,t){(function(r,n){e.exports=n(hn)})(Vt,function(r){function n(l){return l&&typeof l=="object"&&"default"in l?l:{default:l}}var o=n(r),a={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(l,s){return s==="W"?l+"周":l+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(l,s){var i=100*l+s;return i<600?"凌晨":i<900?"早上":i<1100?"上午":i<1300?"中午":i<1800?"下午":"晚上"}};return o.default.locale(a,null,!0),a})})(Jr);dt.extend(Qr);dt.locale("zh-cn");function on(e,t="YYYY-MM-DD HH:mm:ss"){return e?dt(e).format(t):""}const eo={__name:"PermissionDetailModal",props:{modelValue:{type:Boolean,default:!1},permission:{type:Object,default:()=>({})}},emits:["update:modelValue"],setup(e,{expose:t,emit:r}){t();const n=e,o=r,a=_(!1);G(()=>n.modelValue,i=>{a.value=i}),G(a,i=>{o("update:modelValue",i)});const s={props:n,emit:o,visible:a,handleClose:()=>{a.value=!1},ref:_,watch:G,BaseModal:$e,get formatDate(){return on}};return Object.defineProperty(s,"__isScriptSetup",{enumerable:!1,value:!0}),s}},to={key:0,class:"space-y-6"},no={class:"bg-gray-50 p-4 rounded-lg"},ro={class:"flex items-center space-x-4"},oo={class:"flex-1"},ao={class:"grid grid-cols-2 gap-4"},lo={class:"font-medium"},so={class:"font-medium"},io={class:"font-medium text-xs"},uo={class:"font-medium"},co={class:"bg-gray-50 p-4 rounded-lg"},mo={class:"grid grid-cols-2 gap-4"},fo={class:"font-medium"},po={class:"font-medium"},vo={class:"font-medium"},go={class:"font-medium"},ho={class:"font-medium"},yo={class:"font-medium"},wo={key:0,class:"bg-gray-50 p-4 rounded-lg"},xo={class:"text-gray-700"},bo={class:"bg-gray-50 p-4 rounded-lg"},_o={class:"grid grid-cols-3 gap-4"},ko={class:"text-center"},Co={class:"text-2xl font-bold text-blue-600"},So={class:"text-center"},To={class:"text-2xl font-bold text-green-600"},Eo={class:"text-center"},Po={class:"text-2xl font-bold text-purple-600"},Lo={class:"flex justify-end"};function Mo(e,t,r,n,o,a){const l=O("el-avatar"),s=O("el-tag"),i=O("el-button");return b(),R(n.BaseModal,{modelValue:n.visible,"onUpdate:modelValue":t[0]||(t[0]=u=>n.visible=u),title:"权限详情",size:"lg","show-default-footer":!1},{footer:g(()=>[m("div",Lo,[y(i,{onClick:n.handleClose},{default:g(()=>t[18]||(t[18]=[B("关闭",-1)])),_:1,__:[18]})])]),default:g(()=>{var u,c,v,d,f;return[r.permission?(b(),P("div",to,[T(" 用户信息 "),m("div",no,[t[5]||(t[5]=m("h3",{class:"text-lg font-medium mb-3"},"用户信息",-1)),m("div",ro,[y(l,{size:60,src:(u=r.permission.user)==null?void 0:u.avatar},{default:g(()=>{var p,k;return[B(S((k=(p=r.permission.user)==null?void 0:p.name)==null?void 0:k.charAt(0)),1)]}),_:1},8,["src"]),m("div",oo,[m("div",ao,[m("div",null,[t[1]||(t[1]=m("label",{class:"text-sm text-gray-500"},"用户名",-1)),m("div",lo,S(((c=r.permission.user)==null?void 0:c.name)||"-"),1)]),m("div",null,[t[2]||(t[2]=m("label",{class:"text-sm text-gray-500"},"手机号",-1)),m("div",so,S(((v=r.permission.user)==null?void 0:v.phone)||"-"),1)]),m("div",null,[t[3]||(t[3]=m("label",{class:"text-sm text-gray-500"},"微信OpenID",-1)),m("div",io,S(((d=r.permission.user)==null?void 0:d.openid)||"-"),1)]),m("div",null,[t[4]||(t[4]=m("label",{class:"text-sm text-gray-500"},"用户角色",-1)),m("div",uo,[y(s,{type:((f=r.permission.user)==null?void 0:f.role)==="admin"?"danger":"primary",size:"small"},{default:g(()=>{var p;return[B(S(((p=r.permission.user)==null?void 0:p.role)==="admin"?"管理员":"普通用户"),1)]}),_:1},8,["type"])])])])])])]),T(" 权限信息 "),m("div",co,[t[12]||(t[12]=m("h3",{class:"text-lg font-medium mb-3"},"权限信息",-1)),m("div",mo,[m("div",null,[t[6]||(t[6]=m("label",{class:"text-sm text-gray-500"},"权限类型",-1)),m("div",fo,[y(s,{type:r.permission.permissionType==="admin"?"danger":"primary",size:"small"},{default:g(()=>[B(S(r.permission.permissionType==="admin"?"管理员权限":"普通用户权限"),1)]),_:1},8,["type"])])]),m("div",null,[t[7]||(t[7]=m("label",{class:"text-sm text-gray-500"},"权限状态",-1)),m("div",po,[y(s,{type:r.permission.status==="active"?"success":"danger",size:"small"},{default:g(()=>[B(S(r.permission.status==="active"?"启用":"禁用"),1)]),_:1},8,["type"])])]),m("div",null,[t[8]||(t[8]=m("label",{class:"text-sm text-gray-500"},"创建时间",-1)),m("div",vo,S(n.formatDate(r.permission.createdAt)),1)]),m("div",null,[t[9]||(t[9]=m("label",{class:"text-sm text-gray-500"},"更新时间",-1)),m("div",go,S(n.formatDate(r.permission.updatedAt)),1)]),m("div",null,[t[10]||(t[10]=m("label",{class:"text-sm text-gray-500"},"有效期",-1)),m("div",ho,S(r.permission.expiresAt?n.formatDate(r.permission.expiresAt):"永久有效"),1)]),m("div",null,[t[11]||(t[11]=m("label",{class:"text-sm text-gray-500"},"最后登录",-1)),m("div",yo,S(r.permission.lastLoginAt?n.formatDate(r.permission.lastLoginAt):"从未登录"),1)])])]),T(" 备注信息 "),r.permission.remark?(b(),P("div",wo,[t[13]||(t[13]=m("h3",{class:"text-lg font-medium mb-3"},"备注信息",-1)),m("div",xo,S(r.permission.remark),1)])):T("v-if",!0),T(" 登录历史 "),m("div",bo,[t[17]||(t[17]=m("h3",{class:"text-lg font-medium mb-3"},"登录统计",-1)),m("div",_o,[m("div",ko,[m("div",Co,S(r.permission.loginCount||0),1),t[14]||(t[14]=m("div",{class:"text-sm text-gray-500"},"总登录次数",-1))]),m("div",So,[m("div",To,S(r.permission.todayLoginCount||0),1),t[15]||(t[15]=m("div",{class:"text-sm text-gray-500"},"今日登录次数",-1))]),m("div",Eo,[m("div",Po,S(r.permission.weekLoginCount||0),1),t[16]||(t[16]=m("div",{class:"text-sm text-gray-500"},"本周登录次数",-1))])])])])):T("v-if",!0)]}),_:1},8,["modelValue"])}const Bo=ye(eo,[["render",Mo],["__file","E:/wx-nan/webs/admin/src/views/system/components/PermissionDetailModal.vue"]]),Ao={__name:"BaseConfirm",props:{modelValue:{type:Boolean,default:!1},type:{type:String,default:"warning",validator:e=>["warning","info","success","error"].includes(e)},title:{type:String,default:"确认操作"},content:{type:String,default:"确定要执行此操作吗？"},confirmText:{type:String,default:"确认"},cancelText:{type:String,default:"取消"},loading:{type:Boolean,default:!1}},emits:["update:modelValue","confirm","cancel"],setup(e,{expose:t,emit:r}){t();const n=e,o=r,a={warning:{icon:nt,iconClass:"h-6 w-6 text-yellow-600",iconBgClass:"bg-yellow-100 dark:bg-yellow-900",confirmVariant:"warning"},info:{icon:bt,iconClass:"h-6 w-6 text-blue-600",iconBgClass:"bg-blue-100 dark:bg-blue-900",confirmVariant:"primary"},success:{icon:tt,iconClass:"h-6 w-6 text-green-600",iconBgClass:"bg-green-100 dark:bg-green-900",confirmVariant:"success"},error:{icon:_t,iconClass:"h-6 w-6 text-red-600",iconBgClass:"bg-red-100 dark:bg-red-900",confirmVariant:"error"}},l=x(()=>a[n.type]),s=x(()=>l.value.icon),i=x(()=>l.value.iconClass),u=x(()=>l.value.iconBgClass),c=x(()=>l.value.confirmVariant),f={props:n,emit:o,typeConfig:a,config:l,iconComponent:s,iconClass:i,iconBgClass:u,confirmVariant:c,handleConfirm:()=>{o("confirm")},handleCancel:()=>{o("update:modelValue",!1),o("cancel")},computed:x,get ExclamationTriangleIcon(){return nt},get InformationCircleIcon(){return bt},get CheckCircleIcon(){return tt},get XCircleIcon(){return _t},BaseModal:$e,BaseButton:gt};return Object.defineProperty(f,"__isScriptSetup",{enumerable:!1,value:!0}),f}},Do={class:"text-center"},Oo={class:"text-center"},Fo={class:"text-lg font-medium text-gray-900 dark:text-white mb-2"},Vo={class:"text-sm text-gray-500 dark:text-gray-400"},jo={class:"flex justify-center space-x-3"};function Io(e,t,r,n,o,a){return b(),R(n.BaseModal,{"model-value":r.modelValue,title:r.title,size:"sm",closable:!1,"mask-closable":!1,"onUpdate:modelValue":t[0]||(t[0]=l=>e.$emit("update:modelValue",l))},{footer:g(()=>[m("div",jo,[y(n.BaseButton,{variant:"outline",onClick:n.handleCancel,disabled:r.loading},{default:g(()=>[B(S(r.cancelText),1)]),_:1},8,["disabled"]),y(n.BaseButton,{variant:n.confirmVariant,onClick:n.handleConfirm,loading:r.loading},{default:g(()=>[B(S(r.confirmText),1)]),_:1},8,["variant","loading"])])]),default:g(()=>[m("div",Do,[T(" 图标 "),m("div",{class:Y(["mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4",n.iconBgClass])},[(b(),R(Oe(n.iconComponent),{class:Y(n.iconClass),"aria-hidden":"true"},null,8,["class"]))],2),T(" 内容 "),m("div",Oo,[m("h3",Fo,S(r.title),1),m("p",Vo,S(r.content),1)])])]),_:1},8,["model-value","title"])}const Ro=ye(Ao,[["render",Io],["__file","E:/wx-nan/webs/admin/src/components/ui/BaseConfirm.vue"]]);class Ho{constructor(){this.container=null,this.init()}init(){typeof document!="undefined"&&(this.container=document.createElement("div"),this.container.id="confirm-container",document.body.appendChild(this.container))}show(t={}){return new Promise((r,n)=>{const a=E(E({},{type:"warning",title:"确认操作",content:"确定要执行此操作吗？",confirmText:"确认",cancelText:"取消"}),t),l=_(!0),s=_(!1),i=Ft(Ro,fe(E({modelValue:l.value},a),{loading:s.value,"onUpdate:modelValue":v=>{l.value=v,v||(c(),n(new Error("User cancelled")))},onConfirm:()=>ie(this,null,function*(){try{s.value=!0,typeof a.onConfirm=="function"&&(yield a.onConfirm()),l.value=!1,c(),r(!0)}catch(v){s.value=!1,n(v)}}),onCancel:()=>{l.value=!1,c(),n(new Error("User cancelled"))}})),u=document.createElement("div");this.container.appendChild(u),i.mount(u);const c=()=>{setTimeout(()=>{i.unmount(),u&&u.parentNode&&u.parentNode.removeChild(u)},300)}})}warning(t,r={}){return this.show(E({type:"warning",content:t},r))}info(t,r={}){return this.show(E({type:"info",content:t},r))}success(t,r={}){return this.show(E({type:"success",content:t},r))}error(t,r={}){return this.show(E({type:"error",content:t},r))}delete(t="此项",r={}){return this.show(E({type:"error",title:"确认删除",content:`确定要删除${t}吗？此操作不可撤销。`,confirmText:"删除"},r))}batchDelete(t,r={}){return this.show(E({type:"error",title:"确认批量删除",content:`确定要删除选中的 ${t} 项吗？此操作不可撤销。`,confirmText:"删除"},r))}save(t="确定要保存当前修改吗？",r={}){return this.show(E({type:"info",title:"确认保存",content:t,confirmText:"保存"},r))}leave(t="当前有未保存的修改，确定要离开吗？",r={}){return this.show(E({type:"warning",title:"确认离开",content:t,confirmText:"离开"},r))}}const N=new Ho;function Dt(){return{confirm:N.show.bind(N),warning:N.warning.bind(N),info:N.info.bind(N),success:N.success.bind(N),error:N.error.bind(N),delete:N.delete.bind(N),batchDelete:N.batchDelete.bind(N),save:N.save.bind(N),leave:N.leave.bind(N)}}const No={__name:"WechatPermissions",setup(e,{expose:t}){t();const{confirm:r}=Dt(),{success:n,error:o}=Ee(),a=_(!1),l=_([]),s=_([]),i=_(!1),u=_(!1),c=_(!1),v=_(null),d=pe({keyword:"",permissionLevel:"",status:"",expiryStatus:"",dateRange:[]}),f=pe({current:1,pageSize:10,total:0}),p=pe({totalPermissions:0,activePermissions:0,expiringPermissions:0,todayLogins:0}),k=[{text:"首页",to:"/"},{text:"系统管理",to:"/system"},{text:"微信权限管理"}],h=[{key:"keyword",type:"input",label:"关键词",placeholder:"搜索用户昵称、OpenID",prefixIcon:"MagnifyingGlassIcon"},{key:"permissionLevel",type:"select",label:"权限级别",placeholder:"请选择权限级别",options:[{label:"全部",value:""},{label:"超级管理员",value:"super_admin"},{label:"管理员",value:"admin"},{label:"操作员",value:"operator"}]},{key:"status",type:"select",label:"状态",placeholder:"请选择状态",options:[{label:"全部",value:""},{label:"有效",value:"1"},{label:"已撤销",value:"0"}]},{key:"expiryStatus",type:"select",label:"过期状态",placeholder:"请选择过期状态",options:[{label:"全部",value:""},{label:"永不过期",value:"never"},{label:"即将过期",value:"expiring"},{label:"已过期",value:"expired"}]},{key:"dateRange",type:"daterange",label:"授权时间",startPlaceholder:"开始日期",endPlaceholder:"结束日期"}],L=[{key:"user",title:"用户信息"},{key:"permissionLevel",title:"权限级别"},{key:"status",title:"状态"},{key:"grantedBy",title:"授权信息"},{key:"expiresAt",title:"过期时间",sortable:!0}],C=w=>({super_admin:"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",admin:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",operator:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"})[w]||"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200",D=w=>({super_admin:"超级管理员",admin:"管理员",operator:"操作员"})[w]||"未知",H=w=>w.status===0?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":w.expiresAt&&new Date(w.expiresAt)<new Date?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",A=w=>w.status===0?"已撤销":w.expiresAt&&new Date(w.expiresAt)<new Date?"已过期":"有效",U=w=>{const V=new Date,X=new Date(w),Se=Math.ceil((X-V)/(1e3*60*60*24));return Se<=0?"text-red-600 dark:text-red-400":Se<=7?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"},se=w=>{const V=new Date,X=new Date(w),Se=Math.ceil((X-V)/(1e3*60*60*24));return Se<=0?"已过期":Se<=7?`${Se}天后过期`:"正常"},j=()=>ie(this,null,function*(){a.value=!0;try{const w=E({page:f.current,pageSize:f.pageSize},d),V=yield ue.getPermissions(w);l.value=V.data.permissions,f.total=V.data.total}catch(w){console.error("获取权限列表失败:",w),o("获取权限列表失败")}finally{a.value=!1}}),te=()=>ie(this,null,function*(){try{const w=yield ue.getStats();Object.assign(p,w.data)}catch(w){console.error("获取统计数据失败:",w)}}),Ce=()=>{f.current=1,j()},Le=()=>{f.current=1,j()},Me=()=>{j()},Q=w=>{console.log("排序:",w),j()},we=w=>{v.value=w,c.value=!0},ne=w=>{v.value=w,u.value=!0},re=w=>ie(this,null,function*(){var V;try{yield r({title:"确认撤销权限",content:`确定要撤销用户 ${((V=w.wechatUser)==null?void 0:V.nickname)||"未知用户"} 的登录权限吗？`,type:"warning"}),yield ue.revokePermission(w.id),n("权限撤销成功"),j(),te()}catch(X){X.message!=="User cancelled"&&o("撤销权限失败")}}),ve=w=>ie(this,null,function*(){try{yield ue.restorePermission(w.id),n("权限恢复成功"),j(),te()}catch(V){o("恢复权限失败")}}),Ie=()=>ie(this,null,function*(){try{yield r({title:"确认批量撤销",content:`确定要撤销选中的 ${s.value.length} 个权限吗？`,type:"warning"});const w=s.value.map(V=>V.id);yield ue.batchRevokePermissions(w),n("批量撤销成功"),s.value=[],j(),te()}catch(w){w.message!=="User cancelled"&&o("批量撤销失败")}}),Re=()=>{console.log("导出数据")},He=w=>{console.log("点击权限:",w)},M=()=>{i.value=!1,j(),te()},F=()=>{u.value=!1,j(),te()};z(()=>{j(),te()});const q={confirm:r,success:n,error:o,loading:a,permissions:l,selectedPermissions:s,showAddModal:i,showEditModal:u,showDetailModal:c,currentPermission:v,searchParams:d,pagination:f,stats:p,breadcrumbItems:k,searchFields:h,tableColumns:L,getPermissionLevelClass:C,getPermissionLevelText:D,getStatusClass:H,getStatusText:A,getExpiryWarningClass:U,getExpiryText:se,fetchPermissions:j,fetchStats:te,handleSearch:Ce,handleReset:Le,handlePageChange:Me,handleSort:Q,handleView:we,handleEdit:ne,handleRevoke:re,handleRestore:ve,handleBatchRevoke:Ie,handleExport:Re,handleRowClick:He,handleAddSuccess:M,handleEditSuccess:F,ref:_,reactive:pe,computed:x,onMounted:z,get ShieldCheckIcon(){return wn},get CheckCircleIcon(){return tt},get ExclamationTriangleIcon(){return nt},get UserIcon(){return xn},get PlusIcon(){return yn},AddPermissionModal:zr,EditPermissionModal:Kr,PermissionDetailModal:Bo,get wechatPermissionApi(){return ue},get formatDate(){return on},get useConfirm(){return Dt},get useToast(){return Ee}};return Object.defineProperty(q,"__isScriptSetup",{enumerable:!1,value:!0}),q}},Uo={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"},Yo={class:"flex items-center justify-between"},zo={class:"text-lg font-medium text-gray-900 dark:text-white"},Wo={class:"flex items-center space-x-3"},qo={class:"flex items-center"},Zo=["src","alt"],Go={key:1,class:"h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center mr-2"},Xo={class:"text-sm font-medium text-gray-900 dark:text-white"},Ko={class:"text-xs text-gray-500 dark:text-gray-400"},$o={key:0,class:"text-sm"},Qo={class:"font-medium text-gray-900 dark:text-white"},Jo={class:"text-xs text-gray-500 dark:text-gray-400"},ea={key:1,class:"text-sm text-gray-500 dark:text-gray-400"},ta={key:0,class:"text-sm"},na={class:"text-gray-900 dark:text-white"},ra={key:1,class:"text-sm text-green-600 dark:text-green-400"},oa={class:"flex items-center space-x-2"};function aa(e,t,r,n,o,a){const l=O("BaseButton"),s=O("StatsCard"),i=O("SearchForm"),u=O("Pagination"),c=O("BaseTable"),v=O("PageContainer");return b(),R(v,{title:"微信登录权限管理",subtitle:"管理可以通过微信扫码登录后台的用户权限",breadcrumb:n.breadcrumbItems},{"header-actions":g(()=>[y(l,{variant:"primary","prefix-icon":"PlusIcon",onClick:t[0]||(t[0]=d=>n.showAddModal=!0)},{default:g(()=>t[8]||(t[8]=[B(" 添加权限 ",-1)])),_:1,__:[8]})]),default:g(()=>[m("div",Uo,[y(s,{title:"总权限数",value:n.stats.totalPermissions,icon:n.ShieldCheckIcon,color:"blue",description:"已授权用户"},null,8,["value","icon"]),y(s,{title:"有效权限",value:n.stats.activePermissions,icon:n.CheckCircleIcon,color:"green",description:"当前有效"},null,8,["value","icon"]),y(s,{title:"即将过期",value:n.stats.expiringPermissions,icon:n.ExclamationTriangleIcon,color:"yellow",description:"7天内过期"},null,8,["value","icon"]),y(s,{title:"今日登录",value:n.stats.todayLogins,icon:e.UserCheckIcon,color:"purple",description:"微信登录次数"},null,8,["value","icon"])]),y(i,{modelValue:n.searchParams,"onUpdate:modelValue":t[1]||(t[1]=d=>n.searchParams=d),fields:n.searchFields,loading:n.loading,onSearch:n.handleSearch,onReset:n.handleReset},null,8,["modelValue","loading"]),y(c,{data:n.permissions,columns:n.tableColumns,selectable:!0,"selected-rows":n.selectedPermissions,"onUpdate:selectedRows":t[4]||(t[4]=d=>n.selectedPermissions=d),loading:n.loading,onRowClick:n.handleRowClick,onSortChange:n.handleSort},{header:g(()=>[m("div",Yo,[m("h3",zo," 权限列表 ("+S(n.pagination.total)+") ",1),m("div",Wo,[n.selectedPermissions.length?(b(),R(l,{key:0,variant:"error",size:"sm",onClick:n.handleBatchRevoke},{default:g(()=>[B(" 批量撤销 ("+S(n.selectedPermissions.length)+") ",1)]),_:1})):T("v-if",!0),y(l,{variant:"outline",size:"sm",onClick:n.handleExport},{default:g(()=>t[9]||(t[9]=[B(" 导出数据 ",-1)])),_:1,__:[9]})])])]),"cell-user":g(({row:d})=>{var f,p;return[m("div",qo,[(f=d.wechatUser)!=null&&f.avatar?(b(),P("img",{key:0,src:d.wechatUser.avatar,alt:d.wechatUser.nickname,class:"h-8 w-8 rounded-full object-cover mr-2"},null,8,Zo)):(b(),P("div",Go,[y(n.UserIcon,{class:"h-4 w-4 text-gray-500"})])),m("div",null,[m("div",Xo,S(((p=d.wechatUser)==null?void 0:p.nickname)||"未知用户"),1),m("div",Ko,S(d.openid.slice(0,8))+"... ",1)])])]}),"cell-permissionLevel":g(({row:d})=>[m("span",{class:Y([n.getPermissionLevelClass(d.permissionLevel),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},S(n.getPermissionLevelText(d.permissionLevel)),3)]),"cell-status":g(({row:d})=>[m("span",{class:Y([n.getStatusClass(d),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},S(n.getStatusText(d)),3)]),"cell-grantedBy":g(({row:d})=>[d.grantedByUser?(b(),P("div",$o,[m("div",Qo,S(d.grantedByUser.username),1),m("div",Jo,S(n.formatDate(d.grantedAt)),1)])):(b(),P("span",ea," 系统授权 "))]),"cell-expiresAt":g(({row:d})=>[d.expiresAt?(b(),P("div",ta,[m("div",na,S(n.formatDate(d.expiresAt)),1),m("div",{class:Y([n.getExpiryWarningClass(d.expiresAt),"text-xs"])},S(n.getExpiryText(d.expiresAt)),3)])):(b(),P("span",ra," 永不过期 "))]),actions:g(({row:d})=>[m("div",oa,[y(l,{variant:"ghost",size:"sm",onClick:f=>n.handleView(d)},{default:g(()=>t[10]||(t[10]=[B(" 查看 ",-1)])),_:2,__:[10]},1032,["onClick"]),y(l,{variant:"ghost",size:"sm",onClick:f=>n.handleEdit(d),class:"text-blue-600"},{default:g(()=>t[11]||(t[11]=[B(" 编辑 ",-1)])),_:2,__:[11]},1032,["onClick"]),d.status===1?(b(),R(l,{key:0,variant:"ghost",size:"sm",onClick:f=>n.handleRevoke(d),class:"text-red-600 hover:text-red-700"},{default:g(()=>t[12]||(t[12]=[B(" 撤销 ",-1)])),_:2,__:[12]},1032,["onClick"])):(b(),R(l,{key:1,variant:"ghost",size:"sm",onClick:f=>n.handleRestore(d),class:"text-green-600 hover:text-green-700"},{default:g(()=>t[13]||(t[13]=[B(" 恢复 ",-1)])),_:2,__:[13]},1032,["onClick"]))])]),footer:g(()=>[y(u,{current:n.pagination.current,"onUpdate:current":t[2]||(t[2]=d=>n.pagination.current=d),"page-size":n.pagination.pageSize,"onUpdate:pageSize":t[3]||(t[3]=d=>n.pagination.pageSize=d),total:n.pagination.total,onChange:n.handlePageChange},null,8,["current","page-size","total"])]),_:1},8,["data","selected-rows","loading"]),y(n.AddPermissionModal,{modelValue:n.showAddModal,"onUpdate:modelValue":t[5]||(t[5]=d=>n.showAddModal=d),onSuccess:n.handleAddSuccess},null,8,["modelValue"]),y(n.EditPermissionModal,{modelValue:n.showEditModal,"onUpdate:modelValue":t[6]||(t[6]=d=>n.showEditModal=d),permission:n.currentPermission,onSuccess:n.handleEditSuccess},null,8,["modelValue","permission"]),y(n.PermissionDetailModal,{modelValue:n.showDetailModal,"onUpdate:modelValue":t[7]||(t[7]=d=>n.showDetailModal=d),permission:n.currentPermission},null,8,["modelValue","permission"])]),_:1})}const da=ye(No,[["render",aa],["__file","E:/wx-nan/webs/admin/src/views/system/WechatPermissions.vue"]]);export{da as default};
