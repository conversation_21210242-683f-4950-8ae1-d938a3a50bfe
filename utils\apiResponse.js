/**
 * API响应格式标准化工具
 * 统一处理API响应数据格式
 */

/**
 * 标准API响应格式
 * @typedef {Object} ApiResponse
 * @property {number} code - 状态码
 * @property {string} message - 响应消息
 * @property {*} data - 响应数据
 * @property {number} timestamp - 时间戳
 * @property {string} requestId - 请求ID（可选）
 */

/**
 * 分页数据格式
 * @typedef {Object} PaginationData
 * @property {Array} list - 数据列表
 * @property {number} total - 总数
 * @property {number} page - 当前页码
 * @property {number} size - 每页大小
 * @property {number} totalPages - 总页数
 * @property {boolean} hasNext - 是否有下一页
 * @property {boolean} hasPrev - 是否有上一页
 */

class ApiResponseHandler {
  constructor() {
    this.successCodes = [200, 201];
    this.errorCodes = {
      BAD_REQUEST: 400,
      UNAUTHORIZED: 401,
      FORBIDDEN: 403,
      NOT_FOUND: 404,
      CONFLICT: 409,
      VALIDATION_ERROR: 422,
      INTERNAL_ERROR: 500,
      SERVICE_UNAVAILABLE: 503
    };
  }

  /**
   * 检查响应是否成功
   * @param {ApiResponse} response
   * @returns {boolean}
   */
  isSuccess(response) {
    if (!response || typeof response !== 'object') {
      return false;
    }

    return this.successCodes.includes(response.code);
  }

  /**
   * 检查响应是否为错误
   * @param {ApiResponse} response
   * @returns {boolean}
   */
  isError(response) {
    return !this.isSuccess(response);
  }

  /**
   * 标准化响应数据
   * @param {*} rawResponse 原始响应
   * @returns {ApiResponse}
   */
  normalize(rawResponse) {
    // 如果已经是标准格式
    if (this.isStandardFormat(rawResponse)) {
      return rawResponse;
    }

    // 处理不同的响应格式
    if (typeof rawResponse === 'string') {
      return {
        code: 200,
        message: 'Success',
        data: rawResponse,
        timestamp: Date.now()
      };
    }

    if (Array.isArray(rawResponse)) {
      return {
        code: 200,
        message: 'Success',
        data: rawResponse,
        timestamp: Date.now()
      };
    }

    if (typeof rawResponse === 'object') {
      // 检查是否有常见的响应字段
      if (rawResponse.success !== undefined) {
        return {
          code: rawResponse.success ? 200 : 400,
          message:
            rawResponse.message || (rawResponse.success ? 'Success' : 'Error'),
          data: rawResponse.data || rawResponse.result || null,
          timestamp: Date.now()
        };
      }

      if (rawResponse.status !== undefined) {
        return {
          code: rawResponse.status,
          message: rawResponse.message || 'Response',
          data: rawResponse.data || rawResponse,
          timestamp: Date.now()
        };
      }

      // 默认处理
      return {
        code: 200,
        message: 'Success',
        data: rawResponse,
        timestamp: Date.now()
      };
    }

    // 其他类型
    return {
      code: 200,
      message: 'Success',
      data: rawResponse,
      timestamp: Date.now()
    };
  }

  /**
   * 检查是否为标准格式
   * @param {*} response
   * @returns {boolean}
   */
  isStandardFormat(response) {
    return (
      response &&
      typeof response === 'object' &&
      typeof response.code === 'number' &&
      typeof response.message === 'string' &&
      response.data !== undefined
    );
  }

  /**
   * 提取响应数据
   * @param {ApiResponse} response
   * @returns {*}
   */
  extractData(response) {
    const normalized = this.normalize(response);
    return normalized.data;
  }

  /**
   * 提取错误信息
   * @param {ApiResponse} response
   * @returns {string}
   */
  extractError(response) {
    const normalized = this.normalize(response);
    return normalized.message || '未知错误';
  }

  /**
   * 创建成功响应
   * @param {*} data
   * @param {string} message
   * @param {number} code
   * @returns {ApiResponse}
   */
  createSuccess(data = null, message = 'Success', code = 200) {
    return {
      code,
      message,
      data,
      timestamp: Date.now()
    };
  }

  /**
   * 创建错误响应
   * @param {string} message
   * @param {number} code
   * @param {*} data
   * @returns {ApiResponse}
   */
  createError(message = 'Error', code = 400, data = null) {
    return {
      code,
      message,
      data,
      timestamp: Date.now()
    };
  }

  /**
   * 创建分页响应
   * @param {Array} list 数据列表
   * @param {number} total 总数
   * @param {number} page 当前页
   * @param {number} size 每页大小
   * @param {string} message 消息
   * @returns {ApiResponse}
   */
  createPaginationResponse(
    list = [],
    total = 0,
    page = 1,
    size = 10,
    message = 'Success'
  ) {
    const totalPages = Math.ceil(total / size);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const paginationData = {
      list,
      total,
      page,
      size,
      totalPages,
      hasNext,
      hasPrev
    };

    return this.createSuccess(paginationData, message);
  }

  /**
   * 处理分页参数
   * @param {Object} params 查询参数
   * @returns {Object}
   */
  processPaginationParams(params = {}) {
    const page = Math.max(1, parseInt(params.page) || 1);
    const size = Math.min(100, Math.max(1, parseInt(params.size) || 10));
    const offset = (page - 1) * size;

    return {
      page,
      size,
      offset,
      limit: size
    };
  }

  /**
   * 验证必需字段
   * @param {Object} data 数据对象
   * @param {Array} requiredFields 必需字段列表
   * @returns {Object}
   */
  validateRequiredFields(data, requiredFields = []) {
    const errors = {};
    const missingFields = [];

    requiredFields.forEach(field => {
      if (
        data[field] === undefined ||
        data[field] === null ||
        data[field] === ''
      ) {
        missingFields.push(field);
        errors[field] = `${field} is required`;
      }
    });

    return {
      isValid: missingFields.length === 0,
      errors,
      missingFields
    };
  }

  /**
   * 过滤响应数据字段
   * @param {Object} data 数据对象
   * @param {Array} allowedFields 允许的字段
   * @returns {Object}
   */
  filterFields(data, allowedFields = []) {
    if (!data || typeof data !== 'object' || allowedFields.length === 0) {
      return data;
    }

    const filtered = {};
    allowedFields.forEach(field => {
      if (data[field] !== undefined) {
        filtered[field] = data[field];
      }
    });

    return filtered;
  }

  /**
   * 排除敏感字段
   * @param {Object} data 数据对象
   * @param {Array} excludeFields 要排除的字段
   * @returns {Object}
   */
  excludeFields(data, excludeFields = ['password', 'token', 'secret']) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const result = {...data};
    excludeFields.forEach(field => {
      delete result[field];
    });

    return result;
  }

  /**
   * 格式化时间字段
   * @param {Object} data 数据对象
   * @param {Array} timeFields 时间字段列表
   * @returns {Object}
   */
  formatTimeFields(data, timeFields = ['createdAt', 'updatedAt']) {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const result = {...data};
    timeFields.forEach(field => {
      if (result[field]) {
        result[field] = new Date(result[field]).toISOString();
      }
    });

    return result;
  }
}

// 创建全局实例
const apiResponse = new ApiResponseHandler();

module.exports = apiResponse;
