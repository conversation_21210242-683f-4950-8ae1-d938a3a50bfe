<template>
  <div class="space-y-1">
    <label 
      v-if="label" 
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {{ label }}
      <span v-if="required" class="text-error-500 ml-1">*</span>
    </label>
    
    <div class="grid grid-cols-2 gap-2">
      <!-- 开始日期 -->
      <div class="relative">
        <input
          type="date"
          :value="startDate"
          :placeholder="startPlaceholder"
          :disabled="disabled"
          :readonly="readonly"
          :class="inputClasses"
          @input="handleStartDateChange"
          @blur="handleBlur"
          @focus="handleFocus"
        />
      </div>
      
      <!-- 结束日期 -->
      <div class="relative">
        <input
          type="date"
          :value="endDate"
          :placeholder="endPlaceholder"
          :disabled="disabled"
          :readonly="readonly"
          :class="inputClasses"
          @input="handleEndDateChange"
          @blur="handleBlur"
          @focus="handleFocus"
        />
      </div>
    </div>
    
    <!-- 快捷选择 -->
    <div v-if="shortcuts.length" class="flex flex-wrap gap-2 mt-2">
      <button
        v-for="shortcut in shortcuts"
        :key="shortcut.label"
        type="button"
        :class="shortcutClasses"
        @click="handleShortcutClick(shortcut)"
      >
        {{ shortcut.label }}
      </button>
    </div>
    
    <!-- 帮助文本 -->
    <p 
      v-if="helpText" 
      class="text-sm text-gray-500 dark:text-gray-400"
    >
      {{ helpText }}
    </p>
    
    <!-- 错误信息 -->
    <p 
      v-if="error" 
      class="text-sm text-error-600 dark:text-error-400"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: ''
  },
  startPlaceholder: {
    type: String,
    default: '开始日期'
  },
  endPlaceholder: {
    type: String,
    default: '结束日期'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  format: {
    type: String,
    default: 'YYYY-MM-DD'
  },
  shortcuts: {
    type: Array,
    default: () => [
      {
        label: '今天',
        value: () => [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]
      },
      {
        label: '昨天',
        value: () => [
          dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'day').format('YYYY-MM-DD')
        ]
      },
      {
        label: '最近7天',
        value: () => [
          dayjs().subtract(6, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ]
      },
      {
        label: '最近30天',
        value: () => [
          dayjs().subtract(29, 'day').format('YYYY-MM-DD'),
          dayjs().format('YYYY-MM-DD')
        ]
      },
      {
        label: '本月',
        value: () => [
          dayjs().startOf('month').format('YYYY-MM-DD'),
          dayjs().endOf('month').format('YYYY-MM-DD')
        ]
      },
      {
        label: '上月',
        value: () => [
          dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')
        ]
      }
    ]
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus', 'change'])

const inputClasses = computed(() => {
  const baseClasses = [
    'block w-full rounded-lg border-0 shadow-sm ring-1 ring-inset transition-colors duration-200',
    'placeholder:text-gray-400 focus:ring-2 focus:ring-inset',
    'disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200',
    'dark:bg-gray-900 dark:text-white dark:ring-gray-700 dark:placeholder:text-gray-500',
    'dark:focus:ring-primary-500 dark:disabled:bg-gray-800 dark:disabled:text-gray-400'
  ]

  // 尺寸样式
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  // 状态样式
  const stateClasses = [
    'bg-white ring-gray-300 focus:ring-primary-600',
    props.error ? 'ring-error-300 focus:ring-error-600' : ''
  ]

  return [
    ...baseClasses,
    sizeClasses[props.size],
    ...stateClasses
  ].filter(Boolean).join(' ')
})

const shortcutClasses = computed(() => {
  return [
    'px-3 py-1 text-xs rounded-md border border-gray-300 dark:border-gray-600',
    'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300',
    'hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200',
    'focus:outline-none focus:ring-2 focus:ring-primary-500'
  ].join(' ')
})

const startDate = computed(() => {
  return props.modelValue[0] || ''
})

const endDate = computed(() => {
  return props.modelValue[1] || ''
})

const handleStartDateChange = (event) => {
  const value = event.target.value
  const newValue = [value, endDate.value].filter(Boolean)
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const handleEndDateChange = (event) => {
  const value = event.target.value
  const newValue = [startDate.value, value].filter(Boolean)
  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const handleShortcutClick = (shortcut) => {
  const value = shortcut.value()
  emit('update:modelValue', value)
  emit('change', value)
}

const handleBlur = (event) => {
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}
</script>
