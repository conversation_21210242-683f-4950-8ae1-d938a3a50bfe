import{a0 as e}from"./index-XtNpSMFt.js";const d={getOrders:t=>e.get("/orders",t),getTodayOrders:()=>e.get("/orders/today"),getOrderDetail:t=>e.get(`/orders/${t}`),updateOrderStatus:(t,r)=>e.put(`/orders/${t}`,{status:r}),deleteOrder:t=>e.delete(`/orders/${t}`),getOrderStatistics:t=>e.get("/orders/statistics",t),batchUpdateStatus:t=>e.post("/orders/batch-status",t),exportOrders:t=>e.get("/orders/export",t),getOrderAnalytics:t=>e.get("/orders/analytics",t)};export{d as o};
