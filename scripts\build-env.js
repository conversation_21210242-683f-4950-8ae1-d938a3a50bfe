#!/usr/bin/env node

/**
 * 环境构建脚本
 * 根据NODE_ENV读取对应的环境配置文件，并生成运行时配置
 */

const fs = require('fs');
const path = require('path');

// 获取当前环境
const NODE_ENV = process.env.NODE_ENV || 'development';

console.log(`🔧 正在构建环境: ${NODE_ENV}`);

// 读取环境配置文件
function loadEnvConfig(env) {
  const envFile = path.join(process.cwd(), `.env.${env}`);

  if (!fs.existsSync(envFile)) {
    console.warn(`⚠️  环境配置文件不存在: .env.${env}`);
    return {};
  }

  const envContent = fs.readFileSync(envFile, 'utf8');
  const config = {};

  // 解析环境变量
  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        config[key] = value;
      }
    }
  });

  return config;
}

// 生成环境配置文件
function generateEnvConfig(config) {
  const envConfigTemplate = `/**
 * 自动生成的环境配置文件
 * 请勿手动修改此文件
 * 生成时间: ${new Date().toLocaleString()}
 * 环境: ${NODE_ENV}
 */

// 当前环境变量
const ENV_VARS = ${JSON.stringify(config, null, 2)};

// 环境配置映射
const ENV_CONFIG = {
  development: {
    baseURL: ENV_VARS.API_BASE_URL || 'http://localhost:3000/api',
    timeout: parseInt(ENV_VARS.API_TIMEOUT) || 10000,
    wechatAppId: ENV_VARS.WECHAT_APP_ID || 'wx82283b353918af82',
    debug: ENV_VARS.DEBUG === 'true',
    enableMock: ENV_VARS.ENABLE_MOCK === 'true',
    logLevel: ENV_VARS.LOG_LEVEL || 'debug',
    enableCache: ENV_VARS.ENABLE_CACHE === 'true',
    cacheTimeout: parseInt(ENV_VARS.CACHE_TIMEOUT) || 300000
  },
  test: {
    baseURL: ENV_VARS.API_BASE_URL || 'http://*************:3000/api',
    timeout: parseInt(ENV_VARS.API_TIMEOUT) || 12000,
    wechatAppId: ENV_VARS.WECHAT_APP_ID || 'wx82283b353918af82',
    debug: ENV_VARS.DEBUG === 'true',
    enableMock: ENV_VARS.ENABLE_MOCK === 'true',
    logLevel: ENV_VARS.LOG_LEVEL || 'info',
    enableCache: ENV_VARS.ENABLE_CACHE === 'true',
    cacheTimeout: parseInt(ENV_VARS.CACHE_TIMEOUT) || 300000
  },
  production: {
    baseURL: ENV_VARS.API_BASE_URL || 'http://*************:3001/api',
    timeout: parseInt(ENV_VARS.API_TIMEOUT) || 15000,
    wechatAppId: ENV_VARS.WECHAT_APP_ID || 'wx82283b353918af82',
    debug: ENV_VARS.DEBUG === 'true',
    enableMock: ENV_VARS.ENABLE_MOCK === 'true',
    logLevel: ENV_VARS.LOG_LEVEL || 'error',
    enableCache: ENV_VARS.ENABLE_CACHE === 'true',
    cacheTimeout: parseInt(ENV_VARS.CACHE_TIMEOUT) || 600000
  }
};

// 当前环境
const CURRENT_ENV = '${NODE_ENV}';

// 导出当前环境配置
module.exports = {
  currentEnv: CURRENT_ENV,
  envDescription: ENV_VARS.ENV_DESCRIPTION || '${NODE_ENV}环境',
  ...ENV_CONFIG[CURRENT_ENV],
  
  // 保持向后兼容
  ENV_CONFIG,
  ENV_VARS
};
`;

  return envConfigTemplate;
}

try {
  // 加载环境配置
  const envConfig = loadEnvConfig(NODE_ENV);

  // 生成配置文件内容
  const configContent = generateEnvConfig(envConfig);

  // 写入配置文件
  const outputPath = path.join(process.cwd(), 'config', 'env.generated.js');
  fs.writeFileSync(outputPath, configContent, 'utf8');

  console.log(`✅ 环境配置已生成: ${outputPath}`);
  console.log(`📝 环境描述: ${envConfig.ENV_DESCRIPTION || NODE_ENV + '环境'}`);
  console.log(`🔗 API地址: ${envConfig.API_BASE_URL}`);
} catch (error) {
  console.error('❌ 构建环境配置失败:', error.message);
  process.exit(1);
}
