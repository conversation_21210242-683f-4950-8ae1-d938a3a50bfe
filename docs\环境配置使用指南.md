# 环境配置使用指南

## 概述

本项目支持通过 npm scripts 来切换不同的环境配置，实现开发、测试、生产环境的快速切换。

## 可用命令

### 开发环境
```bash
pnpm dev
```
- 使用本地开发服务器 (localhost:3000)
- 启用调试模式
- 详细日志输出

### 测试环境
```bash
pnpm test
```
- 使用线上测试服务器和测试数据库
- 启用调试模式
- 适合功能测试

### 生产环境构建
```bash
pnpm build
```
- 使用生产服务器和正式数据库
- 关闭调试模式
- 优化性能配置

### 测试环境构建
```bash
pnpm build:test
```
- 构建测试环境版本
- 用于测试环境部署

## 环境配置文件

### .env.development (开发环境)
- API地址: http://localhost:3000/api
- 调试模式: 开启
- 日志级别: debug

### .env.test (测试环境)
- API地址: http://*************:3000/api (测试数据库)
- 调试模式: 开启
- 日志级别: info

### .env.production (生产环境)
- API地址: http://*************:3001/api (正式数据库)
- 调试模式: 关闭
- 日志级别: error

## 工作原理

1. 运行命令时，`cross-env` 设置 `NODE_ENV` 环境变量
2. `build-env.js` 脚本读取对应的 `.env.*` 文件
3. 生成 `config/env.generated.js` 运行时配置文件
4. 小程序运行时加载生成的配置

## 自定义配置

如需修改环境配置，请编辑对应的 `.env.*` 文件：

```bash
# .env.development
NODE_ENV=development
API_BASE_URL=http://localhost:3000/api
DEBUG=true
# ... 其他配置
```

## 注意事项

1. `config/env.generated.js` 是自动生成的文件，请勿手动修改
2. 环境配置文件 `.env.*` 已加入版本控制，团队成员共享
3. 如需本地特殊配置，可创建 `.env.local` 文件（已忽略版本控制）
4. 构建前会自动清理和重新生成 npm 包

## 故障排除

### 命令执行失败
1. 确保已安装依赖: `pnpm install`
2. 检查 Node.js 版本是否兼容
3. 查看错误日志定位问题

### 环境配置不生效
1. 检查 `.env.*` 文件格式是否正确
2. 确认 `config/env.generated.js` 是否生成
3. 重新运行构建命令

### API 连接问题
1. 确认服务器地址是否正确
2. 检查网络连接
3. 验证服务器是否正常运行
