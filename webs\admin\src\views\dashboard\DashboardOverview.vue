<template>
  <PageContainer
    title="仪表板"
    subtitle="系统概览和关键指标"
    :show-header="true"
    background="gray"
  >
    <!-- 统计卡片网格 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="总用户数"
        :value="stats.totalUsers"
        :icon="UsersIcon"
        color="blue"
        :change="stats.userGrowth"
        description="较上月增长"
        clickable
        @click="navigateToUsers"
      />
      
      <StatsCard
        title="今日订单"
        :value="stats.todayOrders"
        :icon="ShoppingCartIcon"
        color="green"
        :change="stats.orderGrowth"
        description="较昨日"
        clickable
        @click="navigateToOrders"
      />
      
      <StatsCard
        title="菜品总数"
        :value="stats.totalDishes"
        :icon="CakeIcon"
        color="purple"
        :change="stats.dishGrowth"
        description="本月新增"
        clickable
        @click="navigateToDishes"
      />
      
      <StatsCard
        title="活跃用户"
        :value="stats.activeUsers"
        :icon="UserCheckIcon"
        color="yellow"
        :change="stats.activeGrowth"
        description="本周活跃"
      />
    </div>

    <!-- 图表和数据展示区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- 用户增长趋势图 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">用户增长趋势</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">最近30天用户注册情况</p>
        </div>
        <div class="card-body">
          <div ref="userChartRef" class="h-64"></div>
        </div>
      </div>

      <!-- 订单统计图 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">订单统计</h3>
          <p class="text-sm text-gray-500 dark:text-gray-400">最近7天订单数量</p>
        </div>
        <div class="card-body">
          <div ref="orderChartRef" class="h-64"></div>
        </div>
      </div>
    </div>

    <!-- 最新活动和快捷操作 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 最新用户 -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">最新用户</h3>
            <BaseButton
              variant="ghost"
              size="sm"
              @click="navigateToUsers"
            >
              查看全部
            </BaseButton>
          </div>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <div
              v-for="user in recentUsers"
              :key="user.id"
              class="flex items-center space-x-3"
            >
              <img
                v-if="user.avatar"
                :src="user.avatar"
                :alt="user.name"
                class="h-8 w-8 rounded-full object-cover"
              />
              <div
                v-else
                class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"
              >
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ user.name?.charAt(0)?.toUpperCase() }}
                </span>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ user.name }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {{ user.phone }}
                </p>
              </div>
              <div class="text-sm text-gray-500 dark:text-gray-400">
                {{ formatRelativeTime(user.createdAt) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 热门菜品 -->
      <div class="card">
        <div class="card-header">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">热门菜品</h3>
            <BaseButton
              variant="ghost"
              size="sm"
              @click="navigateToDishes"
            >
              查看全部
            </BaseButton>
          </div>
        </div>
        <div class="card-body">
          <div class="space-y-4">
            <div
              v-for="dish in popularDishes"
              :key="dish.id"
              class="flex items-center space-x-3"
            >
              <img
                :src="dish.image"
                :alt="dish.name"
                class="h-10 w-10 rounded-lg object-cover"
              />
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ dish.name }}
                </p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ dish.category }}
                </p>
              </div>
              <div class="text-sm font-medium text-primary-600 dark:text-primary-400">
                {{ dish.orderCount }}次
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快捷操作 -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">快捷操作</h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-2 gap-3">
            <BaseButton
              variant="outline"
              size="sm"
              block
              @click="navigateToUserCreate"
            >
              <UserPlusIcon class="h-4 w-4 mr-2" />
              新增用户
            </BaseButton>
            
            <BaseButton
              variant="outline"
              size="sm"
              block
              @click="navigateToDishCreate"
            >
              <PlusIcon class="h-4 w-4 mr-2" />
              新增菜品
            </BaseButton>
            
            <BaseButton
              variant="outline"
              size="sm"
              block
              @click="navigateToMenuCreate"
            >
              <DocumentPlusIcon class="h-4 w-4 mr-2" />
              创建菜单
            </BaseButton>
            
            <BaseButton
              variant="outline"
              size="sm"
              block
              @click="navigateToSettings"
            >
              <CogIcon class="h-4 w-4 mr-2" />
              系统设置
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
  </PageContainer>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import {
  UsersIcon,
  ShoppingCartIcon,
  CakeIcon,
  UserCheckIcon,
  UserPlusIcon,
  PlusIcon,
  DocumentPlusIcon,
  CogIcon
} from '@heroicons/vue/24/outline'
import * as echarts from 'echarts'
import { dashboardApi } from '@/api/dashboard'
import { formatRelativeTime } from '@/utils/date'

const router = useRouter()

// 图表引用
const userChartRef = ref(null)
const orderChartRef = ref(null)

// 响应式数据
const stats = reactive({
  totalUsers: 0,
  todayOrders: 0,
  totalDishes: 0,
  activeUsers: 0,
  userGrowth: 0,
  orderGrowth: 0,
  dishGrowth: 0,
  activeGrowth: 0
})

const recentUsers = ref([])
const popularDishes = ref([])

// 方法
const fetchDashboardData = async () => {
  try {
    const response = await dashboardApi.getOverview()
    const data = response.data
    
    // 更新统计数据
    Object.assign(stats, data.stats)
    recentUsers.value = data.recentUsers
    popularDishes.value = data.popularDishes
    
    // 更新图表
    await nextTick()
    initCharts(data.charts)
  } catch (error) {
    console.error('获取仪表板数据失败:', error)
  }
}

const initCharts = (chartData) => {
  // 初始化用户增长趋势图
  if (userChartRef.value) {
    const userChart = echarts.init(userChartRef.value)
    const userOption = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: chartData.userGrowth.dates
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: chartData.userGrowth.values,
        type: 'line',
        smooth: true,
        itemStyle: {
          color: '#3b82f6'
        }
      }]
    }
    userChart.setOption(userOption)
  }
  
  // 初始化订单统计图
  if (orderChartRef.value) {
    const orderChart = echarts.init(orderChartRef.value)
    const orderOption = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: chartData.orderStats.dates
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: chartData.orderStats.values,
        type: 'bar',
        itemStyle: {
          color: '#10b981'
        }
      }]
    }
    orderChart.setOption(orderOption)
  }
}

// 导航方法
const navigateToUsers = () => {
  router.push('/user/list')
}

const navigateToOrders = () => {
  router.push('/order/list')
}

const navigateToDishes = () => {
  router.push('/menu/dishes')
}

const navigateToUserCreate = () => {
  router.push('/user/create')
}

const navigateToDishCreate = () => {
  router.push('/menu/dish/create')
}

const navigateToMenuCreate = () => {
  router.push('/menu/create')
}

const navigateToSettings = () => {
  router.push('/system/settings')
}

// 生命周期
onMounted(() => {
  fetchDashboardData()
})
</script>
