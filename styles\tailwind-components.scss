/* Tailwind CSS 风格组件库 - 小程序专用 */

// 设计系统变量
$primary: #3b82f6;
$primary-light: #60a5fa;
$primary-dark: #2563eb;
$secondary: #ec4899;
$secondary-light: #f472b6;
$secondary-dark: #db2777;
$success: #10b981;
$warning: #f59e0b;
$error: #ef4444;
$info: #06b6d4;

// 灰度色阶
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// 间距系统
$space-1: 8rpx;
$space-2: 12rpx;
$space-3: 16rpx;
$space-4: 24rpx;
$space-5: 32rpx;
$space-6: 48rpx;
$space-8: 64rpx;

// 圆角系统
$rounded-sm: 8rpx;
$rounded: 12rpx;
$rounded-md: 16rpx;
$rounded-lg: 20rpx;
$rounded-xl: 24rpx;
$rounded-2xl: 32rpx;
$rounded-full: 9999rpx;

// 阴影系统
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-lg: 0 16rpx 48rpx rgba(0, 0, 0, 0.16);
$shadow-xl: 0 24rpx 64rpx rgba(0, 0, 0, 0.2);

// 卡片组件
.card {
  background: white;
  border-radius: $rounded-xl;
  box-shadow: $shadow;
  padding: $space-5;
  margin-bottom: $space-4;
  border: 2rpx solid $gray-100;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: $shadow-md;
    transform: translateY(-2rpx);
  }

  &.card-primary {
    border-color: $primary;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 6rpx;
      background: linear-gradient(90deg, $primary, $secondary);
      border-radius: $rounded-xl $rounded-xl 0 0;
    }
  }

  &.card-elevated {
    box-shadow: $shadow-lg;
  }

  &.card-flat {
    box-shadow: none;
    border: 2rpx solid $gray-200;
  }
}

// 按钮组件
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $space-3 $space-5;
  border-radius: $rounded;
  font-size: 28rpx;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  box-sizing: border-box;
  min-height: 80rpx;

  &:active {
    transform: scale(0.98);
  }

  // 主要按钮
  &.btn-primary {
    background: $primary;
    color: white;
    box-shadow: $shadow-sm;

    &:active {
      background: $primary-dark;
    }

    &:disabled {
      background: $gray-300;
      color: $gray-500;
      cursor: not-allowed;
    }
  }

  // 次要按钮
  &.btn-secondary {
    background: $gray-100;
    color: $gray-700;

    &:active {
      background: $gray-200;
    }
  }

  // 成功按钮
  &.btn-success {
    background: $success;
    color: white;

    &:active {
      background: darken($success, 10%);
    }
  }

  // 危险按钮
  &.btn-danger {
    background: $error;
    color: white;

    &:active {
      background: darken($error, 10%);
    }
  }

  // 渐变按钮
  &.btn-gradient {
    background: linear-gradient(135deg, $primary, $secondary);
    color: white;
    box-shadow: $shadow;

    &:active {
      box-shadow: $shadow-sm;
    }
  }

  // 轮廓按钮
  &.btn-outline {
    background: transparent;
    border: 2rpx solid $primary;
    color: $primary;

    &:active {
      background: $primary;
      color: white;
    }
  }

  // 文字按钮
  &.btn-text {
    background: transparent;
    color: $primary;
    box-shadow: none;

    &:active {
      background: rgba($primary, 0.1);
    }
  }

  // 尺寸变体
  &.btn-sm {
    padding: $space-2 $space-3;
    font-size: 24rpx;
    min-height: 64rpx;
  }

  &.btn-lg {
    padding: $space-4 $space-6;
    font-size: 32rpx;
    min-height: 96rpx;
  }

  &.btn-full {
    width: 100%;
  }

  &.btn-round {
    border-radius: $rounded-full;
  }
}

// 输入框组件
.input-group {
  margin-bottom: $space-4;

  .input-label {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: $gray-700;
    margin-bottom: $space-2;
  }

  .input {
    width: 100%;
    padding: $space-3 $space-4;
    border: 2rpx solid $gray-200;
    border-radius: $rounded;
    font-size: 28rpx;
    background: white;
    color: $gray-900;
    transition: all 0.2s ease;
    box-sizing: border-box;
    min-height: 80rpx;

    &:focus {
      border-color: $primary;
      outline: none;
      box-shadow: 0 0 0 6rpx rgba($primary, 0.1);
    }

    &::placeholder {
      color: $gray-400;
    }

    &.input-error {
      border-color: $error;

      &:focus {
        box-shadow: 0 0 0 6rpx rgba($error, 0.1);
      }
    }

    &.input-success {
      border-color: $success;

      &:focus {
        box-shadow: 0 0 0 6rpx rgba($success, 0.1);
      }
    }
  }

  .input-error-text {
    color: $error;
    font-size: 24rpx;
    margin-top: $space-1;
  }

  .input-help-text {
    color: $gray-500;
    font-size: 24rpx;
    margin-top: $space-1;
  }
}

// 标签组件
.tag {
  display: inline-flex;
  align-items: center;
  padding: $space-1 $space-3;
  border-radius: $rounded;
  font-size: 24rpx;
  font-weight: 500;

  &.tag-primary {
    background: rgba($primary, 0.1);
    color: $primary;
  }

  &.tag-success {
    background: rgba($success, 0.1);
    color: $success;
  }

  &.tag-warning {
    background: rgba($warning, 0.1);
    color: $warning;
  }

  &.tag-error {
    background: rgba($error, 0.1);
    color: $error;
  }

  &.tag-gray {
    background: $gray-100;
    color: $gray-600;
  }
}

// 徽章组件
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40rpx;
  height: 40rpx;
  padding: 0 $space-2;
  border-radius: $rounded-full;
  font-size: 20rpx;
  font-weight: 600;
  color: white;
  background: $error;

  &.badge-primary {
    background: $primary;
  }

  &.badge-success {
    background: $success;
  }

  &.badge-warning {
    background: $warning;
  }
}

// 分割线组件
.divider {
  height: 2rpx;
  background: $gray-200;
  margin: $space-4 0;

  &.divider-dashed {
    border-top: 2rpx dashed $gray-200;
    background: none;
    height: 0;
  }

  &.divider-text {
    display: flex;
    align-items: center;
    text-align: center;
    color: $gray-500;
    font-size: 24rpx;

    &::before,
    &::after {
      content: "";
      flex: 1;
      height: 2rpx;
      background: $gray-200;
    }

    &::before {
      margin-right: $space-3;
    }

    &::after {
      margin-left: $space-3;
    }
  }
}

// 布局工具类
.container {
  padding: $space-5;
  min-height: 100vh;
  background: $gray-50;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

// 间距工具类
.gap-1 {
  gap: $space-1;
}
.gap-2 {
  gap: $space-2;
}
.gap-3 {
  gap: $space-3;
}
.gap-4 {
  gap: $space-4;
}
.gap-5 {
  gap: $space-5;
}

// 内边距工具类
.p-3 {
  padding: $space-3;
}
.p-4 {
  padding: $space-4;
}
.p-5 {
  padding: $space-5;
}

.px-4 {
  padding-left: $space-4;
  padding-right: $space-4;
}
.py-3 {
  padding-top: $space-3;
  padding-bottom: $space-3;
}

// 外边距工具类
.mb-2 {
  margin-bottom: $space-2;
}
.mb-3 {
  margin-bottom: $space-3;
}
.mb-4 {
  margin-bottom: $space-4;
}
.mb-5 {
  margin-bottom: $space-5;
}

// 文字工具类
.text-sm {
  font-size: 24rpx;
}
.text-base {
  font-size: 28rpx;
}
.text-lg {
  font-size: 32rpx;
}
.text-xl {
  font-size: 36rpx;
}

.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.font-bold {
  font-weight: 700;
}

.text-center {
  text-align: center;
}

.text-gray-500 {
  color: $gray-500;
}
.text-gray-600 {
  color: $gray-600;
}
.text-gray-700 {
  color: $gray-700;
}
.text-gray-900 {
  color: $gray-900;
}
.text-primary {
  color: $primary;
}
.text-white {
  color: white;
}

// 背景工具类
.bg-white {
  background-color: white;
}
.bg-gray-50 {
  background-color: $gray-50;
}
.bg-primary {
  background-color: $primary;
}

// 圆角工具类
.rounded {
  border-radius: $rounded;
}
.rounded-lg {
  border-radius: $rounded-lg;
}
.rounded-xl {
  border-radius: $rounded-xl;
}
.rounded-full {
  border-radius: $rounded-full;
}

// 阴影工具类
.shadow {
  box-shadow: $shadow;
}
.shadow-md {
  box-shadow: $shadow-md;
}
.shadow-lg {
  box-shadow: $shadow-lg;
}

// 宽度工具类
.w-full {
  width: 100%;
}

// 显示工具类
.block {
  display: block;
}
.hidden {
  display: none;
}

// 动画工具类
.transition {
  transition: all 0.2s ease;
}
