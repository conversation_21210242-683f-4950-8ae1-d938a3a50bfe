<view class="order-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <van-loading type="spinner" size="24px" />
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 订单详情内容 -->
  <view wx:else class="order-detail-content">
    <!-- 订单头部信息 -->
    <view class="order-header">
      <view class="order-title">
        <text class="order-id">订单 #{{order.id.slice(-8)}}</text>
        <van-tag color="{{getStatusColor(order.status)}}" size="medium">
          {{getStatusText(order.status)}}
        </van-tag>
      </view>

      <!-- 推送信息 -->
      <view wx:if="{{order.isPushedToMe}}" class="push-info">
        <van-icon name="share-o" size="18px" color="#1989fa" />
        <text class="push-text">{{order.pushedBy.name}} 推送给您</text>
        <button class="contact-btn" bind:tap="contactPusher">联系</button>
      </view>
    </view>

    <!-- 时间信息卡片 -->
    <view class="time-card">
      <view class="time-item">
        <van-icon name="clock-o" size="16px" color="#666" />
        <view class="time-info">
          <text class="time-label">创建时间</text>
          <text class="time-value">{{order.createdAtFormatted}}</text>
        </view>
      </view>

      <view wx:if="{{order.diningTimeFormatted}}" class="time-item">
        <van-icon name="calendar-o" size="16px" color="#666" />
        <view class="time-info">
          <text class="time-label">用餐时间</text>
          <text class="time-value">{{order.diningTimeFormatted}}</text>
        </view>
      </view>

      <view class="time-item">
        <van-icon name="edit" size="16px" color="#666" />
        <view class="time-info">
          <text class="time-label">更新时间</text>
          <text class="time-value">{{order.updatedAtFormatted}}</text>
        </view>
      </view>
    </view>

    <!-- 菜单信息 -->
    <view wx:if="{{order.menu}}" class="menu-card">
      <view class="card-header">
        <van-icon name="orders-o" size="18px" color="#1989fa" />
        <text class="card-title">菜单信息</text>
      </view>

      <view class="menu-info">
        <text class="menu-title">{{order.menu.remark || '菜单'}}</text>
        <text wx:if="{{order.menuDeleted}}" class="menu-deleted"
          >（原菜单已删除）</text
        >
        <text class="menu-date">菜单日期：{{order.menu.dateFormatted}}</text>
        <text class="menu-creator">创建者：{{order.menu.creator.name}}</text>
      </view>
    </view>

    <!-- 订单项目 -->
    <view class="items-card">
      <view class="card-header">
        <van-icon name="shopping-cart-o" size="18px" color="#1989fa" />
        <text class="card-title">订单项目</text>
      </view>

      <view class="order-items">
        <view
          wx:for="{{order.items}}"
          wx:for-item="item"
          wx:key="dishId"
          class="order-item"
        >
          <view class="item-info">
            <text class="item-name">{{item.name}}</text>
            <text
              wx:if="{{item.description}}"
              class="item-desc"
              >{{item.description}}</text
            >
          </view>
          <text class="item-count">x{{item.count}}</text>
        </view>
      </view>
    </view>

    <!-- 订单备注 -->
    <view wx:if="{{order.remark}}" class="remark-card">
      <view class="card-header">
        <van-icon name="chat-o" size="18px" color="#1989fa" />
        <text class="card-title">订单备注</text>
      </view>
      <text class="remark-text">{{order.remark}}</text>
    </view>

    <!-- 用户信息 -->
    <view class="user-card">
      <view class="card-header">
        <van-icon name="user-o" size="18px" color="#1989fa" />
        <text class="card-title">订单用户</text>
      </view>

      <view class="user-info">
        <image
          class="user-avatar"
          src="{{order.user.avatar || '/assets/image/default-avatar.png'}}"
          mode="aspectFill"
        />
        <view class="user-details">
          <text class="user-name">{{order.user.name}}</text>
          <text
            wx:if="{{order.user.phone}}"
            class="user-phone"
            >{{order.user.phone}}</text
          >
        </view>
      </view>
    </view>
  </view>
</view>
