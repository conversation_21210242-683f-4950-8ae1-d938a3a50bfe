import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useSettingStore = defineStore('settings', () => {
  // 状态
  const title = ref('楠楠家厨管理系统');
  const fixedHeader = ref(true);
  const hideTabs = ref(false);
  const sidebarLogo = ref(true);
  const showFooter = ref(false);
  const showGreyMode = ref(false);
  const showColorWeakness = ref(false);

  // 设置标题
  const setTitle = (newTitle) => {
    title.value = newTitle;
  };

  // 设置固定头部
  const setFixedHeader = (fixed) => {
    fixedHeader.value = fixed;
  };

  // 设置隐藏标签页
  const setHideTabs = (hide) => {
    hideTabs.value = hide;
  };

  // 设置侧边栏Logo
  const setSidebarLogo = (show) => {
    sidebarLogo.value = show;
  };

  return {
    title,
    fixedHeader,
    hideTabs,
    sidebarLogo,
    showFooter,
    showGreyMode,
    showColorWeakness,
    setTitle,
    setFixedHeader,
    setHideTabs,
    setSidebarLogo
  };
});

// 导出hook函数以保持兼容性
export const useSettingStoreHook = () => {
  return useSettingStore();
};
