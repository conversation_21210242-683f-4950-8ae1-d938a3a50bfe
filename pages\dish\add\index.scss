/* 新增菜品页面 - 小程序兼容设计 */
@import "../../../styles/miniprogram-design.scss";

/* 颜色变量 */
$green-600: #059669;
$orange-600: #ea580c;
$blue-600: #2563eb;
$blue-500: #3b82f6;
$blue-50: #eff6ff;
$red-600: #dc2626;
$gray-50: #f9fafb;
$gray-200: #e5e7eb;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-900: #111827;

.container {
  @include page-container;
  @include page-container-safe;

  /* 隐藏滚动条 */
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* 页面标题 */
.card-header {
  @include flex;
  @include items-center;
  @include gap-2;
  @include mb-4;
  @include text-xl;
  @include font-bold;
  @include text-primary;
  padding: 24rpx 0;
}

/* 主卡片 */
.card {
  @include modern-card;
  @include card-primary;
  width: 100%;
}

/* 输入组样式 */
.input-group {
  @include mb-4;
  width: 100%;
}

.input-label {
  @include text-base;
  @include text-primary;
  @include mb-2;
  @include flex;
  @include items-center;
  font-weight: 500;
}

.input {
  @include modern-input;
  color: $gray-900;
  border: 2rpx solid $primary-solid;

  &:focus {
    border-color: $primary-solid;
    box-shadow: 0 0 0 4rpx rgba($primary-solid, 0.1);
  }
}

/* 表单容器样式 */
.input-container {
  @include mb-4;
  width: 100%;
}

.input-warm {
  @include modern-input;
  color: $gray-900;
  border: 2rpx solid $primary-solid;

  &:focus {
    border-color: $primary-solid;
    box-shadow: 0 0 0 4rpx rgba($primary-solid, 0.1);
  }
}

.textarea-style {
  min-height: 120rpx;
  line-height: 1.5;
  padding: 20rpx 24rpx;
}

.placeholder-style {
  color: $gray-400;
  @include text-sm;
}

/* 图片上传样式 */
.image-container {
  position: relative;
  margin-top: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.dish-image {
  width: 100%;
  height: 400rpx;
  object-fit: cover;
  display: block;
}

.image-actions {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  gap: 12rpx;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(8rpx);
  transition: all 0.3s ease;
}

.action-btn:active {
  transform: scale(0.9);
}

.delete-btn {
  background: rgba(239, 68, 68, 0.8);
}

/* 选择器样式 */
.picker-label {
  @include text-base;
  @include text-primary;
  @include mb-2;
  @include flex;
  @include items-center;
  font-weight: 500;
}

.icon-margin {
  margin-right: 12rpx;
}

/* 自定义选择器样式 */
.custom-picker {
  @include modern-card;
  border: 2rpx solid $primary-solid;
  @include rounded-sm;
  padding: 0 24rpx;
  height: 80rpx;
  box-sizing: border-box;
  @include flex;
  @include items-center;
  @include justify-between;
  cursor: pointer;
  @include transition;

  &:active {
    transform: scale(0.98);
    border-color: $blue-600;
  }
}

.picker-text {
  color: $gray-900;
  @include text-lg;
  @include flex-1;
  font-size: 28rpx;
}

.arrow-icon {
  color: $gray-400;
  font-size: 24rpx !important;
}

/* 标签样式 */
.tags-container {
  @include flex;
  @include flex-wrap;
  @include gap-2;
  margin-top: 16rpx;
}

.tag-item {
  padding: 18rpx 28rpx;
  border-radius: 32rpx;
  border: 2rpx solid;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  min-width: 120rpx;
  text-align: center;
  display: inline-block;
  overflow: hidden;

  &.selected {
    transform: scale(1.05);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
    font-weight: 600;
  }

  &:active {
    transform: scale(0.95);
  }
}

.tag-text {
  position: relative;
  z-index: 2;
  transition: color 0.3s ease;
}

.tag-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.tag-check {
  position: absolute;
  top: 4rpx;
  right: 8rpx;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  z-index: 3;
}

/* 开关样式 */
.switch-container {
  @include flex;
  @include items-center;
  @include gap-3;
  margin-top: 16rpx;
}

/* 控制switch大小 */
switch {
  transform: scale(0.8);
  transform-origin: left center;
}

.switch-text {
  @include text-sm;
  color: $gray-600;
  font-weight: 500;
}

/* 按钮样式 */
.btn {
  @include modern-btn;
  @include rounded-sm;
  padding: 16rpx 24rpx;
  @include text-sm;
  @include font-medium;
  @include flex;
  @include items-center;
  @include gap-2;
  @include transition;

  &.btn-secondary {
    @include btn-secondary;
  }
}

/* 提交按钮 */
.submit-btn {
  @include modern-btn;
  @include btn-primary;
  @include text-white;
  border: none;
  @include rounded-sm;
  padding: 20rpx 0;
  @include text-base;
  @include font-semibold;
  width: 100%;
  margin-top: 24rpx;
  @include shadow-sm;
  @include text-center;
  @include transition;
  @include flex;
  @include items-center;
  @include justify-center;
  @include gap-2;
  box-sizing: border-box;

  &:active {
    transform: scale(0.98);
  }

  &.loading {
    opacity: 0.7;
    pointer-events: none;

    .van-icon {
      animation: spin 1s linear infinite;
    }
  }
}

/* 工具类 */
.ml-1 {
  margin-left: 8rpx;
}

.ml-2 {
  margin-left: 16rpx;
}

.mr-3 {
  margin-right: 24rpx;
}

.text-sm {
  @include text-sm;
}

.text-red-500 {
  color: #ef4444;
}

.flex {
  @include flex;
}

.items-center {
  @include items-center;
}

/* 分类选择弹窗样式 - 底部弹窗 */
.category-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  @include flex;
  @include items-end;
  @include justify-center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.show {
    opacity: 1;
    visibility: visible;

    .modal-content {
      transform: translateY(0);
    }
  }
}

.modal-content {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 70vh;
  overflow: hidden;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.15);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  // 适配刘海屏安全区域
  padding-bottom: env(safe-area-inset-bottom);
}

.modal-header {
  position: relative;
  padding: 32rpx 32rpx 24rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;

  // 拖拽指示器
  &::before {
    content: "";
    position: absolute;
    top: 12rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 60rpx;
    height: 6rpx;
    background: #e2e8f0;
    border-radius: 3rpx;
  }
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1a202c;
  text-align: center;
}

.close-icon {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  padding: 8rpx;
  cursor: pointer;
  border-radius: 50%;
  background: #f7fafc;
  color: #718096;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
    background: #edf2f7;
  }
}

.category-grid {
  padding: 24rpx 24rpx 40rpx;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.category-item {
  @include flex;
  @include flex-col;
  @include items-center;
  @include gap-2;
  padding: 20rpx 12rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  background: white;

  &:active {
    transform: scale(0.95);
  }

  &.selected {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
    box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.3);
  }
}

.category-icon {
  font-size: 40rpx;
  line-height: 1;
  transition: transform 0.3s ease;

  .category-item.selected & {
    transform: scale(1.1);
  }
}

.category-label {
  font-size: 24rpx;
  font-weight: 500;
  color: #4a5568;
  text-align: center;
  transition: color 0.3s ease;

  .category-item.selected & {
    color: white;
    font-weight: 600;
  }
}

.selected-mark {
  position: absolute;
  top: 6rpx;
  right: 6rpx;
  width: 24rpx;
  height: 24rpx;
  @include flex;
  @include items-center;
  @include justify-center;
  background: #22c55e;
  border-radius: 50%;
  color: white;
  font-size: 14rpx;
  font-weight: bold;
  opacity: 1;
  transform: scale(1);
  transition: all 0.3s ease;

  &::after {
    content: "✓";
    font-size: 12rpx;
    color: white;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.dish-image-container {
  width: 100% !important;
  height: 350rpx !important;
}