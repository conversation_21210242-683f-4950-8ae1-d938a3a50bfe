<template>
  <div class="login-container">
    <div class="login-background">
      <div class="bg-shape shape-1"></div>
      <div class="bg-shape shape-2"></div>
      <div class="bg-shape shape-3"></div>
    </div>

    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <el-icon size="48" color="#409eff">
            <House />
          </el-icon>
        </div>
        <h1>楠楠家厨管理系统</h1>
        <p>欢迎登录后台管理系统</p>
      </div>

      <!-- 账号密码登录表单 -->
      <!-- 注意：微信扫码登录暂时禁用，待后端API实现后再启用 -->
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名/手机号"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <el-button type="text" @click="handleForgetPassword"
              >忘记密码？</el-button
            >
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-btn"
          >
            <el-icon v-if="!loading"><User /></el-icon>
            {{ loading ? "登录中..." : "登录" }}
          </el-button>
        </el-form-item>

        <!-- 微信登录提示 -->
        <el-form-item>
          <div class="wechat-login-notice">
            <el-alert
              title="微信扫码登录功能开发中"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                微信扫码登录功能正在完善中，请暂时使用账号密码登录
              </template>
            </el-alert>
          </div>
        </el-form-item>

        <!-- 登录提示 -->
        <el-form-item>
          <div class="login-tips">
            <el-text type="info" size="small">
              <el-icon><InfoFilled /></el-icon>
              支持用户名、手机号、邮箱登录
            </el-text>
          </div>
        </el-form-item>

        <el-form-item>
          <div class="register-link">
            <span>还没有账户？</span>
            <el-button type="text" @click="handleRegister">
              立即注册
            </el-button>
          </div>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <p>&copy; 2024 楠楠家厨管理系统. All rights reserved.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, nextTick } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import { User, Lock, House, InfoFilled } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/user";
// import WechatQRLogin from "@/components/login/WechatQRLogin.vue"; // 暂时禁用

const router = useRouter();
const userStore = useUserStore();

const loginFormRef = ref();
const loading = ref(false);
const rememberMe = ref(false);

const loginForm = reactive({
  username: "",
  password: ""
});

// 验证规则
const validateUsername = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入用户名/手机号/邮箱'));
    return;
  }

  // 手机号格式验证
  const phoneRegex = /^1[3-9]\d{9}$/;
  // 邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  // 用户名格式验证（3-20位字母数字下划线）
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;

  if (phoneRegex.test(value) || emailRegex.test(value) || usernameRegex.test(value)) {
    callback();
  } else {
    callback(new Error('请输入正确的用户名、手机号或邮箱'));
  }
};

const validatePassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入密码'));
    return;
  }
  if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'));
    return;
  }
  if (value.length > 20) {
    callback(new Error('密码长度不能超过20位'));
    return;
  }
  callback();
};

const loginRules = {
  username: [
    { validator: validateUsername, trigger: "blur" }
  ],
  password: [
    { validator: validatePassword, trigger: "blur" }
  ]
};

// 登录处理
const handleLogin = async () => {
  if (!loginFormRef.value) return;

  try {
    const valid = await loginFormRef.value.validate();
    if (!valid) return;

    loading.value = true;

    // 显示登录提示
    ElMessage.info("正在验证登录信息...");

    const result = await userStore.login({
      username: loginForm.username.trim(),
      password: loginForm.password,
      loginType: "password"
    });

    if (result.success) {
      ElMessage.success("登录成功！正在跳转...");

      // 记住用户名
      if (rememberMe.value) {
        localStorage.setItem("remembered_username", loginForm.username.trim());
        localStorage.setItem("remember_me", "true");
      } else {
        localStorage.removeItem("remembered_username");
        localStorage.removeItem("remember_me");
      }

      // 等待状态更新后跳转
      await nextTick();

      // 检查是否有重定向地址
      const redirectUrl = sessionStorage.getItem('login_redirect');
      const targetUrl = redirectUrl || "/";

      // 清除重定向地址
      if (redirectUrl) {
        sessionStorage.removeItem('login_redirect');
      }

      // 跳转到目标页面
      setTimeout(() => {
        router.push(targetUrl);
      }, 500);
    } else {
      // 根据错误类型显示不同的错误信息
      const errorMessage = getLoginErrorMessage(result.message, result.code);
      ElMessage.error(errorMessage);
    }
  } catch (error) {
    console.error("登录错误:", error);

    // 根据错误类型显示不同的错误信息
    let errorMessage = "登录失败，请重试";
    if (error.response) {
      const status = error.response.status;
      if (status === 401) {
        errorMessage = "用户名或密码错误";
      } else if (status === 403) {
        errorMessage = "账户已被禁用，请联系管理员";
      } else if (status === 429) {
        errorMessage = "登录尝试次数过多，请稍后再试";
      } else if (status >= 500) {
        errorMessage = "服务器错误，请稍后再试";
      }
    } else if (error.code === 'NETWORK_ERROR') {
      errorMessage = "网络连接失败，请检查网络设置";
    }

    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 获取登录错误信息
const getLoginErrorMessage = (message, code) => {
  const errorMessages = {
    'INVALID_CREDENTIALS': '用户名或密码错误',
    'ACCOUNT_DISABLED': '账户已被禁用，请联系管理员',
    'ACCOUNT_LOCKED': '账户已被锁定，请稍后再试',
    'TOO_MANY_ATTEMPTS': '登录尝试次数过多，请稍后再试',
    'USER_NOT_FOUND': '用户不存在',
    'PASSWORD_EXPIRED': '密码已过期，请重置密码'
  };

  return errorMessages[code] || message || '登录失败，请重试';
};

// 忘记密码
const handleForgetPassword = () => {
  router.push('/forgot-password');
};

// 注册
const handleRegister = () => {
  router.push('/register');
};

// 微信登录相关函数暂时禁用
// TODO: 待后端微信扫码API实现后再启用
/*
const handleWechatLoginSuccess = async (data) => {
  try {
    const { userInfo, token, loginType } = data;
    await userStore.login({
      token,
      userInfo: { ...userInfo, loginType }
    });
    ElMessage.success("微信登录成功！");
    router.push("/");
  } catch (error) {
    console.error("微信登录处理失败:", error);
    ElMessage.error("登录失败，请重试");
  }
};

const handleWechatLoginError = (error) => {
  console.error("微信登录失败:", error);
  ElMessage.error(error.message || "微信登录失败，请重试");
};
*/



// 初始化
onMounted(() => {
  // 检查是否已登录
  if (userStore.isLoggedIn) {
    ElMessage.info("您已登录，正在跳转...");
    router.push("/");
    return;
  }

  // 恢复记住的用户名和记住我状态
  const rememberedUsername = localStorage.getItem("remembered_username");
  const rememberMeStatus = localStorage.getItem("remember_me");

  if (rememberedUsername && rememberMeStatus === "true") {
    loginForm.username = rememberedUsername;
    rememberMe.value = true;
  }

  // 自动聚焦到用户名输入框
  nextTick(() => {
    if (loginFormRef.value) {
      const usernameInput = loginFormRef.value.$el.querySelector('input[placeholder*="用户名"]');
      if (usernameInput && !loginForm.username) {
        usernameInput.focus();
      } else if (loginForm.username) {
        // 如果已有用户名，聚焦到密码框
        const passwordInput = loginFormRef.value.$el.querySelector('input[type="password"]');
        if (passwordInput) {
          passwordInput.focus();
        }
      }
    }
  });

  // 检查URL参数，支持重定向
  const urlParams = new URLSearchParams(window.location.search);
  const redirect = urlParams.get('redirect');
  if (redirect) {
    sessionStorage.setItem('login_redirect', redirect);
  }
});
</script>

<style scoped lang="scss">
.login-container {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}

.bg-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;

  &.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  &.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-box {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 420px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.05)
    );
    border-radius: 20px;
    z-index: -1;
  }
}

.login-header {
  text-align: center;
  margin-bottom: 30px;

  .logo {
    margin-bottom: 16px;
  }

  h1 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  p {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 20px;
  }

  :deep(.el-input__wrapper) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.is-focus {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
    }
  }
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;

  :deep(.el-button--text) {
    color: #409eff;
    font-size: 14px;

    &:hover {
      color: #66b1ff;
    }
  }
}

.register-link {
  text-align: center;
  color: #666;
  font-size: 14px;

  span {
    margin-right: 8px;
  }

  :deep(.el-button--text) {
    color: #409eff;
    padding: 0;
    font-size: 14px;

    &:hover {
      color: #66b1ff;
    }
  }
}

.login-btn {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border: none;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #66b1ff, #409eff);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.login-tips {
  margin-top: 30px;

  .divider-text {
    color: #999;
    font-size: 12px;
    padding: 0 16px;
    background: rgba(255, 255, 255, 0.95);
  }

  .test-accounts {
    margin-top: 16px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .account-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    background: rgba(64, 158, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(64, 158, 255, 0.1);
      transform: translateX(4px);
    }

    span {
      font-size: 12px;
      color: #666;
    }
  }
}

.login-footer {
  text-align: center;
  margin-top: 30px;

  p {
    color: #999;
    font-size: 12px;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    padding: 20px;
  }

  .login-box {
    padding: 30px 20px;
    max-width: 100%;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .bg-shape {
    &.shape-1 {
      width: 150px;
      height: 150px;
    }

    &.shape-2 {
      width: 100px;
      height: 100px;
    }

    &.shape-3 {
      width: 80px;
      height: 80px;
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .login-box {
    background: rgba(30, 30, 30, 0.95);

    .login-header {
      h1 {
        color: #fff;
      }

      p {
        color: #ccc;
      }
    }

    .divider-text {
      color: #ccc;
      background: rgba(30, 30, 30, 0.95);
    }

    .account-item {
      background: rgba(64, 158, 255, 0.1);

      span {
        color: #ccc;
      }
    }

    .login-footer p {
      color: #ccc;
    }
  }
}

// 微信登录提示样式
.wechat-login-notice {
  margin: 16px 0;

  :deep(.el-alert) {
    border-radius: 8px;

    .el-alert__content {
      font-size: 13px;
    }
  }
}

// 登录提示样式
.login-tips {
  text-align: center;
  margin: 8px 0;

  .el-text {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
  }
}
</style>
