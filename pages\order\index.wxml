<!-- 🔥 优化：固定容器布局，左右独立滚动 -->
<view class="order-container">
  <view class="order-layout">
    <!-- 🔥 优化：左侧分类导航 - 去掉图标，文字上下排列 -->
    <view class="category-sidebar">
      <scroll-view
        class="category-scroll"
        scroll-y="true"
        enhanced="true"
        show-scrollbar="false"
        scroll-with-animation="true"
      >
        <view
          wx:for="{{categories}}"
          wx:key="type"
          class="category-item {{currentType === item.type ? 'active' : ''}}"
          data-type="{{item.type}}"
          bindtap="switchCategory"
        >
          <text class="category-name">{{item.name}}</text>
        </view>
      </scroll-view>
    </view>

    <!-- 🔥 重新设计：右侧菜品区域 - 去掉顶部标题，优化布局 -->
    <view class="dishes-main">
      <!-- 菜品滚动区域 -->
      <scroll-view
        class="dishes-scroll"
        scroll-y="true"
        enhanced="true"
        show-scrollbar="false"
      >
        <!-- 参照我的菜品页面：菜品列表 -->
        <view class="dishes-list " wx:if="{{foodList.length > 0}}">
          <view
            wx:for="{{foodList}}"
            wx:key="id"
            class="dish-card {{item.isAdding ? 'adding' : ''}}"
            data-id="{{item.id}}"
            bindtap="goToDetail"
          >
            <!-- 卡片头部：左边图片，右边文字 -->
            <view class="dish-header-layout">
              <!-- 左侧图片 -->
              <view class="dish-image-container">
                <safe-image
                  src="{{item.img}}"
                  imageType="dish"
                  custom-class="dish-image-container"
                  image-class="dish-image"
                  mode="aspectFill"
                  lazyLoad="{{true}}"
                  previewable="{{true}}"
                  width="{{400}}"
                  height="{{300}}"
                  quality="{{80}}"
                  progressive="{{true}}"
                />
                <!-- 添加状态标签 -->
                <view
                  class="status-badge {{item.isAdding ? 'added' : 'available'}}"
                  wx:if="{{item.isAdding}}"
                >
                  已添加
                </view>
              </view>

              <!-- 右侧信息 -->
              <view class="dish-info">
                <view class="dish-title-row">
                  <text class="dish-name">{{item.name}}</text>
                  <text class="dish-category">{{item.createdDate}}</text>
                </view>

                <!-- 标签区域（替代备注位置） -->
                <view
                  class="dish-tags-inline"
                  wx:if="{{item.tags && item.tags.length > 0}}"
                >
                  <view
                    class="tag-item-inline"
                    >{{item.category.name || '未分类'}}</view
                  >
                  <view
                    wx:for="{{item.tags}}"
                    wx:for-item="tag"
                    wx:key="*this"
                    class="tag-item-inline"
                  >
                    {{tag}}
                  </view>
                </view>
              </view>
            </view>
            <!-- 重新设计的添加按钮 -->
            <view class="dish-add-section">
              <view
                class="add-to-cart-btn {{item.isAdding ? 'adding' : ''}}"
                catchtap="addToBasket"
                data-id="{{item.id}}"
                data-name="{{item.name}}"
                data-remark="{{item.remark}}"
                data-img="{{item.img}}"
                data-index="{{index}}"
              >
                <view class="btn-content">
                  <van-icon
                    name="{{item.isAdding ? 'success' : 'shopping-cart-o'}}"
                    size="36rpx"
                    color="#fff"
                  />
                  <text
                    class="btn-text"
                    >{{item.isAdding ? '已添加' : '加入购物车'}}</text
                  >
                </view>
                <view class="btn-ripple" wx:if="{{item.isAdding}}"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 🔥 优化：空状态 -->
        <view class="empty-state" wx:else>
          <view class="empty-icon">🍽️</view>
          <text class="empty-title">暂无菜品</text>
          <text class="empty-desc">试试其他分类吧</text>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 🔥 修复：购物篮悬浮按钮 - 始终显示，调整主题色 -->
  <view
    class="shopping-cart-fab {{basketCount > 0 ? 'has-items' : ''}}"
    bindtap="goToBasket"
  >
    <van-icon name="shopping-cart-o" size="24px" color="#fff" />
    <view class="cart-badge" wx:if="{{basketCount > 0}}">{{basketCount}}</view>
  </view>
</view>
