var V=(F,o,m)=>new Promise((t,R)=>{var z=l=>{try{h(m.next(l))}catch(C){R(C)}},g=l=>{try{h(m.throw(l))}catch(C){R(C)}},h=l=>l.done?t(l.value):Promise.resolve(l.value).then(z,g);h((m=m.apply(F,o)).next())});import{_ as de,r as f,c as O,b as v,d as r,a,w as n,g as y,h as A,C as G,D as J,a3 as K,W as Q,o as Y,n as Z,s as $,E as M,M as ue,R as he,l as D,H as ee,I as te,p as pe,m as x,y as ge,t as S}from"./index-Cy8N1eGd.js";import{S as me,i as B,e as _e}from"./StatsCard-SUxJzo4F.js";import{C as fe}from"./CustomTable-DWghEWMD.js";import{d as ve}from"./menu-BDmPj0yF.js";import{g as ye,b as Ce}from"./common-CIDIMsc8.js";const be={__name:"dishes",setup(F,{expose:o}){o();const m=y(),t=y(),R=y(),z=y(),g=y("7d"),h=y(!1),l=y([]),C=y([]);let i=null,c=null,d=null,u=null;const _=A({page:1,size:10,total:0}),e=A({name:"",category:""}),b=A([{title:"菜品总数",value:156,icon:G,type:"primary",trend:"+8",description:"较上月"},{title:"本周销量",value:1240,icon:J,type:"success",trend:"+15%",description:"较上周"},{title:"平均评分",value:4.6,icon:K,type:"warning",trend:"+0.2",description:"较上月"},{title:"总收入",value:"¥28,650",icon:Q,type:"info",trend:"+12%",description:"较上月"}]),ae=[{prop:"image",label:"图片",width:80,slot:!0},{prop:"name",label:"菜品名称",minWidth:120},{prop:"category",label:"分类",width:100,slot:!0},{prop:"price",label:"价格",width:80,formatter:s=>`¥${s.price}`},{prop:"sales",label:"销量",width:100,slot:!0},{prop:"revenue",label:"收入",width:120,slot:!0},{prop:"rating",label:"评分",width:150,slot:!0}],se=[{prop:"name",label:"菜品名称",type:"input"},{prop:"category",label:"分类",type:"select",options:[{label:"热菜",value:"hot"},{label:"凉菜",value:"cold"},{label:"汤品",value:"soup"},{label:"主食",value:"staple"},{label:"甜品",value:"dessert"}]}],T=()=>V(this,null,function*(){h.value=!0;try{l.value=H(),_.total=50}catch(s){console.error("加载表格数据失败:",s),M.error("加载数据失败")}finally{h.value=!1}}),L=()=>V(this,null,function*(){try{C.value=[{id:1,name:"红烧肉",category:"hot",image:"https://picsum.photos/60/60?random=1",sales:156,revenue:4368},{id:2,name:"宫保鸡丁",category:"hot",image:"https://picsum.photos/60/60?random=2",sales:142,revenue:3692},{id:3,name:"清炒时蔬",category:"cold",image:"https://picsum.photos/60/60?random=3",sales:128,revenue:2048},{id:4,name:"紫菜蛋花汤",category:"soup",image:"https://picsum.photos/60/60?random=4",sales:98,revenue:1470},{id:5,name:"米饭",category:"staple",image:"https://picsum.photos/60/60?random=5",sales:89,revenue:267}]}catch(s){console.error("加载热门菜品失败:",s)}}),H=()=>{const s=[],w=[{name:"红烧肉",category:"hot",price:28},{name:"宫保鸡丁",category:"hot",price:26},{name:"清炒时蔬",category:"cold",price:16},{name:"紫菜蛋花汤",category:"soup",price:15},{name:"米饭",category:"staple",price:3}];for(let p=0;p<10;p++){const k=w[p%w.length],E=Math.floor(Math.random()*100)+50;s.push({id:p+1,name:k.name,category:k.category,price:k.price,image:`https://picsum.photos/60/60?random=${p+1}`,sales:E,revenue:(E*k.price).toFixed(2),rating:(Math.random()*2+3).toFixed(1)})}return s},N=()=>{m.value&&(i=B(m.value),U())},U=()=>{if(!i)return;const p={tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"value"},yAxis:{type:"category",data:["红烧肉","宫保鸡丁","清炒时蔬","紫菜蛋花汤","米饭"]},series:[{name:"销量",type:"bar",data:[156,142,128,98,89],itemStyle:{color:{type:"linear",x:0,y:0,x2:1,y2:0,colorStops:[{offset:0,color:"#409EFF"},{offset:1,color:"#67C23A"}]}}}]};i.setOption(p)},j=()=>{t.value&&(c=B(t.value),W())},W=()=>{if(!c)return;const s=["红烧肉","宫保鸡丁","清炒时蔬","紫菜蛋花汤","米饭"],w=[4368,3692,2048,1470,267],p={tooltip:{trigger:"item",formatter:"{a} <br/>{b}: ¥{c} ({d}%)"},series:[{name:"收入",type:"pie",radius:["40%","70%"],data:s.map((k,E)=>({value:w[E],name:k})),emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};c.setOption(p)},I=()=>{if(!R.value)return;d=B(R.value);const w={tooltip:{trigger:"item"},series:[{name:"分类销量",type:"pie",radius:"60%",data:[{value:35,name:"热菜"},{value:25,name:"凉菜"},{value:20,name:"汤品"},{value:15,name:"主食"},{value:5,name:"甜品"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};d.setOption(w)},X=()=>{if(!z.value)return;u=B(z.value);const p={tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["5星","4星","3星","2星","1星"]},yAxis:{type:"value"},series:[{name:"评分分布",type:"bar",data:[45,35,15,3,2],itemStyle:{color:"#E6A23C"}}]};u.setOption(p)},oe=()=>{L(),M.success("热门菜品数据已刷新")},re=s=>{Object.assign(e,s),_.page=1,T()},ne=()=>{Object.keys(e).forEach(s=>{e[s]=""}),_.page=1,T()},le=s=>{_.page=s,T()},ie=s=>{_.size=s,_.page=1,T()},ce=()=>{M.info("导出功能开发中...")},P=()=>{i&&i.resize(),c&&c.resize(),d&&d.resize(),u&&u.resize()};Y(()=>V(this,null,function*(){yield T(),yield L(),Z(()=>{N(),j(),I(),X()}),window.addEventListener("resize",P)})),$(()=>{window.removeEventListener("resize",P),i&&(i.dispose(),i=null),c&&(c.dispose(),c=null),d&&(d.dispose(),d=null),u&&(u.dispose(),u=null)});const q={salesChartRef:m,revenueChartRef:t,categoryChartRef:R,ratingChartRef:z,salesPeriod:g,tableLoading:h,tableData:l,hotDishes:C,get salesChart(){return i},set salesChart(s){i=s},get revenueChart(){return c},set revenueChart(s){c=s},get categoryChart(){return d},set categoryChart(s){d=s},get ratingChart(){return u},set ratingChart(s){u=s},pagination:_,searchParams:e,dishStats:b,columns:ae,searchFields:se,loadTableData:T,loadHotDishes:L,generateMockTableData:H,initSalesChart:N,updateSalesChart:U,initRevenueChart:j,refreshRevenueChart:W,initCategoryChart:I,initRatingChart:X,refreshHotDishes:oe,handleSearch:re,handleReset:ne,handleCurrentChange:le,handleSizeChange:ie,handleExport:ce,handleResize:P,ref:y,reactive:A,onMounted:Y,nextTick:Z,onUnmounted:$,get echarts(){return _e},get ElMessage(){return M},get Refresh(){return he},get Download(){return ue},get Bowl(){return G},get TrendCharts(){return J},get Star(){return K},get Wallet(){return Q},StatsCard:me,CustomTable:fe,get dishApi(){return ve},get getCategoryText(){return Ce},get getCategoryType(){return ye}};return Object.defineProperty(q,"__isScriptSetup",{enumerable:!1,value:!0}),q}},we={class:"dish-analytics"},xe={class:"chart-card"},Se={class:"chart-header"},Re={class:"chart-controls"},ze={ref:"salesChartRef",class:"chart-container"},Te={class:"chart-card"},ke={class:"chart-header"},De={ref:"revenueChartRef",class:"chart-container"},Ee={class:"chart-card"},Ve={ref:"categoryChartRef",class:"chart-container"},Oe={class:"chart-card"},Ae={ref:"ratingChartRef",class:"chart-container"},Me={class:"data-card"},Be={class:"card-header"},Fe={class:"hot-dishes-list"},Le={class:"dish-image"},Pe={class:"dish-info"},He={class:"dish-name"},Ne={class:"dish-category"},Ue={class:"dish-stats"},je={class:"sales"},We={class:"revenue"},Ie={class:"table-section"},Xe={class:"revenue-text"};function qe(F,o,m,t,R,z){const g=f("el-col"),h=f("el-row"),l=f("el-radio-button"),C=f("el-radio-group"),i=f("el-icon"),c=f("el-button"),d=f("el-image"),u=f("el-tag"),_=f("el-rate");return D(),O("div",we,[v(" 菜品统计卡片 "),r(h,{gutter:20,class:"stats-row"},{default:n(()=>[(D(!0),O(ee,null,te(t.dishStats,(e,b)=>(D(),pe(g,{xs:24,sm:12,md:6,key:b},{default:n(()=>[r(t.StatsCard,{title:e.title,value:e.value,icon:e.icon,type:e.type,trend:e.trend,description:e.description,animated:!0},null,8,["title","value","icon","type","trend","description"])]),_:2},1024))),128))]),_:1}),v(" 图表区域 "),r(h,{gutter:20,class:"charts-section"},{default:n(()=>[v(" 菜品销量排行 "),r(g,{xs:24,lg:12},{default:n(()=>[a("div",xe,[a("div",Se,[o[4]||(o[4]=a("h3",null,"菜品销量排行",-1)),a("div",Re,[r(C,{modelValue:t.salesPeriod,"onUpdate:modelValue":o[0]||(o[0]=e=>t.salesPeriod=e),size:"small",onChange:t.updateSalesChart},{default:n(()=>[r(l,{label:"7d"},{default:n(()=>o[1]||(o[1]=[x("近7天",-1)])),_:1,__:[1]}),r(l,{label:"30d"},{default:n(()=>o[2]||(o[2]=[x("近30天",-1)])),_:1,__:[2]}),r(l,{label:"90d"},{default:n(()=>o[3]||(o[3]=[x("近90天",-1)])),_:1,__:[3]})]),_:1},8,["modelValue"])])]),a("div",ze,null,512)])]),_:1}),v(" 菜品收入分析 "),r(g,{xs:24,lg:12},{default:n(()=>[a("div",Te,[a("div",ke,[o[6]||(o[6]=a("h3",null,"菜品收入分析",-1)),r(c,{size:"small",onClick:t.refreshRevenueChart},{default:n(()=>[r(i,null,{default:n(()=>[r(t.Refresh)]),_:1}),o[5]||(o[5]=x(" 刷新 ",-1))]),_:1,__:[5]})]),a("div",De,null,512)])]),_:1})]),_:1}),v(" 分类分析和趋势 "),r(h,{gutter:20,class:"analysis-section"},{default:n(()=>[v(" 分类销量分布 "),r(g,{xs:24,lg:8},{default:n(()=>[a("div",Ee,[o[7]||(o[7]=a("div",{class:"chart-header"},[a("h3",null,"分类销量分布")],-1)),a("div",Ve,null,512)])]),_:1}),v(" 菜品评分分析 "),r(g,{xs:24,lg:8},{default:n(()=>[a("div",Oe,[o[8]||(o[8]=a("div",{class:"chart-header"},[a("h3",null,"菜品评分分析")],-1)),a("div",Ae,null,512)])]),_:1}),v(" 热门菜品列表 "),r(g,{xs:24,lg:8},{default:n(()=>[a("div",Me,[a("div",Be,[o[10]||(o[10]=a("h3",null,"本周热门菜品",-1)),r(c,{size:"small",onClick:t.refreshHotDishes},{default:n(()=>[r(i,null,{default:n(()=>[r(t.Refresh)]),_:1}),o[9]||(o[9]=x(" 刷新 ",-1))]),_:1,__:[9]})]),a("div",Fe,[(D(!0),O(ee,null,te(t.hotDishes,(e,b)=>(D(),O("div",{key:e.id,class:"dish-item"},[a("div",{class:ge(["dish-rank",`rank-${b+1}`])},S(b+1),3),a("div",Le,[r(d,{src:e.image,fit:"cover"},null,8,["src"])]),a("div",Pe,[a("h4",He,S(e.name),1),a("p",Ne,S(t.getCategoryText(e.category)),1),a("div",Ue,[a("span",je,"销量: "+S(e.sales),1),a("span",We,"收入: ¥"+S(e.revenue),1)])])]))),128))])])]),_:1})]),_:1}),v(" 详细数据表格 "),a("div",Ie,[r(t.CustomTable,{title:"菜品销售详情",data:t.tableData,columns:t.columns,loading:t.tableLoading,pagination:t.pagination,"show-search":!0,"search-fields":t.searchFields,onSearch:t.handleSearch,onReset:t.handleReset,onCurrentChange:t.handleCurrentChange,onSizeChange:t.handleSizeChange},{actions:n(()=>[r(c,{onClick:t.handleExport},{default:n(()=>[r(i,null,{default:n(()=>[r(t.Download)]),_:1}),o[11]||(o[11]=x(" 导出报表 ",-1))]),_:1,__:[11]})]),image:n(({row:e})=>[r(d,{src:e.image,"preview-src-list":[e.image],class:"dish-image",fit:"cover"},null,8,["src","preview-src-list"])]),category:n(({row:e})=>[r(u,{type:t.getCategoryType(e.category),size:"small"},{default:n(()=>[x(S(t.getCategoryText(e.category)),1)]),_:2},1032,["type"])]),sales:n(({row:e})=>[r(u,{type:"primary"},{default:n(()=>[x(S(e.sales)+"份",1)]),_:2},1024)]),revenue:n(({row:e})=>[a("span",Xe,"¥"+S(e.revenue),1)]),rating:n(({row:e})=>[r(_,{modelValue:e.rating,"onUpdate:modelValue":b=>e.rating=b,disabled:"","show-score":"","text-color":"#ff9900","score-template":"{value}"},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["data","loading","pagination"])])])}const $e=de(be,[["render",qe],["__scopeId","data-v-f273e1c1"],["__file","E:/wx-nan/webs/admin/src/views/analytics/dishes.vue"]]);export{$e as default};
