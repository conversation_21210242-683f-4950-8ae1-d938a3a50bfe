const app = getApp();

Page({
  data: {
    notifications: [],
    filteredNotifications: [],
    activeFilter: 'all', // all, unread, read
    totalCount: 0,
    unreadCount: 0,
    readCount: 0,
    loading: false,
    hasMore: true,
    page: 1,
    pageSize: 20,

    // refresh-scroll 组件配置
    scrollHeight: 600,
    requestUrl: '',
    requestParams: {},
    requestHeaders: {},
    emptyText: '暂无通知',
    emptyTip: '下拉刷新试试'
  },

  onLoad() {
    this.initPage();
    this.setupRefreshScroll();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.setupRefreshScroll();
  },

  onReady() {
    // 页面渲染完成后重新计算高度
    this.initPage();
  },

  // 初始化页面
  initPage() {
    // 计算滚动区域高度
    const systemInfo = wx.getSystemInfoSync();
    const windowHeight = systemInfo.windowHeight;
    const statsCardHeight = 80; // 统计卡片高度
    const filterTabsHeight = 50; // 筛选标签高度
    const scrollHeight = windowHeight - statsCardHeight - filterTabsHeight - 20; // 减去一些边距

    this.setData({
      scrollHeight
    });
  },

  // 设置 refresh-scroll 组件配置
  setupRefreshScroll() {
    const envConfig = require('../../config/env');
    const token = wx.getStorageSync('token');

    this.setData({
      requestUrl: `${envConfig.baseURL}/notifications`,
      requestParams: {
        size: this.data.pageSize
      },
      requestHeaders: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
  },

  // 刷新数据
  refreshData() {
    // 触发 refresh-scroll 组件刷新
    const refreshScrollComponent = this.selectComponent('#refresh-scroll');
    if (refreshScrollComponent) {
      refreshScrollComponent.refresh();
    }
  },

  // 格式化时间
  formatTime(date) {
    const now = new Date();
    const diff = now - date;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return '刚刚';
    if (minutes < 60) return `${minutes}分钟前`;
    if (hours < 24) return `${hours}小时前`;
    if (days < 7) return `${days}天前`;

    return date.toLocaleDateString();
  },

  // refresh-scroll 组件数据变化事件
  onDataChange(e) {
    const {list, total, hasMore, isEmpty, isRefresh, originalResponse} =
      e.detail;

    // 处理通知数据
    const formattedNotifications = list.map(item => ({
      ...item,
      timeText: this.formatTime(new Date(item.createdAt))
    }));

    // 如果是刷新，替换所有数据；如果是加载更多，追加数据
    const currentNotifications = isRefresh
      ? formattedNotifications
      : [...this.data.notifications, ...formattedNotifications];

    // 从原始响应中提取 total
    let realTotal = total;
    if (
      originalResponse &&
      originalResponse.data &&
      originalResponse.data.pagination
    ) {
      realTotal = originalResponse.data.pagination.total;
    }

    // 更新数据
    this.setData({
      notifications: currentNotifications,
      totalCount: realTotal || currentNotifications.length,
      unreadCount: currentNotifications.filter(item => !item.read).length,
      readCount: currentNotifications.filter(item => item.read).length,
      hasMore,
      loading: false
    });

    // 应用当前筛选条件
    this.filterNotifications();
  },

  // 设置筛选条件
  setFilter(e) {
    const filter = e.currentTarget.dataset.filter;

    this.setData({activeFilter: filter});
    this.filterNotifications();

    // 更新空状态文本
    this.updateEmptyText(filter);
  },

  // 更新空状态文本
  updateEmptyText(filter) {
    let emptyText = '暂无通知';
    let emptyTip = '下拉刷新试试';

    switch (filter) {
      case 'unread':
        emptyText = '没有未读通知';
        emptyTip = '所有通知都已读完';
        break;
      case 'read':
        emptyText = '没有已读通知';
        emptyTip = '还没有读过任何通知';
        break;
      default:
        emptyText = '还没有收到任何通知';
        emptyTip = '下拉刷新试试';
    }

    this.setData({emptyText, emptyTip});
  },

  // 筛选通知
  filterNotifications() {
    const {notifications, activeFilter} = this.data;
    let filtered = notifications;

    if (activeFilter === 'unread') {
      filtered = notifications.filter(item => !item.read);
    } else if (activeFilter === 'read') {
      filtered = notifications.filter(item => item.read);
    }

    this.setData({filteredNotifications: filtered});
  },

  // 标记为已读
  async markAsRead(e) {
    const id = e.currentTarget.dataset.id;

    try {
      // 先更新本地状态
      const notifications = this.data.notifications.map(item => {
        if (item.id === id && !item.read) {
          return {...item, read: true};
        }
        return item;
      });

      this.setData({
        notifications,
        unreadCount: notifications.filter(item => !item.read).length,
        readCount: notifications.filter(item => item.read).length
      });

      this.filterNotifications();

      // 调用 API 标记为已读
      try {
        const {notificationApi} = require('../../services/api');
        await notificationApi.markAsRead(id);
        console.log(`标记通知 ${id} 为已读成功`);
      } catch (apiError) {
        console.log('API 调用失败:', apiError.message);
      }
    } catch (error) {
      console.error('标记已读失败:', error);
      // 如果 API 调用失败，可以选择回滚本地状态
    }
  },

  // 全部标记为已读
  async markAllRead() {
    if (this.data.unreadCount === 0) {
      wx.showToast({
        title: '没有未读通知',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({title: '处理中...'});

      const notifications = this.data.notifications.map(item => ({
        ...item,
        read: true
      }));

      this.setData({
        notifications,
        unreadCount: 0,
        readCount: notifications.length
      });

      this.filterNotifications();

      wx.showToast({
        title: '已全部标记为已读',
        icon: 'success'
      });

      // 调用 API 全部标记为已读
      try {
        const {notificationApi} = require('../../services/api');
        await notificationApi.markAllRead();
        console.log('全部标记为已读成功');
      } catch (apiError) {
        console.log('API 调用失败:', apiError.message);
      }
    } catch (error) {
      console.error('标记失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 手动刷新数据
  refreshData() {
    const refreshScrollComponent = this.selectComponent('#refresh-scroll');
    if (refreshScrollComponent) {
      refreshScrollComponent.refresh();
    } else {
      this.setupRefreshScroll();
    }
  },

  // 刷新数据（下拉刷新触发）
  onRefresh() {
    this.refreshData();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack();
  }
});
