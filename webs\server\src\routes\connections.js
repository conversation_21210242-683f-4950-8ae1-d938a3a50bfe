const express = require('express');
const router = express.Router();
const {auth} = require('../middlewares/auth');
const {
  getAvailableUsers,
  sendConnectionRequest,
  respondToConnection,
  getMyConnections,
  removeConnection,
  updateConnection,
  getConnectionHistory,
  getConnectionStatistics
} = require('../controllers/connectionController');

// 所有路由都需要认证
router.use(auth);

/**
 * @route GET /api/connections/users
 * @desc 获取可关联的用户列表
 * @access Private
 */
router.get('/users', getAvailableUsers);

/**
 * @route POST /api/connections/request
 * @desc 发送关联申请
 * @access Private
 */
router.post('/request', sendConnectionRequest);

/**
 * @route PUT /api/connections/:id/respond
 * @desc 处理关联申请（同意/拒绝）
 * @access Private
 */
router.put('/:id/respond', respondToConnection);

/**
 * @route GET /api/connections/my
 * @desc 获取我的关联列表
 * @access Private
 */
router.get('/my', getMyConnections);

/**
 * @route PUT /api/connections/:id/update
 * @desc 更新关联备注和分组
 * @access Private
 */
router.put('/:id/update', updateConnection);

/**
 * @route DELETE /api/connections/:id
 * @desc 取消关联关系
 * @access Private
 */
router.delete('/:id', removeConnection);

/**
 * @route GET /api/connections/history
 * @desc 获取关联历史记录
 * @access Private
 */
router.get('/history', getConnectionHistory);

/**
 * @route GET /api/connections/statistics
 * @desc 获取关联统计信息
 * @access Private
 */
router.get('/statistics', getConnectionStatistics);

module.exports = router;
