<view class="container">
  <!-- 顶部标题栏 -->
  <view class="header">
    <view class="header-content">
      <view class="header-title">
        <view class="title-icon">📋</view>
        <view
          class="title-text"
          >{{viewingToday ? '今日菜单' : '历史菜单'}}</view
        >
      </view>
      <view class="header-subtitle">
        {{viewingToday ? '查看今日精选菜品' : '回顾往日美味时光'}}
      </view>
    </view>
  </view>

  <!-- 菜单列表 -->
  <refresh-list
    id="refresh"
    height="{{refreshListHeight}}"
    refresher-enabled="{{true}}"
    show-load-more="{{true}}"
    load-more-status="{{loadMoreStatus}}"
    is-empty="{{historyMenus.length === 0}}"
    loading="{{loading}}"
    empty-text="{{viewingToday ? '今日暂无菜单' : '暂无历史菜单'}}"
    empty-image="/assets/image/empty_cart.svg"
    show-empty-action="{{true}}"
    empty-action-text="刷新数据"
    bind:refresh="onRefresh"
    bind:loadmore="onLoadMore"
    bind:emptyaction="onEmptyAction"
  >
    <view class="menu-list">
      <view
        wx:for="{{historyMenus}}"
        wx:key="id"
        class="menu-card {{item.isToday ? 'today-menu' : ''}}"
      >
        <!-- 卡片头部 -->
        <view class="card-header">
          <view class="date-section">
            <view class="date-icon">📅</view>
            <view class="date-text">{{item.date}}</view>
            <view wx:if="{{item.isToday}}" class="today-badge">今日</view>
          </view>
          <view wx:if="{{item.creator}}" class="creator-info">
            <view class="creator-avatar">👨‍🍳</view>
            <view class="creator-name">{{item.creator.name || '匿名'}}</view>
          </view>
        </view>

        <!-- 菜品摘要 -->
        <view class="summary-section">
          <view class="summary-label">菜品概览</view>
          <view class="summary-text">{{item.summary}}</view>
        </view>

        <!-- 菜品详情 -->
        <view class="dishes-section">
          <view class="dishes-header">
            <view class="dishes-icon">🍽️</view>
            <view class="dishes-title">菜品清单</view>
            <view class="dishes-count">{{item.dishes.length}}道菜</view>
          </view>
          <view class="dishes-grid">
            <view
              wx:for="{{item.dishes}}"
              wx:for-item="dish"
              wx:key="id"
              class="dish-tag"
            >
              <view class="dish-name">{{dish.name}}</view>
              <view class="dish-count">×{{dish.count}}</view>
            </view>
          </view>
        </view>

        <!-- 备注信息 -->
        <view
          wx:if="{{item.remark && item.remark !== '无'}}"
          class="remark-section"
        >
          <view class="remark-icon">💭</view>
          <view class="remark-text">{{item.remark}}</view>
        </view>

        <!-- 统计信息 -->
        <view class="stats-section">
          <view class="stat-item">
            <view class="stat-icon">👥</view>
            <view class="stat-text">{{item.orderCount || 0}}人点餐</view>
          </view>
          <view class="stat-item">
            <view class="stat-icon">⏰</view>
            <view class="stat-text">{{item.formattedTime || '未知时间'}}</view>
          </view>
        </view>
      </view>
    </view>
  </refresh-list>

  <!-- 返回按钮 -->
  <view class="floating-btn" bindtap="goBack">
    <view class="btn-icon">←</view>
    <view class="btn-text">返回</view>
  </view>
</view>
