# 🔧 Nginx配置应用指南

## 📋 配置文件说明

我已经为您生成了完整的Nginx配置文件 `nginx-complete.conf`，包含：

### 🎯 端口配置
- **3001端口**: API服务 + 部署管理
- **8080端口**: 前端管理后台
- **30001端口**: phpMyAdmin（宝塔默认）
- **80端口**: 默认端口（重定向到IP端口）

### 🚀 功能特性
- ✅ 支持IP地址访问（解决域名未备案问题）
- ✅ API反向代理到Node.js服务
- ✅ 前端单页应用支持
- ✅ 静态资源缓存优化
- ✅ 安全头配置
- ✅ 详细的访问日志

## 🔧 应用配置步骤

### 步骤1: 备份当前配置
```bash
# SSH连接服务器
cp /www/server/nginx/conf/nginx.conf /www/server/nginx/conf/nginx.conf.backup
```

### 步骤2: 应用新配置
```bash
# 方式1: 直接替换（推荐）
# 将生成的配置内容复制到宝塔面板
# 软件商店 → Nginx → 配置修改 → 粘贴新配置

# 方式2: 命令行替换
# 将配置文件上传到服务器后执行：
# cp nginx-complete.conf /www/server/nginx/conf/nginx.conf
```

### 步骤3: 测试配置语法
```bash
# 检查配置文件语法
nginx -t

# 如果显示 "syntax is ok" 和 "test is successful" 则配置正确
```

### 步骤4: 重载Nginx配置
```bash
# 重载配置（不中断服务）
nginx -s reload

# 或者在宝塔面板中重启Nginx
```

### 步骤5: 验证端口监听
```bash
# 检查端口是否正常监听
netstat -tlnp | grep nginx

# 应该看到以下端口：
# :80, :3001, :8080, :30001
```

## 🌐 访问地址测试

### API服务测试
```bash
# 健康检查
curl http://*************:3001/api/health

# 如果返回JSON数据，说明API正常
```

### 管理后台测试
```bash
# 浏览器访问
http://*************:8080

# 应该显示管理后台页面（需要先上传前端文件）
```

### 部署管理测试
```bash
# 浏览器访问
http://*************:3001/deploy-trigger.php

# 应该显示部署管理页面
```

## 📁 目录结构要求

确保以下目录和文件存在：

```
/www/wwwroot/www.huanglun.asia/
├── api/                          # 后端代码
│   └── webs/server/
│       ├── src/app.js           # Node.js入口文件
│       ├── deploy-trigger.php   # 部署管理页面
│       └── webhook.php          # Webhook接收
└── admin/                       # 前端代码
    ├── index.html              # 前端入口文件
    ├── static/                 # 静态资源
    └── ...
```

## 🚨 故障排查

### 问题1: 端口无法访问
```bash
# 检查端口是否监听
netstat -tlnp | grep :3001
netstat -tlnp | grep :8080

# 检查防火墙
iptables -L -n | grep 3001
iptables -L -n | grep 8080
```

### 问题2: API代理失败
```bash
# 检查Node.js服务是否运行
pm2 status

# 检查服务监听端口
pm2 logs nannan-api

# 确保服务监听127.0.0.1:3001
```

### 问题3: 前端页面空白
```bash
# 检查前端文件是否存在
ls -la /www/wwwroot/www.huanglun.asia/admin/

# 检查Nginx错误日志
tail -f /www/wwwlogs/nannan-admin-error.log
```

## 🎯 最终访问地址

配置成功后，您可以通过以下地址访问：

- **🏠 管理后台**: http://*************:8080
- **🔌 API接口**: http://*************:3001/api
- **❤️ 健康检查**: http://*************:3001/api/health
- **🚀 部署管理**: http://*************:3001/deploy-trigger.php
- **🗄️ 数据库管理**: 宝塔面板 → 数据库 → phpMyAdmin（更安全）

## 📝 注意事项

1. **确保阿里云安全组已开放端口**: 3001, 8080, 30001
2. **确保宝塔面板防火墙已开放端口**: 3001, 8080
3. **确保PM2服务正常运行**: `pm2 status` 显示 nannan-api 为 online
4. **确保前端文件已上传**: admin目录下有index.html等文件

## 🚀 快速验证清单

- [ ] Nginx配置语法检查通过
- [ ] Nginx重载成功
- [ ] 端口监听正常
- [ ] API健康检查返回正常
- [ ] 前端页面可以访问
- [ ] 部署管理页面可以访问

**按照这个指南操作，您就能通过IP地址正常访问所有功能了！**
