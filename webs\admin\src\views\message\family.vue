<template>
  <div class="family-messages">
    <CustomTable
      title="家庭留言管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出留言
        </el-button>
      </template>
      
      <template #user="{ row }">
        <div class="user-info">
          <el-avatar :src="row.user?.avatar" :size="32">
            {{ row.user?.name?.charAt(0) }}
          </el-avatar>
          <span class="ml-2">{{ row.user?.name }}</span>
        </div>
      </template>
      
      <template #content="{ row }">
        <div class="message-content">
          <p class="content-text">{{ row.content }}</p>
          <div v-if="row.images && row.images.length" class="message-images">
            <el-image
              v-for="(image, index) in row.images.slice(0, 3)"
              :key="index"
              :src="image"
              :preview-src-list="row.images"
              class="message-image"
              fit="cover"
            />
            <span v-if="row.images.length > 3" class="more-images">
              +{{ row.images.length - 3 }}
            </span>
          </div>
        </div>
      </template>
      
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
      
      <template #reply="{ row }">
        <div v-if="row.reply" class="reply-content">
          <p class="reply-text">{{ row.reply }}</p>
          <p class="reply-time">{{ formatTime(row.replyTime) }}</p>
        </div>
        <span v-else class="no-reply">未回复</span>
      </template>
      
      <template #operation="{ row }">
        <el-button size="small" @click="handleViewMessage(row)">查看</el-button>
        <el-button 
          size="small" 
          type="primary" 
          @click="handleReplyMessage(row)"
          v-if="row.status === 'pending'"
        >
          回复
        </el-button>
        <el-button size="small" type="danger" @click="handleDeleteMessage(row)">删除</el-button>
      </template>
    </CustomTable>

    <!-- 回复对话框 -->
    <el-dialog
      v-model="replyDialogVisible"
      title="回复留言"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="reply-dialog">
        <div class="original-message">
          <h4>原留言内容：</h4>
          <p>{{ selectedMessage?.content }}</p>
        </div>
        
        <el-form
          ref="replyFormRef"
          :model="replyForm"
          :rules="replyRules"
          label-width="80px"
        >
          <el-form-item label="回复内容" prop="content">
            <el-input
              v-model="replyForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入回复内容"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="replyDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="replyLoading"
          @click="handleSubmitReply"
        >
          发送回复
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import { messageApi } from '@/api/message'
import { formatTime } from '@/utils/common'
import dayjs from 'dayjs'

const loading = ref(false)
const tableData = ref([])
const replyDialogVisible = ref(false)
const replyLoading = ref(false)
const selectedMessage = ref(null)
const replyFormRef = ref()

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  status: '',
  userName: '',
  dateRange: []
})

const replyForm = reactive({
  content: ''
})

// 表格列配置
const columns = [
  { prop: 'user', label: '用户', width: 150, slot: true },
  { prop: 'content', label: '留言内容', minWidth: 300, slot: true },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { prop: 'reply', label: '回复', minWidth: 200, slot: true },
  { prop: 'createdAt', label: '留言时间', width: 160, formatter: (row) => formatTime(row.createdAt) },
  { label: '操作', width: 180, slot: 'operation', fixed: 'right' }
]

// 搜索字段配置
const searchFields = [
  { prop: 'userName', label: '用户姓名', type: 'input' },
  { prop: 'status', label: '状态', type: 'select', options: [
    { label: '全部', value: '' },
    { label: '待回复', value: 'pending' },
    { label: '已回复', value: 'replied' }
  ]},
  { prop: 'dateRange', label: '留言时间', type: 'daterange' }
]

// 回复表单验证规则
const replyRules = {
  content: [
    { required: true, message: '请输入回复内容', trigger: 'blur' },
    { min: 5, max: 500, message: '回复内容长度在 5 到 500 个字符', trigger: 'blur' }
  ]
}

// 方法
const loadData = async () => {
  loading.value = true
  try {
    // 模拟数据
    tableData.value = generateMockData()
    pagination.total = 30
  } catch (error) {
    console.error('加载留言数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const generateMockData = () => {
  const data = []
  const users = [
    { id: 1, name: '张三', avatar: 'https://picsum.photos/100/100?random=1' },
    { id: 2, name: '李四', avatar: 'https://picsum.photos/100/100?random=2' },
    { id: 3, name: '王五', avatar: 'https://picsum.photos/100/100?random=3' }
  ]
  
  const messages = [
    '今天的菜品很棒，特别是红烧肉！',
    '希望能增加一些素食选择',
    '菜品质量很好，但是分量有点少',
    '服务态度很好，会继续支持的',
    '建议增加一些汤品选择'
  ]
  
  for (let i = 0; i < 10; i++) {
    const hasReply = Math.random() > 0.4
    const hasImages = Math.random() > 0.7
    
    data.push({
      id: i + 1,
      user: users[i % users.length],
      content: messages[i % messages.length],
      images: hasImages ? [
        `https://picsum.photos/200/150?random=${i + 10}`,
        `https://picsum.photos/200/150?random=${i + 20}`
      ] : [],
      status: hasReply ? 'replied' : 'pending',
      reply: hasReply ? '感谢您的反馈，我们会继续努力提供更好的服务！' : null,
      replyTime: hasReply ? dayjs().subtract(Math.floor(Math.random() * 3), 'day').toDate() : null,
      createdAt: dayjs().subtract(Math.floor(Math.random() * 7), 'day').toDate()
    })
  }
  return data
}

const getStatusType = (status) => {
  const statusMap = {
    pending: 'warning',
    replied: 'success'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    pending: '待回复',
    replied: '已回复'
  }
  return statusMap[status] || '未知'
}

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    if (Array.isArray(searchParams[key])) {
      searchParams[key] = []
    } else {
      searchParams[key] = ''
    }
  })
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleViewMessage = (row) => {
  ElMessage.info(`查看留言：${row.content.substring(0, 20)}...`)
}

const handleReplyMessage = (row) => {
  selectedMessage.value = row
  replyForm.content = ''
  replyDialogVisible.value = true
}

const handleSubmitReply = async () => {
  if (!replyFormRef.value) return
  
  try {
    await replyFormRef.value.validate()
    replyLoading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新数据
    selectedMessage.value.status = 'replied'
    selectedMessage.value.reply = replyForm.content
    selectedMessage.value.replyTime = new Date()
    
    ElMessage.success('回复发送成功')
    replyDialogVisible.value = false
  } catch (error) {
    console.error('发送回复失败:', error)
    ElMessage.error('发送回复失败')
  } finally {
    replyLoading.value = false
  }
}

const handleDeleteMessage = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条留言吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('留言删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除留言失败')
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.family-messages {
  @apply p-6 bg-gray-50 min-h-screen;
}

.user-info {
  @apply flex items-center;
}

.message-content {
  .content-text {
    @apply text-sm text-gray-900 mb-2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .message-images {
    @apply flex items-center space-x-2;
    
    .message-image {
      @apply w-12 h-12 rounded border;
    }
    
    .more-images {
      @apply text-xs text-gray-500;
    }
  }
}

.reply-content {
  .reply-text {
    @apply text-sm text-gray-700 mb-1;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .reply-time {
    @apply text-xs text-gray-500;
  }
}

.no-reply {
  @apply text-sm text-gray-400;
}

.reply-dialog {
  .original-message {
    @apply mb-4 p-3 bg-gray-50 rounded;
    
    h4 {
      @apply text-sm font-medium text-gray-900 mb-2;
    }
    
    p {
      @apply text-sm text-gray-700;
    }
  }
}
</style>
