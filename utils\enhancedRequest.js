/**
 * 增强版网络请求工具
 * 集成错误处理、重试机制、loading状态管理
 */

const errorHandler = require('./errorHandler.js');
// 尝试加载UI工具，如果失败则使用兼容版本
let ui;
try {
  ui = require('./ui.js');
} catch (error) {
  ui = require('./ui-compat.js');
}

// 导入统一的环境配置
const {baseURL} = require('../config/env');

// 默认配置
const DEFAULT_CONFIG = {
  timeout: 10000,
  showLoading: true,
  showError: true,
  loadingText: '加载中...',
  retryCount: 0,
  retryDelay: 1000,
  enableCache: false,
  cacheTime: 5 * 60 * 1000 // 5分钟
};

// 请求缓存
const requestCache = new Map();

/**
 * 判断是否应该重试
 * @param {Object} error 错误对象
 * @returns {boolean}
 */
function shouldRetry(error) {
  const retryableCodes = ['NETWORK_ERROR', 'TIMEOUT_ERROR', 500, 502, 503, 504];
  return retryableCodes.includes(error.code);
}

/**
 * 重试请求
 * @param {Object} options 原始请求配置
 * @param {number} retryCount 剩余重试次数
 * @param {number} delay 重试延迟
 * @returns {Promise}
 */
async function retryRequest(options, retryCount, delay) {
  if (retryCount <= 0) {
    throw errorHandler.createError('重试次数已用完', 'RETRY_EXHAUSTED');
  }

  await new Promise(resolve => setTimeout(resolve, delay));

  const newOptions = {
    ...options,
    retryCount: retryCount - 1,
    showLoading: false // 重试时不显示loading
  };

  return enhancedRequest(newOptions);
}

/**
 * 生成缓存key
 * @param {string} url
 * @param {string} method
 * @param {Object} data
 * @returns {string}
 */
function getCacheKey(url, method, data) {
  return `${method}:${url}:${JSON.stringify(data)}`;
}

/**
 * 检查缓存
 * @param {string} cacheKey
 * @returns {Object|null}
 */
function getCache(cacheKey) {
  const cached = requestCache.get(cacheKey);
  if (cached && Date.now() - cached.timestamp < cached.expireTime) {
    return cached.data;
  }
  requestCache.delete(cacheKey);
  return null;
}

/**
 * 设置缓存
 * @param {string} cacheKey
 * @param {Object} data
 * @param {number} cacheTime
 */
function setCache(cacheKey, data, cacheTime) {
  requestCache.set(cacheKey, {
    data,
    timestamp: Date.now(),
    expireTime: cacheTime
  });
}

/**
 * 增强版请求函数
 * @param {Object} options 请求配置
 * @returns {Promise}
 */
function enhancedRequest(options) {
  const config = {...DEFAULT_CONFIG, ...options};

  const {
    url,
    method = 'GET',
    data = {},
    header = {},
    timeout,
    showLoading,
    loadingText,
    enableCache,
    cacheTime
  } = config;

  // 检查缓存（仅GET请求）
  if (enableCache && method === 'GET') {
    const cacheKey = getCacheKey(url, method, data);
    const cached = getCache(cacheKey);
    if (cached) {
      return Promise.resolve(cached);
    }
  }

  return new Promise((resolve, reject) => {
    // 显示loading
    if (showLoading) {
      ui.showLoading(loadingText);
    }

    // 准备请求头
    const token =
      wx.getStorageSync('accessToken') || wx.getStorageSync('token');
    const headers = {
      'Content-Type': 'application/json',
      'ngrok-skip-browser-warning': 'true', // 跳过ngrok警告页面
      ...header
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // 创建请求任务
    const requestTask = wx.request({
      url: `${baseURL}${url}`,
      method,
      data,
      header: headers,
      timeout,
      success: res => {
        if (showLoading) {
          ui.hideLoading();
        }

        // 检查HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 检查业务状态码
          if (
            res.data &&
            res.data.code &&
            res.data.code !== 200 &&
            res.data.code !== 201
          ) {
            const error = errorHandler.createError(
              res.data.message || '业务处理失败',
              res.data.code,
              res.data.data
            );
            reject(error);
          } else {
            // 设置缓存
            if (enableCache && method === 'GET') {
              const cacheKey = getCacheKey(url, method, data);
              setCache(cacheKey, res.data, cacheTime);
            }
            resolve(res.data);
          }
        } else {
          const error = errorHandler.createError(
            res.data?.message || '请求失败',
            res.statusCode,
            res.data
          );
          reject(error);
        }
      },
      fail: err => {
        if (showLoading) {
          ui.hideLoading();
        }

        let error;
        if (err.errMsg?.includes('timeout')) {
          error = errorHandler.createError('请求超时', 'TIMEOUT_ERROR');
        } else if (err.errMsg?.includes('fail')) {
          error = errorHandler.createError('网络连接失败', 'NETWORK_ERROR');
        } else {
          error = errorHandler.createError(
            err.errMsg || '网络错误',
            'NETWORK_ERROR'
          );
        }

        reject(error);
      }
    });

    // 超时处理
    if (timeout && timeout > 0) {
      setTimeout(() => {
        requestTask.abort();
      }, timeout);
    }
  }).catch(async err => {
    // 统一错误处理
    if (config.showError) {
      errorHandler.handleApiError(err, {
        showToast: true,
        onUnauthorized: () => {
          wx.removeStorageSync('token');
          wx.removeStorageSync('accessToken');
          wx.removeStorageSync('userInfo');

          setTimeout(() => {
            wx.reLaunch({
              url: '/pages/login/index'
            });
          }, 1500);
        }
      });
    }

    // 重试机制
    if (config.retryCount > 0 && shouldRetry(err)) {
      return retryRequest(options, config.retryCount, config.retryDelay);
    }

    throw err;
  });
}

/**
 * GET请求
 * @param {string} url
 * @param {Object} data
 * @param {Object} options
 * @returns {Promise}
 */
function get(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'GET',
    data,
    ...options
  });
}

/**
 * POST请求
 * @param {string} url
 * @param {Object} data
 * @param {Object} options
 * @returns {Promise}
 */
function post(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'POST',
    data,
    ...options
  });
}

/**
 * PUT请求
 * @param {string} url
 * @param {Object} data
 * @param {Object} options
 * @returns {Promise}
 */
function put(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'PUT',
    data,
    ...options
  });
}

/**
 * DELETE请求
 * @param {string} url
 * @param {Object} data
 * @param {Object} options
 * @returns {Promise}
 */
function del(url, data = {}, options = {}) {
  return enhancedRequest({
    url,
    method: 'DELETE',
    data,
    ...options
  });
}

/**
 * 清除所有缓存
 */
function clearCache() {
  requestCache.clear();
}

/**
 * 清除指定缓存
 * @param {string} url
 * @param {string} method
 * @param {Object} data
 */
function clearSpecificCache(url, method = 'GET', data = {}) {
  const cacheKey = getCacheKey(url, method, data);
  requestCache.delete(cacheKey);
}

module.exports = {
  request: enhancedRequest,
  get,
  post,
  put,
  del,
  clearCache,
  clearSpecificCache
};
