<template>
  <div class="user-analytics">
    <!-- 用户统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col
        :xs="24"
        :sm="12"
        :md="6"
        v-for="(stat, index) in userStats"
        :key="index"
      >
        <StatsCard
          :title="stat.title"
          :value="stat.value"
          :icon="stat.icon"
          :type="stat.type"
          :trend="stat.trend"
          :description="stat.description"
          :animated="true"
        />
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 用户增长趋势 -->
      <el-col :xs="24" :lg="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长趋势</h3>
            <div class="chart-controls">
              <el-radio-group
                v-model="growthPeriod"
                size="small"
                @change="updateGrowthChart"
              >
                <el-radio-button label="7d">近7天</el-radio-button>
                <el-radio-button label="30d">近30天</el-radio-button>
                <el-radio-button label="90d">近90天</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="growthChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 用户活跃度分析 -->
      <el-col :xs="24" :lg="12">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户活跃度分析</h3>
            <el-button size="small" @click="refreshActivityChart">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div ref="activityChartRef" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 用户行为分析 -->
    <el-row :gutter="20" class="analysis-section">
      <!-- 用户来源分析 -->
      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户来源分析</h3>
          </div>
          <div ref="sourceChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 用户年龄分布 -->
      <el-col :xs="24" :lg="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户年龄分布</h3>
          </div>
          <div ref="ageChartRef" class="chart-container"></div>
        </div>
      </el-col>

      <!-- 活跃用户列表 -->
      <el-col :xs="24" :lg="8">
        <div class="data-card">
          <div class="card-header">
            <h3>本周活跃用户</h3>
            <el-button size="small" @click="refreshActiveUsers">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
          <div class="active-users-list">
            <div
              v-for="(user, index) in activeUsers"
              :key="user.id"
              class="user-item"
            >
              <div class="user-rank">{{ index + 1 }}</div>
              <div class="user-avatar">
                <el-avatar :src="user.avatar" :size="40">
                  {{ user.name ? user.name.charAt(0) : 'U' }}
                </el-avatar>
              </div>
              <div class="user-info">
                <h4 class="user-name">{{ user.name }}</h4>
                <p class="user-phone">{{ user.phone }}</p>
                <div class="user-stats">
                  <span class="orders">订单: {{ user.orderCount }}</span>
                  <span class="amount">消费: ¥{{ user.totalAmount }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <div class="table-section">
      <CustomTable
        title="用户消费详情"
        :data="tableData"
        :columns="columns"
        :loading="tableLoading"
        :pagination="pagination"
        :show-search="true"
        :search-fields="searchFields"
        @search="handleSearch"
        @reset="handleReset"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      >
        <template #actions>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出报表
          </el-button>
        </template>

        <template #user="{ row }">
          <div class="user-info">
            <el-avatar :src="row.avatar" :size="32">
              {{ row.name ? row.name.charAt(0) : 'U' }}
            </el-avatar>
            <div class="user-details">
              <div class="user-name">{{ row.name || '未知用户' }}</div>
              <div class="user-phone">{{ row.phone || '未知手机号' }}</div>
            </div>
          </div>
        </template>

        <template #orderCount="{ row }">
          <el-tag type="primary">{{ row.orderCount }}单</el-tag>
        </template>

        <template #totalAmount="{ row }">
          <span class="amount-text">¥{{ row.totalAmount }}</span>
        </template>

        <template #avgAmount="{ row }">
          <span class="avg-amount">¥{{ row.avgAmount }}</span>
        </template>

        <template #lastOrderTime="{ row }">
          <span>{{ formatTime(row.lastOrderTime) }}</span>
        </template>
      </CustomTable>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { ElMessage } from 'element-plus'
import { Refresh, Download, User, UserFilled, TrendCharts, Timer } from '@element-plus/icons-vue'
import StatsCard from '@/components/StatsCard.vue'
import CustomTable from '@/components/CustomTable.vue'
import { userApi } from '@/api/user'
import { formatTime } from '@/utils/common'
import dayjs from 'dayjs'

const growthChartRef = ref()
const activityChartRef = ref()
const sourceChartRef = ref()
const ageChartRef = ref()
const growthPeriod = ref('7d')
const tableLoading = ref(false)
const tableData = ref([])
const activeUsers = ref([])

let growthChart = null
let activityChart = null
let sourceChart = null
let ageChart = null

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  name: '',
  phone: ''
})

// 用户统计数据
const userStats = reactive([
  {
    title: '总用户数',
    value: 1240,
    icon: User,
    type: 'primary',
    trend: '+15%',
    description: '较上月'
  },
  {
    title: '活跃用户',
    value: 856,
    icon: UserFilled,
    type: 'success',
    trend: '+8%',
    description: '较上月'
  },
  {
    title: '新增用户',
    value: 128,
    icon: TrendCharts,
    type: 'warning',
    trend: '+25%',
    description: '本月'
  },
  {
    title: '平均停留',
    value: '8.5分钟',
    icon: Timer,
    type: 'info',
    trend: '****分钟',
    description: '较上月'
  }
])

// 表格列配置
const columns = [
  { prop: 'user', label: '用户信息', width: 200, slot: true },
  { prop: 'orderCount', label: '订单数量', width: 120, slot: true },
  { prop: 'totalAmount', label: '消费总额', width: 120, slot: true },
  { prop: 'avgAmount', label: '平均消费', width: 120, slot: true },
  { prop: 'lastOrderTime', label: '最后下单', width: 160, slot: true },
  { prop: 'registerTime', label: '注册时间', width: 160, formatter: (row) => formatTime(row.registerTime) }
]

// 搜索字段配置
const searchFields = [
  { prop: 'name', label: '用户姓名', type: 'input' },
  { prop: 'phone', label: '手机号', type: 'input' }
]

// 方法
const loadTableData = async () => {
  tableLoading.value = true
  try {
    // 模拟数据
    tableData.value = generateMockTableData()
    pagination.total = 50
  } catch (error) {
    console.error('加载表格数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    tableLoading.value = false
  }
}

const loadActiveUsers = async () => {
  try {
    // 模拟数据
    activeUsers.value = [
      {
        id: 1,
        name: '张三',
        phone: '138****8001',
        avatar: 'https://picsum.photos/100/100?random=1',
        orderCount: 28,
        totalAmount: 1280
      },
      {
        id: 2,
        name: '李四',
        phone: '138****8002',
        avatar: 'https://picsum.photos/100/100?random=2',
        orderCount: 24,
        totalAmount: 1156
      },
      {
        id: 3,
        name: '王五',
        phone: '138****8003',
        avatar: 'https://picsum.photos/100/100?random=3',
        orderCount: 22,
        totalAmount: 998
      },
      {
        id: 4,
        name: '赵六',
        phone: '138****8004',
        avatar: 'https://picsum.photos/100/100?random=4',
        orderCount: 18,
        totalAmount: 756
      },
      {
        id: 5,
        name: '钱七',
        phone: '138****8005',
        avatar: 'https://picsum.photos/100/100?random=5',
        orderCount: 16,
        totalAmount: 688
      }
    ]
  } catch (error) {
    console.error('加载活跃用户失败:', error)
  }
}

const generateMockTableData = () => {
  const data = []
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']

  for (let i = 0; i < 10; i++) {
    const orderCount = Math.floor(Math.random() * 30) + 5
    const totalAmount = Math.floor(Math.random() * 2000) + 500

    data.push({
      id: i + 1,
      name: names[i % names.length],
      phone: `138****800${i + 1}`,
      avatar: `https://picsum.photos/100/100?random=${i + 1}`,
      orderCount,
      totalAmount: totalAmount.toFixed(2),
      avgAmount: (totalAmount / orderCount).toFixed(2),
      lastOrderTime: dayjs().subtract(Math.floor(Math.random() * 30), 'day').toDate(),
      registerTime: dayjs().subtract(Math.floor(Math.random() * 365), 'day').toDate()
    })
  }
  return data
}

const initGrowthChart = () => {
  if (!growthChartRef.value) return

  growthChart = echarts.init(growthChartRef.value)
  updateGrowthChart()
}

const updateGrowthChart = () => {
  if (!growthChart) return

  const days = growthPeriod.value === '7d' ? 7 : growthPeriod.value === '30d' ? 30 : 90
  const dates = []
  const newUsers = []
  const totalUsers = []

  let total = 1000
  for (let i = days - 1; i >= 0; i--) {
    dates.push(dayjs().subtract(i, 'day').format('MM-DD'))
    const newCount = Math.floor(Math.random() * 20) + 5
    newUsers.push(newCount)
    total += newCount
    totalUsers.push(total)
  }

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增用户', '总用户数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: [
      {
        type: 'value',
        name: '新增用户',
        position: 'left'
      },
      {
        type: 'value',
        name: '总用户数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '新增用户',
        type: 'bar',
        data: newUsers,
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: '总用户数',
        type: 'line',
        yAxisIndex: 1,
        data: totalUsers,
        smooth: true,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  }

  growthChart.setOption(option)
}

const initActivityChart = () => {
  if (!activityChartRef.value) return

  activityChart = echarts.init(activityChartRef.value)
  refreshActivityChart()
}

const refreshActivityChart = () => {
  if (!activityChart) return

  const hours = []
  const activity = []

  for (let i = 0; i < 24; i++) {
    hours.push(i + ':00')
    // 模拟用餐时间活跃度
    let activeCount
    if (i >= 11 && i <= 13) { // 午餐时间
      activeCount = Math.floor(Math.random() * 50) + 80
    } else if (i >= 17 && i <= 19) { // 晚餐时间
      activeCount = Math.floor(Math.random() * 60) + 90
    } else if (i >= 7 && i <= 9) { // 早餐时间
      activeCount = Math.floor(Math.random() * 30) + 40
    } else {
      activeCount = Math.floor(Math.random() * 20) + 10
    }
    activity.push(activeCount)
  }

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: hours
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '活跃用户数',
        type: 'line',
        data: activity,
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        },
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  }

  activityChart.setOption(option)
}

const initSourceChart = () => {
  if (!sourceChartRef.value) return

  sourceChart = echarts.init(sourceChartRef.value)

  const data = [
    { value: 45, name: '微信分享' },
    { value: 25, name: '朋友推荐' },
    { value: 15, name: '搜索引擎' },
    { value: 10, name: '广告投放' },
    { value: 5, name: '其他' }
  ]

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '用户来源',
        type: 'pie',
        radius: '60%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }

  sourceChart.setOption(option)
}

const initAgeChart = () => {
  if (!ageChartRef.value) return

  ageChart = echarts.init(ageChartRef.value)

  const ages = ['18-25', '26-35', '36-45', '46-55', '55+']
  const counts = [25, 35, 20, 15, 5]

  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ages
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用户数量',
        type: 'bar',
        data: counts,
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#67C23A' },
              { offset: 1, color: '#85CE61' }
            ]
          }
        }
      }
    ]
  }

  ageChart.setOption(option)
}

const refreshActiveUsers = () => {
  loadActiveUsers()
  ElMessage.success('活跃用户数据已刷新')
}

// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadTableData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadTableData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadTableData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadTableData()
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 窗口大小变化时重新渲染图表
const handleResize = () => {
  if (growthChart) growthChart.resize()
  if (activityChart) activityChart.resize()
  if (sourceChart) sourceChart.resize()
  if (ageChart) ageChart.resize()
}

onMounted(async () => {
  await loadTableData()
  await loadActiveUsers()

  nextTick(() => {
    initGrowthChart()
    initActivityChart()
    initSourceChart()
    initAgeChart()
  })

  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  if (growthChart) {
    growthChart.dispose()
    growthChart = null
  }
  if (activityChart) {
    activityChart.dispose()
    activityChart = null
  }
  if (sourceChart) {
    sourceChart.dispose()
    sourceChart = null
  }
  if (ageChart) {
    ageChart.dispose()
    ageChart = null
  }
})
</script>

<style scoped lang="scss">
.user-analytics {
  @apply p-6 bg-gray-50 min-h-screen;
}

.stats-row {
  @apply mb-6;
}

.charts-section {
  @apply mb-6;
}

.analysis-section {
  @apply mb-6;
}

.chart-card,
.data-card {
  @apply bg-white rounded-lg shadow-sm p-6 h-full;
}

.chart-header,
.card-header {
  @apply flex justify-between items-center mb-4;

  h3 {
    @apply text-lg font-semibold text-gray-900;
  }

  .chart-controls {
    @apply flex items-center space-x-3;
  }
}

.chart-container {
  @apply h-80;
}

.active-users-list {
  @apply space-y-4;
}

.user-item {
  @apply flex items-center space-x-3 p-3 bg-gray-50 rounded-lg;

  .user-rank {
    @apply w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold;
  }

  .user-avatar {
    @apply flex-shrink-0;
  }

  .user-info {
    @apply flex-1;

    .user-name {
      @apply text-sm font-medium text-gray-900 mb-1;
    }

    .user-phone {
      @apply text-xs text-gray-500 mb-2;
    }

    .user-stats {
      @apply flex space-x-4 text-xs;

      .orders {
        @apply text-blue-600;
      }

      .amount {
        @apply text-green-600;
      }
    }
  }
}

.table-section {
  @apply bg-white rounded-lg shadow-sm;
}

.user-info {
  @apply flex items-center space-x-3;

  .user-details {
    .user-name {
      @apply text-sm font-medium text-gray-900;
    }

    .user-phone {
      @apply text-xs text-gray-500;
    }
  }
}

.amount-text {
  @apply text-green-600 font-semibold;
}

.avg-amount {
  @apply text-blue-600 font-medium;
}
</style>
