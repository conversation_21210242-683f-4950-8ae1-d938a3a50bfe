Component({
  properties: {
    // 关联状态
    status: {
      type: String,
      value: 'pending'
    },
    // 是否为发送者
    isSender: {
      type: Boolean,
      value: false
    },
    // 创建时间
    createdAt: {
      type: String,
      value: ''
    },
    // 是否显示进度条
    showProgress: {
      type: Boolean,
      value: true
    },
    // 是否显示时间
    showTime: {
      type: Boolean,
      value: true
    },
    // 大小模式
    size: {
      type: String,
      value: 'normal' // normal, small, large
    }
  },

  data: {
    statusConfig: {
      pending: {
        icon: 'clock-o',
        color: '#ff9500',
        text: '等待处理',
        senderText: '等待对方确认',
        receiverText: '待您处理',
        progress: 50
      },
      accepted: {
        icon: 'success',
        color: '#07c160',
        text: '已关联',
        senderText: '关联成功',
        receiverText: '关联成功',
        progress: 100
      },
      rejected: {
        icon: 'close',
        color: '#ee0a24',
        text: '已拒绝',
        senderText: '申请被拒绝',
        receiverText: '已拒绝申请',
        progress: 100
      }
    }
  },

  computed: {
    currentConfig() {
      return this.data.statusConfig[this.properties.status] || this.data.statusConfig.pending;
    },
    
    displayText() {
      const config = this.currentConfig;
      return this.properties.isSender ? config.senderText : config.receiverText;
    },
    
    formattedTime() {
      if (!this.properties.createdAt) return '';
      return this.formatRelativeTime(this.properties.createdAt);
    },
    
    sizeClass() {
      return `connection-status--${this.properties.size}`;
    }
  },

  methods: {
    /**
     * 格式化相对时间
     */
    formatRelativeTime(dateString) {
      const now = new Date();
      const date = new Date(dateString);
      const diff = now - date;
      
      const minutes = Math.floor(diff / (1000 * 60));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      
      if (minutes < 1) {
        return '刚刚';
      } else if (minutes < 60) {
        return `${minutes}分钟前`;
      } else if (hours < 24) {
        return `${hours}小时前`;
      } else if (days < 7) {
        return `${days}天前`;
      } else {
        return this.formatDate(date);
      }
    },
    
    /**
     * 格式化日期
     */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    /**
     * 点击状态事件
     */
    onStatusClick() {
      this.triggerEvent('statusclick', {
        status: this.properties.status,
        isSender: this.properties.isSender
      });
    }
  },

  observers: {
    'status, isSender, createdAt': function() {
      // 当属性变化时重新计算computed属性
      this.setData({
        _currentConfig: this.data.statusConfig[this.properties.status] || this.data.statusConfig.pending,
        _displayText: this.properties.isSender ? 
          (this.data.statusConfig[this.properties.status]?.senderText || '等待对方确认') :
          (this.data.statusConfig[this.properties.status]?.receiverText || '待您处理'),
        _formattedTime: this.formatRelativeTime(this.properties.createdAt),
        _sizeClass: `connection-status--${this.properties.size}`
      });
    }
  },

  lifetimes: {
    attached() {
      // 组件初始化时设置数据
      this.setData({
        _currentConfig: this.data.statusConfig[this.properties.status] || this.data.statusConfig.pending,
        _displayText: this.properties.isSender ? 
          (this.data.statusConfig[this.properties.status]?.senderText || '等待对方确认') :
          (this.data.statusConfig[this.properties.status]?.receiverText || '待您处理'),
        _formattedTime: this.formatRelativeTime(this.properties.createdAt),
        _sizeClass: `connection-status--${this.properties.size}`
      });
    }
  }
});
