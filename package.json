{"name": "nanan", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=development npm run build:npm && echo '🚀 开发环境启动完成，请在微信开发者工具中打开项目'", "test": "cross-env NODE_ENV=test npm run build:npm && echo '🧪 测试环境构建完成，使用线上测试库'", "build": "cross-env NODE_ENV=production npm run build:npm && echo '📦 生产环境构建完成'", "build:test": "cross-env NODE_ENV=test npm run build:npm && echo '📦 测试环境构建完成'", "build:npm": "npm run build:env && npm run compile:npm", "build:env": "node scripts/build-env.js", "compile:npm": "echo '正在构建npm包...' && npm run clean:npm && npm install --production=false", "clean:npm": "rimraf miniprogram_npm", "preview": "echo '请在微信开发者工具中使用预览功能'", "upload": "echo '请在微信开发者工具中使用上传功能'"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@vant/weapp": "^1.11.6", "axios": "^1.9.0", "dayjs": "^1.11.11", "miniprogram-api-promise": "^1.0.4", "mobx-miniprogram": "^6.12.3", "mobx-miniprogram-bindings": "^3.0.0", "xml2js": "^0.6.2"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "autoprefixer": "^10.4.21", "cross-env": "^7.0.3", "rimraf": "^5.0.10"}}