import {createApp} from 'vue';
import App from './App.vue';
import router from './router/index.js';
import {createPinia} from 'pinia';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import './style/index.scss';

// 引入全局错误处理
import {setupGlobalErrorHandling} from './utils/errorHandler';

// 引入store初始化
import {initStores} from './store';

// 引入动画库
// import 'animate.css';
// import {MotionPlugin} from '@vueuse/motion';

// 创建应用实例
const app = createApp(App);

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 使用插件
app.use(createPinia());
app.use(router);
app.use(ElementPlus);
// app.use(MotionPlugin);

// 设置全局错误处理
setupGlobalErrorHandling();

// 初始化store
initStores();

// 挂载应用
app.mount('#app');
