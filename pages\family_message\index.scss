/* 留言页面 - 现代化设计 */
@import "../../styles/miniprogram-design.scss";

.container {
  height: 100vh;
  @include bg-gray-50;
  @include flex;
  @include flex-col;
  overflow: hidden;
  padding: 32rpx;
}

.page-header {
  @include flex;
  @include items-center;
  @include justify-center;
  @include mb-4;
}

.page-title {
  @include text-2xl;
  @include font-bold;
  @include text-primary;
  @include text-center;
}

/* 发送留言区域 */
.send-card {
  @include modern-card;
  @include card-primary;
  @include mb-4;
  padding: 32rpx;
  @include flex-shrink-0; /* 防止被压缩 */
}

/* 留言列表区域 */
.messages-card {
  @include modern-card;
  @include card-flat;
  @include flex-1; /* 占据剩余空间 */
  @include flex;
  @include flex-col;
  padding: 32rpx;
  background: #fafbfc;
  border-color: #e2e8f0;
  overflow: hidden; /* 防止内容溢出 */
}

.messages-header {
  @include flex;
  @include items-center;
  @include justify-between;
  @include mb-4;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #e2e8f0;

  .messages-title {
    @include text-lg;
    @include font-bold;
    @include text-gray-900;
  }

  .messages-count {
    @include text-sm;
    @include text-gray-500;
    background: #f1f5f9;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
  }
}

/* 滚动区域样式 */
.messages-scroll {
  @include flex-1;
  width: 100%;
  overflow: hidden;
}

/* 简化的刷新按钮 */
.refresh-button {
  @include flex;
  @include items-center;
  @include justify-center;
  @include gap-2;
  padding: 16rpx 24rpx;
  margin-bottom: 16rpx;
  background: #f8fafc;
  border: 1rpx solid #e2e8f0;
  border-radius: 12rpx;
  @include transition;

  &:active {
    background: #f1f5f9;
    transform: scale(0.98);
  }

  .refresh-icon {
    font-size: 24rpx;
    color: #6366f1;
  }

  .refresh-text {
    @include text-sm;
    @include text-gray-600;
    @include font-medium;
  }

  &.loading {
    opacity: 0.7;
    pointer-events: none;
  }
}

.messages-list {
  margin-top: 24rpx;
}

/* 空状态样式 */
.empty-messages {
  @include flex;
  @include flex-col;
  @include items-center;
  @include justify-center;
  padding: 80rpx 40rpx;
  text-align: center;

  .empty-icon {
    font-size: 120rpx;
    color: #cbd5e1;
    margin-bottom: 24rpx;
  }

  .empty-text {
    @include text-lg;
    @include font-medium;
    @include text-gray-600;
    margin-bottom: 12rpx;
  }

  .empty-tip {
    @include text-sm;
    @include text-gray-400;
  }
}

/* 加载更多样式 */
.load-more {
  @include flex;
  @include items-center;
  @include justify-center;
  padding: 32rpx;
  margin-top: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
  border: 1rpx solid #e2e8f0;
  @include transition;

  &:active {
    background: #f1f5f9;
    transform: scale(0.98);
  }

  .load-more-text {
    @include text-sm;
    @include text-gray-600;
    @include font-medium;
  }
}

/* 没有更多数据样式 */
.no-more {
  @include flex;
  @include items-center;
  @include justify-center;
  padding: 24rpx;
  margin-top: 16rpx;

  .no-more-text {
    @include text-xs;
    @include text-gray-400;
    position: relative;

    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 60rpx;
      height: 1rpx;
      background: #e2e8f0;
    }

    &::before {
      left: -80rpx;
    }

    &::after {
      right: -80rpx;
    }
  }
}

.msg-item {
  @include modern-card;
  @include card-flat;
  @include mb-3;
  padding: 24rpx;
  @include transition;
  background: #ffffff;
  border: 1rpx solid #f1f5f9;

  &:hover {
    transform: translateY(-2rpx);
    @include shadow-sm;
    border-color: #e2e8f0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.msg-header {
  @include flex;
  @include items-center;
  @include justify-between;
  @include mb-3;
  padding-bottom: 12rpx;
  border-bottom: 1rpx solid #f1f5f9;
}

.msg-user {
  @include font-bold;
  @include text-base;
  @include flex-shrink-0;

  &.blue {
    @include text-primary;
  }

  &.pink {
    @include text-secondary;
  }
}

.msg-time {
  @include text-gray-500;
  @include text-xs;
  @include flex-shrink-0;
  margin-left: 16rpx;
}

.msg-content {
  @include text-gray-900;
  @include text-base;
  line-height: 1.6;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

.msg-form {
  @include flex;
  @include gap-2;
  margin-top: 24rpx;
}

.msg-input {
  @include flex-1;
  @include modern-input;
  border-radius: 24rpx 0 0 24rpx;
  height: 88rpx;
  line-height: 88rpx;
}

.placeholder-style {
  @include text-gray-400;
  @include text-sm;
}

.msg-send {
  @include modern-btn;
  @include btn-primary;
  @include flex;
  @include items-center;
  @include justify-center;
  width: 100rpx;
  height: 88rpx;
  border-radius: 0 24rpx 24rpx 0;
  @include text-lg;
}

.send-icon {
  @include text-white;
  @include font-bold;
  font-size: 32rpx;
}

/* 接收者选择器样式 */
.recipient-selector {
  background: #f8fafc;
  border-radius: 16rpx;
  border: 2rpx solid #e2e8f0;
  padding: 20rpx;
  margin-bottom: 24rpx;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  .selector-label {
    font-size: 24rpx;
    color: #475569;
    margin-bottom: 16rpx;
  }

  .selector-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16rpx 0;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    &:active {
      opacity: 0.7;
    }

    .selector-text {
      font-size: 28rpx;
      color: #1e293b;
      flex: 1;
    }

    .selector-arrow {
      color: #94a3b8;
      font-size: 24rpx;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  &:hover {
    box-shadow: 0 2rpx 16rpx rgba(15, 23, 42, 0.06);
  }
}

/* 接收者选择弹窗样式 */
.recipient-popup {
  padding: 32rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;

  .popup-header {
    text-align: center;
    margin-bottom: 48rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 8rpx;
    }

    .popup-subtitle {
      font-size: 24rpx;
      color: #64748b;
    }
  }

  .recipient-list {
    flex: 1;
    overflow: auto;
    margin-bottom: 48rpx;

    .recipient-item {
      display: flex;
      align-items: center;
      padding: 24rpx 0;
      border-bottom: 1rpx solid #f3f4f6;

      &:last-child {
        border-bottom: none;
      }

      .recipient-info {
        margin-left: 24rpx;
        flex: 1;

        .recipient-name {
          font-size: 28rpx;
          font-weight: 500;
          color: #1e293b;
          margin-bottom: 8rpx;
        }

        .recipient-phone {
          font-size: 24rpx;
          color: #64748b;
        }
      }
    }

    /* 接收者缺省页样式 */
    .empty-recipients-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80rpx 40rpx;
      text-align: center;

      .empty-icon {
        margin-bottom: 24rpx;
        opacity: 0.6;
      }

      .empty-text {
        font-size: 28rpx;
        color: #6b7280;
        font-weight: 500;
        margin-bottom: 12rpx;
      }

      .empty-desc {
        font-size: 24rpx;
        color: #9ca3af;
        line-height: 1.5;
        margin-bottom: 40rpx;
      }

      .empty-action {
        /* 按钮样式由van-button组件控制 */
      }
    }
  }

  .popup-actions {
    flex-shrink: 0;
  }
}
