<template>
  <div class="user-management">
    <CustomTable
      title="用户管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    >
      <template #actions>
        <el-button @click="handleRefresh">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出用户
        </el-button>
      </template>

      <template #user="{ row }">
        <div class="user-info">
          <el-avatar :src="row.avatar" :size="40">
            {{ row.name ? row.name.charAt(0) : 'U' }}
          </el-avatar>
          <div class="user-details">
            <div class="user-name">{{ row.name || '未知用户' }}</div>
            <div class="user-phone">{{ row.phone || '未知手机号' }}</div>
          </div>
        </div>
      </template>

      <template #status="{ row }">
        <el-switch
          v-model="row.status"
          :active-value="1"
          :inactive-value="0"
          active-text="正常"
          inactive-text="禁用"
          @change="handleStatusChange(row)"
        />
      </template>

      <template #orderCount="{ row }">
        <el-tag type="primary">{{ row.orderCount }}单</el-tag>
      </template>

      <template #totalAmount="{ row }">
        <span class="amount-text">¥{{ row.totalAmount }}</span>
      </template>

      <template #lastLoginTime="{ row }">
        <span>{{ formatTime(row.lastLoginTime) }}</span>
      </template>

      <template #operation="{ row }">
        <el-button size="small" @click="handleViewUser(row)">查看</el-button>
        <el-button size="small" type="primary" @click="handleEditUser(row)"
          >编辑</el-button
        >
        <el-button
          size="small"
          :type="row.status === 1 ? 'warning' : 'success'"
          @click="handleToggleStatus(row)"
        >
          {{ row.status === 1 ? '禁用' : '启用' }}
        </el-button>
      </template>
    </CustomTable>

    <!-- 用户详情/编辑对话框 -->
    <UserDialog
      v-model="dialogVisible"
      :user-id="selectedUserId"
      :is-edit="isEditMode"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Download } from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import UserDialog from './components/UserDialog.vue'
import { userApi } from '@/api/user'
import { formatTime } from '@/utils/common'

const loading = ref(false)
const tableData = ref([])

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  name: '',
  phone: '',
  status: ''
})

// 对话框相关
const dialogVisible = ref(false)
const selectedUserId = ref(null)
const isEditMode = ref(false)

// 表格列配置
const columns = [
  { prop: 'user', label: '用户信息', width: 200, slot: true },
  { prop: 'orderCount', label: '订单数量', width: 120, slot: true },
  { prop: 'totalAmount', label: '消费总额', width: 120, slot: true },
  { prop: 'lastLoginTime', label: '最后登录', width: 160, slot: true },
  { prop: 'registerTime', label: '注册时间', width: 160, formatter: (row) => formatTime(row.registerTime) },
  { prop: 'status', label: '状态', width: 100, slot: true },
  { label: '操作', width: 200, slot: 'operation', fixed: 'right' }
]

// 搜索字段配置
const searchFields = [
  { prop: 'name', label: '用户姓名', type: 'input' },
  { prop: 'phone', label: '手机号', type: 'input' },
  { prop: 'status', label: '状态', type: 'select', options: [
    { label: '全部', value: '' },
    { label: '正常', value: '1' },
    { label: '禁用', value: '0' }
  ]}
]

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }

    const result = await userApi.getUsers(params)

    if (result.code === 200) {
      tableData.value = result.data.list || []
      pagination.total = result.data.total || 0
    } else {
      ElMessage.error(result.message || '加载数据失败')
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}



// 事件处理
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

const handleRefresh = () => {
  loadData()
  ElMessage.success('数据已刷新')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleViewUser = (row) => {
  selectedUserId.value = row.id
  isEditMode.value = false
  dialogVisible.value = true
}

const handleEditUser = (row) => {
  selectedUserId.value = row.id
  isEditMode.value = true
  dialogVisible.value = true
}

const handleDialogSuccess = () => {
  loadData()
}

const handleStatusChange = async (row) => {
  try {
    const result = await userApi.updateUser(row.id, { status: row.status })
    if (result.code === 200) {
      ElMessage.success('用户状态更新成功')
    } else {
      ElMessage.error(result.message || '状态更新失败')
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1
    }
  } catch (error) {
    console.error('更新用户状态失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

const handleToggleStatus = async (row) => {
  const action = row.status === 1 ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}用户 ${row.name} 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const newStatus = row.status === 1 ? 0 : 1
    const result = await userApi.updateUser(row.id, { status: newStatus })

    if (result.code === 200) {
      row.status = newStatus
      ElMessage.success(`用户${action}成功`)
    } else {
      ElMessage.error(result.message || `用户${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`用户${action}失败:`, error)
      ElMessage.error(`用户${action}失败`)
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped lang="scss">
.user-management {
  @apply p-6 bg-gray-50 min-h-screen;
}

.user-info {
  @apply flex items-center space-x-3;

  .user-details {
    .user-name {
      @apply text-sm font-medium text-gray-900;
    }

    .user-phone {
      @apply text-xs text-gray-500;
    }
  }
}

.amount-text {
  @apply text-green-600 font-semibold;
}
</style>
