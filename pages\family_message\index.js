const app = getApp();
const {
	messageApi,
	connectionApi
} = require('../../services/api');
const {
	applyPageMixin
} = require('../../utils/page-mixin');

// 导入统一的环境配置
const {
	baseURL
} = require('../../config/env');

Page(
	applyPageMixin({
		data: {
			messages: [],
			messageInput: '',
			userInfo: {},
			loading: true,
			// 接收者选择相关
			connectedUsers: [],
			selectedRecipients: [],
			showRecipientPopup: false,
			selectedRecipientsText: '所有关联用户',
			// 分页相关
			currentPage: 1,
			pageSize: 10,
			hasMore: true,
			refreshing: false,
			loadingMore: false,
			// 简化的刷新状态
			refreshing: false,
			// 滚动区域高度
			scrollViewHeight: 400,
			// 请求配置
			requestUrl: '',
			requestParams: {},
			requestHeaders: {}
		},

		onLoad() {
			// 获取用户信息
			const userInfo = wx.getStorageSync('userInfo') || {};
			this.setData({
				userInfo
			});

			// 计算滚动区域高度
			this.calculateScrollViewHeight();

			// 配置请求参数
			this.setupRequest();
		},

		onShow() {
			// 加载关联用户数据
			this.loadConnectedUsers();
		},

		// 旧的loadMessages方法已移除，现在使用组件的自动请求机制

		// 格式化时间
		formatTime(dateString) {
			const date = new Date(dateString);
			const now = new Date();
			const diff = now - date;

			// 如果是今天
			if (diff < 24 * 60 * 60 * 1000) {
				return `${String(date.getHours()).padStart(2, '0')}:${String(
          date.getMinutes()
        ).padStart(2, '0')}`;
			}

			// 如果是其他日期
			return `${date.getMonth() + 1}-${date.getDate()} ${String(
        date.getHours()
      ).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
		},

		// 配置请求参数
		setupRequest() {
			const {
				userInfo
			} = this.data;

			// 获取认证token
			const token =
				wx.getStorageSync('token') || wx.getStorageSync('userToken');

			// 使用项目配置的 baseURL
			this.setData({
				requestUrl: `${baseURL}/messages`, // 使用统一的 baseURL 配置
				requestParams: {
					userId: userInfo.id || 'cmbmg8ox5000m6yh818dy4gsn',
					recipients: this.data.selectedRecipients || []
				},
				requestHeaders: {
					// 可以在这里添加额外的请求头
					// 'Custom-Header': 'value'
				}
			});
		},

		// 留言数据处理函数
		handleMessageData(list, response, isRefresh) {
			// 格式化留言数据
			const formattedMessages = list.map(msg => {
				const userType = msg.userId % 2 === 0 ? 'blue' : 'pink';
				return {
					id: msg.id,
					userName: msg.user?.name || msg.user_name || '用户',
					content: msg.content || '暂无内容',
					time: this.formatTime(msg.createdAt || msg.created_at || new Date()),
					userType,
					read: msg.read || false,
					userId: msg.userId
				};
			});

			return formattedMessages;
		},

		// 计算滚动区域高度
		calculateScrollViewHeight() {
			const query = wx.createSelectorQuery().in(this);

			// 获取系统信息
			const systemInfo = wx.getSystemInfoSync();
			const windowHeight = systemInfo.windowHeight;

			// 计算其他元素的高度
			query.select('.page-header').boundingClientRect();
			query.select('.send-card').boundingClientRect();
			query.select('.messages-header').boundingClientRect();

			query.exec(res => {
				let usedHeight = 0;

				// 累加已使用的高度
				res.forEach(rect => {
					if (rect) {
						usedHeight += rect.height;
					}
				});

				// 预留一些边距和安全区域
				const padding = 100; // 预留100px的边距
				const availableHeight = windowHeight - usedHeight - padding;

				// 设置最小高度和最大高度
				const minHeight = 300;
				const maxHeight = 600;
				const finalHeight = Math.max(
					minHeight,
					Math.min(maxHeight, availableHeight)
				);

				this.setData({
					scrollViewHeight: finalHeight
				});
			});
		},

		// 处理组件数据变化
		onDataChange(e) {
			const {
				list,
				total,
				hasMore,
				isEmpty,
				isRefresh
			} = e.detail;

			// 在这里处理数据格式化（之前在 dataHandler 中处理）
			const formattedMessages = list.map(msg => {
				// 根据用户ID设置不同的样式类
				const userType = msg.userId % 2 === 0 ? 'blue' : 'pink';

				return {
					id: msg.id,
					userName: msg.user?.name || msg.user_name || '用户',
					content: msg.content || '暂无内容',
					time: this.formatTime(msg.createdAt || msg.created_at || new Date()),
					userType,
					read: msg.read || false,
					userId: msg.userId
				};
			});

			this.setData({
				messages: formattedMessages,
				hasMore,
				loadingMore: false
			});

			// 更新留言数量显示
			this.updateMessageCount(formattedMessages.length);
		},

		// 更新留言数量
		updateMessageCount(count) {
			// 可以在这里更新页面标题或其他UI
		},

		// 格式化时间
		formatTime(dateString) {
			const date = new Date(dateString);
			const now = new Date();
			const diff = now - date;

			// 一天内显示时间
			if (diff < 24 * 60 * 60 * 1000) {
				return date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit'
				});
			}

			// 超过一天显示日期
			return date.toLocaleDateString('zh-CN', {
				month: '2-digit',
				day: '2-digit'
			});
		},

		// 刷新和加载更多现在由组件内部处理，不再需要这些方法

		// 留言输入
		onMessageInput(e) {
			this.setData({
				messageInput: e.detail.value
			});
		},

		// 加载已关联用户
		async loadConnectedUsers() {
			try {
				this.showLoading({
					text: '加载关联用户...'
				});

				const res = await connectionApi.getMyConnections('accepted');

				if (res.code === 200) {
					const connectedUsers = (res.data || []).map(conn => ({
						id: conn.connectedUser.id,
						name: conn.connectedUser.name,
						phone: conn.connectedUser.phone
					}));

					this.setData({
						connectedUsers
					});
				} else {
					console.error('获取关联用户失败:', res.message);
				}
			} catch (error) {
				console.error('加载关联用户失败:', error);
				wx.showToast({
					title: '加载关联用户失败',
					icon: 'none'
				});
			} finally {
				this.hideLoading();
			}
		},

		// 显示接收者选择器
		showRecipientSelector() {
			this.setData({
				showRecipientPopup: true
			});
		},

		// 隐藏接收者选择器
		hideRecipientSelector() {
			this.setData({
				showRecipientPopup: false
			});
		},

		// 接收者选择变化
		onRecipientChange(event) {
			this.setData({
				selectedRecipients: event.detail
			});
		},

		// 确认接收者选择
		confirmRecipientSelection() {
			const {
				selectedRecipients,
				connectedUsers
			} = this.data;

			let selectedRecipientsText = '';
			if (selectedRecipients.length === 0) {
				selectedRecipientsText = '所有关联用户';
			} else if (selectedRecipients.length === connectedUsers.length) {
				selectedRecipientsText = '所有关联用户';
			} else {
				const selectedNames = selectedRecipients
					.map(id => {
						const user = connectedUsers.find(u => u.id === id);
						return user ? user.name : '';
					})
					.filter(name => name);

				if (selectedNames.length <= 2) {
					selectedRecipientsText = selectedNames.join('、');
				} else {
					selectedRecipientsText = `${selectedNames.slice(0, 2).join('、')} 等${
            selectedNames.length
          }人`;
				}
			}

			this.setData({
				selectedRecipientsText,
				showRecipientPopup: false
			});
		},

		// 添加留言
		async addMessage() {
			const {
				messageInput,
				userInfo,
				selectedRecipients,
				connectedUsers
			} =
			this.data;

			if (!messageInput.trim()) {
				wx.showToast({
					title: '请输入留言内容',
					icon: 'none'
				});
				return;
			}

			try {
				this.showLoading({
					text: '发送中...'
				});

				// 确定接收者列表
				let recipients = [];
				if (selectedRecipients.length === 0) {
					// 没有选择，发给所有关联用户
					recipients = connectedUsers.map(user => user.id);
				} else {
					// 发给选中的用户
					recipients = selectedRecipients;
				}

				const result = await messageApi.createMessage({
					content: messageInput.trim(),
					recipients: recipients
				});

				if (result.code === 200 || result.code === 201) {
					// 清空输入框
					this.setData({
						messageInput: ''
					});

					// 刷新消息列表
					const refreshScrollComponent =
						this.selectComponent('#refresh-scroll');
					if (refreshScrollComponent) {
						refreshScrollComponent.reload();
					}

					// 发送成功提示（不带图标）
					const recipientText =
						recipients.length === connectedUsers.length ?
						'所有关联用户' :
						`${recipients.length}位用户`;

					wx.showToast({
						title: `留言已发送`,
						icon: 'none', // 不显示图标
						duration: 2000
					});

					// 发送成功后不进行页面跳转，保持在当前页面
				} else {
					throw new Error(result.message || '发送失败');
				}
			} catch (error) {
				console.error('发送留言失败:', error);
				wx.showToast({
					title: error.message || '发送失败，请重试',
					icon: 'error'
				});
			} finally {
				this.hideLoading();
			}
		},

		// 跳转到用户关联页面
		goToUserConnection() {
			wx.navigateTo({
				url: '/pages/user_connection/index',

				fail: error => {
					console.error('跳转到用户关联页面失败:', error);
					wx.showToast({
						title: '页面跳转失败',
						icon: 'none'
					});
				}
			});
		}
	})
);