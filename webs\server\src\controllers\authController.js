const prisma = require('../utils/prisma');
const {hashPassword, comparePassword} = require('../utils/password');
const {
  generateToken,
  generateTokenPair,
  revokeUserTokens
} = require('../utils/jwt');
const {success, error} = require('../utils/response');
const wechatService = require('../services/wechatService');
const notificationService = require('../services/notificationService');
const emailService = require('../services/emailService');

/**
 * 用户登录
 * @route POST /api/auth/login
 */
const login = async (req, res) => {
  try {
    const {username, password, loginType} = req.body;

    // 微信登录
    if (loginType === 'wechat') {
      const {code} = req.body;

      if (!code) {
        return error(res, 'WeChat code is required', 400);
      }

      // 获取微信 OpenID
      const {openid} = await wechatService.getOpenId(code);
      console.log('获取到openid:', openid);

      // 首先通过 openid 查找用户
      let user = await prisma.user.findUnique({
        where: {openid}
      });

      // 如果通过 openid 没找到用户，创建新用户
      // 注意：现在不再通过手机号匹配，因为我们不获取手机号了

      if (!user) {
        // 创建新的微信用户
        user = await prisma.user.create({
          data: {
            name: '微信用户', // 默认名称，用户后续可以修改
            openid
          }
        });

        console.log(
          `新用户通过微信注册: ${user.name} (openid: ${openid.substring(
            0,
            10
          )}...)`
        );
      }

      // 生成单点登录 token
      const tokenPair = generateTokenPair(user, true);

      return success(res, {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        user: {
          id: user.id,
          name: user.name,
          phone: user.phone,
          avatar: user.avatar,
          role: user.role
        },
        message: '微信登录成功'
      });
    }

    // 账号密码登录
    if (!username || !password) {
      return error(res, 'Username and password are required', 400);
    }

    // 查找用户（支持用户名、手机号、邮箱登录）
    const user = await prisma.user.findFirst({
      where: {
        OR: [{phone: username}, {name: username}, {email: username}]
      }
    });

    if (!user || !user.password) {
      return error(res, 'Invalid credentials', 401);
    }

    // 验证密码
    const isPasswordValid = await comparePassword(password, user.password);

    if (!isPasswordValid) {
      return error(res, 'Invalid credentials', 401);
    }

    // 生成单点登录 token
    const tokenPair = generateTokenPair(user, true);

    return success(res, {
      token: tokenPair.accessToken,
      refreshToken: tokenPair.refreshToken,
      user: {
        id: user.id,
        name: user.name,
        phone: user.phone,
        avatar: user.avatar,
        role: user.role
      },
      message: '登录成功'
    });
  } catch (err) {
    console.error('Login error:', err);
    return error(res, 'Login failed', 500);
  }
};

/**
 * 用户注册
 * @route POST /api/auth/register
 */
const register = async (req, res) => {
  try {
    const {name, phone, password} = req.body;

    if (!name || !phone || !password) {
      return error(res, 'Name, phone and password are required', 400);
    }

    // 检查手机号是否已存在
    const existingUser = await prisma.user.findUnique({
      where: {phone}
    });

    if (existingUser) {
      return error(res, 'Phone number already registered', 409);
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const user = await prisma.user.create({
      data: {
        name,
        phone,
        password: hashedPassword
      }
    });

    // 生成单点登录 token
    const tokenPair = generateTokenPair(user, true);

    // 发送新用户注册通知给管理员
    try {
      await notificationService.notifyUserRegistration(user);
    } catch (notifyError) {
      console.error('Failed to send registration notification:', notifyError);
      // 不影响主要功能，只记录错误
    }

    return success(
      res,
      {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        user: {
          id: user.id,
          name: user.name,
          phone: user.phone,
          role: user.role
        }
      },
      'Registration successful',
      201
    );
  } catch (err) {
    console.error('Registration error:', err);
    return error(res, 'Registration failed', 500);
  }
};

/**
 * 获取当前用户信息
 * @route GET /api/auth/me
 */
const getMe = async (req, res) => {
  try {
    const userId = req.user.id;

    const user = await prisma.user.findUnique({
      where: {id: userId},
      select: {
        id: true,
        name: true,
        phone: true,
        avatar: true,
        role: true,
        createdAt: true
      }
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    return success(res, user);
  } catch (err) {
    console.error('Get user error:', err);
    return error(res, 'Failed to get user information', 500);
  }
};

/**
 * 用户登出
 * @route POST /api/auth/logout
 */
const logout = async (req, res) => {
  try {
    const userId = req.user.id;

    // 撤销用户的所有令牌（单点登录：登出所有设备）
    revokeUserTokens(userId);

    return success(res, null, '登出成功');
  } catch (err) {
    console.error('Logout error:', err);
    return error(res, 'Logout failed', 500);
  }
};

/**
 * 刷新令牌
 * @route POST /api/auth/refresh
 */
const refreshToken = async (req, res) => {
  try {
    const {refreshToken} = req.body;

    if (!refreshToken) {
      return error(res, 'Refresh token is required', 400);
    }

    // 验证刷新令牌
    const {verifyRefreshToken} = require('../utils/jwt');
    const userId = verifyRefreshToken(refreshToken);

    if (!userId) {
      return error(res, 'Invalid refresh token', 401);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: {id: userId},
      select: {
        id: true,
        name: true,
        phone: true,
        role: true
      }
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    // 生成新的令牌对
    const tokenPair = generateTokenPair(user, false); // 不撤销其他令牌

    return success(res, tokenPair, '令牌刷新成功');
  } catch (err) {
    console.error('Refresh token error:', err);
    return error(res, 'Token refresh failed', 500);
  }
};

/**
 * 管理员注册（支持邮箱）
 * @route POST /api/auth/admin-register
 */
const adminRegister = async (req, res) => {
  try {
    const {username, phone, email, password} = req.body;

    // 验证必填字段
    if (!username || !password) {
      return error(res, 'Username and password are required', 400);
    }

    if (!phone && !email) {
      return error(res, 'Phone or email is required', 400);
    }

    // 检查用户名是否已存在
    const existingUsername = await prisma.user.findFirst({
      where: {name: username}
    });

    if (existingUsername) {
      return error(res, 'Username already exists', 409);
    }

    // 检查手机号是否已存在
    if (phone) {
      const existingPhone = await prisma.user.findUnique({
        where: {phone}
      });

      if (existingPhone) {
        return error(res, 'Phone number already registered', 409);
      }
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await prisma.user.findUnique({
        where: {email}
      });

      if (existingEmail) {
        return error(res, 'Email already registered', 409);
      }
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);

    // 创建管理员用户
    const user = await prisma.user.create({
      data: {
        name: username,
        phone: phone || null,
        email: email || null,
        password: hashedPassword,
        role: 'admin' // 设置为管理员角色
      }
    });

    // 生成token
    const tokenPair = generateTokenPair(user, true);

    return success(
      res,
      {
        token: tokenPair.accessToken,
        refreshToken: tokenPair.refreshToken,
        user: {
          id: user.id,
          name: user.name,
          phone: user.phone,
          email: user.email,
          role: user.role
        }
      },
      'Admin registration successful',
      201
    );
  } catch (err) {
    console.error('Admin registration error:', err);
    return error(res, 'Registration failed', 500);
  }
};

/**
 * 发送重置密码邮件
 * @route POST /api/auth/send-reset-password-email
 */
const sendResetPasswordEmail = async (req, res) => {
  try {
    const {email} = req.body;

    if (!email) {
      return error(res, 'Email is required', 400);
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: {email}
    });

    if (!user) {
      return error(res, 'Email not registered', 404);
    }

    // 发送重置密码邮件
    const result = await emailService.sendResetPasswordEmail(email, user.name);

    if (result.success) {
      return success(
        res,
        {
          resetToken: result.resetToken // 开发环境返回，生产环境应该移除
        },
        result.message
      );
    } else {
      return error(res, result.message, 500);
    }
  } catch (err) {
    console.error('Send reset password email error:', err);
    return error(res, 'Failed to send reset password email', 500);
  }
};

/**
 * 验证重置密码token
 * @route POST /api/auth/verify-reset-password-token
 */
const verifyResetPasswordToken = async (req, res) => {
  try {
    const {token} = req.body;

    if (!token) {
      return error(res, 'Reset token is required', 400);
    }

    const result = emailService.verifyResetPasswordToken(token);

    if (result.success) {
      return success(
        res,
        {
          email: result.email
        },
        result.message
      );
    } else {
      return error(res, result.message, 400);
    }
  } catch (err) {
    console.error('Verify reset password token error:', err);
    return error(res, 'Token verification failed', 500);
  }
};

/**
 * 重置密码
 * @route POST /api/auth/reset-password
 */
const resetPassword = async (req, res) => {
  try {
    const {token, password} = req.body;

    if (!token || !password) {
      return error(res, 'Token and password are required', 400);
    }

    // 验证并使用token
    const result = emailService.useResetPasswordToken(token);

    if (!result.success) {
      return error(res, result.message, 400);
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {email: result.email}
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    // 哈希新密码
    const hashedPassword = await hashPassword(password);

    // 更新密码
    await prisma.user.update({
      where: {id: user.id},
      data: {
        password: hashedPassword,
        loginFailCount: 0 // 重置登录失败次数
      }
    });

    // 撤销所有现有token
    revokeUserTokens(user.id);

    return success(res, null, 'Password reset successful');
  } catch (err) {
    console.error('Reset password error:', err);
    return error(res, 'Password reset failed', 500);
  }
};

/**
 * 通过验证码重置密码
 * @route POST /api/auth/reset-password-with-code
 */
const resetPasswordWithCode = async (req, res) => {
  try {
    const {email, code, password} = req.body;

    if (!email || !code || !password) {
      return error(res, 'Email, code and password are required', 400);
    }

    // 验证邮箱验证码
    const verifyResult = emailService.verifyEmailCode(
      email,
      code,
      'reset-password'
    );

    if (!verifyResult.success) {
      return error(res, verifyResult.message, 400);
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: {email}
    });

    if (!user) {
      return error(res, 'User not found', 404);
    }

    // 哈希新密码
    const hashedPassword = await hashPassword(password);

    // 更新密码
    await prisma.user.update({
      where: {id: user.id},
      data: {
        password: hashedPassword
      }
    });

    // 撤销所有现有token
    revokeUserTokens(user.id);

    return success(res, null, 'Password reset successful');
  } catch (err) {
    console.error('Reset password with code error:', err);
    return error(res, 'Password reset failed', 500);
  }
};

/**
 * 修改密码
 * @route POST /api/auth/change-password
 */
const changePassword = async (req, res) => {
  try {
    const {oldPassword, newPassword} = req.body;
    const userId = req.user.id;

    if (!oldPassword || !newPassword) {
      return error(res, 'Old password and new password are required', 400);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: {id: userId}
    });

    if (!user || !user.password) {
      return error(res, 'User not found or no password set', 404);
    }

    // 验证旧密码
    const isOldPasswordValid = await comparePassword(
      oldPassword,
      user.password
    );

    if (!isOldPasswordValid) {
      return error(res, 'Old password is incorrect', 400);
    }

    // 哈希新密码
    const hashedNewPassword = await hashPassword(newPassword);

    // 更新密码
    await prisma.user.update({
      where: {id: userId},
      data: {
        password: hashedNewPassword
      }
    });

    return success(res, null, 'Password changed successfully');
  } catch (err) {
    console.error('Change password error:', err);
    return error(res, 'Password change failed', 500);
  }
};

/**
 * 检查用户名是否可用
 * @route POST /api/auth/check-username
 */
const checkUsername = async (req, res) => {
  try {
    const {username} = req.body;

    if (!username) {
      return error(res, 'Username is required', 400);
    }

    const existingUser = await prisma.user.findFirst({
      where: {name: username}
    });

    return success(
      res,
      {
        available: !existingUser
      },
      existingUser ? 'Username already exists' : 'Username is available'
    );
  } catch (err) {
    console.error('Check username error:', err);
    return error(res, 'Username check failed', 500);
  }
};

/**
 * 检查邮箱是否可用
 * @route POST /api/auth/check-email
 */
const checkEmail = async (req, res) => {
  try {
    const {email} = req.body;

    if (!email) {
      return error(res, 'Email is required', 400);
    }

    const existingUser = await prisma.user.findUnique({
      where: {email}
    });

    return success(
      res,
      {
        available: !existingUser
      },
      existingUser ? 'Email already registered' : 'Email is available'
    );
  } catch (err) {
    console.error('Check email error:', err);
    return error(res, 'Email check failed', 500);
  }
};

/**
 * 检查手机号是否可用
 * @route POST /api/auth/check-phone
 */
const checkPhone = async (req, res) => {
  try {
    const {phone} = req.body;

    if (!phone) {
      return error(res, 'Phone is required', 400);
    }

    const existingUser = await prisma.user.findUnique({
      where: {phone}
    });

    return success(
      res,
      {
        available: !existingUser
      },
      existingUser
        ? 'Phone number already registered'
        : 'Phone number is available'
    );
  } catch (err) {
    console.error('Check phone error:', err);
    return error(res, 'Phone check failed', 500);
  }
};

module.exports = {
  login,
  register,
  getMe,
  logout,
  refreshToken,
  adminRegister,
  sendResetPasswordEmail,
  verifyResetPasswordToken,
  resetPassword,
  resetPasswordWithCode,
  changePassword,
  checkUsername,
  checkEmail,
  checkPhone
};
