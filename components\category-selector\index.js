Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    categories: {
      type: Array,
      value: []
    },
    selectedCategory: {
      type: String,
      value: ''
    }
  },

  data: {
    tempSelectedCategory: ''
  },

  observers: {
    'show': function(show) {
      if (show) {
        this.setData({
          tempSelectedCategory: this.properties.selectedCategory
        });
      }
    }
  },

  methods: {
    // 阻止事件冒泡
    stopPropagation() {
      // 空函数，阻止点击事件冒泡
    },

    // 点击遮罩关闭
    onMaskTap() {
      this.triggerEvent('close');
    },

    // 点击关闭按钮
    onClose() {
      this.triggerEvent('close');
    },

    // 选择分类
    onCategorySelect(e) {
      const { category, label } = e.currentTarget.dataset;
      this.setData({
        tempSelectedCategory: category
      });
    },

    // 确认选择
    onConfirm() {
      const selectedItem = this.properties.categories.find(
        item => item.value === this.data.tempSelectedCategory
      );
      
      if (selectedItem) {
        this.triggerEvent('select', {
          category: selectedItem.value,
          label: selectedItem.label,
          icon: selectedItem.icon
        });
      }
      
      this.triggerEvent('close');
    }
  }
});
