<template>
  <BaseModal
    :model-value="modelValue"
    :title="title"
    size="sm"
    :closable="false"
    :mask-closable="false"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <div class="text-center">
      <!-- 图标 -->
      <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4" :class="iconBgClass">
        <component
          :is="iconComponent"
          :class="iconClass"
          aria-hidden="true"
        />
      </div>
      
      <!-- 内容 -->
      <div class="text-center">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
          {{ title }}
        </h3>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          {{ content }}
        </p>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <template #footer>
      <div class="flex justify-center space-x-3">
        <BaseButton
          variant="outline"
          @click="handleCancel"
          :disabled="loading"
        >
          {{ cancelText }}
        </BaseButton>
        <BaseButton
          :variant="confirmVariant"
          @click="handleConfirm"
          :loading="loading"
        >
          {{ confirmText }}
        </BaseButton>
      </div>
    </template>
  </BaseModal>
</template>

<script setup>
import { computed } from 'vue'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline'
import BaseModal from './BaseModal.vue'
import BaseButton from './BaseButton.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'warning',
    validator: (value) => ['warning', 'info', 'success', 'error'].includes(value)
  },
  title: {
    type: String,
    default: '确认操作'
  },
  content: {
    type: String,
    default: '确定要执行此操作吗？'
  },
  confirmText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'confirm', 'cancel'])

// 类型配置
const typeConfig = {
  warning: {
    icon: ExclamationTriangleIcon,
    iconClass: 'h-6 w-6 text-yellow-600',
    iconBgClass: 'bg-yellow-100 dark:bg-yellow-900',
    confirmVariant: 'warning'
  },
  info: {
    icon: InformationCircleIcon,
    iconClass: 'h-6 w-6 text-blue-600',
    iconBgClass: 'bg-blue-100 dark:bg-blue-900',
    confirmVariant: 'primary'
  },
  success: {
    icon: CheckCircleIcon,
    iconClass: 'h-6 w-6 text-green-600',
    iconBgClass: 'bg-green-100 dark:bg-green-900',
    confirmVariant: 'success'
  },
  error: {
    icon: XCircleIcon,
    iconClass: 'h-6 w-6 text-red-600',
    iconBgClass: 'bg-red-100 dark:bg-red-900',
    confirmVariant: 'error'
  }
}

const config = computed(() => typeConfig[props.type])

const iconComponent = computed(() => config.value.icon)
const iconClass = computed(() => config.value.iconClass)
const iconBgClass = computed(() => config.value.iconBgClass)
const confirmVariant = computed(() => config.value.confirmVariant)

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('update:modelValue', false)
  emit('cancel')
}
</script>
