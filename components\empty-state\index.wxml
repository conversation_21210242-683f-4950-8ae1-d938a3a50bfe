<!-- 通用缺省页组件 -->
<view class="empty-state {{customClass}}">
  <!-- 图标 -->
  <view class="empty-icon" wx:if="{{icon}}">
    <van-icon name="{{icon}}" size="{{iconSize || '60rpx'}}" color="{{iconColor || '#D1D5DB'}}" />
  </view>
  
  <!-- 自定义图标插槽 -->
  <view class="empty-icon" wx:else>
    <slot name="icon"></slot>
  </view>
  
  <!-- 主要文字 -->
  <view class="empty-text" wx:if="{{text}}">{{text}}</view>
  
  <!-- 描述文字 -->
  <view class="empty-desc" wx:if="{{description}}">{{description}}</view>
  
  <!-- 操作按钮 -->
  <view class="empty-action" wx:if="{{actionText}}">
    <van-button 
      size="{{actionSize || 'small'}}" 
      type="{{actionType || 'primary'}}" 
      plain="{{actionPlain !== false}}"
      bind:click="onActionClick"
    >
      {{actionText}}
    </van-button>
  </view>
  
  <!-- 自定义操作插槽 -->
  <view class="empty-action" wx:else>
    <slot name="action"></slot>
  </view>
</view>
