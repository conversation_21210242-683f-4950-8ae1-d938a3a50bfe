<template>
  <div class="message-list">
    <CustomTable
      title="家庭留言"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :search-fields="searchFields"
      :selection="true"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      @selection-change="handleSelectionChange"
    >
      <template #actions>
        <el-button
          type="success"
          :disabled="selectedRows.length === 0"
          @click="handleBatchMarkRead"
        >
          <el-icon><Check /></el-icon>
          批量标记已读
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button type="info" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>

      <template #user="{ row }">
        <div class="user-info">
          <el-avatar :src="row.user?.avatar" :alt="row.user?.name" size="small">
            {{ row.user?.name?.charAt(0) }}
          </el-avatar>
          <span class="user-name">{{ row.user?.name || '未知用户' }}</span>
        </div>
      </template>

      <template #content="{ row }">
        <div class="message-content">
          <p>{{ row.content }}</p>
          <small class="message-time">{{ formatTime(row.createdAt) }}</small>
        </div>
      </template>

      <template #read="{ row }">
        <el-tag :type="row.read ? 'success' : 'warning'" size="small">
          {{ row.read ? '已读' : '未读' }}
        </el-tag>
      </template>

      <template #operation="{ row }">
        <el-button size="small" type="primary" link @click="handleView(row)">
          <el-icon><View /></el-icon>
          查看
        </el-button>
        <el-button
          size="small"
          type="success"
          link
          @click="handleMarkRead(row)"
          v-if="!row.read"
        >
          <el-icon><Check /></el-icon>
          标记已读
        </el-button>
        <el-popconfirm
          title="确定要删除这条留言吗？"
          @confirm="handleDelete(row)"
        >
          <template #reference>
            <el-button size="small" type="danger" link>
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </CustomTable>

    <!-- 留言详情对话框 -->
    <el-dialog v-model="detailVisible" title="留言详情" width="500px">
      <div class="message-detail" v-if="currentMessage">
        <div class="detail-header">
          <el-avatar
            :src="currentMessage.user?.avatar"
            :alt="currentMessage.user?.name"
            size="large"
          >
            {{ currentMessage.user?.name?.charAt(0) }}
          </el-avatar>
          <div class="user-info">
            <h4>{{ currentMessage.user?.name || '未知用户' }}</h4>
            <p class="time">
              {{ formatTime(currentMessage.createdAt, 'YYYY-MM-DD HH:mm:ss') }}
            </p>
          </div>
          <el-tag
            :type="currentMessage.read ? 'success' : 'warning'"
            size="small"
          >
            {{ currentMessage.read ? '已读' : '未读' }}
          </el-tag>
        </div>
        <div class="detail-content">
          <p>{{ currentMessage.content }}</p>
        </div>
        <div class="detail-actions">
          <el-button
            type="success"
            @click="handleMarkRead(currentMessage)"
            v-if="!currentMessage.read"
          >
            标记已读
          </el-button>
          <el-button type="danger" @click="handleDelete(currentMessage)">
            删除留言
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Check,
  Delete,
  Download,
  View
} from '@element-plus/icons-vue'
import CustomTable from '@/components/CustomTable.vue'
import { messageApi } from '@/api/message'
import { formatTime } from '@/utils/common'

const loading = ref(false)
const tableData = ref([])
const detailVisible = ref(false)
const currentMessage = ref(null)
const selectedRows = ref([])

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const searchParams = reactive({
  content: '',
  read: '',
  startDate: '',
  endDate: ''
})

// 表格列配置
const columns = [
  { prop: 'user', label: '用户', width: 120, slot: true },
  { prop: 'content', label: '内容', minWidth: 200, slot: true },
  { prop: 'read', label: '状态', width: 80, slot: true },
  {
    prop: 'createdAt',
    label: '创建时间',
    width: 150,
    formatter: (row) => formatTime(row.createdAt, 'YYYY-MM-DD HH:mm')
  },
  { label: '操作', width: 180, slot: 'operation', fixed: 'right' }
]

// 搜索字段配置
const searchFields = [
  {
    prop: 'content',
    label: '留言内容',
    type: 'input',
    placeholder: '请输入留言内容'
  },
  {
    prop: 'read',
    label: '状态',
    type: 'select',
    placeholder: '选择状态',
    options: [
      { label: '已读', value: true },
      { label: '未读', value: false }
    ]
  },
  {
    prop: 'startDate',
    label: '开始日期',
    type: 'date',
    placeholder: '选择开始日期'
  },
  {
    prop: 'endDate',
    label: '结束日期',
    type: 'date',
    placeholder: '选择结束日期'
  }
]

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }

    const response = await messageApi.getMessages(params)
    if (response.data) {
      tableData.value = response.data.list || response.data
      pagination.total = response.data.total || response.data.length
    }
  } catch (error) {
    console.error('加载留言失败:', error)
    ElMessage.error('加载数据失败')
    // 使用模拟数据
    tableData.value = [
      {
        id: '1',
        content: '今天的菜很好吃！',
        read: false,
        user: { name: '张三', avatar: '' },
        createdAt: new Date()
      },
      {
        id: '2',
        content: '希望明天能有红烧肉',
        read: true,
        user: { name: '李四', avatar: '' },
        createdAt: new Date()
      }
    ]
    pagination.total = 2
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = (params) => {
  Object.assign(searchParams, params)
  pagination.page = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    searchParams[key] = ''
  })
  pagination.page = 1
  loadData()
}

// 分页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 查看详情
const handleView = (row) => {
  currentMessage.value = row
  detailVisible.value = true
}

// 标记已读
const handleMarkRead = async (row) => {
  try {
    await messageApi.markAsRead(row.id)
    ElMessage.success('标记已读成功')
    row.read = true
    if (detailVisible.value && currentMessage.value?.id === row.id) {
      currentMessage.value.read = true
    }
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 删除留言
const handleDelete = async (row) => {
  try {
    await messageApi.deleteMessage(row.id)
    ElMessage.success('删除成功')
    if (detailVisible.value && currentMessage.value?.id === row.id) {
      detailVisible.value = false
    }
    loadData()
  } catch (error) {
    console.error('删除留言失败:', error)
    ElMessage.error('删除失败')
  }
}

// 批量标记已读
const handleBatchMarkRead = async () => {
  try {
    const messageIds = selectedRows.value.map(row => row.id)
    await messageApi.batchMarkAsRead({ messageIds })
    ElMessage.success('批量标记已读成功')
    loadData()
  } catch (error) {
    console.error('批量标记已读失败:', error)
    ElMessage.error('批量操作失败')
  }
}

// 批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的留言吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const messageIds = selectedRows.value.map(row => row.id)
    await messageApi.batchDeleteMessages({ messageIds })
    ElMessage.success('批量删除成功')
    loadData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 导出数据
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

onMounted(() => {
  loadData()
})
</script>
