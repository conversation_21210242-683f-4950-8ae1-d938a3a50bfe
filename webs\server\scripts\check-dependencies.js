#!/usr/bin/env node
/**
 * 依赖检查脚本
 * 确保所有必需的依赖都已正确安装
 */

const fs = require('fs');
const { execSync } = require('child_process');

// 关键依赖列表
const CRITICAL_DEPENDENCIES = [
  'express',
  'prisma',
  '@prisma/client',
  'jsonwebtoken',
  'bcryptjs',
  'cors',
  'dotenv',
  'multer',
  'mysql2',
  'node-cron',
  'node-fetch'
];

console.log('🔍 检查关键依赖...');

let allDependenciesOk = true;

// 检查package.json
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`📦 项目: ${packageJson.name} v${packageJson.version}`);
  
  // 检查每个关键依赖
  CRITICAL_DEPENDENCIES.forEach(dep => {
    const inDependencies = packageJson.dependencies && packageJson.dependencies[dep];
    const inDevDependencies = packageJson.devDependencies && packageJson.devDependencies[dep];
    
    if (inDependencies) {
      console.log(`✅ ${dep}: ${inDependencies} (dependencies)`);
    } else if (inDevDependencies) {
      console.log(`⚠️  ${dep}: ${inDevDependencies} (devDependencies - 可能导致生产环境问题)`);
    } else {
      console.log(`❌ ${dep}: 未在package.json中找到`);
      allDependenciesOk = false;
    }
  });
  
} catch (error) {
  console.error('❌ 无法读取package.json:', error.message);
  process.exit(1);
}

// 检查node_modules中的实际安装情况
console.log('\n🔍 检查实际安装情况...');

CRITICAL_DEPENDENCIES.forEach(dep => {
  try {
    require.resolve(dep);
    console.log(`✅ ${dep}: 已安装`);
  } catch (error) {
    console.log(`❌ ${dep}: 未安装或无法加载`);
    allDependenciesOk = false;
  }
});

// 检查Node.js和npm版本
console.log('\n🔍 环境信息...');
try {
  const nodeVersion = process.version;
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`Node.js: ${nodeVersion}`);
  console.log(`npm: ${npmVersion}`);
} catch (error) {
  console.log('⚠️ 无法获取npm版本');
}

// 总结
console.log('\n' + '='.repeat(50));
if (allDependenciesOk) {
  console.log('🎉 所有关键依赖检查通过！');
  process.exit(0);
} else {
  console.log('❌ 发现依赖问题，请运行以下命令修复：');
  console.log('   npm install');
  console.log('   或者删除node_modules重新安装：');
  console.log('   rm -rf node_modules package-lock.json && npm install');
  process.exit(1);
}
