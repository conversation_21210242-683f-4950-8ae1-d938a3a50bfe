var V=(b,t,u)=>new Promise((s,M)=>{var C=m=>{try{f(u.next(m))}catch(h){M(h)}},v=m=>{try{f(u.throw(m))}catch(h){M(h)}},f=m=>m.done?s(m.value):Promise.resolve(m.value).then(C,v);f((u=u.apply(b,t)).next())});import{_ as N,r as y,c as _,b as D,a as e,d as o,w as d,t as i,p as j,H as x,I as E,g as k,G as R,q as w,o as S,F as L,h as W,L as I,R as O,S as U,O as q,k as z,E as p,l as c,m as Y}from"./index-XtNpSMFt.js";import{m as A,d as G}from"./menu-DNv9gB1J.js";const H={__name:"today",setup(b,{expose:t}){t();const u=k(R().format("YYYY-MM-DD")),s=k([]),M=k(!1),C=k(!1),v=w(()=>R().format("YYYY年MM月DD日 dddd")),f=w(()=>s.value.reduce((a,l)=>a+(parseFloat(l.price)||0),0)),m=w(()=>new Set(s.value.map(l=>l.category)).size),h=w(()=>u.value===R().format("YYYY-MM-DD")),B=w(()=>{const a=new Map;return s.value.forEach(l=>{a.has(l.category)||a.set(l.category,{name:l.category,dishes:[]}),a.get(l.category).dishes.push(l)}),Array.from(a.values())}),g=()=>V(this,null,function*(){try{const a=yield A.getTodayMenu(u.value);a.code===200&&a.data&&a.data.dishes?s.value=a.data.dishes:s.value=[]}catch(a){console.error("加载今日菜单失败:",a),s.value=[]}}),T=a=>V(this,null,function*(){try{yield z.confirm(`确定要从今日菜单中移除"${a.name}"吗？`,"确认移除",{type:"warning"});const l=yield A.removeDishFromMenu(u.value,a.id);l.code===200?(p.success("移除成功"),g()):p.error(l.message||"移除失败")}catch(l){l!=="cancel"&&(console.error("移除菜品失败:",l),p.error("移除失败"))}}),r=a=>{g()},n=()=>{g()},F=()=>V(this,null,function*(){if(s.value.length===0){p.warning("当前没有菜单可清空");return}try{yield z.confirm("确定要清空今日菜单吗？此操作不可恢复！","确认清空",{type:"warning"});const a=yield A.clearTodayMenu(u.value);a.code===200?(p.success("菜单已清空"),g()):p.error(a.message||"清空失败")}catch(a){a!=="cancel"&&(console.error("清空菜单失败:",a),p.error("清空失败"))}});S(()=>{g()});const P={selectedDate:u,menuDishes:s,saveLoading:M,previewVisible:C,currentDate:v,totalPrice:f,categoryCount:m,isToday:h,categoriesWithDishes:B,loadTodayMenu:g,handleRemoveDish:T,handleDateChange:r,handleRefresh:n,handleClearAll:F,ref:k,reactive:W,computed:w,onMounted:S,watch:L,get ElMessage(){return p},get ElMessageBox(){return z},get Delete(){return q},get Calendar(){return U},get Refresh(){return O},get Picture(){return I},get dishApi(){return G},get menuApi(){return A},get dayjs(){return R}};return Object.defineProperty(P,"__isScriptSetup",{enumerable:!1,value:!0}),P}},J={class:"today-menu"},K={class:"page-header"},Q={class:"header-content"},X={class:"header-info"},Z={class:"date-info"},$={class:"header-stats"},ee={class:"stat-card"},se={class:"stat-number"},te={class:"stat-card"},ae={class:"stat-number"},oe={class:"stat-card"},ne={class:"stat-number"},le={class:"action-bar"},re={class:"action-left"},ie={class:"action-right"},de={class:"menu-display"},ce={key:0,class:"empty-menu"},_e={key:1,class:"menu-content"},ue={class:"category-header"},me={class:"category-title"},he={class:"category-count"},ve={class:"dishes-grid"},ge={class:"dish-image"},pe={class:"image-slot"},fe={class:"dish-info"},ye={class:"dish-name"},De={key:0,class:"dish-description"},we={class:"dish-meta"},Me={class:"dish-price"},ke={class:"menu-preview"},Ye={class:"preview-header"},be={class:"preview-categories"},Ce={class:"category-title"},Ve={class:"category-dishes"},xe={class:"preview-dish-info"};function Ee(b,t,u,s,M,C){const v=y("el-icon"),f=y("el-date-picker"),m=y("el-tag"),h=y("el-button"),B=y("el-empty"),g=y("el-image"),T=y("el-dialog");return c(),_("div",J,[D(" 页面头部 "),e("div",K,[e("div",Q,[e("div",X,[t[2]||(t[2]=e("h1",{class:"page-title"},"今日菜单",-1)),t[3]||(t[3]=e("p",{class:"page-subtitle"},"查看和管理小程序用户看到的今日菜单",-1)),e("div",Z,[o(v,null,{default:d(()=>[o(s.Calendar)]),_:1}),e("span",null,i(s.currentDate),1)])]),e("div",$,[e("div",ee,[e("div",se,i(s.menuDishes.length),1),t[4]||(t[4]=e("div",{class:"stat-label"},"菜品数量",-1))]),e("div",te,[e("div",ae,i(s.totalPrice.toFixed(2)),1),t[5]||(t[5]=e("div",{class:"stat-label"},"总价值(¥)",-1))]),e("div",oe,[e("div",ne,i(s.categoryCount),1),t[6]||(t[6]=e("div",{class:"stat-label"},"涵盖分类",-1))])])])]),D(" 操作栏 "),e("div",le,[e("div",re,[o(f,{modelValue:s.selectedDate,"onUpdate:modelValue":t[0]||(t[0]=r=>s.selectedDate=r),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",onChange:s.handleDateChange,size:"large"},null,8,["modelValue"]),s.isToday?(c(),j(m,{key:0,type:"success",class:"today-tag"},{default:d(()=>t[7]||(t[7]=[Y("今天",-1)])),_:1,__:[7]})):D("v-if",!0)]),e("div",ie,[o(h,{onClick:s.handleRefresh,size:"large"},{default:d(()=>[o(v,null,{default:d(()=>[o(s.Refresh)]),_:1}),t[8]||(t[8]=Y(" 刷新 ",-1))]),_:1,__:[8]}),o(h,{onClick:s.handleClearAll,type:"danger",plain:"",size:"large",disabled:s.menuDishes.length===0},{default:d(()=>[o(v,null,{default:d(()=>[o(s.Delete)]),_:1}),t[9]||(t[9]=Y(" 清空菜单 ",-1))]),_:1,__:[9]},8,["disabled"])])]),D(" 菜单展示区域 "),e("div",de,[s.menuDishes.length===0?(c(),_("div",ce,[o(B,{description:"今日暂无菜单"},{default:d(()=>[o(h,{type:"primary",onClick:s.handleRefresh},{default:d(()=>t[10]||(t[10]=[Y("刷新数据",-1)])),_:1,__:[10]})]),_:1})])):(c(),_("div",_e,[D(" 分类展示 "),(c(!0),_(x,null,E(s.categoriesWithDishes,r=>(c(),_("div",{key:r.name,class:"category-section"},[e("div",ue,[e("h3",me,i(r.name),1),e("span",he,i(r.dishes.length)+"道菜",1)]),e("div",ve,[(c(!0),_(x,null,E(r.dishes,n=>(c(),_("div",{key:n.id,class:"dish-card"},[e("div",ge,[o(g,{src:n.image,alt:n.name,fit:"cover"},{error:d(()=>[e("div",pe,[o(v,null,{default:d(()=>[o(s.Picture)]),_:1})])]),_:2},1032,["src","alt"])]),e("div",fe,[e("h4",ye,i(n.name),1),n.description?(c(),_("p",De,i(n.description),1)):D("v-if",!0),e("div",we,[e("span",Me,"¥"+i(n.price||"0"),1),o(h,{type:"danger",size:"small",plain:"",onClick:F=>s.handleRemoveDish(n)},{default:d(()=>[o(v,null,{default:d(()=>[o(s.Delete)]),_:1}),t[11]||(t[11]=Y(" 移除 ",-1))]),_:2,__:[11]},1032,["onClick"])])])]))),128))])]))),128))]))]),D(" 菜单预览对话框 "),o(T,{modelValue:s.previewVisible,"onUpdate:modelValue":t[1]||(t[1]=r=>s.previewVisible=r),title:"菜单预览",width:"800px"},{default:d(()=>[e("div",ke,[e("div",Ye,[e("h2",null,i(s.selectedDate)+" 菜单",1),e("p",null,"共 "+i(b.selectedDishes.length)+" 道菜",1)]),e("div",be,[(c(!0),_(x,null,E(s.categoriesWithDishes,r=>(c(),_("div",{key:r.key,class:"preview-category"},[e("h3",Ce,i(r.name),1),e("div",Ve,[(c(!0),_(x,null,E(r.dishes,n=>(c(),_("div",{key:n.id,class:"preview-dish"},[o(g,{src:n.image,class:"preview-dish-image",fit:"cover"},null,8,["src"]),e("div",xe,[e("h4",null,i(n.name),1),e("p",null,"¥"+i(n.price),1)])]))),128))])]))),128))])])]),_:1},8,["modelValue"])])}const Te=N(H,[["render",Ee],["__scopeId","data-v-1ec7977d"],["__file","E:/wx-nan/webs/admin/src/views/menu/today.vue"]]);export{Te as default};
