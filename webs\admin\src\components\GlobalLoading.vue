<template>
  <Teleport to="body">
    <Transition name="loading-fade">
      <div
        v-if="visible"
        class="global-loading-overlay"
        :class="{ 'loading-blur': blur }"
      >
        <div class="loading-container">
          <div class="loading-spinner">
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
            <div class="spinner-ring"></div>
          </div>
          <div v-if="text" class="loading-text">{{ text }}</div>
          <div v-if="tip" class="loading-tip">{{ tip }}</div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  text: {
    type: String,
    default: '加载中...'
  },
  tip: {
    type: String,
    default: ''
  },
  blur: {
    type: Boolean,
    default: true
  }
})
</script>

<style scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-blur {
  backdrop-filter: blur(4px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
  margin-bottom: 1rem;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
}

.spinner-ring:nth-child(1) {
  animation-delay: -0.45s;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.3s;
}

.spinner-ring:nth-child(3) {
  animation-delay: -0.15s;
}

.loading-text {
  font-size: 16px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.loading-tip {
  font-size: 14px;
  color: #6b7280;
  text-align: center;
  max-width: 200px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-fade-enter-active,
.loading-fade-leave-active {
  transition: all 0.3s ease;
}

.loading-fade-enter-from,
.loading-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.loading-fade-enter-to,
.loading-fade-leave-from {
  opacity: 1;
  transform: scale(1);
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .global-loading-overlay {
    background: rgba(17, 24, 39, 0.8);
  }
  
  .loading-container {
    background: #1f2937;
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .spinner-ring {
    border-top-color: #60a5fa;
  }
  
  .loading-text {
    color: #f3f4f6;
  }
  
  .loading-tip {
    color: #9ca3af;
  }
}
</style>
