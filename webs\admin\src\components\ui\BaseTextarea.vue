<template>
  <div class="space-y-1">
    <label 
      v-if="label" 
      :for="textareaId"
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {{ label }}
      <span v-if="required" class="text-error-500 ml-1">*</span>
    </label>
    
    <div class="relative">
      <textarea
        :id="textareaId"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :rows="rows"
        :maxlength="maxLength"
        :class="textareaClasses"
        v-bind="$attrs"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      ></textarea>
      
      <!-- 字符计数 -->
      <div 
        v-if="showCount && maxLength"
        class="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 px-1 rounded"
      >
        {{ currentLength }}/{{ maxLength }}
      </div>
    </div>
    
    <!-- 帮助文本 -->
    <p 
      v-if="helpText" 
      class="text-sm text-gray-500 dark:text-gray-400"
    >
      {{ helpText }}
    </p>
    
    <!-- 错误信息 -->
    <p 
      v-if="error" 
      class="text-sm text-error-600 dark:text-error-400"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  rows: {
    type: Number,
    default: 4
  },
  maxLength: {
    type: Number,
    default: null
  },
  showCount: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  resize: {
    type: String,
    default: 'vertical',
    validator: (value) => ['none', 'both', 'horizontal', 'vertical'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue', 'blur', 'focus'])

const textareaId = ref(`textarea-${Math.random().toString(36).substr(2, 9)}`)

const textareaClasses = computed(() => {
  const baseClasses = [
    'block w-full rounded-lg border-0 shadow-sm ring-1 ring-inset transition-colors duration-200',
    'placeholder:text-gray-400 focus:ring-2 focus:ring-inset',
    'disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200',
    'dark:bg-gray-900 dark:text-white dark:ring-gray-700 dark:placeholder:text-gray-500',
    'dark:focus:ring-primary-500 dark:disabled:bg-gray-800 dark:disabled:text-gray-400'
  ]

  // 尺寸样式
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  // 状态样式
  const stateClasses = [
    'bg-white ring-gray-300 focus:ring-primary-600',
    props.error ? 'ring-error-300 focus:ring-error-600' : ''
  ]

  // 调整大小样式
  const resizeClasses = {
    none: 'resize-none',
    both: 'resize',
    horizontal: 'resize-x',
    vertical: 'resize-y'
  }

  return [
    ...baseClasses,
    sizeClasses[props.size],
    ...stateClasses,
    resizeClasses[props.resize]
  ].filter(Boolean).join(' ')
})

const currentLength = computed(() => {
  return props.modelValue ? props.modelValue.length : 0
})

const handleInput = (event) => {
  emit('update:modelValue', event.target.value)
}

const handleBlur = (event) => {
  emit('blur', event)
}

const handleFocus = (event) => {
  emit('focus', event)
}
</script>
