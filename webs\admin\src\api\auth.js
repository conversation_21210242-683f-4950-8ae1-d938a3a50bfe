import request from '@/utils/request';

/**
 * 认证相关 API
 */
export const authApi = {
  /**
   * 用户登录
   * @param {Object} data - 登录数据
   * @returns {Promise} 登录结果
   */
  login(data) {
    return request({
      url: '/auth/login',
      method: 'POST',
      data
    });
  },

  /**
   * 用户注册
   * @param {Object} data - 注册数据
   * @returns {Promise} 注册结果
   */
  register(data) {
    return request({
      url: '/auth/register',
      method: 'POST',
      data
    });
  },

  /**
   * 管理员注册
   * @param {Object} data - 注册数据
   * @returns {Promise} 注册结果
   */
  adminRegister(data) {
    return request({
      url: '/auth/admin-register',
      method: 'POST',
      data
    });
  },

  /**
   * 用户登出
   * @returns {Promise} 登出结果
   */
  logout() {
    return request({
      url: '/auth/logout',
      method: 'POST'
    });
  },

  /**
   * 刷新token
   * @param {string} refreshToken - 刷新token
   * @returns {Promise} 新的token
   */
  refreshToken(refreshToken) {
    return request({
      url: '/auth/refresh-token',
      method: 'POST',
      data: {refreshToken}
    });
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} 用户信息
   */
  getCurrentUser() {
    return request({
      url: '/auth/me',
      method: 'GET'
    });
  },

  /**
   * 发送邮箱验证码
   * @param {string} email - 邮箱地址
   * @param {string} type - 验证码类型 (register, reset-password)
   * @returns {Promise} 发送结果
   */
  sendEmailCode(email, type = 'register') {
    return request({
      url: '/auth/send-email-code',
      method: 'POST',
      data: {email, type}
    });
  },

  /**
   * 发送手机验证码
   * @param {string} phone - 手机号
   * @param {string} type - 验证码类型 (register, reset-password)
   * @returns {Promise} 发送结果
   */
  sendSmsCode(phone, type = 'register') {
    return request({
      url: '/auth/send-sms-code',
      method: 'POST',
      data: {phone, type}
    });
  },

  /**
   * 验证邮箱验证码
   * @param {string} email - 邮箱地址
   * @param {string} code - 验证码
   * @param {string} type - 验证码类型
   * @returns {Promise} 验证结果
   */
  verifyEmailCode(email, code, type = 'register') {
    return request({
      url: '/auth/verify-email-code',
      method: 'POST',
      data: {email, code, type}
    });
  },

  /**
   * 验证手机验证码
   * @param {string} phone - 手机号
   * @param {string} code - 验证码
   * @param {string} type - 验证码类型
   * @returns {Promise} 验证结果
   */
  verifySmsCode(phone, code, type = 'register') {
    return request({
      url: '/auth/verify-sms-code',
      method: 'POST',
      data: {phone, code, type}
    });
  },

  /**
   * 重置密码 - 发送重置链接
   * @param {string} email - 邮箱地址
   * @returns {Promise} 发送结果
   */
  sendResetPasswordEmail(email) {
    return request({
      url: '/auth/send-reset-password-email',
      method: 'POST',
      data: {email}
    });
  },

  /**
   * 重置密码 - 验证重置token
   * @param {string} token - 重置token
   * @returns {Promise} 验证结果
   */
  verifyResetPasswordToken(token) {
    return request({
      url: '/auth/verify-reset-password-token',
      method: 'POST',
      data: {token}
    });
  },

  /**
   * 重置密码 - 设置新密码
   * @param {string} token - 重置token
   * @param {string} password - 新密码
   * @returns {Promise} 重置结果
   */
  resetPassword(token, password) {
    return request({
      url: '/auth/reset-password',
      method: 'POST',
      data: {token, password}
    });
  },

  /**
   * 通过验证码重置密码
   * @param {string} email - 邮箱地址
   * @param {string} code - 验证码
   * @param {string} password - 新密码
   * @returns {Promise} 重置结果
   */
  resetPasswordWithCode(email, code, password) {
    return request({
      url: '/auth/reset-password-with-code',
      method: 'POST',
      data: {email, code, password}
    });
  },

  /**
   * 修改密码
   * @param {string} oldPassword - 旧密码
   * @param {string} newPassword - 新密码
   * @returns {Promise} 修改结果
   */
  changePassword(oldPassword, newPassword) {
    return request({
      url: '/auth/change-password',
      method: 'POST',
      data: {oldPassword, newPassword}
    });
  },

  /**
   * 验证邀请码
   * @param {string} inviteCode - 邀请码
   * @returns {Promise} 验证结果
   */
  verifyInviteCode(inviteCode) {
    return request({
      url: '/auth/verify-invite-code',
      method: 'POST',
      data: {inviteCode}
    });
  },

  /**
   * 检查用户名是否可用
   * @param {string} username - 用户名
   * @returns {Promise} 检查结果
   */
  checkUsername(username) {
    return request({
      url: '/auth/check-username',
      method: 'POST',
      data: {username}
    });
  },

  /**
   * 检查邮箱是否可用
   * @param {string} email - 邮箱地址
   * @returns {Promise} 检查结果
   */
  checkEmail(email) {
    return request({
      url: '/auth/check-email',
      method: 'POST',
      data: {email}
    });
  },

  /**
   * 检查手机号是否可用
   * @param {string} phone - 手机号
   * @returns {Promise} 检查结果
   */
  checkPhone(phone) {
    return request({
      url: '/auth/check-phone',
      method: 'POST',
      data: {phone}
    });
  }
};

export default authApi;
