import {http} from '@/utils/request';

// 菜品相关API
export const dishApi = {
  // 获取菜品列表
  getDishes: params => http.get('/dishes', {params}),

  // 获取菜品详情
  getDishDetail: id => http.get(`/dishes/${id}`),

  // 创建菜品
  createDish: data => http.post('/dishes', data),

  // 更新菜品
  updateDish: (id, data) => http.put(`/dishes/${id}`, data),

  // 删除菜品
  deleteDish: id => http.delete(`/dishes/${id}`),

  // 获取菜品分类
  getCategories: () => http.get('/dishes/categories'),

  // 创建菜品分类
  createCategory: data => http.post('/dishes/categories', data),

  // 更新菜品分类
  updateCategory: (id, data) => http.put(`/dishes/categories/${id}`, data),

  // 删除菜品分类
  deleteCategory: id => http.delete(`/dishes/categories/${id}`),

  // 获取热门菜品
  getHotDishes: params => http.get('/dishes/hot', {params}),

  // 获取菜品统计
  getDishStatistics: () => http.get('/dishes/statistics'),

  // 获取菜品分析数据
  getDishAnalytics: params => http.get('/dishes/analytics', {params}),

  // 导出菜品
  exportDishes: params => http.get('/dishes/export', {params}),

  // 批量操作菜品
  batchOperation: data => http.post('/dishes/batch', data),

  // 获取分类菜品
  getDishesByCategory: () => http.get('/dishes/by-category')
};

// 菜单相关API
export const menuApi = {
  // 获取菜单列表
  getMenus: params => http.get('/menus', {params}),

  // 获取今日菜单
  getTodayMenu: date => http.get('/menus/today', {params: {date}}),

  // 获取历史菜单
  getHistoryMenus: params => http.get('/menus/history', {params}),

  // 创建菜单
  createMenu: data => http.post('/menus', data),

  // 更新菜单
  updateMenu: (id, data) => http.put(`/menus/${id}`, data),

  // 删除菜单
  deleteMenu: id => http.delete(`/menus/${id}`),

  // 从菜单中移除菜品
  removeDishFromMenu: (date, dishId) =>
    http.delete(`/menus/today/dishes/${dishId}`, {params: {date}}),

  // 清空今日菜单
  clearTodayMenu: date => http.delete('/menus/today', {params: {date}}),

  // 获取菜单统计
  getStatistics: () => http.get('/menus/statistics')
};

// 分类相关API
export const categoryApi = {
  // 获取分类列表
  getCategories: params => http.get('/dishes/categories', params),

  // 创建分类
  createCategory: data => http.post('/dishes/categories', data),

  // 更新分类
  updateCategory: (id, data) => http.put(`/dishes/categories/${id}`, data),

  // 删除分类
  deleteCategory: id => http.delete(`/dishes/categories/${id}`)
};

// 修复dishApi中的分类相关方法
dishApi.getCategories = params => {
  // 如果没有参数，直接调用
  if (!params || Object.keys(params).length === 0) {
    return http.get('/dishes/categories');
  }
  // 有参数时，使用查询字符串
  const queryString = new URLSearchParams(params).toString();
  return http.get(`/dishes/categories?${queryString}`);
};
dishApi.createCategory = data => http.post('/dishes/categories', data);
dishApi.updateCategory = (id, data) =>
  http.put(`/dishes/categories/${id}`, data);
dishApi.deleteCategory = id => http.delete(`/dishes/categories/${id}`);
