const {connectionApi} = require('../../services/api');

Page({
  data: {
    historyList: [],
    loading: false,
    loadingMore: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 20,

    // 筛选条件
    filterStatus: 'all', // all, pending, accepted, rejected
    filterType: 'all', // all, sent, received

    // 统计信息
    statistics: {
      total: 0,
      pending: 0,
      accepted: 0,
      rejected: 0,
      sent: 0,
      received: 0
    },

    showFilterSheet: false
  },

  onLoad() {
    this.loadHistoryList();
    this.loadStatistics();
  },

  onPullDownRefresh() {
    this.refreshHistoryList();
  },

  onReachBottom() {
    // 移除自动触发，改为通过组件手动触发
    // if (this.data.hasMore && !this.data.loading) {
    //   this.loadMoreHistory();
    // }
  },

  /**
   * 加载历史记录列表
   */
  async loadHistoryList() {
    try {
      this.setData({loading: true});

      const params = {
        page: 1,
        size: this.data.pageSize,
        status:
          this.data.filterStatus === 'all' ? undefined : this.data.filterStatus,
        type: this.data.filterType === 'all' ? undefined : this.data.filterType
      };

      const res = await connectionApi.getConnectionHistory(params);

      if (res.code === 200) {
        const historyList = res.data.list.map(item => ({
          ...item,
          formattedTime: this.formatTime(item.createdAt),
          isSender: item.senderId === wx.getStorageSync('userInfo')?.id
        }));

        this.setData({
          historyList,
          hasMore: res.data.hasNext,
          page: 1
        });
      } else {
        wx.showToast({
          title: res.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('Load history error:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'none'
      });
    } finally {
      this.setData({loading: false});
    }
  },

  /**
   * 刷新历史记录
   */
  async refreshHistoryList() {
    try {
      this.setData({refreshing: true});
      await this.loadHistoryList();
      await this.loadStatistics();
      wx.stopPullDownRefresh();
    } catch (error) {
      wx.stopPullDownRefresh();
    } finally {
      this.setData({refreshing: false});
    }
  },

  /**
   * 加载更多历史记录
   */
  async loadMoreHistory() {
    try {
      this.setData({loadingMore: true});

      const nextPage = this.data.page + 1;
      const params = {
        page: nextPage,
        size: this.data.pageSize,
        status:
          this.data.filterStatus === 'all' ? undefined : this.data.filterStatus,
        type: this.data.filterType === 'all' ? undefined : this.data.filterType
      };

      const res = await connectionApi.getConnectionHistory(params);

      if (res.code === 200) {
        const newHistoryList = res.data.list.map(item => ({
          ...item,
          formattedTime: this.formatTime(item.createdAt),
          isSender: item.senderId === wx.getStorageSync('userInfo')?.id
        }));

        this.setData({
          historyList: [...this.data.historyList, ...newHistoryList],
          hasMore: res.data.hasNext,
          page: nextPage
        });
      }
    } catch (error) {
      console.error('Load more history error:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({loadingMore: false});
    }
  },

  /**
   * 加载统计信息
   */
  async loadStatistics() {
    try {
      const res = await connectionApi.getConnectionStatistics();

      if (res.code === 200) {
        this.setData({
          statistics: res.data
        });
      }
    } catch (error) {
      console.error('Load statistics error:', error);
    }
  },

  /**
   * 显示筛选面板
   */
  showFilterSheet() {
    this.setData({showFilterSheet: true});
  },

  /**
   * 隐藏筛选面板
   */
  hideFilterSheet() {
    this.setData({showFilterSheet: false});
  },

  /**
   * 筛选状态改变
   */
  onFilterStatusChange(event) {
    const status = event.currentTarget.dataset.status;
    this.setData({
      filterStatus: status,
      showFilterSheet: false
    });
    this.loadHistoryList();
  },

  /**
   * 筛选类型改变
   */
  onFilterTypeChange(event) {
    const type = event.currentTarget.dataset.type;
    this.setData({
      filterType: type,
      showFilterSheet: false
    });
    this.loadHistoryList();
  },

  /**
   * 重新发送申请
   */
  async resendRequest(event) {
    const {id, receiverId, receiverName} = event.currentTarget.dataset;

    try {
      wx.showModal({
        title: '重新发送申请',
        content: `确定要向 ${receiverName} 重新发送关联申请吗？`,
        success: async res => {
          if (res.confirm) {
            wx.showLoading({title: '发送中...'});

            const result = await connectionApi.sendConnectionRequest({
              receiverId,
              message: '希望与您建立关联'
            });

            wx.hideLoading();

            if (result.code === 200) {
              wx.showToast({
                title: '申请已发送',
                icon: 'success'
              });
              this.refreshHistoryList();
            } else {
              wx.showToast({
                title: result.message || '发送失败',
                icon: 'none'
              });
            }
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '发送失败',
        icon: 'none'
      });
    }
  },

  /**
   * 查看详情
   */
  viewDetail(event) {
    const {id} = event.currentTarget.dataset;
    const item = this.data.historyList.find(h => h.id === id);

    if (item) {
      wx.showModal({
        title: '申请详情',
        content: `申请消息：${item.message || '无'}\n申请时间：${
          item.formattedTime
        }\n状态：${this.getStatusText(item.status)}`,
        showCancel: false
      });
    }
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      pending: '等待处理',
      accepted: '已同意',
      rejected: '已拒绝'
    };
    return statusMap[status] || '未知';
  },

  /**
   * 格式化时间
   */
  formatTime(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now - date;

    if (diff < 60000) {
      return '刚刚';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`;
    } else if (diff < 604800000) {
      return `${Math.floor(diff / 86400000)}天前`;
    } else {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    }
  },

  /**
   * 清除状态筛选
   */
  clearStatusFilter() {
    this.setData({
      filterStatus: 'all'
    });
    this.loadHistoryList();
  },

  /**
   * 清除类型筛选
   */
  clearTypeFilter() {
    this.setData({
      filterType: 'all'
    });
    this.loadHistoryList();
  },

  /**
   * 关联状态点击处理
   */
  onConnectionStatusClick(event) {
    const {status, isSender} = event.detail;

    let message = '';
    switch (status) {
      case 'pending':
        message = isSender ? '申请已发送，等待对方确认' : '申请待处理';
        break;
      case 'accepted':
        message = '申请已被同意';
        break;
      case 'rejected':
        message = '申请已被拒绝';
        break;
      default:
        message = '未知状态';
    }

    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
});
