/**
 * 服务器配置
 * 参照小程序配置方式，支持快速切换不同环境
 */

// 当前使用的服务器（可以快速切换）
const CURRENT_SERVER = 'dev'; // dev | test | production

// 服务器配置
const SERVER_CONFIG = {
  // 开发环境
  dev: {
    name: '开发环境',
    baseURL: 'http://localhost:3000/api',
    timeout: 10000,
    description: '本地开发服务器'
  },

  // 测试环境
  test: {
    name: '测试环境',
    baseURL: 'http://test-api.nannan-kitchen.com/api',
    timeout: 15000,
    description: '测试服务器'
  },

  // 生产环境
  production: {
    name: '生产环境',
    baseURL: 'https://api.nannan-kitchen.com/api',
    timeout: 20000,
    description: '生产服务器'
  }
};

/**
 * 获取当前服务器配置
 * @returns {Object} 当前服务器配置
 */
export function getCurrentServerConfig() {
  const config = SERVER_CONFIG[CURRENT_SERVER];

  if (!config) {
    console.error(`无效的服务器配置: ${CURRENT_SERVER}`);
    return SERVER_CONFIG.dev; // 默认返回开发环境
  }

  console.log(`当前使用服务器: ${config.name} (${config.baseURL})`);
  return config;
}

/**
 * 获取所有服务器配置
 * @returns {Object} 所有服务器配置
 */
export function getAllServerConfigs() {
  return SERVER_CONFIG;
}

/**
 * 获取当前服务器类型
 * @returns {string} 当前服务器类型
 */
export function getCurrentServerType() {
  return CURRENT_SERVER;
}

/**
 * 检查是否为生产环境
 * @returns {boolean} 是否为生产环境
 */
export function isProduction() {
  return CURRENT_SERVER === 'production';
}

/**
 * 检查是否为开发环境
 * @returns {boolean} 是否为开发环境
 */
export function isDevelopment() {
  return CURRENT_SERVER === 'dev';
}

/**
 * 检查是否为测试环境
 * @returns {boolean} 是否为测试环境
 */
export function isTest() {
  return CURRENT_SERVER === 'test';
}

/**
 * 获取 WebSocket 地址
 * @returns {string} WebSocket 地址
 */
export function getWebSocketURL() {
  const config = getCurrentServerConfig();
  return config.baseURL.replace(/^http/, 'ws').replace('/api', '/ws');
}

/**
 * 获取文件上传地址
 * @returns {string} 文件上传地址
 */
export function getUploadURL() {
  const config = getCurrentServerConfig();
  return `${config.baseURL}/upload`;
}

/**
 * 获取文件下载地址
 * @param {string} filename 文件名
 * @returns {string} 文件下载地址
 */
export function getDownloadURL(filename) {
  const config = getCurrentServerConfig();
  return `${config.baseURL}/download/${filename}`;
}

/**
 * 获取静态资源地址
 * @param {string} path 资源路径
 * @returns {string} 静态资源地址
 */
export function getStaticURL(path) {
  const config = getCurrentServerConfig();
  return `${config.baseURL}/static/${path}`;
}

// 导出当前配置（用于兼容旧代码）
export const serverConfig = getCurrentServerConfig();

// 默认导出
export default {
  getCurrentServerConfig,
  getAllServerConfigs,
  getCurrentServerType,
  isProduction,
  isDevelopment,
  isTest,
  getWebSocketURL,
  getUploadURL,
  getDownloadURL,
  getStaticURL,
  serverConfig,
  CURRENT_SERVER
};
