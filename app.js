const i18n = require('i18n/index');
import ui from './utils/ui.js';
App({
  onLaunch: function () {
    i18n.getLanguage();
    this.setTabBarLanguage();
    const $t = i18n.$t();
    const that = this;
    // 检测新版本
    const updateManager = wx.getUpdateManager();
    updateManager.onUpdateReady(function () {
      wx.showModal({
        confirmText: $t.common.confirm,
        cancelText: $t.common.cancel,
        content: $t.common.upgrade,
        success(res) {
          if (res.confirm) {
            // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
            updateManager.applyUpdate();
          }
        }
      });
    });
    /**
     * 初次加载判断网络情况
     * 无网络状态下根据实际情况进行调整
     */
    wx.getNetworkType({
      success(res) {
        const networkType = res.networkType;
        if (networkType === 'none') {
          that.globalData.isConnected = false;
          wx.showToast({
            title: $t.common.noNetwork,
            icon: 'loading'
          });
        }
      }
    });
    /**
     * 监听网络状态变化
     * 可根据业务需求进行调整
     */
    wx.onNetworkStatusChange(function (res) {
      if (!res.isConnected) {
        that.globalData.isConnected = false;
        wx.showToast({
          title: $t.common.networkDown,
          icon: 'loading'
        });
      } else {
        that.globalData.isConnected = true;
        wx.hideToast();
      }
    });
  },
  onShow(e) {},
  initLanguage(_this) {
    _this.setData({
      language: i18n.getLanguage(),
      $t: i18n.$t()
    });
  },
  changeLang(_this) {
    const langs = i18n.langs;
    const nameArray = [];
    langs.forEach(ele => nameArray.push(ele.name));
    wx.showActionSheet({
      itemList: nameArray,
      success: e => {
        const lang = langs[e.tapIndex];
        wx.setStorageSync('Language', lang.code);
        _this.setData({
          language: i18n.getLanguage(),
          $t: i18n.$t()
        });
        this.setTabBarLanguage();
      }
    });
  },
  setTabBarLanguage() {
    i18n.setTabBarLanguage();
  },
  globalData: {
    isConnected: true,
    // 全局Loading状态
    loading: {
      show: false,
      text: '',
      type: 'spinner',
      color: '#6366F1'
    }
  },

  // 全局 UI 工具
  ui: ui,

  /**
   * 全局Loading管理
   */
  loading: {
    // 显示Loading
    show: function (options = {}) {
      const app = getApp();
      const defaultOptions = {
        show: true,
        text: options.text || '',
        type: options.type || 'spinner',
        color: options.color || '#6366F1',
        mask: options.mask !== false, // 默认显示遮罩
        duration: options.duration || 0 // 0表示不自动隐藏
      };

      // 更新全局状态
      app.globalData.loading = {
        ...app.globalData.loading,
        ...defaultOptions
      };

      // 通知所有页面更新Loading状态
      this._notifyPages();

      // 如果设置了duration，自动隐藏
      if (defaultOptions.duration > 0) {
        setTimeout(() => {
          this.hide();
        }, defaultOptions.duration);
      }

      return this; // 支持链式调用
    },

    // 隐藏Loading
    hide: function () {
      const app = getApp();
      app.globalData.loading.show = false;

      // 通知所有页面更新Loading状态
      this._notifyPages();

      return this; // 支持链式调用
    },

    // 更新Loading文字
    setText: function (text) {
      const app = getApp();
      app.globalData.loading.text = text;
      this._notifyPages();
      return this;
    },

    // 通知所有页面更新Loading状态
    _notifyPages: function () {
      const pages = getCurrentPages();
      const app = getApp();

      pages.forEach(page => {
        if (page.setData && typeof page.setData === 'function') {
          page.setData({
            globalLoading: app.globalData.loading
          });
        }
      });
    }
  }
});
