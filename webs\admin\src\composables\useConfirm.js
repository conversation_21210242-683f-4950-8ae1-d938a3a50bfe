import { createApp, ref } from 'vue'
import BaseConfirm from '@/components/ui/BaseConfirm.vue'

/**
 * 确认对话框服务
 */
class ConfirmService {
  constructor() {
    this.container = null
    this.init()
  }

  init() {
    // 创建容器
    if (typeof document !== 'undefined') {
      this.container = document.createElement('div')
      this.container.id = 'confirm-container'
      document.body.appendChild(this.container)
    }
  }

  /**
   * 显示确认对话框
   * @param {Object} options - 配置选项
   * @returns {Promise} 返回用户选择的 Promise
   */
  show(options = {}) {
    return new Promise((resolve, reject) => {
      const defaultOptions = {
        type: 'warning',
        title: '确认操作',
        content: '确定要执行此操作吗？',
        confirmText: '确认',
        cancelText: '取消'
      }

      const finalOptions = { ...defaultOptions, ...options }

      // 创建响应式状态
      const visible = ref(true)
      const loading = ref(false)

      // 创建确认对话框组件
      const confirmApp = createApp(BaseConfirm, {
        modelValue: visible.value,
        ...finalOptions,
        loading: loading.value,
        'onUpdate:modelValue': (value) => {
          visible.value = value
          if (!value) {
            cleanup()
            reject(new Error('User cancelled'))
          }
        },
        onConfirm: async () => {
          try {
            loading.value = true
            
            // 如果有异步确认函数，等待执行
            if (typeof finalOptions.onConfirm === 'function') {
              await finalOptions.onConfirm()
            }
            
            visible.value = false
            cleanup()
            resolve(true)
          } catch (error) {
            loading.value = false
            // 如果确认函数抛出错误，不关闭对话框
            reject(error)
          }
        },
        onCancel: () => {
          visible.value = false
          cleanup()
          reject(new Error('User cancelled'))
        }
      })

      // 创建挂载点
      const mountPoint = document.createElement('div')
      this.container.appendChild(mountPoint)

      // 挂载组件
      const instance = confirmApp.mount(mountPoint)

      // 清理函数
      const cleanup = () => {
        setTimeout(() => {
          confirmApp.unmount()
          if (mountPoint && mountPoint.parentNode) {
            mountPoint.parentNode.removeChild(mountPoint)
          }
        }, 300) // 等待动画完成
      }
    })
  }

  /**
   * 警告确认
   * @param {string} content - 确认内容
   * @param {Object} options - 额外配置
   */
  warning(content, options = {}) {
    return this.show({
      type: 'warning',
      content,
      ...options
    })
  }

  /**
   * 信息确认
   * @param {string} content - 确认内容
   * @param {Object} options - 额外配置
   */
  info(content, options = {}) {
    return this.show({
      type: 'info',
      content,
      ...options
    })
  }

  /**
   * 成功确认
   * @param {string} content - 确认内容
   * @param {Object} options - 额外配置
   */
  success(content, options = {}) {
    return this.show({
      type: 'success',
      content,
      ...options
    })
  }

  /**
   * 错误确认
   * @param {string} content - 确认内容
   * @param {Object} options - 额外配置
   */
  error(content, options = {}) {
    return this.show({
      type: 'error',
      content,
      ...options
    })
  }

  /**
   * 删除确认
   * @param {string} itemName - 要删除的项目名称
   * @param {Object} options - 额外配置
   */
  delete(itemName = '此项', options = {}) {
    return this.show({
      type: 'error',
      title: '确认删除',
      content: `确定要删除${itemName}吗？此操作不可撤销。`,
      confirmText: '删除',
      ...options
    })
  }

  /**
   * 批量删除确认
   * @param {number} count - 删除数量
   * @param {Object} options - 额外配置
   */
  batchDelete(count, options = {}) {
    return this.show({
      type: 'error',
      title: '确认批量删除',
      content: `确定要删除选中的 ${count} 项吗？此操作不可撤销。`,
      confirmText: '删除',
      ...options
    })
  }

  /**
   * 保存确认
   * @param {string} content - 确认内容
   * @param {Object} options - 额外配置
   */
  save(content = '确定要保存当前修改吗？', options = {}) {
    return this.show({
      type: 'info',
      title: '确认保存',
      content,
      confirmText: '保存',
      ...options
    })
  }

  /**
   * 离开确认
   * @param {string} content - 确认内容
   * @param {Object} options - 额外配置
   */
  leave(content = '当前有未保存的修改，确定要离开吗？', options = {}) {
    return this.show({
      type: 'warning',
      title: '确认离开',
      content,
      confirmText: '离开',
      ...options
    })
  }
}

// 创建全局实例
const confirmService = new ConfirmService()

/**
 * useConfirm 组合式函数
 */
export function useConfirm() {
  return {
    confirm: confirmService.show.bind(confirmService),
    warning: confirmService.warning.bind(confirmService),
    info: confirmService.info.bind(confirmService),
    success: confirmService.success.bind(confirmService),
    error: confirmService.error.bind(confirmService),
    delete: confirmService.delete.bind(confirmService),
    batchDelete: confirmService.batchDelete.bind(confirmService),
    save: confirmService.save.bind(confirmService),
    leave: confirmService.leave.bind(confirmService)
  }
}

// 默认导出服务实例
export default confirmService
