<template>
  <div class="space-y-1">
    <label 
      v-if="label" 
      class="block text-sm font-medium text-gray-700 dark:text-gray-300"
    >
      {{ label }}
      <span v-if="required" class="text-error-500 ml-1">*</span>
    </label>
    
    <div
      :class="uploadAreaClasses"
      @click="handleClick"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @drop.prevent="handleDrop"
    >
      <input
        ref="fileInputRef"
        type="file"
        :accept="accept"
        :multiple="multiple"
        :disabled="disabled"
        class="hidden"
        @change="handleFileSelect"
      />
      
      <!-- 上传区域内容 -->
      <div v-if="!fileList.length || multiple" class="text-center">
        <component
          :is="uploadIcon"
          class="mx-auto h-12 w-12 text-gray-400"
          aria-hidden="true"
        />
        <div class="mt-4">
          <p class="text-sm text-gray-600 dark:text-gray-400">
            <span class="font-medium text-primary-600 hover:text-primary-500 cursor-pointer">
              点击上传
            </span>
            或拖拽文件到此处
          </p>
          <p class="text-xs text-gray-500 dark:text-gray-500 mt-1">
            {{ acceptText }}
          </p>
        </div>
      </div>
      
      <!-- 文件列表 -->
      <div v-if="fileList.length" class="space-y-2">
        <div
          v-for="(file, index) in fileList"
          :key="index"
          class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
        >
          <div class="flex items-center space-x-3">
            <!-- 文件预览 -->
            <div class="flex-shrink-0">
              <img
                v-if="file.type?.startsWith('image/') && file.preview"
                :src="file.preview"
                :alt="file.name"
                class="h-10 w-10 rounded object-cover"
              />
              <div
                v-else
                class="h-10 w-10 rounded bg-gray-200 dark:bg-gray-700 flex items-center justify-center"
              >
                <DocumentIcon class="h-6 w-6 text-gray-400" />
              </div>
            </div>
            
            <!-- 文件信息 -->
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                {{ file.name }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ formatFileSize(file.size) }}
              </p>
            </div>
          </div>
          
          <!-- 文件状态和操作 -->
          <div class="flex items-center space-x-2">
            <!-- 上传进度 -->
            <div v-if="file.status === 'uploading'" class="flex items-center space-x-2">
              <div class="w-16 bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                <div
                  class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${file.progress || 0}%` }"
                ></div>
              </div>
              <span class="text-xs text-gray-500">{{ file.progress || 0 }}%</span>
            </div>
            
            <!-- 上传成功 -->
            <CheckCircleIcon
              v-else-if="file.status === 'success'"
              class="h-5 w-5 text-green-500"
            />
            
            <!-- 上传失败 -->
            <ExclamationCircleIcon
              v-else-if="file.status === 'error'"
              class="h-5 w-5 text-red-500"
            />
            
            <!-- 删除按钮 -->
            <button
              type="button"
              @click.stop="handleRemove(index)"
              class="text-gray-400 hover:text-red-500 transition-colors duration-200"
            >
              <XMarkIcon class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 帮助文本 -->
    <p 
      v-if="helpText" 
      class="text-sm text-gray-500 dark:text-gray-400"
    >
      {{ helpText }}
    </p>
    
    <!-- 错误信息 -->
    <p 
      v-if="error" 
      class="text-sm text-error-600 dark:text-error-400"
    >
      {{ error }}
    </p>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  CloudArrowUpIcon,
  DocumentIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: ''
  },
  accept: {
    type: String,
    default: '*/*'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  maxSize: {
    type: Number,
    default: 10 * 1024 * 1024 // 10MB
  },
  maxFiles: {
    type: Number,
    default: 10
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  helpText: {
    type: String,
    default: ''
  },
  uploadUrl: {
    type: String,
    default: '/api/upload'
  },
  autoUpload: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'upload-success', 'upload-error', 'file-remove'])

const fileInputRef = ref(null)
const fileList = ref([])
const isDragOver = ref(false)

const uploadAreaClasses = computed(() => {
  const baseClasses = [
    'relative border-2 border-dashed rounded-lg p-6 transition-colors duration-200',
    'hover:border-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2'
  ]
  
  if (props.disabled) {
    baseClasses.push('border-gray-200 bg-gray-50 cursor-not-allowed')
  } else if (isDragOver.value) {
    baseClasses.push('border-primary-400 bg-primary-50 dark:bg-primary-900/20')
  } else if (props.error) {
    baseClasses.push('border-red-300 bg-red-50 dark:bg-red-900/20')
  } else {
    baseClasses.push('border-gray-300 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800')
  }
  
  return baseClasses.join(' ')
})

const uploadIcon = computed(() => {
  return CloudArrowUpIcon
})

const acceptText = computed(() => {
  if (props.accept === '*/*') {
    return `支持任意格式，单个文件不超过 ${formatFileSize(props.maxSize)}`
  }
  
  const types = props.accept.split(',').map(type => type.trim())
  return `支持 ${types.join(', ')} 格式，单个文件不超过 ${formatFileSize(props.maxSize)}`
})

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const validateFile = (file) => {
  // 检查文件大小
  if (file.size > props.maxSize) {
    return `文件大小不能超过 ${formatFileSize(props.maxSize)}`
  }
  
  // 检查文件类型
  if (props.accept !== '*/*') {
    const acceptTypes = props.accept.split(',').map(type => type.trim())
    const isValidType = acceptTypes.some(type => {
      if (type.startsWith('.')) {
        return file.name.toLowerCase().endsWith(type.toLowerCase())
      }
      return file.type.match(type.replace('*', '.*'))
    })
    
    if (!isValidType) {
      return '文件类型不支持'
    }
  }
  
  return null
}

const createFilePreview = (file) => {
  return new Promise((resolve) => {
    if (file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => resolve(e.target.result)
      reader.readAsDataURL(file)
    } else {
      resolve(null)
    }
  })
}

const uploadFile = async (fileItem) => {
  if (!props.autoUpload) return
  
  fileItem.status = 'uploading'
  fileItem.progress = 0
  
  try {
    const formData = new FormData()
    formData.append('file', fileItem.file)
    
    const xhr = new XMLHttpRequest()
    
    // 监听上传进度
    xhr.upload.addEventListener('progress', (e) => {
      if (e.lengthComputable) {
        fileItem.progress = Math.round((e.loaded / e.total) * 100)
      }
    })
    
    // 处理上传完成
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText)
        fileItem.status = 'success'
        fileItem.url = response.url
        emit('upload-success', fileItem, response)
        updateModelValue()
      } else {
        fileItem.status = 'error'
        fileItem.error = '上传失败'
        emit('upload-error', fileItem, new Error('Upload failed'))
      }
    })
    
    // 处理上传错误
    xhr.addEventListener('error', () => {
      fileItem.status = 'error'
      fileItem.error = '上传失败'
      emit('upload-error', fileItem, new Error('Upload failed'))
    })
    
    xhr.open('POST', props.uploadUrl)
    xhr.send(formData)
    
  } catch (error) {
    fileItem.status = 'error'
    fileItem.error = error.message
    emit('upload-error', fileItem, error)
  }
}

const addFiles = async (files) => {
  const newFiles = Array.from(files)
  
  // 检查文件数量限制
  if (!props.multiple && newFiles.length > 1) {
    alert('只能选择一个文件')
    return
  }
  
  if (fileList.value.length + newFiles.length > props.maxFiles) {
    alert(`最多只能上传 ${props.maxFiles} 个文件`)
    return
  }
  
  for (const file of newFiles) {
    const error = validateFile(file)
    if (error) {
      alert(error)
      continue
    }
    
    const preview = await createFilePreview(file)
    const fileItem = {
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      preview,
      status: 'ready',
      progress: 0,
      url: null,
      error: null
    }
    
    if (!props.multiple) {
      fileList.value = [fileItem]
    } else {
      fileList.value.push(fileItem)
    }
    
    // 自动上传
    if (props.autoUpload) {
      uploadFile(fileItem)
    }
  }
  
  updateModelValue()
}

const updateModelValue = () => {
  const files = fileList.value.map(item => ({
    name: item.name,
    size: item.size,
    type: item.type,
    url: item.url,
    status: item.status
  }))
  emit('update:modelValue', files)
}

const handleClick = () => {
  if (!props.disabled) {
    fileInputRef.value?.click()
  }
}

const handleFileSelect = (event) => {
  const files = event.target.files
  if (files?.length) {
    addFiles(files)
  }
  // 清空 input 值，允许重复选择同一文件
  event.target.value = ''
}

const handleDragOver = (event) => {
  if (!props.disabled) {
    isDragOver.value = true
  }
}

const handleDragLeave = (event) => {
  isDragOver.value = false
}

const handleDrop = (event) => {
  isDragOver.value = false
  if (!props.disabled) {
    const files = event.dataTransfer.files
    if (files?.length) {
      addFiles(files)
    }
  }
}

const handleRemove = (index) => {
  const removedFile = fileList.value[index]
  fileList.value.splice(index, 1)
  emit('file-remove', removedFile)
  updateModelValue()
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue.length !== fileList.value.length) {
    // 外部值变化时同步内部状态
    fileList.value = newValue.map(file => ({
      ...file,
      file: null, // 外部传入的文件对象可能不包含原始 File 对象
      preview: file.url, // 使用 URL 作为预览
      status: file.status || 'success'
    }))
  }
}, { immediate: true })
</script>
