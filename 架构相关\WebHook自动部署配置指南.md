# WebHook自动部署配置指南

## 🎯 目标
实现GitHub代码推送后自动部署到服务器，同时更新测试环境和生产环境。

## 🔧 宝塔WebHook配置

### 1. 安装WebHook插件
1. 宝塔面板 → 软件商店
2. 搜索 "WebHook" 
3. 安装 "宝塔WebHook" 插件

### 2. 创建WebHook
1. 宝塔面板 → WebHook → 添加Hook
2. 配置参数：

```
名称: wx-nan自动部署
执行脚本: /www/wwwroot/www.huanglun.asia/api/webs/server/deploy.sh
脚本参数: (留空)
密钥: wx-nan-webhook-2025-secret
```

### 3. 获取WebHook地址
创建完成后会得到类似地址：
```
http://8.148.231.104:8888/hook?access_key=your-access-key
```

## 🐙 GitHub WebHook配置

### 1. 进入仓库设置
1. GitHub仓库 → Settings → Webhooks
2. 点击 "Add webhook"

### 2. 配置WebHook
```
Payload URL: http://8.148.231.104:8888/hook?access_key=your-access-key
Content type: application/json
Secret: wx-nan-webhook-2025-secret
Events: Just the push event
Active: ✅
```

### 3. 测试WebHook
1. 保存配置后，GitHub会发送测试请求
2. 检查 "Recent Deliveries" 确认连接成功

## 🚀 智能自动部署流程

### 触发条件
- 向 `master` 分支推送代码
- GitHub自动发送WebHook请求到服务器

### 智能部署逻辑
1. **代码更新**: Git pull最新代码
2. **变更分析**: 检查哪些文件发生了变更
3. **智能决策**:
   - 如果 `webs/server/` 目录有变更 → 重启服务器
   - 如果只有小程序代码变更 → 跳过服务器重启
   - 如果 `webs/admin/` 有变更 → 重新构建管理后台

### 部署步骤
**服务器代码变更时**:
1. **依赖安装**: npm install
2. **数据库同步**: Prisma generate & db push
3. **服务重启**:
   - nannan-api-test (测试环境)
   - nannan-api (生产环境)

**仅前端代码变更时**:
1. **代码更新**: 仅拉取最新代码
2. **跳过重启**: 不重启服务器，节省资源

### 部署日志
查看部署日志：
```bash
tail -f /www/deploy.log
```

## 🔍 故障排除

### 常见问题

1. **WebHook无法访问**
   - 检查防火墙是否开放8888端口
   - 确认宝塔WebHook插件正常运行

2. **脚本执行失败**
   - 检查脚本权限：`chmod +x deploy.sh`
   - 查看错误日志：`tail /www/deploy.log`

3. **服务重启失败**
   - 确认PM2服务名称正确
   - 检查端口是否被占用

### 手动测试
```bash
# 手动执行部署脚本
bash /www/wwwroot/www.huanglun.asia/api/webs/server/deploy.sh

# 检查服务状态
pm2 status

# 测试API
curl http://8.148.231.104:3000/api/health  # 测试环境
curl http://8.148.231.104:3001/api/health  # 生产环境
```

## 📊 监控和通知

### 部署状态监控
- 宝塔面板 → WebHook → 查看执行记录
- PM2监控：`pm2 monit`

### 成功标志
- 两个服务都显示 "online" 状态
- API健康检查返回200状态码
- 部署日志显示 "✅ 部署完成"

## 🎉 使用效果

配置完成后，每次向GitHub推送代码：
1. 自动触发部署流程
2. 无需手动登录服务器
3. 测试环境和生产环境同时更新
4. 可通过日志查看部署状态

这样就实现了真正的CI/CD自动化部署！
