<template>
  <div class="forgot-password-container">
    <div class="forgot-password-form">
      <div class="form-header">
        <h2>重置密码</h2>
        <p>请输入您的邮箱地址，我们将发送验证码帮助您重置密码</p>
      </div>

      <el-form
        ref="forgotFormRef"
        :model="forgotForm"
        :rules="forgotRules"
        label-width="0"
        size="large"
      >
        <!-- 第一步：输入邮箱 -->
        <template v-if="step === 1">
          <el-form-item prop="email">
            <el-input
              v-model="forgotForm.email"
              placeholder="请输入注册邮箱"
              prefix-icon="Message"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="loading"
              @click="sendEmailCode"
              style="width: 100%"
            >
              <el-icon v-if="!loading"><Message /></el-icon>
              {{ loading ? '发送中...' : '发送验证码' }}
            </el-button>
          </el-form-item>
        </template>

        <!-- 第二步：输入验证码和新密码 -->
        <template v-if="step === 2">
          <el-form-item prop="email">
            <el-input
              v-model="forgotForm.email"
              placeholder="邮箱地址"
              prefix-icon="Message"
              disabled
            />
          </el-form-item>

          <el-form-item prop="emailCode">
            <div class="code-input-group">
              <el-input
                v-model="forgotForm.emailCode"
                placeholder="请输入邮箱验证码"
                prefix-icon="Lock"
                clearable
                maxlength="6"
              />
              <el-button
                :disabled="emailCountdown > 0"
                @click="resendEmailCode"
                class="code-btn"
              >
                {{ emailCountdown > 0 ? `${emailCountdown}s` : '重新发送' }}
              </el-button>
            </div>
          </el-form-item>

          <el-form-item prop="newPassword">
            <el-input
              v-model="forgotForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item prop="confirmPassword">
            <el-input
              v-model="forgotForm.confirmPassword"
              type="password"
              placeholder="请确认新密码"
              prefix-icon="Lock"
              show-password
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              :loading="loading"
              @click="handleResetPassword"
              style="width: 100%"
            >
              <el-icon v-if="!loading"><CircleCheck /></el-icon>
              {{ loading ? '重置中...' : '重置密码' }}
            </el-button>
          </el-form-item>

          <el-form-item>
            <el-button type="text" @click="goBackToStep1" style="width: 100%">
              返回上一步
            </el-button>
          </el-form-item>
        </template>

        <div class="form-footer">
          <el-link type="primary" @click="$router.push('/login')">
            返回登录
          </el-link>
          <span class="divider">|</span>
          <el-link type="primary" @click="$router.push('/register')">
            注册账户
          </el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, CircleCheck } from '@element-plus/icons-vue'
import { authApi } from '@/api/auth'

const router = useRouter()
const forgotFormRef = ref()
const loading = ref(false)
const step = ref(1) // 1: 输入邮箱, 2: 输入验证码和新密码
const emailCountdown = ref(0)
let emailTimer = null

const forgotForm = reactive({
  email: '',
  emailCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 验证规则
const validateEmail = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入邮箱地址'));
    return;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱地址'));
    return;
  }
  callback();
};

const validateEmailCode = (rule, value, callback) => {
  if (step.value === 2) {
    if (!value) {
      callback(new Error('请输入验证码'));
      return;
    }
    if (value.length !== 6) {
      callback(new Error('验证码为6位数字'));
      return;
    }
  }
  callback();
};

const validateNewPassword = (rule, value, callback) => {
  if (step.value === 2) {
    if (!value) {
      callback(new Error('请输入新密码'));
      return;
    }
    if (value.length < 6) {
      callback(new Error('密码长度不能少于6位'));
      return;
    }
    if (value.length > 20) {
      callback(new Error('密码长度不能超过20位'));
      return;
    }
  }
  callback();
};

const validateConfirmPassword = (rule, value, callback) => {
  if (step.value === 2) {
    if (!value) {
      callback(new Error('请确认密码'));
      return;
    }
    if (value !== forgotForm.newPassword) {
      callback(new Error('两次输入的密码不一致'));
      return;
    }
  }
  callback();
};

const forgotRules = {
  email: [
    { validator: validateEmail, trigger: 'blur' }
  ],
  emailCode: [
    { validator: validateEmailCode, trigger: 'blur' }
  ],
  newPassword: [
    { validator: validateNewPassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 发送邮箱验证码
const sendEmailCode = async () => {
  try {
    await forgotFormRef.value.validateField('email')
    loading.value = true

    const response = await authApi.sendEmailCode(forgotForm.email.trim(), 'reset-password')

    if (response.code === 200) {
      step.value = 2
      ElMessage.success('验证码已发送到您的邮箱')
      startEmailCountdown()
    } else {
      ElMessage.error(response.message || '发送失败，请重试')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重新发送邮箱验证码
const resendEmailCode = async () => {
  if (emailCountdown.value > 0) return

  loading.value = true
  try {
    const response = await authApi.sendEmailCode(forgotForm.email.trim(), 'reset-password')

    if (response.code === 200) {
      ElMessage.success('验证码已重新发送')
      startEmailCountdown()
    } else {
      ElMessage.error(response.message || '发送失败，请重试')
    }
  } catch (error) {
    console.error('重新发送验证码失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置密码
const handleResetPassword = async () => {
  try {
    await forgotFormRef.value.validate()
    loading.value = true

    // 先验证邮箱验证码
    const verifyResponse = await authApi.verifyEmailCode(
      forgotForm.email.trim(),
      forgotForm.emailCode.trim(),
      'reset-password'
    )

    if (verifyResponse.code !== 200) {
      ElMessage.error(verifyResponse.message || '验证码错误')
      return
    }

    // 验证码正确，重置密码
    const resetResponse = await authApi.resetPasswordWithCode(
      forgotForm.email.trim(),
      forgotForm.emailCode.trim(),
      forgotForm.newPassword
    )

    if (resetResponse.code === 200) {
      ElMessage.success('密码重置成功！正在跳转到登录页面...')
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    } else {
      ElMessage.error(resetResponse.message || '重置失败，请重试')
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    ElMessage.error('重置失败，请重试')
  } finally {
    loading.value = false
  }
}

// 返回第一步
const goBackToStep1 = () => {
  step.value = 1
  forgotForm.emailCode = ''
  forgotForm.newPassword = ''
  forgotForm.confirmPassword = ''
  if (emailTimer) {
    clearInterval(emailTimer)
    emailTimer = null
    emailCountdown.value = 0
  }
}

// 开始邮箱验证码倒计时
const startEmailCountdown = () => {
  emailCountdown.value = 60
  emailTimer = setInterval(() => {
    emailCountdown.value--
    if (emailCountdown.value <= 0) {
      clearInterval(emailTimer)
      emailTimer = null
    }
  }, 1000)
}

// 清理定时器
onUnmounted(() => {
  if (emailTimer) {
    clearInterval(emailTimer)
  }
})
</script>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.forgot-password-form {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.form-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.divider {
  margin: 0 10px;
  color: #ddd;
}

.success-message {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.success-message h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 20px;
}

.success-message p {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

.success-message strong {
  color: #333;
}

/* 找回方式选择标签 */
.reset-method-tabs {
  display: flex;
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.tab-item {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  background: #f5f7fa;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
}

.tab-item:hover {
  background: #e6f7ff;
  color: #409eff;
}

.tab-item.active {
  background: #409eff;
  color: white;
}

.tab-item .el-icon {
  font-size: 16px;
}

/* 验证码输入组 */
.code-input-group {
  display: flex;
  gap: 8px;
}

.code-input-group .el-input {
  flex: 1;
}

.code-btn {
  min-width: 100px;
  white-space: nowrap;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
