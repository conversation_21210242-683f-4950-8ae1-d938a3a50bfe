<template>
  <div class="forgot-password-container">
    <div class="forgot-password-form">
      <div class="form-header">
        <h2>找回密码</h2>
        <p>选择找回方式，我们将帮助您安全地重置密码</p>
      </div>

      <!-- 找回方式选择 -->
      <div class="reset-method-tabs">
        <div
          class="tab-item"
          :class="{ active: resetMethod === 'email' }"
          @click="resetMethod = 'email'"
        >
          <el-icon><Message /></el-icon>
          邮箱找回
        </div>
        <div
          class="tab-item"
          :class="{ active: resetMethod === 'phone' }"
          @click="resetMethod = 'phone'"
        >
          <el-icon><Phone /></el-icon>
          手机找回
        </div>
      </div>

      <el-form
        ref="forgotFormRef"
        :model="forgotForm"
        :rules="forgotRules"
        label-width="0"
        size="large"
      >
        <!-- 邮箱找回 -->
        <template v-if="resetMethod === 'email'">
          <el-form-item prop="email">
            <el-input
              v-model="forgotForm.email"
              placeholder="请输入注册邮箱"
              prefix-icon="Message"
              clearable
            />
          </el-form-item>
        </template>

        <!-- 手机找回 -->
        <template v-if="resetMethod === 'phone'">
          <el-form-item prop="phone">
            <el-input
              v-model="forgotForm.phone"
              placeholder="请输入注册手机号"
              prefix-icon="Phone"
              clearable
              maxlength="11"
            />
          </el-form-item>

          <el-form-item prop="smsCode">
            <div class="code-input-group">
              <el-input
                v-model="forgotForm.smsCode"
                placeholder="请输入验证码"
                prefix-icon="Lock"
                clearable
                maxlength="6"
              />
              <el-button
                :disabled="smsCountdown > 0"
                @click="sendSmsCode"
                class="code-btn"
              >
                {{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
              </el-button>
            </div>
          </el-form-item>
        </template>

        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleForgotPassword"
            style="width: 100%"
          >
            <el-icon v-if="!loading"><Message /></el-icon>
            {{ loading ? '发送中...' : (resetMethod === 'email' ? '发送重置链接' : '验证并重置') }}
          </el-button>
        </el-form-item>

        <div class="form-footer">
          <el-link type="primary" @click="$router.push('/login')">
            返回登录
          </el-link>
          <span class="divider">|</span>
          <el-link type="primary" @click="$router.push('/register')">
            注册账户
          </el-link>
        </div>
      </el-form>

      <!-- 发送成功提示 -->
      <div v-if="emailSent" class="success-message">
        <el-icon class="success-icon"><CircleCheck /></el-icon>
        <h3>邮件已发送</h3>
        <p>
          我们已向 <strong>{{ forgotForm.email }}</strong> 发送了重置密码链接
        </p>
        <p>请检查您的邮箱（包括垃圾邮件文件夹）</p>
        <el-button type="primary" @click="resendEmail" :loading="loading">
          重新发送
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, CircleCheck, Phone } from '@element-plus/icons-vue'
import { authApi } from '@/api/auth'

const router = useRouter()
const forgotFormRef = ref()
const loading = ref(false)
const emailSent = ref(false)
const resetMethod = ref('email') // 'email' | 'phone'
const smsCountdown = ref(0)
let smsTimer = null

const forgotForm = reactive({
  email: '',
  phone: '',
  smsCode: ''
})

// 验证规则
const validatePhone = (rule, value, callback) => {
  if (resetMethod.value === 'phone') {
    if (!value) {
      callback(new Error('请输入手机号'));
      return;
    }
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(value)) {
      callback(new Error('请输入正确的手机号'));
      return;
    }
  }
  callback();
};

const validateSmsCode = (rule, value, callback) => {
  if (resetMethod.value === 'phone') {
    if (!value) {
      callback(new Error('请输入验证码'));
      return;
    }
    if (value.length !== 6) {
      callback(new Error('验证码为6位数字'));
      return;
    }
  }
  callback();
};

const validateEmail = (rule, value, callback) => {
  if (resetMethod.value === 'email') {
    if (!value) {
      callback(new Error('请输入邮箱地址'));
      return;
    }
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
      callback(new Error('请输入正确的邮箱地址'));
      return;
    }
  }
  callback();
};

const forgotRules = {
  email: [
    { validator: validateEmail, trigger: 'blur' }
  ],
  phone: [
    { validator: validatePhone, trigger: 'blur' }
  ],
  smsCode: [
    { validator: validateSmsCode, trigger: 'blur' }
  ]
}

const handleForgotPassword = async () => {
  try {
    await forgotFormRef.value.validate()
    loading.value = true

    if (resetMethod.value === 'email') {
      // 邮箱找回密码
      const response = await authApi.sendResetPasswordEmail(forgotForm.email.trim())

      if (response.code === 200) {
        emailSent.value = true
        ElMessage.success('重置密码邮件已发送')
      } else {
        ElMessage.error(response.message || '发送失败，请重试')
      }
    } else {
      // 手机验证码找回密码
      const response = await authApi.verifySmsCode(
        forgotForm.phone.trim(),
        forgotForm.smsCode.trim(),
        'reset-password'
      )

      if (response.success) {
        ElMessage.success('验证成功，正在跳转到重置密码页面...')

        // 跳转到重置密码页面，携带验证token
        setTimeout(() => {
          router.push({
            path: '/reset-password',
            query: {
              token: response.data.resetToken,
              method: 'phone'
            }
          })
        }, 1000)
      } else {
        ElMessage.error(response.message || '验证失败，请重试')
      }
    }
  } catch (error) {
    console.error('操作失败:', error)

    let errorMessage = '操作失败，请重试'
    if (error.response) {
      const status = error.response.status
      const data = error.response.data

      if (status === 404) {
        errorMessage = resetMethod.value === 'email' ? '邮箱未注册' : '手机号未注册'
      } else if (status === 400) {
        errorMessage = data.message || '请求参数错误'
      } else if (status === 429) {
        errorMessage = '操作过于频繁，请稍后再试'
      } else if (status >= 500) {
        errorMessage = '服务器错误，请稍后再试'
      }
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.value = false
  }
}

// 发送短信验证码
const sendSmsCode = async () => {
  if (!forgotForm.phone) {
    ElMessage.error('请先输入手机号')
    return
  }

  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(forgotForm.phone)) {
    ElMessage.error('请输入正确的手机号')
    return
  }

  try {
    const response = await authApi.sendSmsCode(forgotForm.phone.trim(), 'reset-password')

    if (response.code === 200) {
      ElMessage.success('验证码已发送')
      startSmsCountdown()
    } else {
      ElMessage.error(response.message || '发送失败，请重试')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送失败，请重试')
  }
}

// 开始短信倒计时
const startSmsCountdown = () => {
  smsCountdown.value = 60
  smsTimer = setInterval(() => {
    smsCountdown.value--
    if (smsCountdown.value <= 0) {
      clearInterval(smsTimer)
      smsTimer = null
    }
  }, 1000)
}

const resendEmail = async () => {
  loading.value = true
  try {
    const response = await authApi.sendResetPasswordEmail(forgotForm.email.trim())

    if (response.code === 200) {
      ElMessage.success('邮件已重新发送')
    } else {
      ElMessage.error(response.message || '发送失败，请重试')
    }
  } catch (error) {
    ElMessage.error('发送失败，请重试')
  } finally {
    loading.value = false
  }
}

// 清理定时器
onUnmounted(() => {
  if (smsTimer) {
    clearInterval(smsTimer)
  }
})
</script>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.forgot-password-form {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.form-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
}

.divider {
  margin: 0 10px;
  color: #ddd;
}

.success-message {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 16px;
}

.success-message h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 20px;
}

.success-message p {
  margin: 8px 0;
  color: #666;
  line-height: 1.5;
}

.success-message strong {
  color: #333;
}

/* 找回方式选择标签 */
.reset-method-tabs {
  display: flex;
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.tab-item {
  flex: 1;
  padding: 12px 16px;
  text-align: center;
  background: #f5f7fa;
  color: #606266;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 14px;
}

.tab-item:hover {
  background: #e6f7ff;
  color: #409eff;
}

.tab-item.active {
  background: #409eff;
  color: white;
}

.tab-item .el-icon {
  font-size: 16px;
}

/* 验证码输入组 */
.code-input-group {
  display: flex;
  gap: 8px;
}

.code-input-group .el-input {
  flex: 1;
}

.code-btn {
  min-width: 100px;
  white-space: nowrap;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
