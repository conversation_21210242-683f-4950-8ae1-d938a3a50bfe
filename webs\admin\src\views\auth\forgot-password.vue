<template>
  <div class="forgot-password-container">
    <div class="forgot-password-form">
      <div class="form-header">
        <h2>重置密码</h2>
        <p>请输入您的邮箱地址，获取验证码后设置新密码</p>
      </div>

      <el-form
        ref="forgotFormRef"
        :model="forgotForm"
        :rules="forgotRules"
        label-width="0"
        size="large"
      >
        <!-- 邮箱输入 -->
        <el-form-item prop="email">
          <el-input
            v-model="forgotForm.email"
            placeholder="请输入注册邮箱"
            prefix-icon="Message"
            clearable
          />
        </el-form-item>

        <!-- 验证码输入 -->
        <el-form-item prop="emailCode">
          <div class="code-input-group">
            <el-input
              v-model="forgotForm.emailCode"
              placeholder="请输入邮箱验证码"
              prefix-icon="Lock"
              clearable
              maxlength="6"
            />
            <el-button
              :disabled="emailCountdown > 0 || !forgotForm.email"
              @click="sendEmailCode"
              class="code-btn"
              :loading="sendingCode"
            >
              {{ emailCountdown > 0 ? `${emailCountdown}s` : '发送验证码' }}
            </el-button>
          </div>
        </el-form-item>

        <!-- 新密码 -->
        <el-form-item prop="newPassword">
          <el-input
            v-model="forgotForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <!-- 确认密码 -->
        <el-form-item prop="confirmPassword">
          <el-input
            v-model="forgotForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <!-- 重置密码按钮 -->
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="handleResetPassword"
            style="width: 100%"
          >
            <el-icon v-if="!loading"><CircleCheck /></el-icon>
            {{ loading ? '重置中...' : '重置密码' }}
          </el-button>
        </el-form-item>

        <div class="form-footer">
          <el-link type="primary" @click="$router.push('/login')">
            返回登录
          </el-link>
          <span class="divider">|</span>
          <el-link type="primary" @click="$router.push('/register')">
            注册账户
          </el-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Message, CircleCheck } from '@element-plus/icons-vue'
import { authApi } from '@/api/auth'

const router = useRouter()
const forgotFormRef = ref()
const loading = ref(false)
const sendingCode = ref(false)
const emailCountdown = ref(0)
let emailTimer = null

const forgotForm = reactive({
  email: '',
  emailCode: '',
  newPassword: '',
  confirmPassword: ''
})

// 验证规则
const validateEmail = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入邮箱地址'));
    return;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value)) {
    callback(new Error('请输入正确的邮箱地址'));
    return;
  }
  callback();
};

const validateEmailCode = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入验证码'));
    return;
  }
  if (value.length !== 6) {
    callback(new Error('验证码为6位数字'));
    return;
  }
  callback();
};

const validateNewPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入新密码'));
    return;
  }
  if (value.length < 6) {
    callback(new Error('密码长度不能少于6位'));
    return;
  }
  if (value.length > 20) {
    callback(new Error('密码长度不能超过20位'));
    return;
  }
  callback();
};

const validateConfirmPassword = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请确认密码'));
    return;
  }
  if (value !== forgotForm.newPassword) {
    callback(new Error('两次输入的密码不一致'));
    return;
  }
  callback();
};

const forgotRules = {
  email: [
    { validator: validateEmail, trigger: 'blur' }
  ],
  emailCode: [
    { validator: validateEmailCode, trigger: 'blur' }
  ],
  newPassword: [
    { validator: validateNewPassword, trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: validateConfirmPassword, trigger: 'blur' }
  ]
}

// 发送邮箱验证码
const sendEmailCode = async () => {
  try {
    await forgotFormRef.value.validateField('email')
    sendingCode.value = true

    const response = await authApi.sendEmailCode(forgotForm.email.trim(), 'reset-password')

    if (response.code === 200) {
      ElMessage.success('验证码已发送到您的邮箱')
      startEmailCountdown()
    } else {
      ElMessage.error(response.message || '发送失败，请重试')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送失败，请重试')
  } finally {
    sendingCode.value = false
  }
}

// 重置密码
const handleResetPassword = async () => {
  try {
    await forgotFormRef.value.validate()
    loading.value = true

    // 先验证邮箱验证码
    const verifyResponse = await authApi.verifyEmailCode(
      forgotForm.email.trim(),
      forgotForm.emailCode.trim(),
      'reset-password'
    )

    if (verifyResponse.code !== 200) {
      ElMessage.error(verifyResponse.message || '验证码错误')
      return
    }

    // 验证码正确，重置密码
    const resetResponse = await authApi.resetPasswordWithCode(
      forgotForm.email.trim(),
      forgotForm.emailCode.trim(),
      forgotForm.newPassword
    )

    if (resetResponse.code === 200) {
      ElMessage.success('密码重置成功！正在跳转到登录页面...')
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    } else {
      ElMessage.error(resetResponse.message || '重置失败，请重试')
    }
  } catch (error) {
    console.error('重置密码失败:', error)
    ElMessage.error('重置失败，请重试')
  } finally {
    loading.value = false
  }
}

// 开始邮箱验证码倒计时
const startEmailCountdown = () => {
  emailCountdown.value = 60
  emailTimer = setInterval(() => {
    emailCountdown.value--
    if (emailCountdown.value <= 0) {
      clearInterval(emailTimer)
      emailTimer = null
    }
  }, 1000)
}

// 清理定时器
onUnmounted(() => {
  if (emailTimer) {
    clearInterval(emailTimer)
  }
})
</script>

<style scoped>
.forgot-password-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.forgot-password-form {
  width: 100%;
  max-width: 500px;
  min-width: 320px;
  padding: 40px;
  margin: 0 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .forgot-password-form {
    padding: 30px 20px;
    margin: 0 10px;
  }
}

@media (max-width: 480px) {
  .forgot-password-form {
    padding: 20px 15px;
    margin: 0 5px;
  }
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.form-header p {
  color: #666;
  font-size: 14px;
}

.code-input-group {
  display: flex;
  gap: 10px;
}

.code-input-group .el-input {
  flex: 1;
}

.code-btn {
  width: 120px;
  flex-shrink: 0;
}

.form-footer {
  text-align: center;
  margin-top: 20px;
  color: #666;
  font-size: 14px;
}

.divider {
  margin: 0 10px;
  color: #ddd;
}
</style>
