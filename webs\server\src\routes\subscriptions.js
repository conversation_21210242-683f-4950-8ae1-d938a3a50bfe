const express = require('express');
const router = express.Router();
const subscriptionController = require('../controllers/subscriptionController');
const {auth} = require('../middlewares/auth');

// 发送订单通知 (需要认证)
router.post(
  '/order-notification',
  auth,
  subscriptionController.sendOrderNotification
);

// 发送菜单更新通知 (需要认证)
router.post(
  '/menu-notification',
  auth,
  subscriptionController.sendMenuNotification
);

// 测试订阅消息发送 (需要认证)
router.post('/test', auth, subscriptionController.sendTestMessage);

module.exports = router;
