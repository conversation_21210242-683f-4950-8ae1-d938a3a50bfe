<template>
  <div class="layout-container">
    <!-- 头部 -->
    <div class="layout-header">
      <div class="header-left">
        <el-button link @click="toggleSidebar" class="sidebar-toggle">
          <el-icon><Menu /></el-icon>
        </el-button>
        <h1 class="logo">楠楠家厨管理系统</h1>
      </div>

      <div class="header-right">
        <div class="user-info" @click="handleUserClick">
          <el-avatar
            :size="32"
            :src="userStore.userInfo?.avatar || 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'"
          />
          <span
            class="username"
            >{{ userStore.userInfo?.name || '管理员' }}</span
          >
          <el-button link @click="handleLogout" style="margin-left: 8px;">
            退出登录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主体 -->
    <div class="layout-main">
      <!-- 侧边栏 -->
      <div :class="['layout-sidebar', { collapsed: isCollapsed }]">
        <el-menu
          :default-active="activeMenu"
          :collapse="isCollapsed"
          :unique-opened="true"
          background-color="#001529"
          text-color="#fff"
          active-text-color="#1890ff"
          router
        >
          <template v-for="route in menuRoutes" :key="route.path">
            <el-sub-menu
              v-if="route.children && route.children.length > 1"
              :index="route.path"
            >
              <template #title>
                <el-icon><component :is="getIcon(route.meta?.icon)" /></el-icon>
                <span>{{ route.meta?.title }}</span>
              </template>
              <el-menu-item
                v-for="child in route.children"
                :key="child.path"
                :index="route.path + '/' + child.path"
              >
                <el-icon><component :is="getIcon(child.meta?.icon)" /></el-icon>
                <span>{{ child.meta?.title }}</span>
              </el-menu-item>
            </el-sub-menu>

            <el-menu-item
              v-else
              :index="route.children?.[0] ? (route.path === '/' ? '/' + route.children[0].path : route.path + '/' + route.children[0].path) : route.path"
            >
              <el-icon
                ><component
                  :is="
                    getIcon(route.meta?.icon || route.children?.[0]?.meta?.icon)
                  "
              /></el-icon>
              <span>{{
                route.meta?.title || route.children?.[0]?.meta?.title
              }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </div>

      <!-- 内容区域 -->
      <div class="layout-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import {
  Menu,
  House,
  List,
  ShoppingCart,
  ChatDotRound,
  User,
  PieChart,
  Bowl,
  Grid,
  Bell,
  Calendar
} from "@element-plus/icons-vue";

const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const isCollapsed = ref(false);

// 图标映射
const iconMap = {
  dashboard: House,
  menu: Bowl,
  list: List,
  dish: Bowl,
  category: Grid,
  order: ShoppingCart,
  today: Calendar,
  message: ChatDotRound,
  notification: Bell,
  user: User,
  chart: PieChart,
  overview: PieChart
};

// 获取菜单路由
const menuRoutes = computed(() => {
  return router.options.routes
    .filter(route => {
      // 只显示有children的路由（Layout路由），排除登录相关页面
      return route.children &&
             route.children.length > 0 &&
             route.path !== "/login" &&
             route.path !== "/register" &&
             route.path !== "/forgot-password" &&
             !route.meta?.hidden;
    });
});

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path;
});

// 切换侧边栏
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value;
};

// 获取图标组件
const getIcon = iconName => {
  return iconMap[iconName] || List;
};

// 处理用户点击
const handleUserClick = () => {
  console.log("用户信息点击");
};

// 处理退出登录
const handleLogout = () => {
  userStore.logout();
  router.push("/login");
};
</script>

<style scoped lang="scss">
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  height: 64px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  color: #666;
  transition: color 0.3s;

  &:hover {
    color: #333;
  }

  :deep(.el-button) {
    border: none;
    background: transparent;
  }
}

.logo {
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f5f5;
  }
}

.username {
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  width: 256px;
  background: #001529;
  transition: all 0.3s;

  &.collapsed {
    width: 64px;
  }

  :deep(.el-menu) {
    border-right: none;
    height: 100%;
  }

  :deep(.el-menu-item) {
    transition: all 0.2s;
    color: #fff !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
      color: #fff !important;
    }

    span {
      color: #fff !important;
    }

    .el-icon {
      color: #fff !important;
    }

    &.is-active {
      background-color: #1890ff !important;
      color: #fff !important;

      span {
        color: #fff !important;
      }

      .el-icon {
        color: #fff !important;
      }
    }
  }

  :deep(.el-sub-menu__title) {
    transition: all 0.2s;
    color: #fff !important;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1) !important;
      color: #fff !important;
    }

    span {
      color: #fff !important;
    }

    .el-icon {
      color: #fff !important;
    }
  }
}

.layout-content {
  flex: 1;
  background: #f5f5f5;
  overflow: auto;
  padding: 24px;
}

// 响应式设计
@media (max-width: 768px) {
  .layout-sidebar {
    position: fixed;
    left: 0;
    top: 64px;
    height: calc(100vh - 64px);
    z-index: 50;
    transform: translateX(0);
    transition: transform 0.3s;

    &.collapsed {
      transform: translateX(-100%);
      width: 256px;
    }
  }

  .layout-content {
    padding: 16px;
  }

  .header-left {
    gap: 8px;
  }

  .logo {
    font-size: 18px;
  }

  .username {
    display: none;
  }
}
</style>
