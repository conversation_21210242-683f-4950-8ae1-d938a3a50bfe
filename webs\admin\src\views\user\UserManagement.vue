<template>
  <PageContainer
    title="用户管理"
    subtitle="管理系统中的所有用户信息"
    :breadcrumb="breadcrumbItems"
  >
    <template #header-actions>
      <BaseButton
        variant="primary"
        prefix-icon="PlusIcon"
        @click="handleCreate"
      >
        新增用户
      </BaseButton>
    </template>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatsCard
        title="总用户数"
        :value="stats.totalUsers"
        :icon="UsersIcon"
        color="blue"
        :change="stats.userGrowth"
        description="较上月"
      />
      <StatsCard
        title="活跃用户"
        :value="stats.activeUsers"
        :icon="UserCheckIcon"
        color="green"
        :change="stats.activeGrowth"
        description="本月活跃"
      />
      <StatsCard
        title="新增用户"
        :value="stats.newUsers"
        :icon="UserPlusIcon"
        color="purple"
        :change="stats.newUserGrowth"
        description="本月新增"
      />
      <StatsCard
        title="管理员"
        :value="stats.adminUsers"
        :icon="ShieldCheckIcon"
        color="yellow"
        description="管理员用户"
      />
    </div>

    <!-- 搜索表单 -->
    <SearchForm
      v-model="searchParams"
      :fields="searchFields"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 用户表格 -->
    <BaseTable
      :data="users"
      :columns="tableColumns"
      :selectable="true"
      v-model:selected-rows="selectedUsers"
      :loading="loading"
      @row-click="handleRowClick"
      @sort-change="handleSort"
    >
      <template #header>
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            用户列表 ({{ pagination.total }})
          </h3>
          <div class="flex items-center space-x-3">
            <BaseButton
              v-if="selectedUsers.length"
              variant="error"
              size="sm"
              @click="handleBatchDelete"
            >
              批量删除 ({{ selectedUsers.length }})
            </BaseButton>
            <BaseButton
              variant="outline"
              size="sm"
              @click="handleExport"
            >
              导出数据
            </BaseButton>
          </div>
        </div>
      </template>

      <!-- 用户头像 -->
      <template #cell-avatar="{ row }">
        <div class="flex items-center">
          <img
            v-if="row.avatar"
            :src="row.avatar"
            :alt="row.name"
            class="h-8 w-8 rounded-full object-cover"
          />
          <div
            v-else
            class="h-8 w-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"
          >
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
              {{ row.name?.charAt(0)?.toUpperCase() }}
            </span>
          </div>
        </div>
      </template>

      <!-- 用户角色 -->
      <template #cell-role="{ row }">
        <span
          :class="getRoleBadgeClass(row.role)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ getRoleText(row.role) }}
        </span>
      </template>

      <!-- 用户状态 -->
      <template #cell-status="{ row }">
        <span
          :class="getStatusBadgeClass(row.status)"
          class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
        >
          {{ getStatusText(row.status) }}
        </span>
      </template>

      <!-- 最后登录时间 -->
      <template #cell-lastLoginAt="{ row }">
        <span v-if="row.lastLoginAt" class="text-sm text-gray-900 dark:text-gray-100">
          {{ formatDate(row.lastLoginAt) }}
        </span>
        <span v-else class="text-sm text-gray-500 dark:text-gray-400">
          从未登录
        </span>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center space-x-2">
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleEdit(row)"
          >
            编辑
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleResetPassword(row)"
          >
            重置密码
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleDelete(row)"
            class="text-red-600 hover:text-red-700"
          >
            删除
          </BaseButton>
        </div>
      </template>

      <!-- 分页 -->
      <template #footer>
        <Pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          @change="handlePageChange"
        />
      </template>
    </BaseTable>

    <!-- 用户编辑模态框 -->
    <UserEditModal
      v-model="showEditModal"
      :user="currentUser"
      @success="handleEditSuccess"
    />
  </PageContainer>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { 
  UsersIcon, 
  UserCheckIcon, 
  UserPlusIcon, 
  ShieldCheckIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import {
  PageContainer,
  StatsCard,
  SearchForm,
  BaseTable,
  BaseButton,
  Pagination
} from '@/components/ui'
import UserEditModal from './components/UserEditModal.vue'
import { userApi } from '@/api/user'
import { formatDate } from '@/utils/date'

// 响应式数据
const loading = ref(false)
const users = ref([])
const selectedUsers = ref([])
const showEditModal = ref(false)
const currentUser = ref(null)

// 搜索参数
const searchParams = reactive({
  keyword: '',
  role: '',
  status: '',
  dateRange: []
})

// 分页参数
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0
})

// 统计数据
const stats = reactive({
  totalUsers: 0,
  activeUsers: 0,
  newUsers: 0,
  adminUsers: 0,
  userGrowth: 0,
  activeGrowth: 0,
  newUserGrowth: 0
})

// 面包屑导航
const breadcrumbItems = [
  { text: '首页', to: '/' },
  { text: '用户管理', to: '/user' },
  { text: '用户列表' }
]

// 搜索字段配置
const searchFields = [
  {
    key: 'keyword',
    type: 'input',
    label: '关键词',
    placeholder: '搜索用户名、手机号、邮箱',
    prefixIcon: 'MagnifyingGlassIcon'
  },
  {
    key: 'role',
    type: 'select',
    label: '用户角色',
    placeholder: '请选择角色',
    options: [
      { label: '全部', value: '' },
      { label: '管理员', value: 'admin' },
      { label: '普通用户', value: 'user' },
      { label: '家庭成员', value: 'family' }
    ]
  },
  {
    key: 'status',
    type: 'select',
    label: '用户状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '正常', value: 'active' },
      { label: '禁用', value: 'inactive' },
      { label: '暂停', value: 'suspended' }
    ]
  },
  {
    key: 'dateRange',
    type: 'daterange',
    label: '注册时间',
    placeholder: '选择时间范围'
  }
]

// 表格列配置
const tableColumns = [
  {
    key: 'avatar',
    title: '头像',
    width: 80
  },
  {
    key: 'name',
    title: '用户名',
    sortable: true
  },
  {
    key: 'phone',
    title: '手机号'
  },
  {
    key: 'email',
    title: '邮箱'
  },
  {
    key: 'role',
    title: '角色'
  },
  {
    key: 'status',
    title: '状态'
  },
  {
    key: 'lastLoginAt',
    title: '最后登录',
    sortable: true
  },
  {
    key: 'createdAt',
    title: '注册时间',
    sortable: true,
    render: (value) => formatDate(value)
  }
]

// 计算属性
const getRoleBadgeClass = (role) => {
  const classes = {
    admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    user: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    family: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[role] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getStatusBadgeClass = (status) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    suspended: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  }
  return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getRoleText = (role) => {
  const texts = {
    admin: '管理员',
    user: '普通用户',
    family: '家庭成员'
  }
  return texts[role] || '未知'
}

const getStatusText = (status) => {
  const texts = {
    active: '正常',
    inactive: '禁用',
    suspended: '暂停'
  }
  return texts[status] || '未知'
}

// 方法
const fetchUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchParams
    }
    
    const response = await userApi.getUsers(params)
    users.value = response.data.users
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchStats = async () => {
  try {
    const response = await userApi.getStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  fetchUsers()
}

const handleReset = () => {
  pagination.current = 1
  fetchUsers()
}

const handlePageChange = () => {
  fetchUsers()
}

const handleSort = (sortInfo) => {
  // 处理排序
  console.log('排序:', sortInfo)
  fetchUsers()
}

const handleCreate = () => {
  currentUser.value = null
  showEditModal.value = true
}

const handleEdit = (user) => {
  currentUser.value = user
  showEditModal.value = true
}

const handleDelete = async (user) => {
  if (confirm(`确定要删除用户 ${user.name} 吗？`)) {
    try {
      await userApi.deleteUser(user.id)
      fetchUsers()
    } catch (error) {
      console.error('删除用户失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (confirm(`确定要删除选中的 ${selectedUsers.value.length} 个用户吗？`)) {
    try {
      const userIds = selectedUsers.value.map(user => user.id)
      await userApi.batchDeleteUsers(userIds)
      selectedUsers.value = []
      fetchUsers()
    } catch (error) {
      console.error('批量删除用户失败:', error)
    }
  }
}

const handleResetPassword = async (user) => {
  if (confirm(`确定要重置用户 ${user.name} 的密码吗？`)) {
    try {
      await userApi.resetPassword(user.id)
      alert('密码重置成功')
    } catch (error) {
      console.error('重置密码失败:', error)
    }
  }
}

const handleExport = () => {
  // 导出数据逻辑
  console.log('导出数据')
}

const handleRowClick = (user) => {
  // 行点击事件
  console.log('点击用户:', user)
}

const handleEditSuccess = () => {
  showEditModal.value = false
  fetchUsers()
  fetchStats()
}

// 生命周期
onMounted(() => {
  fetchUsers()
  fetchStats()
})
</script>
