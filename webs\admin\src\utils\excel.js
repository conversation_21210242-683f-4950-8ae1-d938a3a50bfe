import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import { ElMessage } from 'element-plus'

/**
 * 导出数据到Excel
 * @param {Array} data - 要导出的数据
 * @param {Array} columns - 列配置
 * @param {string} filename - 文件名
 * @param {Object} options - 其他选项
 */
export const exportToExcel = (data, columns, filename = 'export', options = {}) => {
  try {
    const {
      sheetName = 'Sheet1',
      bookType = 'xlsx',
      writeOptions = { bookType: 'xlsx', type: 'array' }
    } = options

    // 处理数据，根据columns配置提取和格式化数据
    const exportData = data.map(row => {
      const newRow = {}
      columns.forEach(col => {
        const value = getNestedValue(row, col.prop)
        newRow[col.label] = formatCellValue(value, col)
      })
      return newRow
    })

    // 创建工作簿
    const wb = XLSX.utils.book_new()
    
    // 创建工作表
    const ws = XLSX.utils.json_to_sheet(exportData)
    
    // 设置列宽
    const colWidths = columns.map(col => ({
      wch: col.width ? Math.floor(col.width / 8) : 15
    }))
    ws['!cols'] = colWidths

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, sheetName)

    // 生成Excel文件
    const excelBuffer = XLSX.write(wb, writeOptions)
    
    // 保存文件
    const blob = new Blob([excelBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    saveAs(blob, `${filename}.${bookType}`)
    
    ElMessage.success('导出成功')
    
  } catch (error) {
    console.error('导出Excel失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}

/**
 * 从Excel文件导入数据
 * @param {File} file - Excel文件
 * @param {Object} options - 导入选项
 * @returns {Promise<Array>} 导入的数据
 */
export const importFromExcel = (file, options = {}) => {
  return new Promise((resolve, reject) => {
    try {
      const {
        sheetIndex = 0,
        headerRow = 1,
        startRow = 2,
        columnMapping = {},
        validator = null
      } = options

      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result)
          const workbook = XLSX.read(data, { type: 'array' })
          
          // 获取第一个工作表
          const sheetName = workbook.SheetNames[sheetIndex]
          const worksheet = workbook.Sheets[sheetName]
          
          // 转换为JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            range: startRow - 1
          })
          
          // 获取表头
          const headers = XLSX.utils.sheet_to_json(worksheet, {
            header: 1,
            range: `${headerRow}:${headerRow}`
          })[0]
          
          // 处理数据
          const processedData = jsonData.map(row => {
            const rowData = {}
            headers.forEach((header, index) => {
              const mappedKey = columnMapping[header] || header
              rowData[mappedKey] = row[index]
            })
            return rowData
          }).filter(row => {
            // 过滤空行
            return Object.values(row).some(value => value !== undefined && value !== '')
          })
          
          // 数据验证
          if (validator) {
            const validationResults = processedData.map((row, index) => {
              const result = validator(row, index)
              return { ...row, _validation: result }
            })
            
            const errors = validationResults.filter(row => !row._validation.valid)
            if (errors.length > 0) {
              reject({
                type: 'validation',
                message: '数据验证失败',
                errors: errors
              })
              return
            }
          }
          
          resolve(processedData)
          
        } catch (error) {
          reject({
            type: 'parse',
            message: '文件解析失败',
            error: error
          })
        }
      }
      
      reader.onerror = () => {
        reject({
          type: 'read',
          message: '文件读取失败'
        })
      }
      
      reader.readAsArrayBuffer(file)
      
    } catch (error) {
      reject({
        type: 'unknown',
        message: '导入失败',
        error: error
      })
    }
  })
}

/**
 * 下载Excel模板
 * @param {Array} columns - 列配置
 * @param {string} filename - 文件名
 * @param {Array} sampleData - 示例数据
 */
export const downloadTemplate = (columns, filename = 'template', sampleData = []) => {
  try {
    // 创建表头
    const headers = columns.map(col => col.label)
    
    // 创建示例数据
    const templateData = [headers]
    
    if (sampleData.length > 0) {
      sampleData.forEach(row => {
        const templateRow = columns.map(col => {
          const value = getNestedValue(row, col.prop)
          return formatCellValue(value, col)
        })
        templateData.push(templateRow)
      })
    } else {
      // 添加一行空数据作为示例
      const emptyRow = columns.map(col => col.example || '')
      templateData.push(emptyRow)
    }
    
    // 创建工作簿
    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.aoa_to_sheet(templateData)
    
    // 设置列宽
    const colWidths = columns.map(col => ({
      wch: col.width ? Math.floor(col.width / 8) : 15
    }))
    ws['!cols'] = colWidths
    
    // 添加工作表
    XLSX.utils.book_append_sheet(wb, ws, 'Template')
    
    // 导出文件
    const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' })
    const blob = new Blob([excelBuffer], { 
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
    })
    
    saveAs(blob, `${filename}.xlsx`)
    
    ElMessage.success('模板下载成功')
    
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败: ' + error.message)
  }
}

/**
 * 获取嵌套对象的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径，如 'user.name'
 * @returns {any} 值
 */
const getNestedValue = (obj, path) => {
  if (!path) return obj
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : ''
  }, obj)
}

/**
 * 格式化单元格值
 * @param {any} value - 原始值
 * @param {Object} column - 列配置
 * @returns {string} 格式化后的值
 */
const formatCellValue = (value, column) => {
  if (value === null || value === undefined) {
    return ''
  }
  
  // 如果有自定义格式化函数
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value)
  }
  
  // 根据类型格式化
  switch (column.type) {
    case 'date':
      return value instanceof Date ? value.toLocaleDateString() : value
    case 'datetime':
      return value instanceof Date ? value.toLocaleString() : value
    case 'number':
      return typeof value === 'number' ? value.toString() : value
    case 'boolean':
      return value ? '是' : '否'
    default:
      return value.toString()
  }
}

/**
 * 验证Excel文件
 * @param {File} file - 文件
 * @returns {Object} 验证结果
 */
export const validateExcelFile = (file) => {
  const errors = []
  
  // 检查文件类型
  const allowedTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ]
  
  if (!allowedTypes.includes(file.type)) {
    errors.push('文件类型不正确，请选择Excel文件(.xls或.xlsx)')
  }
  
  // 检查文件大小 (10MB)
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    errors.push('文件大小不能超过10MB')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

export default {
  exportToExcel,
  importFromExcel,
  downloadTemplate,
  validateExcelFile
}
