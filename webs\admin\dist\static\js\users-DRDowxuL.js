var E=(L,s,y)=>new Promise((o,S)=>{var T=i=>{try{_(y.next(i))}catch(x){S(x)}},m=i=>{try{_(y.throw(i))}catch(x){S(x)}},_=i=>i.done?o(i.value):Promise.resolve(i.value).then(T,m);_((y=y.apply(L,s)).next())});import{_ as _t,r as M,c as U,b as C,d as n,a as e,w as r,g as b,h as k,j as Q,a4 as W,D as Y,X as Z,o as $,n as tt,s as et,E as D,G as O,M as ft,R as gt,l as R,H as at,I as ot,p as vt,m as w,t as h}from"./index-Cy8N1eGd.js";import{S as yt,i as F,e as Ct}from"./StatsCard-SUxJzo4F.js";import{C as bt}from"./CustomTable-DWghEWMD.js";import{u as wt}from"./user-DGiv-qs1.js";import{f as st}from"./common-CIDIMsc8.js";const xt={__name:"users",setup(L,{expose:s}){s();const y=b(),o=b(),S=b(),T=b(),m=b("7d"),_=b(!1),i=b([]),x=b([]);let d=null,c=null,p=null,f=null;const t=k({page:1,size:10,total:0}),A=k({name:"",phone:""}),rt=k([{title:"总用户数",value:1240,icon:Q,type:"primary",trend:"+15%",description:"较上月"},{title:"活跃用户",value:856,icon:W,type:"success",trend:"+8%",description:"较上月"},{title:"新增用户",value:128,icon:Y,type:"warning",trend:"+25%",description:"本月"},{title:"平均停留",value:"8.5分钟",icon:Z,type:"info",trend:"****分钟",description:"较上月"}]),nt=[{prop:"user",label:"用户信息",width:200,slot:!0},{prop:"orderCount",label:"订单数量",width:120,slot:!0},{prop:"totalAmount",label:"消费总额",width:120,slot:!0},{prop:"avgAmount",label:"平均消费",width:120,slot:!0},{prop:"lastOrderTime",label:"最后下单",width:160,slot:!0},{prop:"registerTime",label:"注册时间",width:160,formatter:a=>st(a.registerTime)}],lt=[{prop:"name",label:"用户姓名",type:"input"},{prop:"phone",label:"手机号",type:"input"}],z=()=>E(this,null,function*(){_.value=!0;try{i.value=P(),t.total=50}catch(a){console.error("加载表格数据失败:",a),D.error("加载数据失败")}finally{_.value=!1}}),V=()=>E(this,null,function*(){try{x.value=[{id:1,name:"张三",phone:"138****8001",avatar:"https://picsum.photos/100/100?random=1",orderCount:28,totalAmount:1280},{id:2,name:"李四",phone:"138****8002",avatar:"https://picsum.photos/100/100?random=2",orderCount:24,totalAmount:1156},{id:3,name:"王五",phone:"138****8003",avatar:"https://picsum.photos/100/100?random=3",orderCount:22,totalAmount:998},{id:4,name:"赵六",phone:"138****8004",avatar:"https://picsum.photos/100/100?random=4",orderCount:18,totalAmount:756},{id:5,name:"钱七",phone:"138****8005",avatar:"https://picsum.photos/100/100?random=5",orderCount:16,totalAmount:688}]}catch(a){console.error("加载活跃用户失败:",a)}}),P=()=>{const a=[],g=["张三","李四","王五","赵六","钱七","孙八","周九","吴十"];for(let u=0;u<10;u++){const l=Math.floor(Math.random()*30)+5,v=Math.floor(Math.random()*2e3)+500;a.push({id:u+1,name:g[u%g.length],phone:`138****800${u+1}`,avatar:`https://picsum.photos/100/100?random=${u+1}`,orderCount:l,totalAmount:v.toFixed(2),avgAmount:(v/l).toFixed(2),lastOrderTime:O().subtract(Math.floor(Math.random()*30),"day").toDate(),registerTime:O().subtract(Math.floor(Math.random()*365),"day").toDate()})}return a},G=()=>{y.value&&(d=F(y.value),N())},N=()=>{if(!d)return;const a=m.value==="7d"?7:m.value==="30d"?30:90,g=[],u=[],l=[];let v=1e3;for(let B=a-1;B>=0;B--){g.push(O().subtract(B,"day").format("MM-DD"));const K=Math.floor(Math.random()*20)+5;u.push(K),v+=K,l.push(v)}const pt={tooltip:{trigger:"axis"},legend:{data:["新增用户","总用户数"]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:g},yAxis:[{type:"value",name:"新增用户",position:"left"},{type:"value",name:"总用户数",position:"right"}],series:[{name:"新增用户",type:"bar",data:u,itemStyle:{color:"#409EFF"}},{name:"总用户数",type:"line",yAxisIndex:1,data:l,smooth:!0,itemStyle:{color:"#67C23A"}}]};d.setOption(pt)},I=()=>{o.value&&(c=F(o.value),X())},X=()=>{if(!c)return;const a=[],g=[];for(let l=0;l<24;l++){a.push(l+":00");let v;l>=11&&l<=13?v=Math.floor(Math.random()*50)+80:l>=17&&l<=19?v=Math.floor(Math.random()*60)+90:l>=7&&l<=9?v=Math.floor(Math.random()*30)+40:v=Math.floor(Math.random()*20)+10,g.push(v)}const u={tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:a},yAxis:{type:"value"},series:[{name:"活跃用户数",type:"line",data:g,smooth:!0,areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(64, 158, 255, 0.3)"},{offset:1,color:"rgba(64, 158, 255, 0.1)"}]}},itemStyle:{color:"#409EFF"}}]};c.setOption(u)},H=()=>{if(!S.value)return;p=F(S.value);const g={tooltip:{trigger:"item"},series:[{name:"用户来源",type:"pie",radius:"60%",data:[{value:45,name:"微信分享"},{value:25,name:"朋友推荐"},{value:15,name:"搜索引擎"},{value:10,name:"广告投放"},{value:5,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]};p.setOption(g)},q=()=>{if(!T.value)return;f=F(T.value);const u={tooltip:{trigger:"axis"},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:["18-25","26-35","36-45","46-55","55+"]},yAxis:{type:"value"},series:[{name:"用户数量",type:"bar",data:[25,35,20,15,5],itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#67C23A"},{offset:1,color:"#85CE61"}]}}}]};f.setOption(u)},it=()=>{V(),D.success("活跃用户数据已刷新")},dt=a=>{Object.assign(A,a),t.page=1,z()},ct=()=>{Object.keys(A).forEach(a=>{A[a]=""}),t.page=1,z()},ut=a=>{t.page=a,z()},ht=a=>{t.size=a,t.page=1,z()},mt=()=>{D.info("导出功能开发中...")},j=()=>{d&&d.resize(),c&&c.resize(),p&&p.resize(),f&&f.resize()};$(()=>E(this,null,function*(){yield z(),yield V(),tt(()=>{G(),I(),H(),q()}),window.addEventListener("resize",j)})),et(()=>{window.removeEventListener("resize",j),d&&(d.dispose(),d=null),c&&(c.dispose(),c=null),p&&(p.dispose(),p=null),f&&(f.dispose(),f=null)});const J={growthChartRef:y,activityChartRef:o,sourceChartRef:S,ageChartRef:T,growthPeriod:m,tableLoading:_,tableData:i,activeUsers:x,get growthChart(){return d},set growthChart(a){d=a},get activityChart(){return c},set activityChart(a){c=a},get sourceChart(){return p},set sourceChart(a){p=a},get ageChart(){return f},set ageChart(a){f=a},pagination:t,searchParams:A,userStats:rt,columns:nt,searchFields:lt,loadTableData:z,loadActiveUsers:V,generateMockTableData:P,initGrowthChart:G,updateGrowthChart:N,initActivityChart:I,refreshActivityChart:X,initSourceChart:H,initAgeChart:q,refreshActiveUsers:it,handleSearch:dt,handleReset:ct,handleCurrentChange:ut,handleSizeChange:ht,handleExport:mt,handleResize:j,ref:b,reactive:k,onMounted:$,nextTick:tt,onUnmounted:et,get echarts(){return Ct},get ElMessage(){return D},get Refresh(){return gt},get Download(){return ft},get User(){return Q},get UserFilled(){return W},get TrendCharts(){return Y},get Timer(){return Z},StatsCard:yt,CustomTable:bt,get userApi(){return wt},get formatTime(){return st},get dayjs(){return O}};return Object.defineProperty(J,"__isScriptSetup",{enumerable:!1,value:!0}),J}},At={class:"user-analytics"},Mt={class:"chart-card"},St={class:"chart-header"},Tt={class:"chart-controls"},zt={ref:"growthChartRef",class:"chart-container"},Rt={class:"chart-card"},Et={class:"chart-header"},Ut={ref:"activityChartRef",class:"chart-container"},kt={class:"chart-card"},Dt={ref:"sourceChartRef",class:"chart-container"},Ot={class:"chart-card"},Ft={ref:"ageChartRef",class:"chart-container"},Lt={class:"data-card"},Vt={class:"card-header"},jt={class:"active-users-list"},Bt={class:"user-rank"},Pt={class:"user-avatar"},Gt={class:"user-info"},Nt={class:"user-name"},It={class:"user-phone"},Xt={class:"user-stats"},Ht={class:"orders"},qt={class:"amount"},Jt={class:"table-section"},Kt={class:"user-info"},Qt={class:"user-details"},Wt={class:"user-name"},Yt={class:"user-phone"},Zt={class:"amount-text"},$t={class:"avg-amount"};function te(L,s,y,o,S,T){const m=M("el-col"),_=M("el-row"),i=M("el-radio-button"),x=M("el-radio-group"),d=M("el-icon"),c=M("el-button"),p=M("el-avatar"),f=M("el-tag");return R(),U("div",At,[C(" 用户统计卡片 "),n(_,{gutter:20,class:"stats-row"},{default:r(()=>[(R(!0),U(at,null,ot(o.userStats,(t,A)=>(R(),vt(m,{xs:24,sm:12,md:6,key:A},{default:r(()=>[n(o.StatsCard,{title:t.title,value:t.value,icon:t.icon,type:t.type,trend:t.trend,description:t.description,animated:!0},null,8,["title","value","icon","type","trend","description"])]),_:2},1024))),128))]),_:1}),C(" 图表区域 "),n(_,{gutter:20,class:"charts-section"},{default:r(()=>[C(" 用户增长趋势 "),n(m,{xs:24,lg:12},{default:r(()=>[e("div",Mt,[e("div",St,[s[4]||(s[4]=e("h3",null,"用户增长趋势",-1)),e("div",Tt,[n(x,{modelValue:o.growthPeriod,"onUpdate:modelValue":s[0]||(s[0]=t=>o.growthPeriod=t),size:"small",onChange:o.updateGrowthChart},{default:r(()=>[n(i,{label:"7d"},{default:r(()=>s[1]||(s[1]=[w("近7天",-1)])),_:1,__:[1]}),n(i,{label:"30d"},{default:r(()=>s[2]||(s[2]=[w("近30天",-1)])),_:1,__:[2]}),n(i,{label:"90d"},{default:r(()=>s[3]||(s[3]=[w("近90天",-1)])),_:1,__:[3]})]),_:1},8,["modelValue"])])]),e("div",zt,null,512)])]),_:1}),C(" 用户活跃度分析 "),n(m,{xs:24,lg:12},{default:r(()=>[e("div",Rt,[e("div",Et,[s[6]||(s[6]=e("h3",null,"用户活跃度分析",-1)),n(c,{size:"small",onClick:o.refreshActivityChart},{default:r(()=>[n(d,null,{default:r(()=>[n(o.Refresh)]),_:1}),s[5]||(s[5]=w(" 刷新 ",-1))]),_:1,__:[5]})]),e("div",Ut,null,512)])]),_:1})]),_:1}),C(" 用户行为分析 "),n(_,{gutter:20,class:"analysis-section"},{default:r(()=>[C(" 用户来源分析 "),n(m,{xs:24,lg:8},{default:r(()=>[e("div",kt,[s[7]||(s[7]=e("div",{class:"chart-header"},[e("h3",null,"用户来源分析")],-1)),e("div",Dt,null,512)])]),_:1}),C(" 用户年龄分布 "),n(m,{xs:24,lg:8},{default:r(()=>[e("div",Ot,[s[8]||(s[8]=e("div",{class:"chart-header"},[e("h3",null,"用户年龄分布")],-1)),e("div",Ft,null,512)])]),_:1}),C(" 活跃用户列表 "),n(m,{xs:24,lg:8},{default:r(()=>[e("div",Lt,[e("div",Vt,[s[10]||(s[10]=e("h3",null,"本周活跃用户",-1)),n(c,{size:"small",onClick:o.refreshActiveUsers},{default:r(()=>[n(d,null,{default:r(()=>[n(o.Refresh)]),_:1}),s[9]||(s[9]=w(" 刷新 ",-1))]),_:1,__:[9]})]),e("div",jt,[(R(!0),U(at,null,ot(o.activeUsers,(t,A)=>(R(),U("div",{key:t.id,class:"user-item"},[e("div",Bt,h(A+1),1),e("div",Pt,[n(p,{src:t.avatar,size:40},{default:r(()=>[w(h(t.name?t.name.charAt(0):"U"),1)]),_:2},1032,["src"])]),e("div",Gt,[e("h4",Nt,h(t.name),1),e("p",It,h(t.phone),1),e("div",Xt,[e("span",Ht,"订单: "+h(t.orderCount),1),e("span",qt,"消费: ¥"+h(t.totalAmount),1)])])]))),128))])])]),_:1})]),_:1}),C(" 详细数据表格 "),e("div",Jt,[n(o.CustomTable,{title:"用户消费详情",data:o.tableData,columns:o.columns,loading:o.tableLoading,pagination:o.pagination,"show-search":!0,"search-fields":o.searchFields,onSearch:o.handleSearch,onReset:o.handleReset,onCurrentChange:o.handleCurrentChange,onSizeChange:o.handleSizeChange},{actions:r(()=>[n(c,{onClick:o.handleExport},{default:r(()=>[n(d,null,{default:r(()=>[n(o.Download)]),_:1}),s[11]||(s[11]=w(" 导出报表 ",-1))]),_:1,__:[11]})]),user:r(({row:t})=>[e("div",Kt,[n(p,{src:t.avatar,size:32},{default:r(()=>[w(h(t.name?t.name.charAt(0):"U"),1)]),_:2},1032,["src"]),e("div",Qt,[e("div",Wt,h(t.name||"未知用户"),1),e("div",Yt,h(t.phone||"未知手机号"),1)])])]),orderCount:r(({row:t})=>[n(f,{type:"primary"},{default:r(()=>[w(h(t.orderCount)+"单",1)]),_:2},1024)]),totalAmount:r(({row:t})=>[e("span",Zt,"¥"+h(t.totalAmount),1)]),avgAmount:r(({row:t})=>[e("span",$t,"¥"+h(t.avgAmount),1)]),lastOrderTime:r(({row:t})=>[e("span",null,h(o.formatTime(t.lastOrderTime)),1)]),_:1},8,["data","loading","pagination"])])])}const le=_t(xt,[["render",te],["__scopeId","data-v-e79b550a"],["__file","E:/wx-nan/webs/admin/src/views/analytics/users.vue"]]);export{le as default};
