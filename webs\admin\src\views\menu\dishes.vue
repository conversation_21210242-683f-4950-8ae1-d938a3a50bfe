<template>
  <div class="dish-management">
    <CustomTable
      title="菜品管理"
      :data="tableData"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :show-search="true"
      :show-selection="true"
      :search-fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      @selection-change="handleSelectionChange"
    >
      <template #toolbar>
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增菜品
        </el-button>
        <el-button
          type="danger"
          :disabled="selectedRows.length === 0"
          @click="handleBatchDelete"
        >
          <el-icon><Delete /></el-icon>
          批量删除
        </el-button>
        <el-button type="success" @click="handleBatchImport">
          <el-icon><Upload /></el-icon>
          批量导入
        </el-button>
        <el-button type="warning" @click="handleDownloadTemplate">
          <el-icon><Download /></el-icon>
          下载模板
        </el-button>
        <el-button type="info" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </template>

      <template #image="{ row }">
        <el-image
          v-if="row.image && row.image.startsWith('http')"
          :src="row.image"
          :alt="row.name"
          style="width: 60px; height: 60px; border-radius: 8px"
          fit="cover"
          :preview-src-list="[row.image]"
          :preview-teleported="true"
        >
          <template #error>
            <div
              class="image-slot"
              style="width: 60px; height: 60px; border-radius: 8px; background: #f5f7fa; display: flex; align-items: center; justify-content: center; color: #909399;"
            >
              <el-icon size="24"><Picture /></el-icon>
            </div>
          </template>
        </el-image>
        <div
          v-else
          class="image-slot"
          style="width: 60px; height: 60px; border-radius: 8px; background: #f5f7fa; display: flex; align-items: center; justify-content: center; color: #909399;"
        >
          <el-icon size="24"><Picture /></el-icon>
        </div>
      </template>

      <template #category="{ row }">
        <el-tag :type="getCategoryType(row.category)" size="small">
          {{ row.category }}
        </el-tag>
      </template>

      <template #isAvailable="{ row }">
        <el-switch
          v-model="row.isAvailable"
          @change="handleStatusChange(row)"
          :loading="row.statusLoading"
        />
      </template>

      <template #operation="{ row }">
        <el-button size="small" type="primary" link @click="handleView(row)">
          <el-icon><View /></el-icon>
          查看
        </el-button>
        <el-button size="small" type="warning" link @click="handleEdit(row)">
          <el-icon><Edit /></el-icon>
          编辑
        </el-button>
        <el-button size="small" type="success" link @click="handleCopy(row)">
          <el-icon><CopyDocument /></el-icon>
          复制
        </el-button>
        <el-popconfirm
          title="确定要删除这个菜品吗？"
          @confirm="handleDelete(row)"
        >
          <template #reference>
            <el-button size="small" type="danger" link>
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-popconfirm>
      </template>
    </CustomTable>

    <!-- 菜品表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <DishForm
        ref="dishFormRef"
        :form-data="formData"
        :is-edit="isEdit"
        @submit="handleSubmit"
        @cancel="handleDialogClose"
      />
    </el-dialog>

    <!-- 菜品详情对话框 -->
    <el-dialog v-model="detailVisible" title="菜品详情" width="600px">
      <div class="dish-detail" v-if="currentDish">
        <div class="detail-header">
          <el-image
            v-if="currentDish.image && currentDish.image.startsWith('http')"
            :src="currentDish.image"
            :alt="currentDish.name"
            style="width: 200px; height: 150px; border-radius: 8px"
            fit="cover"
          >
            <template #error>
              <div
                style="width: 200px; height: 150px; border-radius: 8px; background: #f5f7fa; display: flex; align-items: center; justify-content: center; color: #909399;"
              >
                <el-icon size="48"><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div
            v-else
            class="image-slot"
            style="width: 200px; height: 150px; border-radius: 8px; background: #f5f7fa; display: flex; align-items: center; justify-content: center; color: #909399;"
          >
            <el-icon size="48"><Picture /></el-icon>
          </div>
          <div class="detail-info">
            <h3>{{ currentDish.name }}</h3>
            <p class="category">分类：{{ currentDish.category }}</p>
            <p class="status">
              状态：
              <el-tag
                :type="currentDish.isAvailable ? 'success' : 'danger'"
                size="small"
              >
                {{ currentDish.isAvailable ? "可用" : "不可用" }}
              </el-tag>
            </p>
          </div>
        </div>

        <div class="detail-content">
          <el-descriptions :column="1" border>
            <el-descriptions-item label="菜品描述">
              {{ currentDish.description || "暂无描述" }}
            </el-descriptions-item>
            <el-descriptions-item label="食材">
              {{ currentDish.ingredients || "暂无食材信息" }}
            </el-descriptions-item>
            <el-descriptions-item label="制作方法">
              {{ currentDish.cookingMethod || "暂无制作方法" }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ formatTime(currentDish.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatTime(currentDish.updatedAt) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  View,
  Edit,
  Delete,
  Upload,
  Download,
  Picture,
  CopyDocument
} from "@element-plus/icons-vue";
import CustomTable from "@/components/CustomTable.vue";
import DishForm from "./components/DishForm.vue";
import { dishApi } from "@/api/menu";
import { formatTime } from "@/utils/common";
import { exportToExcel, importFromExcel, downloadTemplate, validateExcelFile } from "@/utils/excel";

const loading = ref(false);
const tableData = ref([]);
const dialogVisible = ref(false);
const detailVisible = ref(false);
const isEdit = ref(false);
const dishFormRef = ref();
const currentDish = ref(null);
const selectedRows = ref([]);

const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
});

const searchParams = reactive({});

const formData = ref({});

// 表格列配置
const columns = [
  { prop: "image", label: "图片", width: 80, slot: true },
  { prop: "name", label: "菜品名称", minWidth: 120 },
  { prop: "category", label: "分类", width: 100, slot: true },
  {
    prop: "description",
    label: "描述",
    minWidth: 150,
    showOverflowTooltip: true
  },
  { prop: "isAvailable", label: "状态", width: 80, slot: true },
  {
    prop: "createdAt",
    label: "创建时间",
    width: 150,
    formatter: row => formatTime(row.createdAt, "YYYY-MM-DD HH:mm")
  },
  { label: "操作", width: 200, slot: "operation", fixed: "right" }
];

// 搜索字段配置
const searchFields = [
  {
    prop: "name",
    label: "菜品名称",
    type: "input",
    placeholder: "请输入菜品名称"
  },
  {
    prop: "category",
    label: "分类",
    type: "select",
    placeholder: "选择分类",
    options: [
      { label: "热菜", value: "热菜" },
      { label: "凉菜", value: "凉菜" },
      { label: "汤品", value: "汤品" },
      { label: "主食", value: "主食" },
      { label: "甜品", value: "甜品" }
    ]
  },
  {
    prop: "isAvailable",
    label: "状态",
    type: "select",
    placeholder: "选择状态",
    options: [
      { label: "可用", value: true },
      { label: "不可用", value: false }
    ]
  }
];

// 计算属性
const dialogTitle = computed(() => {
  return isEdit.value ? "编辑菜品" : "新增菜品";
});

// 获取分类类型
const getCategoryType = category => {
  const typeMap = {
    热菜: "danger",
    凉菜: "success",
    汤品: "warning",
    主食: "primary",
    甜品: "info"
  };
  return typeMap[category] || "primary";
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    };

    const response = await dishApi.getDishes(params);
    if (response.code === 200) {
      tableData.value = response.data.list || [];
      pagination.total = response.data.total || 0;
    } else {
      ElMessage.error(response.message || '加载数据失败');
    }
  } catch (error) {
    console.error("加载菜品列表失败:", error);
    ElMessage.error("加载数据失败");
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = params => {
  Object.assign(searchParams, params);
  pagination.page = 1;
  loadData();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchParams).forEach(key => {
    delete searchParams[key];
  });
  pagination.page = 1;
  loadData();
};

// 分页变化
const handleCurrentChange = page => {
  pagination.page = page;
  loadData();
};

const handleSizeChange = size => {
  pagination.size = size;
  pagination.page = 1;
  loadData();
};

// 状态变化
const handleStatusChange = async row => {
  row.statusLoading = true;
  try {
    const result = await dishApi.updateDish(row.id, { isPublished: row.isAvailable });
    if (result.code === 200) {
      ElMessage.success("状态更新成功");
    } else {
      ElMessage.error(result.message || "状态更新失败");
      // 回滚状态
      row.isAvailable = !row.isAvailable;
    }
  } catch (error) {
    console.error("更新状态失败:", error);
    ElMessage.error("状态更新失败");
    // 回滚状态
    row.isAvailable = !row.isAvailable;
  } finally {
    row.statusLoading = false;
  }
};

// 新增菜品
const handleCreate = () => {
  isEdit.value = false;
  formData.value = {};
  dialogVisible.value = true;
};

// 查看菜品
const handleView = row => {
  currentDish.value = row;
  detailVisible.value = true;
};

// 编辑菜品
const handleEdit = row => {
  isEdit.value = true;
  formData.value = { ...row };
  dialogVisible.value = true;
};

// 复制菜品
const handleCopy = row => {
  isEdit.value = false;
  formData.value = {
    ...row,
    id: undefined,
    name: `${row.name} - 副本`
  };
  dialogVisible.value = true;
};

// 删除菜品
const handleDelete = async row => {
  try {
    const result = await dishApi.deleteDish(row.id);
    if (result.code === 200) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error(result.message || "删除失败");
    }
  } catch (error) {
    console.error("删除菜品失败:", error);
    ElMessage.error("删除失败");
  }
};

// 批量删除菜品
const handleBatchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的菜品');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 个菜品吗？`,
      '批量删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    const dishIds = selectedRows.value.map(row => row.id);
    await dishApi.batchOperation({
      operation: 'delete',
      dishIds
    });

    ElMessage.success('批量删除成功');
    selectedRows.value = [];
    loadData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error);
      ElMessage.error('批量删除失败');
    }
  }
};

// 选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 批量导入
const handleBatchImport = () => {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';

  input.onchange = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // 验证文件
    const validation = validateExcelFile(file);
    if (!validation.valid) {
      ElMessage.error(validation.errors.join(', '));
      return;
    }

    try {
      ElMessage.info('正在导入数据...');

      // 导入数据
      const importedData = await importFromExcel(file, {
        columnMapping: {
          '菜品名称': 'name',
          '分类': 'category',
          '描述': 'description',
          '食材': 'ingredients',
          '制作方法': 'cookingMethod',
          '是否可用': 'isAvailable'
        },
        validator: (row, index) => {
          const errors = [];
          if (!row.name) errors.push(`第${index + 1}行：菜品名称不能为空`);
          if (!row.category) errors.push(`第${index + 1}行：分类不能为空`);
          if (!row.description) errors.push(`第${index + 1}行：描述不能为空`);

          return {
            valid: errors.length === 0,
            errors
          };
        }
      });

      // 批量创建菜品
      const createPromises = importedData.map(item => {
        return dishApi.createDish({
          ...item,
          isAvailable: item.isAvailable === '是' || item.isAvailable === true,
          image: '' // 导入时暂不支持图片
        });
      });

      await Promise.all(createPromises);

      ElMessage.success(`成功导入 ${importedData.length} 条数据`);
      loadData();

    } catch (error) {
      console.error('导入失败:', error);
      if (error.type === 'validation') {
        ElMessage.error('数据验证失败，请检查Excel格式');
      } else {
        ElMessage.error('导入失败: ' + error.message);
      }
    }
  };

  input.click();
};

// 导出数据
const handleExport = () => {
  try {
    // 定义导出列
    const exportColumns = [
      { prop: 'name', label: '菜品名称', width: 120 },
      { prop: 'category', label: '分类', width: 100 },
      { prop: 'description', label: '描述', width: 200 },
      { prop: 'ingredients', label: '食材', width: 150 },
      { prop: 'cookingMethod', label: '制作方法', width: 200 },
      {
        prop: 'isAvailable',
        label: '是否可用',
        width: 100,
        formatter: (value) => value ? '是' : '否'
      },
      {
        prop: 'createdAt',
        label: '创建时间',
        width: 150,
        formatter: (value) => formatTime(value)
      }
    ];

    // 导出当前页面数据
    exportToExcel(tableData.value, exportColumns, '菜品数据', {
      sheetName: '菜品列表'
    });

  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败: ' + error.message);
  }
};

// 下载导入模板
const handleDownloadTemplate = () => {
  try {
    const templateColumns = [
      { prop: 'name', label: '菜品名称', example: '宫保鸡丁' },
      { prop: 'category', label: '分类', example: '热菜' },
      { prop: 'description', label: '描述', example: '经典川菜，酸甜可口' },
      { prop: 'ingredients', label: '食材', example: '鸡肉,花生米,青椒,红椒' },
      { prop: 'cookingMethod', label: '制作方法', example: '1.鸡肉切丁腌制 2.热锅下油炒制...' },
      { prop: 'isAvailable', label: '是否可用', example: '是' }
    ];

    downloadTemplate(templateColumns, '菜品导入模板');

  } catch (error) {
    console.error('下载模板失败:', error);
    ElMessage.error('下载模板失败: ' + error.message);
  }
};

// 提交表单
const handleSubmit = async data => {
  try {
    if (isEdit.value) {
      await dishApi.updateDish(formData.value.id, data);
      ElMessage.success("更新成功");
    } else {
      await dishApi.createDish(data);
      ElMessage.success("创建成功");
    }

    dialogVisible.value = false;
    loadData();
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  }
};

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false;
  formData.value = {};
  if (dishFormRef.value) {
    dishFormRef.value.resetForm();
  }
};

onMounted(() => {
  loadData();
});
</script>

<style scoped lang="scss">
.dish-management {
  padding: 20px;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}

.dish-detail {
  .detail-header {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .detail-info {
      flex: 1;

      h3 {
        margin: 0 0 10px 0;
        font-size: 20px;
        color: #333;
      }

      p {
        margin: 8px 0;
        color: #666;

        &.category {
          font-size: 14px;
        }

        &.status {
          font-size: 14px;
        }
      }
    }
  }

  .detail-content {
    :deep(.el-descriptions__body) {
      background: #fafafa;
    }

    :deep(.el-descriptions__label) {
      width: 100px;
      font-weight: 500;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dish-management {
    padding: 16px;
  }

  .dish-detail {
    .detail-header {
      flex-direction: column;
      text-align: center;
    }
  }
}
</style>
