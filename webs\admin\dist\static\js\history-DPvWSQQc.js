var te=Object.defineProperty,ae=Object.defineProperties;var se=Object.getOwnPropertyDescriptors;var F=Object.getOwnPropertySymbols;var oe=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable;var N=(m,t,s)=>t in m?te(m,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):m[t]=s,z=(m,t)=>{for(var s in t||(t={}))oe.call(t,s)&&N(m,s,t[s]);if(F)for(var s of F(t))ne.call(t,s)&&N(m,s,t[s]);return m},H=(m,t)=>ae(m,se(t));var w=(m,t,s)=>new Promise((a,y)=>{var f=c=>{try{i(s.next(c))}catch(v){y(v)}},u=c=>{try{i(s.throw(c))}catch(v){y(v)}},i=c=>c.done?a(c.value):Promise.resolve(c.value).then(f,u);i((s=s.apply(m,t)).next())});import{_ as le,r as D,c as b,d,b as B,w as r,g as x,h as E,q as I,o as O,E as g,G as M,M as re,k as A,l as k,m as p,t as h,a as _,H as R,I as W}from"./index-Cy8N1eGd.js";import{C as ie}from"./CustomTable-DWghEWMD.js";import{m as T}from"./menu-BDmPj0yF.js";const ce={__name:"history",setup(m,{expose:t}){t();const s=x(!1),a=x([]),y=x(!1),f=x(null),u=E({page:1,size:10,total:0}),i=E({date:"",status:""}),c=x([{key:"hot",name:"热菜"},{key:"cold",name:"凉菜"},{key:"soup",name:"汤品"},{key:"staple",name:"主食"},{key:"dessert",name:"甜品"}]),v=[{prop:"date",label:"日期",width:120,formatter:e=>Y(e.date)},{prop:"dishCount",label:"菜品数量",width:100,slot:!0},{prop:"totalPrice",label:"总价值",width:100,formatter:e=>`¥${e.totalPrice||0}`},{prop:"status",label:"状态",width:100,slot:!0},{prop:"createdBy",label:"创建人",width:120},{prop:"createdAt",label:"创建时间",width:160,formatter:e=>V(e.createdAt)},{prop:"updatedAt",label:"更新时间",width:160,formatter:e=>V(e.updatedAt)},{label:"操作",width:180,slot:"operation",fixed:"right"}],S=[{prop:"date",label:"日期",type:"date"},{prop:"status",label:"状态",type:"select",options:[{label:"正常",value:"active"},{label:"已删除",value:"deleted"}]}],o=I(()=>!f.value||!f.value.dishes?[]:c.value.map(e=>H(z({},e),{dishes:f.value.dishes.filter(l=>l.category===e.key)})).filter(e=>e.dishes.length>0)),n=()=>w(this,null,function*(){s.value=!0;try{const e=z({page:u.page,size:u.size},i),l=yield T.getHistoryMenus(e);l.data&&(a.value=l.data.list||[],u.total=l.data.total||0)}catch(e){console.error("加载历史菜单失败:",e),g.error("加载数据失败"),a.value=[],u.total=0}finally{s.value=!1}}),Y=e=>M(e).format("MM-DD"),V=e=>M(e).format("YYYY-MM-DD HH:mm"),q=e=>({active:"success",deleted:"danger"})[e]||"info",G=e=>({active:"正常",deleted:"已删除"})[e]||e,L=e=>!e||!Array.isArray(e)?0:e.reduce((l,C)=>l+(C.price||0),0),U=e=>{Object.assign(i,e),u.page=1,n()},J=()=>{Object.keys(i).forEach(e=>{i[e]=""}),u.page=1,n()},K=e=>{u.page=e,n()},Q=e=>{u.size=e,u.page=1,n()},X=e=>{f.value=e,y.value=!0},P=e=>w(this,null,function*(){try{yield A.confirm("确定要将此菜单复制为今日菜单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"});const l={date:M().format("YYYY-MM-DD"),dishes:e.dishes.map(C=>({dishId:C.id,dishName:C.name,category:C.category,price:C.price,image:C.image}))};yield T.createMenu(l),g.success("菜单复制成功")}catch(l){l!=="cancel"&&(console.error("复制菜单失败:",l),g.error("复制菜单失败"))}}),Z=()=>w(this,null,function*(){try{yield P(f.value),y.value=!1}catch(e){}}),$=e=>w(this,null,function*(){try{yield A.confirm("确定要删除这个菜单吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),yield T.deleteMenu(e.id),g.success("删除成功"),n()}catch(l){l!=="cancel"&&(console.error("删除菜单失败:",l),g.error("删除失败"))}}),ee=()=>w(this,null,function*(){try{g.info("导出功能开发中...")}catch(e){console.error("导出失败:",e),g.error("导出失败")}});O(()=>{n()});const j={loading:s,tableData:a,detailVisible:y,selectedMenu:f,pagination:u,searchParams:i,categories:c,columns:v,searchFields:S,categoriesWithDishes:o,loadData:n,formatDate:Y,formatTime:V,getStatusType:q,getStatusText:G,calculateTotalPrice:L,handleSearch:U,handleReset:J,handleCurrentChange:K,handleSizeChange:Q,handleView:X,handleCopy:P,handleCopyFromDetail:Z,handleDelete:$,handleExport:ee,ref:x,reactive:E,onMounted:O,computed:I,get ElMessage(){return g},get ElMessageBox(){return A},get Download(){return re},CustomTable:ie,get menuApi(){return T},get dayjs(){return M}};return Object.defineProperty(j,"__isScriptSetup",{enumerable:!1,value:!0}),j}},de={class:"menu-history"},ue={key:0,class:"menu-detail"},me={class:"detail-header"},pe={class:"menu-stats"},he={class:"dish-categories"},_e={class:"category-title"},fe={class:"dish-grid"},ge={class:"dish-image"},ye={class:"dish-info"},ve={class:"dish-name"},Ce={class:"dish-price"},be={key:0,class:"dish-desc"};function ke(m,t,s,a,y,f){const u=D("el-icon"),i=D("el-button"),c=D("el-tag"),v=D("el-image"),S=D("el-dialog");return k(),b("div",de,[d(a.CustomTable,{title:"历史菜单",data:a.tableData,columns:a.columns,loading:a.loading,pagination:a.pagination,"show-search":!0,"search-fields":a.searchFields,onSearch:a.handleSearch,onReset:a.handleReset,onCurrentChange:a.handleCurrentChange,onSizeChange:a.handleSizeChange},{actions:r(()=>[d(i,{type:"primary",onClick:a.handleExport},{default:r(()=>[d(u,null,{default:r(()=>[d(a.Download)]),_:1}),t[2]||(t[2]=p(" 导出数据 ",-1))]),_:1,__:[2]})]),dishCount:r(({row:o})=>[d(c,{type:"info"},{default:r(()=>[p(h(o.dishCount)+"道菜",1)]),_:2},1024)]),status:r(({row:o})=>[d(c,{type:a.getStatusType(o.status)},{default:r(()=>[p(h(a.getStatusText(o.status)),1)]),_:2},1032,["type"])]),operation:r(({row:o})=>[d(i,{size:"small",onClick:n=>a.handleView(o)},{default:r(()=>t[3]||(t[3]=[p("查看",-1)])),_:2,__:[3]},1032,["onClick"]),d(i,{size:"small",type:"primary",onClick:n=>a.handleCopy(o)},{default:r(()=>t[4]||(t[4]=[p("复制",-1)])),_:2,__:[4]},1032,["onClick"]),d(i,{size:"small",type:"danger",onClick:n=>a.handleDelete(o)},{default:r(()=>t[5]||(t[5]=[p("删除",-1)])),_:2,__:[5]},1032,["onClick"])]),_:1},8,["data","loading","pagination"]),B(" 菜单详情对话框 "),d(S,{modelValue:a.detailVisible,"onUpdate:modelValue":t[1]||(t[1]=o=>a.detailVisible=o),title:"菜单详情",width:"900px"},{footer:r(()=>[d(i,{onClick:t[0]||(t[0]=o=>a.detailVisible=!1)},{default:r(()=>t[6]||(t[6]=[p("关闭",-1)])),_:1,__:[6]}),d(i,{type:"primary",onClick:a.handleCopyFromDetail},{default:r(()=>t[7]||(t[7]=[p(" 复制为今日菜单 ",-1)])),_:1,__:[7]})]),default:r(()=>[a.selectedMenu?(k(),b("div",ue,[_("div",me,[_("h3",null,h(a.selectedMenu.date)+" 菜单",1),_("div",pe,[d(c,null,{default:r(()=>[p("共 "+h(a.selectedMenu.dishCount)+" 道菜",1)]),_:1}),d(c,{type:"success",class:"ml-2"},{default:r(()=>[p(" 总价值 ¥"+h(a.calculateTotalPrice(a.selectedMenu.dishes)),1)]),_:1})])]),_("div",he,[(k(!0),b(R,null,W(a.categoriesWithDishes,o=>(k(),b("div",{key:o.key,class:"category-section"},[_("h4",_e,h(o.name)+" ("+h(o.dishes.length)+") ",1),_("div",fe,[(k(!0),b(R,null,W(o.dishes,n=>(k(),b("div",{key:n.id,class:"dish-card"},[_("div",ge,[d(v,{src:n.image,fit:"cover"},null,8,["src"])]),_("div",ye,[_("h5",ve,h(n.name),1),_("p",Ce,"¥"+h(n.price),1),n.description?(k(),b("p",be,h(n.description),1)):B("v-if",!0)])]))),128))])]))),128))])])):B("v-if",!0)]),_:1},8,["modelValue"])])}const Te=le(ce,[["render",ke],["__scopeId","data-v-28e2c8f4"],["__file","E:/wx-nan/webs/admin/src/views/menu/history.vue"]]);export{Te as default};
