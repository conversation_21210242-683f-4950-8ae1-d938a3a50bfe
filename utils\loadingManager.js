/**
 * Loading状态管理器
 * 统一管理页面和组件的loading状态
 */

// 尝试加载UI工具，如果失败则使用兼容版本
let ui;
try {
  ui = require('./ui.js');
} catch (error) {
  ui = require('./ui-compat.js');
}

class LoadingManager {
  constructor() {
    // 全局loading状态
    this.globalLoading = false;
    this.globalLoadingCount = 0;

    // 页面级loading状态
    this.pageLoadings = new Map();

    // 组件级loading状态
    this.componentLoadings = new Map();

    // 按钮loading状态
    this.buttonLoadings = new Map();

    // loading配置
    this.config = {
      minDuration: 300, // 最小显示时间
      maxDuration: 30000, // 最大显示时间
      defaultText: '加载中...'
    };
  }

  /**
   * 显示全局loading
   * @param {string} text loading文本
   * @param {Object} options 配置选项
   */
  showGlobal(text = this.config.defaultText, options = {}) {
    this.globalLoadingCount++;

    if (!this.globalLoading) {
      this.globalLoading = true;
      ui.showLoading(text);

      // 设置最大显示时间
      setTimeout(() => {
        if (this.globalLoading) {
          this.hideGlobal();
          console.warn('Global loading timeout, force hide');
        }
      }, options.maxDuration || this.config.maxDuration);
    }
  }

  /**
   * 隐藏全局loading
   */
  hideGlobal() {
    this.globalLoadingCount = Math.max(0, this.globalLoadingCount - 1);

    if (this.globalLoadingCount === 0 && this.globalLoading) {
      this.globalLoading = false;
      ui.hideLoading();
    }
  }

  /**
   * 强制隐藏全局loading
   */
  forceHideGlobal() {
    this.globalLoading = false;
    this.globalLoadingCount = 0;
    ui.hideLoading();
  }

  /**
   * 设置页面loading状态
   * @param {string} pageId 页面ID
   * @param {boolean} loading 是否loading
   * @param {string} text loading文本
   */
  setPageLoading(pageId, loading, text = this.config.defaultText) {
    if (loading) {
      this.pageLoadings.set(pageId, {
        loading: true,
        text,
        startTime: Date.now()
      });
    } else {
      const pageLoading = this.pageLoadings.get(pageId);
      if (pageLoading) {
        const duration = Date.now() - pageLoading.startTime;

        // 确保最小显示时间
        if (duration < this.config.minDuration) {
          setTimeout(() => {
            this.pageLoadings.delete(pageId);
          }, this.config.minDuration - duration);
        } else {
          this.pageLoadings.delete(pageId);
        }
      }
    }
  }

  /**
   * 获取页面loading状态
   * @param {string} pageId 页面ID
   * @returns {Object|null}
   */
  getPageLoading(pageId) {
    return this.pageLoadings.get(pageId) || null;
  }

  /**
   * 检查页面是否在loading
   * @param {string} pageId 页面ID
   * @returns {boolean}
   */
  isPageLoading(pageId) {
    const pageLoading = this.pageLoadings.get(pageId);
    return pageLoading && pageLoading.loading;
  }

  /**
   * 设置组件loading状态
   * @param {string} componentId 组件ID
   * @param {boolean} loading 是否loading
   * @param {Object} options 配置选项
   */
  setComponentLoading(componentId, loading, options = {}) {
    if (loading) {
      this.componentLoadings.set(componentId, {
        loading: true,
        text: options.text || this.config.defaultText,
        startTime: Date.now(),
        ...options
      });
    } else {
      const componentLoading = this.componentLoadings.get(componentId);
      if (componentLoading) {
        const duration = Date.now() - componentLoading.startTime;

        if (duration < this.config.minDuration) {
          setTimeout(() => {
            this.componentLoadings.delete(componentId);
          }, this.config.minDuration - duration);
        } else {
          this.componentLoadings.delete(componentId);
        }
      }
    }
  }

  /**
   * 获取组件loading状态
   * @param {string} componentId 组件ID
   * @returns {Object|null}
   */
  getComponentLoading(componentId) {
    return this.componentLoadings.get(componentId) || null;
  }

  /**
   * 检查组件是否在loading
   * @param {string} componentId 组件ID
   * @returns {boolean}
   */
  isComponentLoading(componentId) {
    const componentLoading = this.componentLoadings.get(componentId);
    return componentLoading && componentLoading.loading;
  }

  /**
   * 设置按钮loading状态
   * @param {string} buttonId 按钮ID
   * @param {boolean} loading 是否loading
   * @param {Object} options 配置选项
   */
  setButtonLoading(buttonId, loading, options = {}) {
    if (loading) {
      this.buttonLoadings.set(buttonId, {
        loading: true,
        text: options.text || '处理中...',
        startTime: Date.now(),
        originalText: options.originalText,
        ...options
      });
    } else {
      this.buttonLoadings.delete(buttonId);
    }
  }

  /**
   * 获取按钮loading状态
   * @param {string} buttonId 按钮ID
   * @returns {Object|null}
   */
  getButtonLoading(buttonId) {
    return this.buttonLoadings.get(buttonId) || null;
  }

  /**
   * 检查按钮是否在loading
   * @param {string} buttonId 按钮ID
   * @returns {boolean}
   */
  isButtonLoading(buttonId) {
    const buttonLoading = this.buttonLoadings.get(buttonId);
    return buttonLoading && buttonLoading.loading;
  }

  /**
   * 包装异步函数，自动管理loading状态
   * @param {Function} asyncFn 异步函数
   * @param {Object} options 配置选项
   * @returns {Function}
   */
  wrapAsync(asyncFn, options = {}) {
    const {
      type = 'global', // global, page, component, button
      id,
      text,
      showError = true
    } = options;

    return async (...args) => {
      try {
        // 显示loading
        switch (type) {
          case 'global':
            this.showGlobal(text);
            break;
          case 'page':
            this.setPageLoading(id, true, text);
            break;
          case 'component':
            this.setComponentLoading(id, true, {text});
            break;
          case 'button':
            this.setButtonLoading(id, true, {text});
            break;
        }

        const result = await asyncFn(...args);
        return result;
      } catch (error) {
        if (showError) {
          console.error('Async function error:', error);
        }
        throw error;
      } finally {
        // 隐藏loading
        switch (type) {
          case 'global':
            this.hideGlobal();
            break;
          case 'page':
            this.setPageLoading(id, false);
            break;
          case 'component':
            this.setComponentLoading(id, false);
            break;
          case 'button':
            this.setButtonLoading(id, false);
            break;
        }
      }
    };
  }

  /**
   * 批量操作loading状态
   * @param {Array} operations 操作列表
   */
  batchOperation(operations = []) {
    operations.forEach(op => {
      const {type, action, id, ...options} = op;

      switch (type) {
        case 'global':
          if (action === 'show') {
            this.showGlobal(options.text);
          } else {
            this.hideGlobal();
          }
          break;
        case 'page':
          this.setPageLoading(id, action === 'show', options.text);
          break;
        case 'component':
          this.setComponentLoading(id, action === 'show', options);
          break;
        case 'button':
          this.setButtonLoading(id, action === 'show', options);
          break;
      }
    });
  }

  /**
   * 清除所有loading状态
   */
  clearAll() {
    this.forceHideGlobal();
    this.pageLoadings.clear();
    this.componentLoadings.clear();
    this.buttonLoadings.clear();
  }

  /**
   * 获取当前所有loading状态
   * @returns {Object}
   */
  getAllLoadingStates() {
    return {
      global: {
        loading: this.globalLoading,
        count: this.globalLoadingCount
      },
      pages: Array.from(this.pageLoadings.entries()).map(([id, state]) => ({
        id,
        ...state
      })),
      components: Array.from(this.componentLoadings.entries()).map(
        ([id, state]) => ({
          id,
          ...state
        })
      ),
      buttons: Array.from(this.buttonLoadings.entries()).map(([id, state]) => ({
        id,
        ...state
      }))
    };
  }
}

// 创建全局实例
const loadingManager = new LoadingManager();

// 挂载到全局app
const app = getApp();
if (app) {
  app.loadingManager = loadingManager;
}

module.exports = loadingManager;
