/* 动画效果库 - 小程序兼容 */

// 🎭 动画变量
$animation-duration-fast: 0.15s;
$animation-duration-normal: 0.3s;
$animation-duration-slow: 0.5s;
$animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
$animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

// 🎯 基础动画 Mixins

// 淡入动画
@mixin fade-in($duration: $animation-duration-normal) {
  animation: fadeIn $duration $animation-easing;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 从下往上滑入
@mixin slide-up($duration: $animation-duration-normal) {
  animation: slideUp $duration $animation-easing;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 从左往右滑入
@mixin slide-right($duration: $animation-duration-normal) {
  animation: slideRight $duration $animation-easing;
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-40rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 缩放弹入
@mixin scale-in($duration: $animation-duration-normal) {
  animation: scaleIn $duration $animation-easing-bounce;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 弹跳动画
@mixin bounce($duration: $animation-duration-slow) {
  animation: bounce $duration $animation-easing-bounce;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -6rpx, 0);
  }
  70% {
    transform: translate3d(0, -3rpx, 0);
  }
  90% {
    transform: translate3d(0, -1rpx, 0);
  }
}

// 摇摆动画
@mixin shake($duration: $animation-duration-slow) {
  animation: shake $duration $animation-easing;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2rpx);
  }
}

// 脉冲动画
@mixin pulse($duration: 2s) {
  animation: pulse $duration infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 🎨 组合动画类

// 卡片入场动画
@mixin card-enter($delay: 0s) {
  opacity: 0;
  transform: translateY(20rpx);
  animation: cardEnter $animation-duration-normal $animation-easing $delay forwards;
}

@keyframes cardEnter {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 按钮点击动画
@mixin button-press {
  transition: all $animation-duration-fast $animation-easing;
  
  &:active {
    transform: scale(0.95);
  }
}

// 悬浮动画
@mixin float($duration: 3s) {
  animation: float $duration ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}

// 旋转动画
@mixin rotate($duration: 1s) {
  animation: rotate $duration linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 🎭 页面级动画

// 页面入场动画
@mixin page-enter {
  .page-content {
    @include fade-in($animation-duration-normal);
  }
  
  .page-header {
    @include slide-up($animation-duration-normal);
  }
  
  .page-card {
    @include card-enter(0.1s);
    
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    
    &:nth-child(3) {
      animation-delay: 0.3s;
    }
    
    &:nth-child(4) {
      animation-delay: 0.4s;
    }
  }
}

// 列表项动画
@mixin list-item-enter($index: 0) {
  @include card-enter(#{$index * 0.1s});
}

// 🎯 交互动画类

// 点击波纹效果
@mixin ripple-effect {
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }
  
  &:active::before {
    width: 200%;
    height: 200%;
  }
}

// 悬停提升效果
@mixin hover-lift {
  transition: all $animation-duration-normal $animation-easing;
  
  &:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  }
}

// 🎪 特殊效果

// 渐变背景动画
@mixin gradient-animation($duration: 3s) {
  background-size: 200% 200%;
  animation: gradientShift $duration ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

// 打字机效果
@mixin typewriter($steps: 20, $duration: 2s) {
  overflow: hidden;
  white-space: nowrap;
  border-right: 2rpx solid;
  animation: 
    typing $duration steps($steps, end),
    blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: currentColor;
  }
}

// 🎨 工具类

// 延迟动画
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

// 动画持续时间
.animate-fast { animation-duration: $animation-duration-fast; }
.animate-normal { animation-duration: $animation-duration-normal; }
.animate-slow { animation-duration: $animation-duration-slow; }

// 动画填充模式
.animate-forwards { animation-fill-mode: forwards; }
.animate-backwards { animation-fill-mode: backwards; }
.animate-both { animation-fill-mode: both; }

// 动画播放状态
.animate-paused { animation-play-state: paused; }
.animate-running { animation-play-state: running; }
